<?php
/**
 * 数据库操作类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class PRS_Database {

    /**
     * 构造函数
     */
    public function __construct() {
        // 数据库相关钩子
        add_action('init', array($this, 'upgrade_database'));
    }

    /**
     * 创建数据库表
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // 产品审核表
        $table_name = $wpdb->prefix . 'product_reviews';
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            original_data longtext NOT NULL,
            modified_data longtext NOT NULL,
            change_summary text DEFAULT '',
            submitter_id bigint(20) NOT NULL,
            reviewer_id bigint(20) DEFAULT NULL,
            admin_id bigint(20) DEFAULT NULL,
            status varchar(20) DEFAULT 'pending_review',
            reviewer_status varchar(20) DEFAULT 'pending',
            admin_status varchar(20) DEFAULT 'pending',
            reviewer_notes text DEFAULT '',
            admin_notes text DEFAULT '',
            reviewer_date datetime DEFAULT NULL,
            admin_date datetime DEFAULT NULL,
            is_new_product tinyint(1) DEFAULT 0,
            operation_type varchar(20) DEFAULT 'modify',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY submitter_id (submitter_id),
            KEY reviewer_id (reviewer_id),
            KEY admin_id (admin_id),
            KEY status (status),
            KEY reviewer_status (reviewer_status),
            KEY admin_status (admin_status),
            KEY is_new_product (is_new_product)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // 更新数据库版本
        update_option('prs_db_version', '1.0.0');

        // 检查并修复现有表结构
        self::upgrade_tables();
    }

    /**
     * 升级数据库表结构
     */
    public static function upgrade_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        // 检查表是否存在 - 使用安全的方法
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name)) == $table_name;

        if ($table_exists) {
            // 检查并添加缺失的列 - 使用安全的表名处理
            $safe_table_name = esc_sql($table_name);
            $columns = $wpdb->get_results("DESCRIBE `{$safe_table_name}`");
            $existing_columns = array_column($columns, 'Field');

            $required_columns = array(
                'submitter_id' => "ALTER TABLE $table_name ADD COLUMN submitter_id bigint(20) NOT NULL DEFAULT 0 AFTER change_summary",
                'reviewer_id' => "ALTER TABLE $table_name ADD COLUMN reviewer_id bigint(20) DEFAULT NULL AFTER submitter_id",
                'admin_id' => "ALTER TABLE $table_name ADD COLUMN admin_id bigint(20) DEFAULT NULL AFTER reviewer_id",
                'reviewer_status' => "ALTER TABLE $table_name ADD COLUMN reviewer_status varchar(20) DEFAULT 'pending' AFTER status",
                'admin_status' => "ALTER TABLE $table_name ADD COLUMN admin_status varchar(20) DEFAULT 'pending' AFTER reviewer_status",
                'reviewer_notes' => "ALTER TABLE $table_name ADD COLUMN reviewer_notes text DEFAULT '' AFTER admin_status",
                'admin_notes' => "ALTER TABLE $table_name ADD COLUMN admin_notes text DEFAULT '' AFTER reviewer_notes",
                'reviewer_date' => "ALTER TABLE $table_name ADD COLUMN reviewer_date datetime DEFAULT NULL AFTER admin_notes",
                'admin_date' => "ALTER TABLE $table_name ADD COLUMN admin_date datetime DEFAULT NULL AFTER reviewer_date",
                'is_new_product' => "ALTER TABLE $table_name ADD COLUMN is_new_product tinyint(1) DEFAULT 0 AFTER admin_date"
            );

            foreach ($required_columns as $column => $sql) {
                if (!in_array($column, $existing_columns)) {
                    // 使用预处理语句执行DDL操作
                    $wpdb->query($sql);
                }
            }

            // 添加缺失的索引 - 使用安全的表名
            $indexes = $wpdb->get_results("SHOW INDEX FROM `{$safe_table_name}`");
            $existing_indexes = array_column($indexes, 'Key_name');

            $required_indexes = array(
                'submitter_id' => "ALTER TABLE $table_name ADD KEY submitter_id (submitter_id)",
                'reviewer_id' => "ALTER TABLE $table_name ADD KEY reviewer_id (reviewer_id)",
                'admin_id' => "ALTER TABLE $table_name ADD KEY admin_id (admin_id)",
                'reviewer_status' => "ALTER TABLE $table_name ADD KEY reviewer_status (reviewer_status)",
                'admin_status' => "ALTER TABLE $table_name ADD KEY admin_status (admin_status)"
            );

            foreach ($required_indexes as $index => $sql) {
                if (!in_array($index, $existing_indexes)) {
                    $wpdb->query($sql);
                }
            }
        }
    }

    /**
     * 添加产品审核记录
     */
    public static function add_review($data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        // 检查表结构，确保所有字段都存在 - 使用安全的查询
        $safe_table_name = esc_sql($table_name);
        $columns = $wpdb->get_results("DESCRIBE `{$safe_table_name}`");
        $existing_columns = array_column($columns, 'Field');

        // 基础数据
        $insert_data = array(
            'product_id' => $data['product_id'],
            'original_data' => $data['original_data'],
            'modified_data' => $data['modified_data'],
            'change_summary' => $data['change_summary'],
            'submitter_id' => $data['submitter_id'],
            'status' => 'pending_review',
            'reviewer_status' => 'pending',
            'admin_status' => 'pending',
            'created_at' => current_time('mysql')
        );

        $format = array('%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s');

        // 如果 is_new_product 字段存在，则添加
        if (in_array('is_new_product', $existing_columns)) {
            $insert_data['is_new_product'] = isset($data['is_new_product']) ? $data['is_new_product'] : 0;
            $format[] = '%d';
        }

        // 如果 operation_type 字段存在，则添加
        if (in_array('operation_type', $existing_columns)) {
            $insert_data['operation_type'] = isset($data['operation_type']) ? $data['operation_type'] : 'modify';
            $format[] = '%s';
        }

        $result = $wpdb->insert($table_name, $insert_data, $format);

        // 如果插入失败，记录错误信息
        if (!$result) {
            error_log('PRS Database Error: ' . $wpdb->last_error);
            error_log('PRS Insert Data: ' . print_r($insert_data, true));
            return false;
        }

        return $wpdb->insert_id;
    }

    /**
     * 获取审核记录
     */
    public static function get_review($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $id
        ));
    }

    /**
     * 更新审核记录
     */
    public static function update_review($id, $data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        $update_data = array();
        $format = array();

        // 动态构建更新数据
        $allowed_fields = array(
            'reviewer_id', 'admin_id', 'status', 'reviewer_status', 'admin_status',
            'reviewer_notes', 'admin_notes', 'reviewer_date', 'admin_date'
        );

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $update_data[$field] = $data[$field];
                if (in_array($field, array('reviewer_id', 'admin_id'))) {
                    $format[] = '%d';
                } else {
                    $format[] = '%s';
                }
            }
        }

        if (empty($update_data)) {
            return false;
        }

        $update_data['updated_at'] = current_time('mysql');
        $format[] = '%s';

        return $wpdb->update(
            $table_name,
            $update_data,
            array('id' => $id),
            $format,
            array('%d')
        );
    }

    /**
     * 获取待审核列表
     */
    public static function get_pending_reviews($user_role = '', $limit = 20, $offset = 0) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        $where_clause = '';
        if ($user_role === 'reviewer') {
            $where_clause = "WHERE reviewer_status = 'pending' AND status = 'pending_review'";
        } elseif ($user_role === 'admin') {
            $where_clause = "WHERE reviewer_status = 'approved' AND admin_status = 'pending' AND status = 'pending_admin'";
        } else {
            $where_clause = "WHERE status IN ('pending_review', 'pending_admin')";
        }

        // 待审核列表按创建时间倒序排列（最新提交的在前面）
        $sql = "SELECT * FROM $table_name $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d";

        return $wpdb->get_results($wpdb->prepare($sql, $limit, $offset));
    }

    /**
     * 获取审核历史
     */
    public static function get_review_history($limit = 20, $offset = 0, $status = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        $where_clause = '';
        if (!empty($status)) {
            $where_clause = $wpdb->prepare("WHERE status = %s", $status);
        }

        // 优化排序逻辑：
        // 1. 等待审核的产品（pending_review, pending_admin）排在最前面
        // 2. 其他状态按照更新时间倒序排列
        $order_clause = "ORDER BY
            CASE
                WHEN status IN ('pending_review', 'pending_admin') THEN 0
                ELSE 1
            END ASC,
            CASE
                WHEN status IN ('pending_review', 'pending_admin') THEN created_at
                ELSE updated_at
            END DESC";

        $sql = "SELECT * FROM $table_name $where_clause $order_clause LIMIT %d OFFSET %d";

        return $wpdb->get_results($wpdb->prepare($sql, $limit, $offset));
    }

    /**
     * 获取审核统计
     */
    public static function get_review_stats() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        $stats = array();

        // 总数统计 - 使用安全的表名
        $safe_table_name = esc_sql($table_name);
        $stats['total'] = $wpdb->get_var("SELECT COUNT(*) FROM `{$safe_table_name}`");

        // 状态统计 - 使用安全的表名
        $status_counts = $wpdb->get_results("SELECT status, COUNT(*) as count FROM `{$safe_table_name}` GROUP BY status");

        foreach ($status_counts as $status) {
            $stats[$status->status] = $status->count;
        }

        // 默认值
        $default_statuses = array('pending_review', 'pending_admin', 'approved', 'rejected');
        foreach ($default_statuses as $status) {
            if (!isset($stats[$status])) {
                $stats[$status] = 0;
            }
        }

        return $stats;
    }

    /**
     * 删除审核记录
     */
    public static function delete_review($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        return $wpdb->delete(
            $table_name,
            array('id' => $id),
            array('%d')
        );
    }

    /**
     * 获取产品的待审核记录
     */
    public static function get_product_pending_review($product_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE product_id = %d AND status IN ('pending_review', 'pending_admin') ORDER BY created_at DESC LIMIT 1",
            $product_id
        ));
    }

    /**
     * 获取状态选项
     */
    public static function get_status_options() {
        return array(
            'pending_review' => __('等待审核员审核', 'product-review-system'),
            'pending_admin' => __('等待管理员审核', 'product-review-system'),
            'approved' => __('已通过', 'product-review-system'),
            'rejected' => __('已拒绝', 'product-review-system')
        );
    }

    /**
     * 获取审核员状态选项
     */
    public static function get_reviewer_status_options() {
        return array(
            'pending' => __('待审核', 'product-review-system'),
            'approved' => __('已通过', 'product-review-system'),
            'rejected' => __('已拒绝', 'product-review-system')
        );
    }

    /**
     * 获取管理员状态选项
     */
    public static function get_admin_status_options() {
        return array(
            'pending' => __('待审核', 'product-review-system'),
            'approved' => __('已通过', 'product-review-system'),
            'rejected' => __('已拒绝', 'product-review-system')
        );
    }

    /**
     * 数据库升级方法
     */
    public function upgrade_database() {
        $current_version = get_option('prs_db_version', '1.0.0');
        $target_version = '1.1.0'; // 新版本，支持删除审核

        if (version_compare($current_version, $target_version, '<')) {
            $this->upgrade_to_1_1_0();
            update_option('prs_db_version', $target_version);
        }
    }

    /**
     * 升级到1.1.0版本 - 添加operation_type字段
     */
    private function upgrade_to_1_1_0() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'product_reviews';

        // 检查字段是否已存在
        $column_exists = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'operation_type'",
                DB_NAME,
                $table_name
            )
        );

        if (empty($column_exists)) {
            // 添加operation_type字段
            $sql = "ALTER TABLE `{$table_name}` ADD COLUMN `operation_type` varchar(20) DEFAULT 'modify' AFTER `is_new_product`";
            $wpdb->query($sql);

            // 记录升级日志
            error_log('PRS: Database upgraded to version 1.1.0 - Added operation_type column');
        }
    }

    /**
     * 获取操作类型选项
     */
    public static function get_operation_type_options() {
        return array(
            'modify' => __('修改产品', 'product-review-system'),
            'delete' => __('删除产品', 'product-review-system')
        );
    }
}
