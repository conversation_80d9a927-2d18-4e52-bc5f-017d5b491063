<?php
/**
 * 测试删除重定向功能
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once($wp_load_path);
    } else {
        die('请在WordPress环境中运行此测试文件');
    }
}

// 检查用户权限
if (!current_user_can('manage_options')) {
    die('您没有权限运行此测试');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>删除重定向测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>删除重定向功能测试</h1>
    
    <?php
    $user_id = get_current_user_id();
    
    // 处理测试操作
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'test_redirect':
                $test_url = admin_url('admin.php?page=product-review-system');
                set_transient('prs_force_redirect_' . $user_id, $test_url, 60);
                echo '<div class="test-section">';
                echo '<p class="success">✓ 已设置测试重定向，页面应该会自动跳转到产品审核系统</p>';
                echo '</div>';
                ?>
                <script>
                setTimeout(function() {
                    window.location.href = '<?php echo esc_js($test_url); ?>';
                }, 1000);
                </script>
                <?php
                break;
                
            case 'clear_transients':
                delete_transient('prs_force_redirect_' . $user_id);
                delete_transient('prs_redirect_after_delete_' . $user_id);
                delete_transient('prs_delete_success_' . $user_id);
                delete_transient('prs_delete_error_' . $user_id);
                echo '<div class="test-section">';
                echo '<p class="success">✓ 已清除所有重定向相关的transient</p>';
                echo '</div>';
                break;
        }
    }
    
    echo '<div class="test-section">';
    echo '<h2>当前Transient状态</h2>';
    
    $force_redirect = get_transient('prs_force_redirect_' . $user_id);
    $delete_redirect = get_transient('prs_redirect_after_delete_' . $user_id);
    $delete_success = get_transient('prs_delete_success_' . $user_id);
    $delete_error = get_transient('prs_delete_error_' . $user_id);
    
    echo '<p><strong>强制重定向:</strong> ' . ($force_redirect ? esc_html($force_redirect) : '无') . '</p>';
    echo '<p><strong>删除重定向:</strong> ' . ($delete_redirect ? esc_html($delete_redirect) : '无') . '</p>';
    echo '<p><strong>删除成功消息:</strong> ' . ($delete_success ? esc_html($delete_success) : '无') . '</p>';
    echo '<p><strong>删除错误消息:</strong> ' . ($delete_error ? esc_html($delete_error) : '无') . '</p>';
    echo '</div>';
    
    echo '<div class="test-section">';
    echo '<h2>测试操作</h2>';
    ?>
    
    <form method="post">
        <button type="submit" name="action" value="test_redirect">测试重定向功能</button>
        <button type="submit" name="action" value="clear_transients">清除所有Transient</button>
    </form>
    
    </div>
    
    <div class="test-section">
        <h2>重定向机制说明</h2>
        <p>删除产品后的重定向使用了多重机制：</p>
        <ol>
            <li><strong>HTTP重定向</strong>：如果HTTP头还没有发送，直接使用wp_redirect</li>
            <li><strong>JavaScript重定向</strong>：在admin_footer中输出JavaScript立即重定向</li>
            <li><strong>Transient重定向</strong>：在admin_init中检查transient并执行重定向</li>
            <li><strong>延迟JavaScript</strong>：在页面加载完成后执行的JavaScript重定向</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>调试信息</h2>
        <p><strong>当前用户ID:</strong> <?php echo $user_id; ?></p>
        <p><strong>当前页面:</strong> <?php echo esc_html($_SERVER['REQUEST_URI']); ?></p>
        <p><strong>是否是管理后台:</strong> <?php echo is_admin() ? '是' : '否'; ?></p>
    </div>
    
    <div class="test-section">
        <h2>快速链接</h2>
        <p><a href="<?php echo admin_url('edit.php?post_type=product'); ?>" target="_blank">产品列表页面</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=product-review-system'); ?>" target="_blank">产品审核系统</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=prs-pending-reviews'); ?>" target="_blank">待审核列表</a></p>
    </div>
    
    <script>
    // 检查是否有强制重定向
    <?php if ($force_redirect): ?>
    console.log('检测到强制重定向: <?php echo esc_js($force_redirect); ?>');
    setTimeout(function() {
        console.log('执行强制重定向...');
        window.location.href = '<?php echo esc_js($force_redirect); ?>';
    }, 2000);
    <?php endif; ?>
    </script>
</body>
</html>
