<?php
/**
 * 管理设置页面类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class Login_Captcha_Admin_Settings {

    /**
     * 注册设置
     */
    public function register_settings() {
        // 注册设置组
        register_setting('login_captcha_settings', 'login_captcha_enabled');
        register_setting('login_captcha_settings', 'login_captcha_type');
        register_setting('login_captcha_settings', 'login_captcha_length');
        register_setting('login_captcha_settings', 'login_captcha_width');
        register_setting('login_captcha_settings', 'login_captcha_height');

        // 添加设置部分
        add_settings_section(
            'login_captcha_general',
            __('基本设置', 'login-captcha'),
            array($this, 'general_section_callback'),
            'login_captcha_settings'
        );

        add_settings_section(
            'login_captcha_appearance',
            __('外观设置', 'login-captcha'),
            array($this, 'appearance_section_callback'),
            'login_captcha_settings'
        );

        // 添加设置字段
        add_settings_field(
            'login_captcha_enabled',
            __('启用验证码', 'login-captcha'),
            array($this, 'enabled_field_callback'),
            'login_captcha_settings',
            'login_captcha_general'
        );

        add_settings_field(
            'login_captcha_type',
            __('验证码类型', 'login-captcha'),
            array($this, 'type_field_callback'),
            'login_captcha_settings',
            'login_captcha_general'
        );

        add_settings_field(
            'login_captcha_length',
            __('验证码长度', 'login-captcha'),
            array($this, 'length_field_callback'),
            'login_captcha_settings',
            'login_captcha_general'
        );

        add_settings_field(
            'login_captcha_width',
            __('图片宽度', 'login-captcha'),
            array($this, 'width_field_callback'),
            'login_captcha_settings',
            'login_captcha_appearance'
        );

        add_settings_field(
            'login_captcha_height',
            __('图片高度', 'login-captcha'),
            array($this, 'height_field_callback'),
            'login_captcha_settings',
            'login_captcha_appearance'
        );
    }

    /**
     * 显示管理页面
     */
    public function display_page() {
        // 检查用户权限
        if (!current_user_can('manage_options')) {
            wp_die(__('您没有权限访问此页面。', 'login-captcha'));
        }

        // 处理表单提交
        if (isset($_POST['submit'])) {
            // 验证nonce
            if (!check_admin_referer('login_captcha_settings_nonce')) {
                wp_die(__('安全验证失败', 'login-captcha'));
            }

            // 验证和清理输入
            $enabled = isset($_POST['login_captcha_enabled']) ? 1 : 0;
            $type = sanitize_text_field($_POST['login_captcha_type'] ?? 'mixed');
            $length = intval($_POST['login_captcha_length'] ?? 4);
            $width = intval($_POST['login_captcha_width'] ?? 120);
            $height = intval($_POST['login_captcha_height'] ?? 40);

            // 验证值的范围
            $type = in_array($type, array('numbers', 'letters', 'mixed')) ? $type : 'mixed';
            $length = max(3, min(8, $length)); // 限制长度在3-8之间
            $width = max(80, min(300, $width)); // 限制宽度在80-300之间
            $height = max(30, min(100, $height)); // 限制高度在30-100之间

            // 更新选项
            update_option('login_captcha_enabled', $enabled);
            update_option('login_captcha_type', $type);
            update_option('login_captcha_length', $length);
            update_option('login_captcha_width', $width);
            update_option('login_captcha_height', $height);

            echo '<div class="notice notice-success"><p>' . esc_html__('设置已保存。', 'login-captcha') . '</p></div>';
        }

        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="card">
                <h2><?php _e('验证码预览', 'login-captcha'); ?></h2>
                <p><?php _e('这是当前设置下验证码的预览效果：', 'login-captcha'); ?></p>
                <div id="captcha-preview">
                    <img src="<?php echo admin_url('admin-ajax.php?action=login_captcha_image&captcha_id=preview_' . time()); ?>" 
                         alt="<?php _e('验证码预览', 'login-captcha'); ?>" 
                         style="border: 1px solid #ddd; margin: 10px 0;">
                    <br>
                    <button type="button" id="refresh-preview" class="button">
                        <?php _e('刷新预览', 'login-captcha'); ?>
                    </button>
                </div>
            </div>

            <form method="post" action="">
                <?php
                wp_nonce_field('login_captcha_settings_nonce');
                settings_fields('login_captcha_settings');
                do_settings_sections('login_captcha_settings');
                submit_button();
                ?>
            </form>

            <div class="card">
                <h2><?php _e('使用说明', 'login-captcha'); ?></h2>
                <ul>
                    <li><?php _e('启用验证码后，用户在登录时需要输入正确的验证码才能继续。', 'login-captcha'); ?></li>
                    <li><?php _e('验证码类型支持纯数字、纯字母或数字字母混合。', 'login-captcha'); ?></li>
                    <li><?php _e('验证码长度建议设置为4-6位，太短不安全，太长影响用户体验。', 'login-captcha'); ?></li>
                    <li><?php _e('图片尺寸可以根据您的主题样式进行调整。', 'login-captcha'); ?></li>
                </ul>
            </div>

            <div class="card">
                <h2><?php _e('安全提示', 'login-captcha'); ?></h2>
                <ul>
                    <li><?php _e('验证码可以有效防止暴力破解登录密码。', 'login-captcha'); ?></li>
                    <li><?php _e('建议配合强密码策略和登录失败限制使用。', 'login-captcha'); ?></li>
                    <li><?php _e('验证码会在5分钟后自动过期，提高安全性。', 'login-captcha'); ?></li>
                </ul>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#refresh-preview').click(function() {
                var img = $('#captcha-preview img');
                var src = img.attr('src');
                var newSrc = src.replace(/preview_\d+/, 'preview_' + Date.now());
                img.attr('src', newSrc);
            });
        });
        </script>
        <?php
    }

    /**
     * 基本设置部分回调
     */
    public function general_section_callback() {
        echo '<p>' . __('配置验证码的基本功能设置。', 'login-captcha') . '</p>';
    }

    /**
     * 外观设置部分回调
     */
    public function appearance_section_callback() {
        echo '<p>' . __('调整验证码图片的外观样式。', 'login-captcha') . '</p>';
    }

    /**
     * 启用验证码字段回调
     */
    public function enabled_field_callback() {
        $enabled = get_option('login_captcha_enabled', 1);
        echo '<input type="checkbox" name="login_captcha_enabled" value="1" ' . checked(1, $enabled, false) . '>';
        echo '<label for="login_captcha_enabled">' . __('在登录页面显示验证码', 'login-captcha') . '</label>';
    }

    /**
     * 验证码类型字段回调
     */
    public function type_field_callback() {
        $type = get_option('login_captcha_type', 'mixed');
        $types = array(
            'numbers' => __('纯数字', 'login-captcha'),
            'letters' => __('纯字母', 'login-captcha'),
            'mixed' => __('数字字母混合', 'login-captcha')
        );

        echo '<select name="login_captcha_type">';
        foreach ($types as $value => $label) {
            echo '<option value="' . esc_attr($value) . '" ' . selected($type, $value, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('选择验证码包含的字符类型。', 'login-captcha') . '</p>';
    }

    /**
     * 验证码长度字段回调
     */
    public function length_field_callback() {
        $length = get_option('login_captcha_length', 4);
        echo '<input type="number" name="login_captcha_length" value="' . esc_attr($length) . '" min="3" max="8" step="1">';
        echo '<p class="description">' . __('验证码字符数量，建议3-8位。', 'login-captcha') . '</p>';
    }

    /**
     * 图片宽度字段回调
     */
    public function width_field_callback() {
        $width = get_option('login_captcha_width', 120);
        echo '<input type="number" name="login_captcha_width" value="' . esc_attr($width) . '" min="80" max="300" step="10">';
        echo '<span> px</span>';
        echo '<p class="description">' . __('验证码图片宽度，建议80-300像素。', 'login-captcha') . '</p>';
    }

    /**
     * 图片高度字段回调
     */
    public function height_field_callback() {
        $height = get_option('login_captcha_height', 40);
        echo '<input type="number" name="login_captcha_height" value="' . esc_attr($height) . '" min="30" max="100" step="5">';
        echo '<span> px</span>';
        echo '<p class="description">' . __('验证码图片高度，建议30-100像素。', 'login-captcha') . '</p>';
    }
}
