<?php
/**
 * Plugin Name: 登录验证码
 * Plugin URI: https://example.com/login-captcha
 * Description: 为WordPress登录页面添加随机数字或字母验证码，提高网站安全性
 * Version: 1.0.0
 * Author: JMA
 * Text Domain: login-captcha
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.4
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('LOGIN_CAPTCHA_VERSION', '1.0.0');
define('LOGIN_CAPTCHA_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('LOGIN_CAPTCHA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('LOGIN_CAPTCHA_PLUGIN_FILE', __FILE__);

/**
 * 登录验证码主类
 */
class Login_Captcha {

    /**
     * 单例实例
     */
    private static $instance = null;

    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        $this->init();
    }

    /**
     * 初始化插件
     */
    public function init() {
        // 加载文本域
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // 初始化钩子
        add_action('init', array($this, 'init_hooks'));
        
        // 激活和停用钩子
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // 包含必要文件
        $this->includes();
    }

    /**
     * 加载文本域
     */
    public function load_textdomain() {
        load_plugin_textdomain('login-captcha', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * 初始化钩子
     */
    public function init_hooks() {
        // 在登录表单中添加验证码
        add_action('login_form', array($this, 'add_captcha_to_login_form'));
        
        // 验证登录时的验证码
        add_filter('authenticate', array($this, 'validate_captcha'), 30, 3);
        
        // 添加登录页面样式
        add_action('login_enqueue_scripts', array($this, 'enqueue_login_scripts'));
        
        // 处理验证码图片请求
        add_action('wp_ajax_nopriv_login_captcha_image', array($this, 'generate_captcha_image'));

        // 处理nonce生成请求
        add_action('wp_ajax_nopriv_login_captcha_nonce', array($this, 'generate_captcha_nonce'));
        
        // 添加管理菜单
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // 注册设置
        add_action('admin_init', array($this, 'register_settings'));
    }

    /**
     * 包含必要文件
     */
    public function includes() {
        require_once LOGIN_CAPTCHA_PLUGIN_DIR . 'includes/class-captcha-generator.php';
        require_once LOGIN_CAPTCHA_PLUGIN_DIR . 'includes/class-admin-settings.php';
    }

    /**
     * 在登录表单中添加验证码
     */
    public function add_captcha_to_login_form() {
        // 检查是否启用验证码
        if (!get_option('login_captcha_enabled', 1)) {
            return;
        }

        // 生成验证码会话ID
        $captcha_id = wp_generate_uuid4();
        
        // 生成nonce用于验证码图片请求
        $nonce = wp_create_nonce('login_captcha_image');

        echo '<p class="login-captcha-field" style="border: none !important; border-left: none !important; box-shadow: none !important; padding: 0 !important; padding-left: 0 !important; margin-left: 0 !important; text-indent: 0 !important;">';
        echo '<label for="login_captcha" style="border: none !important; border-left: none !important; box-shadow: none !important; padding: 0 !important; padding-left: 0 !important; margin-left: 0 !important; text-indent: 0 !important;">' . __('验证码', 'login-captcha') . '</label>';
        echo '<div class="login-captcha-container">';
        echo '<input type="text" name="login_captcha" id="login_captcha" class="input" value="" autocomplete="off" required placeholder="' . __('请输入验证码', 'login-captcha') . '">';
        echo '<div class="captcha-image-container">';
        echo '<img id="captcha-image" src="' . esc_url(admin_url('admin-ajax.php?action=login_captcha_image&captcha_id=' . urlencode($captcha_id) . '&_wpnonce=' . $nonce)) . '" alt="' . esc_attr__('验证码', 'login-captcha') . '" title="' . esc_attr__('点击刷新验证码', 'login-captcha') . '">';
        echo '<a href="#" id="refresh-captcha">' . __('刷新验证码', 'login-captcha') . '</a>';
        echo '</div>';
        echo '</div>';
        echo '<input type="hidden" name="captcha_id" value="' . esc_attr($captcha_id) . '">';
        echo '<input type="hidden" name="captcha_nonce" value="' . esc_attr($nonce) . '">';
        echo '</p>';
    }

    /**
     * 验证登录时的验证码
     */
    public function validate_captcha($user, $username, $password) {
        // 如果已经有错误，直接返回
        if (is_wp_error($user)) {
            return $user;
        }

        // 检查是否启用验证码
        if (!get_option('login_captcha_enabled', 1)) {
            return $user;
        }

        // 跳过某些请求
        if (defined('XMLRPC_REQUEST') && XMLRPC_REQUEST) {
            return $user;
        }
        if (defined('REST_REQUEST') && REST_REQUEST) {
            return $user;
        }

        // 跳过WP-CLI请求
        if (defined('WP_CLI') && WP_CLI) {
            return $user;
        }

        // 检查验证码字段
        if (empty($_POST['login_captcha']) || empty($_POST['captcha_id'])) {
            $this->log_failed_attempt('empty_captcha');
            return new WP_Error('captcha_empty', __('请输入验证码', 'login-captcha'));
        }

        $captcha_input = sanitize_text_field($_POST['login_captcha']);
        $captcha_id = sanitize_text_field($_POST['captcha_id']);

        // 验证captcha_id格式（UUID格式）
        if (!$this->is_valid_uuid($captcha_id)) {
            $this->log_failed_attempt('invalid_captcha_id');
            return new WP_Error('captcha_invalid', __('验证码错误，请重新输入', 'login-captcha'));
        }

        // 验证输入长度
        if (strlen($captcha_input) > 10) {
            $this->log_failed_attempt('captcha_too_long');
            return new WP_Error('captcha_invalid', __('验证码错误，请重新输入', 'login-captcha'));
        }

        // 检查暴力破解
        if ($this->is_brute_force_attempt()) {
            $this->log_failed_attempt('brute_force');
            return new WP_Error('captcha_blocked', __('尝试次数过多，请稍后再试', 'login-captcha'));
        }

        // 验证验证码
        $captcha_generator = new Login_Captcha_Generator();
        if (!$captcha_generator->verify_captcha($captcha_id, $captcha_input)) {
            $this->log_failed_attempt('wrong_captcha');
            return new WP_Error('captcha_invalid', __('验证码错误，请重新输入', 'login-captcha'));
        }

        return $user;
    }

    /**
     * 检查是否为暴力破解尝试
     */
    private function is_brute_force_attempt() {
        $ip = $this->get_client_ip();
        $transient_key = 'login_captcha_failed_' . md5($ip);
        $failed_attempts = get_transient($transient_key);

        // 5分钟内超过10次失败尝试
        return $failed_attempts && $failed_attempts >= 10;
    }

    /**
     * 记录失败尝试
     */
    private function log_failed_attempt($reason) {
        $ip = $this->get_client_ip();
        $transient_key = 'login_captcha_failed_' . md5($ip);
        $failed_attempts = get_transient($transient_key);

        if ($failed_attempts === false) {
            $failed_attempts = 0;
        }

        set_transient($transient_key, $failed_attempts + 1, 300); // 5分钟

        // 记录到错误日志（如果启用了调试）
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("Login Captcha: Failed attempt from {$ip}, reason: {$reason}");
        }
    }

    /**
     * 加载登录页面脚本和样式
     */
    public function enqueue_login_scripts() {
        wp_enqueue_style('login-captcha-style', LOGIN_CAPTCHA_PLUGIN_URL . 'assets/css/login-captcha.css', array(), LOGIN_CAPTCHA_VERSION);
        wp_enqueue_script('login-captcha-script', LOGIN_CAPTCHA_PLUGIN_URL . 'assets/js/login-captcha.js', array('jquery'), LOGIN_CAPTCHA_VERSION, true);
        
        // 传递AJAX URL到JavaScript
        wp_localize_script('login-captcha-script', 'loginCaptcha', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'refreshText' => __('刷新验证码', 'login-captcha')
        ));
    }

    /**
     * 生成验证码图片
     */
    public function generate_captcha_image() {
        $captcha_id = isset($_GET['captcha_id']) ? sanitize_text_field($_GET['captcha_id']) : '';

        // 验证captcha_id格式（UUID格式）
        if (empty($captcha_id) || !$this->is_valid_uuid($captcha_id)) {
            wp_die(__('无效的验证码ID', 'login-captcha'), 400);
        }

        // 验证nonce（如果提供的话）
        $nonce = $_GET['_wpnonce'] ?? '';
        if (!empty($nonce) && !wp_verify_nonce($nonce, 'login_captcha_image')) {
            wp_die(__('安全验证失败', 'login-captcha'), 403);
        }

        // 速率限制检查
        $this->check_rate_limit();

        $captcha_generator = new Login_Captcha_Generator();
        $captcha_generator->generate_image($captcha_id);
        exit;
    }

    /**
     * 检查速率限制
     */
    private function check_rate_limit() {
        $ip = $this->get_client_ip();
        $transient_key = 'login_captcha_rate_limit_' . md5($ip);
        $requests = get_transient($transient_key);

        if ($requests === false) {
            $requests = 0;
        }

        // 每分钟最多30次请求
        if ($requests >= 30) {
            wp_die(__('频繁请求，请稍后重试', 'login-captcha'), 429);
        }

        set_transient($transient_key, $requests + 1, 60);
    }

    /**
     * 获取客户端IP地址
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 如果是多个IP，取第一个
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // 验证IP格式
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 验证UUID格式
     */
    private function is_valid_uuid($uuid) {
        // 基本格式检查：长度和字符
        if (strlen($uuid) !== 36) {
            return false;
        }

        // 检查连字符位置
        if ($uuid[8] !== '-' || $uuid[13] !== '-' || $uuid[18] !== '-' || $uuid[23] !== '-') {
            return false;
        }

        // 移除连字符后检查是否都是十六进制字符
        $hex_part = str_replace('-', '', $uuid);
        if (strlen($hex_part) !== 32 || !ctype_xdigit($hex_part)) {
            return false;
        }

        return true;
    }

    /**
     * 生成验证码nonce
     */
    public function generate_captcha_nonce() {
        // 速率限制检查
        $this->check_rate_limit();

        // 生成新的nonce
        $nonce = wp_create_nonce('login_captcha_image');

        // 返回JSON响应
        wp_send_json_success(array(
            'nonce' => $nonce
        ));
    }

    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        add_options_page(
            __('登录验证码设置', 'login-captcha'),
            __('登录验证码', 'login-captcha'),
            'manage_options',
            'login-captcha-settings',
            array($this, 'admin_page')
        );
    }

    /**
     * 管理页面回调
     */
    public function admin_page() {
        $admin_settings = new Login_Captcha_Admin_Settings();
        $admin_settings->display_page();
    }

    /**
     * 注册设置
     */
    public function register_settings() {
        $admin_settings = new Login_Captcha_Admin_Settings();
        $admin_settings->register_settings();
    }

    /**
     * 插件激活
     */
    public function activate() {
        // 设置默认选项
        add_option('login_captcha_enabled', 1);
        add_option('login_captcha_type', 'mixed'); // mixed, numbers, letters
        add_option('login_captcha_length', 4);
        add_option('login_captcha_width', 120);
        add_option('login_captcha_height', 40);
        
        // 创建临时目录
        $temp_dir = LOGIN_CAPTCHA_PLUGIN_DIR . 'temp/';
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }
    }

    /**
     * 插件停用
     */
    public function deactivate() {
        // 清理临时文件
        $temp_dir = LOGIN_CAPTCHA_PLUGIN_DIR . 'temp/';
        if (file_exists($temp_dir)) {
            $files = glob($temp_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
}

// 初始化插件
function login_captcha_init() {
    return Login_Captcha::get_instance();
}

// 启动插件
add_action('plugins_loaded', 'login_captcha_init');
