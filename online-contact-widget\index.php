<?php
/*
Plugin Name: 多合一在线客服插件
Plugin URI: http://wordpress.org/plugins/online-contact-widget/
Description: Online Contact Widget（多合一在线客服插件），旨在为WordPress网站提供一系列可配置在线客服支持，包括QQ、微信（微信号、公众号和小程序QR-code）、电话、Email和工单等（海外站点还可以选WhatsApp，Telegram，Line，Messenger,Viber和Signal）。
Version: 1.2.0
Author: 闪电博
Author URI: http://www.wbolt.com/
Text Domain: wb-ocw
*/


if (!defined('ABSPATH')) {
    return;
}

define('ONLINE_CONTACT_WIDGET_PATH', __DIR__);
define('ONLINE_CONTACT_WIDGET_FILE', __FILE__);
define('ONLINE_CONTACT_WIDGET_VERSION', '1.2.0');
define('ONLINE_CONTACT_WIDGET_CODE', 'ocw');
define('ONLINE_CONTACT_WIDGET_URL', plugin_dir_url(ONLINE_CONTACT_WIDGET_FILE));

if (!defined('WB_OCW_DM')) {
    define('WB_OCW_DM', 'wb-ocw');
}

if (!class_exists('WBP')) {
    require_once ONLINE_CONTACT_WIDGET_PATH . '/wbpc/wbp.php';
}

if (!class_exists('WB_Vite')) {
    require_once ONLINE_CONTACT_WIDGET_PATH . '/wbpc/vite.php';
}

require_once __DIR__ . '/classes/admin.class.php';
require_once __DIR__ . '/classes/front.class.php';
require_once __DIR__ . '/classes/contact.class.php';
require_once __DIR__ . '/classes/captcha.class.php';
require_once __DIR__ . '/classes/sms.class.php';
require_once __DIR__ . '/classes/mail.class.php';

OCW_Admin::init();
OCW_Front::init();
OCW_Contact::init();
OCW_Captcha::init();
OCW_Sms::init();
OCW_Mail::init();

function wb_ocw_load_textdomain()
{
    if (!is_textdomain_loaded(WBM_DM)) {
        load_plugin_textdomain(WBM_DM, false, plugin_basename(ONLINE_CONTACT_WIDGET_PATH) . '/wbm/languages/');
    }
    load_plugin_textdomain(WB_OCW_DM, false, plugin_basename(ONLINE_CONTACT_WIDGET_PATH) . '/languages/');
}
add_action('plugins_loaded', 'wb_ocw_load_textdomain');
