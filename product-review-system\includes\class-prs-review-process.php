<?php
/**
 * 审核流程处理类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class PRS_Review_Process {

    /**
     * 构造函数
     */
    public function __construct() {
        // 处理审核操作
        add_action('admin_init', array($this, 'handle_review_actions'));
    }

    /**
     * 处理审核操作
     */
    public function handle_review_actions() {
        if (!isset($_GET['page']) || $_GET['page'] !== 'product-review-system') {
            return;
        }

        $action = isset($_GET['action']) ? $_GET['action'] : '';
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        switch ($action) {
            case 'reviewer_approve':
                $this->handle_reviewer_approve($id);
                break;
            case 'reviewer_reject':
                $this->handle_reviewer_reject($id);
                break;
            case 'admin_approve':
                $this->handle_admin_approve($id);
                break;
            case 'admin_reject':
                $this->handle_admin_reject($id);
                break;
        }
    }

    /**
     * 审核员通过
     */
    private function handle_reviewer_approve($id) {
        // 验证nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'reviewer_approve_' . $id)) {
            wp_die(__('安全验证失败', 'product-review-system'));
        }

        // 检查权限
        if (!PRS_Permissions::can_user_review()) {
            wp_die(__('您没有权限执行此操作。', 'product-review-system'));
        }

        $review = PRS_Database::get_review($id);
        if (!$review) {
            wp_die(__('审核记录不存在。', 'product-review-system'));
        }

        // 检查状态
        if ($review->status !== 'pending_review') {
            wp_die(__('该审核记录状态不正确。', 'product-review-system'));
        }

        // 更新审核记录
        $update_data = array(
            'reviewer_id' => get_current_user_id(),
            'reviewer_status' => 'approved',
            'status' => 'pending_admin',
            'reviewer_date' => current_time('mysql'),
            'reviewer_notes' => isset($_POST['reviewer_notes']) ? sanitize_textarea_field($_POST['reviewer_notes']) : ''
        );

        $result = PRS_Database::update_review($id, $update_data);

        if ($result) {
            // 发送通知给管理员
            PRS_Notifications::notify_admins($id);

            // 添加成功消息
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('审核通过，已提交给管理员进行最终审核。', 'product-review-system') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('操作失败，请重试。', 'product-review-system') . '</p></div>';
            });
        }

        wp_redirect(admin_url('admin.php?page=product-review-system&action=view&id=' . $id));
        exit;
    }

    /**
     * 审核员拒绝
     */
    private function handle_reviewer_reject($id) {
        // 验证nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'reviewer_reject_' . $id)) {
            wp_die(__('安全验证失败', 'product-review-system'));
        }

        // 检查权限
        if (!PRS_Permissions::can_user_review()) {
            wp_die(__('您没有权限执行此操作。', 'product-review-system'));
        }

        $review = PRS_Database::get_review($id);
        if (!$review) {
            wp_die(__('审核记录不存在。', 'product-review-system'));
        }

        // 检查状态
        if ($review->status !== 'pending_review') {
            wp_die(__('该审核记录状态不正确。', 'product-review-system'));
        }

        // 更新审核记录
        $update_data = array(
            'reviewer_id' => get_current_user_id(),
            'reviewer_status' => 'rejected',
            'status' => 'rejected',
            'reviewer_date' => current_time('mysql'),
            'reviewer_notes' => isset($_POST['reviewer_notes']) ? sanitize_textarea_field($_POST['reviewer_notes']) : ''
        );

        $result = PRS_Database::update_review($id, $update_data);

        if ($result) {
            // 发送通知给提交者
            PRS_Notifications::notify_submitter($id, 'rejected');

            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('审核已拒绝，已通知提交者。', 'product-review-system') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('操作失败，请重试。', 'product-review-system') . '</p></div>';
            });
        }

        wp_redirect(admin_url('admin.php?page=product-review-system&action=view&id=' . $id));
        exit;
    }

    /**
     * 管理员通过
     */
    private function handle_admin_approve($id) {
        // 验证nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'admin_approve_' . $id)) {
            wp_die(__('安全验证失败', 'product-review-system'));
        }

        // 检查权限
        if (!PRS_Permissions::can_user_admin_review()) {
            wp_die(__('您没有权限执行此操作。', 'product-review-system'));
        }

        $review = PRS_Database::get_review($id);
        if (!$review) {
            wp_die(__('审核记录不存在。', 'product-review-system'));
        }

        // 检查状态
        if ($review->status !== 'pending_admin') {
            wp_die(__('该审核记录状态不正确。', 'product-review-system'));
        }

        // 应用产品修改
        $success = $this->apply_product_changes($review);

        if ($success) {
            // 更新审核记录
            $update_data = array(
                'admin_id' => get_current_user_id(),
                'admin_status' => 'approved',
                'status' => 'approved',
                'admin_date' => current_time('mysql'),
                'admin_notes' => isset($_POST['admin_notes']) ? sanitize_textarea_field($_POST['admin_notes']) : ''
            );

            PRS_Database::update_review($id, $update_data);

            // 发送通知给提交者
            PRS_Notifications::notify_submitter($id, 'approved');

            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('审核通过，产品修改已应用。', 'product-review-system') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('应用产品修改失败，请检查产品数据。', 'product-review-system') . '</p></div>';
            });
        }

        wp_redirect(admin_url('admin.php?page=product-review-system&action=view&id=' . $id));
        exit;
    }

    /**
     * 管理员拒绝
     */
    private function handle_admin_reject($id) {
        // 验证nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'admin_reject_' . $id)) {
            wp_die(__('安全验证失败', 'product-review-system'));
        }

        // 检查权限
        if (!PRS_Permissions::can_user_admin_review()) {
            wp_die(__('您没有权限执行此操作。', 'product-review-system'));
        }

        $review = PRS_Database::get_review($id);
        if (!$review) {
            wp_die(__('审核记录不存在。', 'product-review-system'));
        }

        // 检查状态
        if ($review->status !== 'pending_admin') {
            wp_die(__('该审核记录状态不正确。', 'product-review-system'));
        }

        // 更新审核记录
        $update_data = array(
            'admin_id' => get_current_user_id(),
            'admin_status' => 'rejected',
            'status' => 'rejected',
            'admin_date' => current_time('mysql'),
            'admin_notes' => isset($_POST['admin_notes']) ? sanitize_textarea_field($_POST['admin_notes']) : ''
        );

        $result = PRS_Database::update_review($id, $update_data);

        if ($result) {
            // 发送通知给提交者
            PRS_Notifications::notify_submitter($id, 'rejected');

            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('审核已拒绝，已通知提交者。', 'product-review-system') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('操作失败，请重试。', 'product-review-system') . '</p></div>';
            });
        }

        wp_redirect(admin_url('admin.php?page=product-review-system&action=view&id=' . $id));
        exit;
    }

    /**
     * 应用产品修改
     */
    private function apply_product_changes($review) {
        $product_id = $review->product_id;
        $product = wc_get_product($product_id);
        if (!$product) {
            error_log('PRS: Product not found with ID: ' . $product_id);
            return false;
        }

        // 检查操作类型
        $operation_type = isset($review->operation_type) ? $review->operation_type : 'modify';

        if ($operation_type === 'delete') {
            // 处理删除操作
            return $this->apply_product_delete($review);
        } else {
            // 处理修改操作
            return $this->apply_product_modify($review);
        }
    }

    /**
     * 应用产品修改操作
     */
    private function apply_product_modify($review) {
        $modified_data = json_decode($review->modified_data, true);
        if (!$modified_data) {
            error_log('PRS: Failed to decode modified_data JSON');
            return false;
        }

        $product_id = $review->product_id;

        // 使用产品处理器的方法来应用修改，这样可以正确处理钩子
        if (class_exists('PRS_Product_Handler')) {
            $handler = new PRS_Product_Handler();
            $success = $handler->apply_approved_changes($product_id, $modified_data);

            if ($success) {
                // 额外的状态同步处理
                $this->sync_product_status($product_id, $modified_data);

                // 记录成功日志
                error_log('PRS: Successfully applied product changes for product ID: ' . $product_id);
                return true;
            } else {
                error_log('PRS: Failed to apply product changes via handler for product ID: ' . $product_id);
                return false;
            }
        }

        // 如果产品处理器不可用，使用备用方法
        return $this->apply_product_changes_fallback($product_id, $modified_data);
    }

    /**
     * 应用产品删除操作
     */
    private function apply_product_delete($review) {
        $product_id = $review->product_id;

        try {
            // 清除产品的审核状态
            delete_post_meta($product_id, '_product_under_review');
            delete_post_meta($product_id, '_product_review_status');

            // 执行删除操作（移到回收站）
            $result = wp_trash_post($product_id);

            if ($result) {
                error_log('PRS: Successfully deleted product ID: ' . $product_id);
                return true;
            } else {
                error_log('PRS: Failed to delete product ID: ' . $product_id);
                return false;
            }

        } catch (Exception $e) {
            error_log('PRS: Error deleting product ID ' . $product_id . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 同步产品状态 - 确保产品状态正确更新
     */
    private function sync_product_status($product_id, $modified_data) {
        try {
            // 强制更新产品状态
            if (isset($modified_data['status'])) {
                $post_data = array(
                    'ID' => $product_id,
                    'post_status' => $modified_data['status']
                );
                wp_update_post($post_data);

                // 如果是发布状态，确保产品可见性正确
                if ($modified_data['status'] === 'publish') {
                    // 更新产品可见性
                    if (isset($modified_data['catalog_visibility'])) {
                        update_post_meta($product_id, '_visibility', $modified_data['catalog_visibility']);
                    }

                    // 确保产品在目录中可见
                    $visibility = get_post_meta($product_id, '_visibility', true);
                    if (empty($visibility)) {
                        update_post_meta($product_id, '_visibility', 'visible');
                    }
                }
            }

            // 清除所有相关缓存
            wp_cache_delete($product_id, 'posts');
            wp_cache_delete($product_id, 'post_meta');
            wc_delete_product_transients($product_id);

            // 清除WooCommerce对象缓存
            if (function_exists('wc_get_product')) {
                $product = wc_get_product($product_id);
                if ($product) {
                    $product->read_meta_data(true); // 强制重新读取元数据
                }
            }

            // 触发产品更新钩子
            do_action('woocommerce_product_object_updated_props', wc_get_product($product_id), array('status'));
            do_action('prs_product_status_synced', $product_id, $modified_data);

        } catch (Exception $e) {
            error_log('PRS: Failed to sync product status: ' . $e->getMessage());
        }
    }

    /**
     * 备用的产品修改应用方法
     */
    private function apply_product_changes_fallback($product_id, $modified_data) {
        try {
            // 更新基本信息
            $post_data = array(
                'ID' => $product_id,
                'post_title' => $modified_data['name'],
                'post_content' => $modified_data['description'],
                'post_excerpt' => $modified_data['short_description'],
                'post_status' => $modified_data['status']
            );

            $result = wp_update_post($post_data);
            if (is_wp_error($result)) {
                error_log('PRS: Failed to update post: ' . $result->get_error_message());
                return false;
            }

            // 更新产品元数据
            $meta_fields = array(
                '_sku' => 'sku',
                '_regular_price' => 'regular_price',
                '_sale_price' => 'sale_price',
                '_stock' => 'stock_quantity',
                '_stock_status' => 'stock_status',
                '_weight' => 'weight',
                '_length' => 'length',
                '_width' => 'width',
                '_height' => 'height',
                '_featured' => 'featured',
                '_visibility' => 'catalog_visibility',
                '_virtual' => 'virtual',
                '_downloadable' => 'downloadable'
            );

            foreach ($meta_fields as $meta_key => $data_key) {
                if (isset($modified_data[$data_key])) {
                    $value = $modified_data[$data_key];
                    // 处理布尔值
                    if (in_array($data_key, array('featured', 'virtual', 'downloadable'))) {
                        $value = $value ? 'yes' : 'no';
                    }
                    update_post_meta($product_id, $meta_key, $value);
                }
            }

            // 更新分类和标签
            if (isset($modified_data['categories'])) {
                wp_set_post_terms($product_id, $modified_data['categories'], 'product_cat');
            }

            if (isset($modified_data['tags'])) {
                wp_set_post_terms($product_id, $modified_data['tags'], 'product_tag');
            }

            // 同步产品状态
            $this->sync_product_status($product_id, $modified_data);

            return true;

        } catch (Exception $e) {
            error_log('PRS: Failed to apply product changes (fallback): ' . $e->getMessage());
            return false;
        }
    }
}
