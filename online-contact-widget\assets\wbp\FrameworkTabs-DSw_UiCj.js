import{K as a,L as s,aw as e,aB as t,a as l,o as n,a2 as u,q as m,m as c,J as r,R as i,S as o,O as b,w as d,v as w,t as v,u as h}from"./wbs-Dtem2-xP.js";const _={class:"with-tab base"},f={class:"wbs-tab-nav"},g={class:"wbs-content"},p={__name:"FrameworkTabs",setup(p){const{wbsCnf:k}=a(),{wb_i18n:x}=s(k),S=k.extend_menus,j=k.disable_menus,q=k.custom_menus,y=e(),B=y.meta.slug,C=y.meta.parentSlug,F=t[B],J=l([]);return n((()=>{const a=C&&q&&q[C];if(a)J.value=a;else{const a=C&&S&&S[C],s=C&&j&&j[C],e=y.matched.length>1?y.matched[0].children:F.children;J.value=e.filter((e=>(!s||!s.includes(e.meta.slug))&&(a&&a.includes(e.meta.slug)?e:e.tab)))}})),(a,s)=>{const e=u("router-link"),t=u("router-view");return c(),m("div",_,[r("ul",f,[(c(!0),m(i,null,o(J.value,(a=>(c(),m("li",{class:"wb-tab-item",key:a.meta.slug},[b(e,{to:a.path},{default:d((()=>[w(v(a.meta.label?h(x)[a.meta.label]:a.name),1)])),_:2},1032,["to"])])))),128))]),r("div",g,[b(t)])])}}};export{p as default};
