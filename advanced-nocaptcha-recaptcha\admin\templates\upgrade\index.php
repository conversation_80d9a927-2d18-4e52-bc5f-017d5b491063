<?php
/**
 * Update page content.
 *
 * @package C4WP
 * @since 7.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}
?>

<div class="wrap features-wrap">
	<div class="c4wp-upgrade-section">
		
		<div class="content-block">
			<div class="logo-wrap">
				<img src="<?php echo esc_url( C4WP_PLUGIN_URL . 'assets/img/c4wp-logo-full.png' ); ?>" alt="">
			</div>
			<p><?php esc_html_e( 'Add antispam protection to block bots and allow real humans to interact with your website. Add CAPTCHA from services such as Google reCAPTCHA, hCaptcha and Cloudflare Turnstile to any form on your website such as comments forms, login pages and checkout forms.', 'advanced-nocaptcha-recaptcha' ); ?></p>
			<p><?php esc_html_e( 'Get started with just a few clicks with CAPTCHA 4WP, the only plugin that supports multiple CAPTCHA services. Upgrade today and benefit from numerous features, some of which are listed below.', 'advanced-nocaptcha-recaptcha' ); ?></p>
			<div class="premium-cta">
			<a href="https://captcha4wp.com/?utm_source=plugin&utm_medium=repo+link&utm_campaign=wordpress_org&utm_content=c4wp" target="_blank" rel="noopener"><?php esc_html_e( 'Upgrade to Premium', 'advanced-nocaptcha-recaptcha' ); ?></a>				
			</div>
		</div>

		<div class="content-block">
			<table class="c21 feature-table">
				<tbody>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10 c4"><span class="c5"></span></p>
						</td>
						<td class="c8 row-head" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><?php esc_html_e( 'Premium', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c12 row-head" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><?php esc_html_e( 'Free', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Add CAPTCHA checks from Google reCAPTCHA', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA antispam checks on any website from Google reCAPTCHA.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Add CAPTCHA checks from other services', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA antispam checks on any website from hCaptcha and Cloudflare Turnstile.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>

					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Email support and forum access', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'We stand behind all of our products with world-class support and a team of professionals who absolutely care.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Easy to set up and use', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Set up CAPTCHA in minutes and add it to virtually any form you want at the click of a button.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Choose from different types of tests', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'With support for multiple CAPTCHA methods available straight out of the box, you can choose the one that best fits your requirements.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'WordPress login and comments spam protection', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Easily add CAPTCHA to your WordPress login page and comments section to protect your website from spam.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'WordPress registration and lost password/reset form protection', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Easily add CAPTCHA to protect your website from spam and fake users registration and lost password/reset pages from spam.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Use CAPTCHA in any country', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Select from different domains to prevent service outages due to domain restrictions.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Set CAPTCHA passmark score', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Fine-tune CAPTCHA tests in real-time by setting the passmark score to avoid false positives.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Configurable CAPTCHA language', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Choose from multiple CAPTCHA text languages.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>


					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Failover check for automated CAPTCHA', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Configure CAPTCHA failover to eliminate false positives and ensure customers’ journeys are not disrupted. Choose from multiple failover options, depending on your requirements.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
					</tr>

					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Use the language that your website viewers understand', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Avoid confusion and configure the plugin to automatically change the CAPTCHA text language to match that of the visitor.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Spam protection for your WooCommerce stores', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA to WooCommerce forms such as the login page and checkout at the click of a mouse.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Specify where to put the CAPTCHA test on WooCommerce checkout page', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Personalize your customers\' experience and meet your branding requirements.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( '1-click Contact Form 7 spam protection', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA to any Contact Form 7 form at the click of a button. No shortcodes required.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>

					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( '1-click spam protection for forms built with Gravity Forms', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA to any form built with Gravity Forms at the click of a button. No shortcodes required.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>

					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( '1-click spam protection for forms built with WPForms', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA to any form built with WPForms at the click of a button. No shortcodes required.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>

					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( '1-click spam protection for Mailchimp for WordPress forms', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Add CAPTCHA to any MC4WP form at the click of a button. No shortcodes required.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'CAPTCHA spam protection for BuddyPress, bbPress, & many other third-party plugins', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Out of the box support for many popular third-party plugins.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Add CAPTCHA to any type of form, even PHP forms', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Use the plugin\'s shortcode or code snippet to add CAPTCHA tests to any type of forms, even non-WordPress PHP forms.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'Strike a balance between security and usability', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Request CAPTCHA on failed logins only for an improved user experience that doesn\'t compromise security.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'White-list logged in users', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'Logged in users can be trusted, so there is no need for CAPTCHA. You can also configure this by user role.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'White-list specific IP addresses', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'White-list IPs to ensure automated process & integrations running to your website continue to operate smoothly.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
					<tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'White-list specific URLs', 'advanced-nocaptcha-recaptcha' ); ?></strong><?php esc_html_e( 'If you configure CAPTCHA to run invisibly on all pages, use this setting to exclude it from specific URLs.', 'advanced-nocaptcha-recaptcha' ); ?></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr><tr class="c2">
						<td class="c6" colspan="1" rowspan="1">
							<p class="c10"><span class="c5"><strong><?php esc_html_e( 'No Ads!', 'advanced-nocaptcha-recaptcha' ); ?></strong></span></p>
						</td>
						<td class="c8" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-saved"></span></span></p>
						</td>
						<td class="c12" colspan="1" rowspan="1">
							<p class="c7"><span class="c5"><span class="dashicons dashicons-no"></span></span></p>
						</td>
					</tr>
				</tbody>
			</table>
		</div>

		<div class="premium-cta">
			<a href="https://captcha4wp.com/?utm_source=plugin&utm_medium=repo+link&utm_campaign=wordpress_org&utm_content=c4wp" target="_blank" rel="noopener"><?php esc_html_e( 'Upgrade to Premium', 'advanced-nocaptcha-recaptcha' ); ?></a> 
		</div>

	</div>
</div>

<style>
	#postbox-container-1 {
		display: none;
	}
	.features-wrap {
		background: #fff;
		padding: 25px 30px;
		max-width: 880px;
	}

	.features-wrap h2 {
		font-size: 28px;
			margin-bottom: 30px;
	}

	.features-wrap p {
		font-size: 14px;
		line-height: 28px;
		font-weight: normal;
	}

	.feature-list {
		margin-bottom: 20px;
	}

	.feature-list li {
		margin-bottom: 10px;
		font-size: 15px;
	}

	.feature-list li .dashicons {
		color: #50284E;
	}

	.premium-cta {
		margin: 25px 0 15px;
		text-align: center;
	}

	.premium-cta .text-link {
		color: #50284E;
		background: transparent;
		border: #50284E;
		text-decoration: dashed;
	}

	.premium-cta a, .table-link {
		background-color: #50284E;
		color: #fff;
		padding: 15px 26px;
		border-radius: 30px;
		font-size: 16px;
		white-space: nowrap;
		text-decoration: none;
		font-weight: 700;
		display: inline-block;
		margin-right: 15px;
		border: 2px solid #50284E;
	}

	.premium-cta a:hover, .table-link:hover, .premium-cta a.inverse, .table-link.inverse {
		color: #50284E;
		background-color: #fff;
	}

	.content-block {
		margin-bottom: 26px;
		border-bottom: 1px solid #eee;
		padding-bottom: 15px;
		overflow: hidden;
	}

	.feature-table strong {
		font-size: 16px;
		clear: both;
		display: block;
	}
	
	.feature-table tr td {
		text-align: center;
		min-width: 200px
	}
	.feature-table tr td:first-of-type {
		text-align: left;
		font-weight: 500;
	}
	.feature-table td p {
		margin-top: 0;
	}
	.row-head span {
		font-size: 17px;
		font-weight: 700;
	}
	.feature-table .dashicons {
		color: #50284E;
	}
	.feature-table .dashicons-no {
		color: red;
	}
	.table-link {
		font-size: 14px;
		padding: 9px;
		width: 193px;
		margin-top: 10px;
	}
	.pull-up {
		position: relative;
		top: -23px;
	}

	.logo-wrap img {
		max-width: 230px;
		margin-top: 20px;
	}

	.logo-wrap {
		float: left;
		margin-right: 30px;
	}

</style>
