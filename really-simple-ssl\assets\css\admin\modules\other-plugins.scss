.rsssl {
  .rsssl-other-plugins {
    .rsssl-placeholder {
      background-color:transparent;
    }
    .rsp-logo img {
      height: 20px;
    }
  }

  .rsssl-other-plugins-container {
    display: flex !important;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 10px;
    font-size: var(--rsp-fs-300);
    line-height: 1.7;
    gap: var(--rsp-spacing-xxs);

    @media screen and (max-width: 992px) {
      flex-direction: row;
      overflow: hidden;
    }

    .rsssl-other-plugins-element {
      width: 100%;
      display: flex;
      align-content: space-between;
      justify-content: space-between;
      gap: 10px;
      --rsp-other-plugins-color: var(---rsp-brand-primary);

	  &.rsssl-zip-recipes {
		--rsp-other-plugins-color: var(--rsp-pink);
	  }

	  &.rsssl-complianz-gdpr {
		--rsp-other-plugins-color: var(--rsp-blue);
	  }

      &.rsssl-complianz-terms-conditions {
        --rsp-other-plugins-color: var(--rsp-black);
      }

      &.rsssl-simplybook {
        --rsp-other-plugins-color: var(--rsp-simplybook-blue);
      }

	  &.rsssl-really-simple-ssl {
		--rsp-other-plugins-color: var(--rsp-yellow);
	  }

      a {
        width: max-content;
        color: var(--rsp-text-color-light);
        transition: color 0.3s ease;
        display: flex;
        align-items: center;
        min-width: 0;
        gap: var(--rsp-spacing-xs);
        text-decoration: none;

        &:hover {
          color: var(--rsp-other-plugins-color);
          text-decoration: underline;

          .rsssl-bullet {
            background-color: var(--rsp-other-plugins-color);
          }

          .rsssl-other-plugins-content {
            text-decoration: underline;
          }
        }
      }

      .rsssl-bullet {
        transition: background-color 0.3s ease;
        background-color: var(--rsp-other-plugins-color);
      }

      .rsssl-other-plugins-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .rsssl-other-plugin-status {
        min-width: fit-content;
      }
    }
  }
}
