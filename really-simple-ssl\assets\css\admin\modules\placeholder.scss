@function randomNum($min, $max) {
  $rand: random();
  $randomNum: $min + floor($rand * (($max - $min) + 1));

  @return $randomNum;
}

$base-color: #ddd;
$shine-color: #e8e8e8;
$animation-duration: 1.6s;
@mixin background-gradient {
  background-image: linear-gradient(90deg, $base-color 0px, $shine-color 40px, $base-color 80px);
  background-size: 600px;
}
.rsssl-datatable-placeholder {
  div {
    background-color:var(--rsp-grey-300);
    height:25px;
    &:nth-child(even) {
        background-color:#fff;
    }
  }
}

.rsssl-rest-error-message {
  margin:30px;
  ul {
    list-style:disc;
    margin:20px;
  }
}

.rsssl-placeholder {
  box-sizing: border-box;
  width: 100%;
  text-align: left;
  margin: 0;
  padding-bottom: 24px;
  color: #1e1e1e;
  -moz-font-smoothing: subpixel-antialiased;
  -webkit-font-smoothing: subpixel-antialiased;
  border-radius: 2px;
  & {
    flex-grow: 100;
  }

  .rsssl-placeholder-line {
    float: left;
    width: 100%;
    height: 16px;
    margin-top: 12px;
    border-radius: 7px;
    &:last-of-type{
      margin-bottom: 24px;
    }
    animation: shine-lines $animation-duration infinite linear;
    @include background-gradient;
    @for $i from 1 through 20 {
      &:nth-of-type( #{$i} ) {
        width: ( random(40) + 60 ) * 1%;
      }
    }
  }

  .rsssl-placeholder-line ~ .rsssl-placeholder-line {
    background-color: #ddd;
  }

}
.rsssl-dashboard-placeholder {
  &.rsssl-grid-item.rsssl-row-2 {
    grid-row: span 1;
  }
}
.rsssl-settings-placeholder {
  .rsssl-grid-item{
    min-height:400px;
  }
}
.rsssl-menu-placeholder {
  min-height:400px;
}

@keyframes shine-lines {
  0% {
    background-position:- 400px;
  }
  100% {
    background-position: 220px;
  }
}