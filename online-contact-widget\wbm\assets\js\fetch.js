const a=window.wb_base?window.wb_base:window.wbm_js_cnf?window.wbm_js_cnf:{ajax_url:"/wp-admin/admin-ajax.php",home_url:"/"},n={stringify(e){if(typeof e!="object")return"";const t=[];return Object.entries(e).forEach(([r,s])=>{t.push(`${r}=${s}`)}),t.join("&")},async getData(e){const t={method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:n.stringify(e)};try{return(await fetch(a.ajax_url,t)).json()}catch(r){Promise.reject(r)}},async getList(e){const t={method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:n.stringify(e)};try{return(await fetch(a.ajax_url,t)).text()}catch(r){Promise.reject(r)}},async getHTML(e){try{return(await fetch(e)).text()}catch(t){Promise.reject(t)}}},{getData:o,getList:c,getHTML:i}=n;export{n as A};
