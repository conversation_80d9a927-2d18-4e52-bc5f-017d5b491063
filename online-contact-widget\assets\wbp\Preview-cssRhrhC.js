import{K as o,a as s,U as c,o as t,q as l,m as e,s as i,J as a,n,N as p,a8 as w,Z as u,t as d,u as r,R as _,S as v,T as b}from"./wbs-Dtem2-xP.js";const m={class:"ocw-buoy-item"},f=["src"],h=["xlink:href"],g={key:0,class:"ocw-animated-circles ocw-animated"},x={class:"ocw-panel-head"},y={class:"ocw-pic-head"},k=["src"],z={class:"ocw-head-info"},q={class:"ocw-name"},j=["innerHTML"],T={class:"ocw-panel-main"},C={class:"buoy-default"},M={class:"ocw-msg-item"},O=["src"],H=["innerHTML"],L={class:"ocw-contact-tool"},W={class:"ocw-title"},P=["xlink:href"],Q={__name:"Preview",props:{cnf:Object,opt:Object},emits:["close-preview"],setup(Q,{emit:S}){const{wbsCnf:B}=o(),G=Q,I=S,J=s(!1),K=c({});return t((()=>{J.value=2==G.opt.is_fold,Object.assign(K,{"ocw-dark":1==G.opt.dark_switch,"ocw-big":1==G.opt.size_select,"ocw-fillet":1==G.opt.fillet_select}),G.opt.position&&(K[G.opt.position]=!0)})),(o,s)=>(e(),l("div",{class:i(["preview-wp",{"ocw-dark":Q.opt.dark_switch>0}]),style:w({"--ocw-theme-color":Q.opt.custom_theme_color,"--ocw-buoy-icon-size":Q.opt.buoy_icon_size+"px","--ocw-bfs":(Q.opt.base_font_size?Q.opt.base_font_size:12)+"px","--ocw-head-bg-color":Q.opt.color_head,"--ocw-head-fcolor":Q.opt.panel_hd_fcolor,"--ocw-unfold-size":Q.opt.unfold_size+"px","--ocw-unfold-radius":Q.opt.unfold_radius+"px","--ocw-panel-width":Q.opt.panel_width+"px","--ocw-offset-y":Q.opt.position_offset_y+"px","--ocw-offset-x":Q.opt.position_offset_x+"px"})},[Q.opt.is_fold>0?(e(),l("div",{key:0,class:i(["wb-ocw ocw-buoy",Q.opt.position?Q.opt.position:"rb"])},[a("div",m,[a("div",{class:"ocw-buoy-btn j-buoy-btn",onClick:s[0]||(s[0]=o=>J.value=!J.value)},[s[2]||(s[2]=a("i",{class:"ocw-point"},null,-1)),6==Q.opt.fold_icon&&Q.opt.buoy_icon_custom?(e(),l("img",{key:0,style:w({width:Q.opt.buoy_icon_size+"px",height:Q.opt.buoy_icon_size+"px"}),src:Q.opt.buoy_icon_custom,class:"ocw-wb-icon"},null,12,f)):(e(),l("svg",{key:1,class:"ocw-wb-icon ib",style:w({fill:Q.opt.custom_theme_color,width:Q.opt.buoy_icon_size+"px",height:Q.opt.buoy_icon_size+"px"})},[a("use",{"xlink:href":"#ocw-buoy-"+Q.opt.fold_icon},null,8,h)],4)),p(a("span",{class:"ocw-btn-text"},d(Q.opt.fold_label),513),[[u,Q.opt.name_switch>0]])]),Q.opt.buoy_animation>0&&!J.value?(e(),l("div",g,s[3]||(s[3]=[a("div",{class:"ocw-circle c-1"},null,-1),a("div",{class:"ocw-circle c-2"},null,-1),a("div",{class:"ocw-circle c-3"},null,-1)]))):n("",!0),a("div",{class:i(["ocw-buoy-panel",{"head-more-color":Q.opt.color_head,"active-panel":J.value}])},[a("div",x,[a("div",y,[a("img",{src:Q.opt.avatar_url?Q.opt.avatar_url:r(B).dir_url+"/assets/images/pic_head.png"},null,8,k),s[4]||(s[4]=a("i",{class:"ocw-b"},null,-1))]),a("div",z,[a("p",q,d(Q.opt.contact_name),1),a("p",{class:"ocw-text",innerHTML:Q.opt.contact_msg},null,8,j)])]),a("div",T,[s[5]||(s[5]=a("div",{class:"ocw-now-time"},[a("span",{class:"ocw-time"},"12:01")],-1)),a("div",C,[a("div",M,[a("img",{class:"ocw-pic",src:Q.opt.avatar_url?Q.opt.avatar_url:r(B).dir_url+"/assets/images/pic_head.png"},null,8,O),a("div",{class:"ocw-msg-con",innerHTML:Q.opt.open_msg},null,8,H)])]),a("div",L,[a("h4",W,d(Q.opt.mode_tips),1),a("div",{class:i(["ocw-tool-list",{"tool-list-color":Q.opt.custom_theme_color}]),id:"OCW_btnItems"},[(e(!0),l(_,null,v(Q.opt.items,(o=>p((e(),l("a",{class:i(["ocw-btn-tool",o]),key:o},[(e(),l("svg",{class:i(["ocw-wb-icon","ocw-"+o])},[a("use",{"xlink:href":"#ocw-"+o},null,8,P)],2))],2)),[[u,"backtop"!=o]]))),128))],2)])]),a("span",{class:"ocw-btn-close",onClick:s[1]||(s[1]=o=>I("close-preview",!0))},s[6]||(s[6]=[a("svg",{class:"cow-wb-icon ocw-close"},[a("use",{"xlink:href":"#ocw-close"})],-1)]))],2)])],2)):(e(),l("div",{key:1,class:i(["plugin-pc j-ocw-pc",K]),id:"OCW_Wp"},s[7]||(s[7]=[b('<div class="ocw-el-item qq"><span class="ocw-btn-item" title="QQ"><svg class="ocw-wb-icon ocw-qq"><use xlink:href="#ocw-qq"></use></svg></span></div><div class="ocw-el-item wx"><span class="ocw-btn-item" title="WX"><svg class="ocw-wb-icon ocw-wx"><use xlink:href="#ocw-wx"></use></svg></span></div><div class="ocw-el-item tel"><span class="ocw-btn-item" title="Phone"><svg class="ocw-wb-icon ocw-tel"><use xlink:href="#ocw-tel"></use></svg></span></div><div class="ocw-el-item msg"><span class="ocw-btn-item ocw-msg-btn" title="MSG"><svg class="ocw-wb-icon ocw-msg"><use xlink:href="#ocw-msg"></use></svg></span></div><div class="ocw-el-item backtop"><span class="ocw-btn-item" title="BT"><svg class="ocw-wb-icon ocw-backtop"><use xlink:href="#ocw-backtop"></use></svg></span></div>',5)]),2))],6))}};export{Q as default};
