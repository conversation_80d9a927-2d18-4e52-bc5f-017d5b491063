<?php
/**
 * AJAX处理类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class PRS_Ajax {

    /**
     * 构造函数
     */
    public function __construct() {
        // 注册AJAX处理函数
        add_action('wp_ajax_prs_approve_review', array($this, 'handle_approve_review'));
        add_action('wp_ajax_prs_reject_review', array($this, 'handle_reject_review'));
        add_action('wp_ajax_prs_get_review_details', array($this, 'handle_get_review_details'));
        add_action('wp_ajax_publish_product', array($this, 'handle_publish_product'));
        add_action('wp_ajax_prs_bulk_action', array($this, 'handle_bulk_action'));

        // 注册异步通知处理器
        add_action('prs_send_admin_notification', array($this, 'send_admin_notification'));
        add_action('prs_send_submitter_notification', array($this, 'send_submitter_notification'), 10, 2);
    }

    /**
     * 处理审核通过
     */
    public function handle_approve_review() {
        // 开始时间记录
        $start_time = microtime(true);

        // 速率限制检查
        if (!PRS_Security::check_rate_limit('approve_review', get_current_user_id(), 10, 60)) {
            PRS_Security::log_security_event('rate_limit_exceeded', array('action' => 'approve_review'));
            wp_send_json_error(__('操作过于频繁，请稍后再试', 'product-review-system'));
        }

        // 验证nonce
        if (!PRS_Security::verify_csrf_token('prs_admin_nonce')) {
            PRS_Security::log_security_event('csrf_token_failed', array('action' => 'approve_review'));
            wp_send_json_error(__('安全验证失败', 'product-review-system'));
        }

        $review_id = intval($_POST['review_id']);
        // 修复权限绕过漏洞：服务器端验证用户角色，不信任客户端输入
        $user_role = PRS_Permissions::get_user_review_role();
        $notes = PRS_Security::validate_input($_POST['notes'], 'textarea');

        // 验证用户权限
        if (!PRS_Security::verify_permission('review') && !PRS_Security::verify_permission('admin_review')) {
            PRS_Security::log_security_event('unauthorized_access', array('action' => 'approve_review', 'review_id' => $review_id));
            wp_send_json_error(__('您没有权限执行此操作', 'product-review-system'));
        }

        // 验证审核记录访问权限
        if (!PRS_Security::verify_review_ownership($review_id)) {
            PRS_Security::log_security_event('unauthorized_review_access', array('review_id' => $review_id));
            wp_send_json_error(__('您没有权限访问此审核记录', 'product-review-system'));
        }

        // 防止重复提交：检查是否正在处理
        $lock_key = 'prs_processing_' . $review_id;
        if (get_transient($lock_key)) {
            wp_send_json_error(__('正在处理中，请勿重复提交', 'product-review-system'));
        }

        // 设置处理锁，30秒过期
        set_transient($lock_key, true, 30);

        try {
            $review = PRS_Database::get_review($review_id);
            if (!$review) {
                delete_transient($lock_key);
                wp_send_json_error(__('审核记录不存在', 'product-review-system'));
            }

            if ($user_role === 'reviewer') {
                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS AJAX: Checking reviewer permissions for user: ' . get_current_user_id());
                }
                if (!PRS_Permissions::can_user_review()) {
                    PRS_Security::log_security_event('unauthorized_ajax_reviewer_action', array(
                        'user_id' => get_current_user_id(),
                        'review_id' => $review_id
                    ));
                    delete_transient($lock_key);
                    wp_send_json_error(__('您没有权限执行此操作', 'product-review-system'));
                }

                if ($review->status !== 'pending_review') {
                    if (PRS_Security::is_debug_enabled()) {
                        error_log('PRS AJAX: Review status is not pending_review, current status: ' . $review->status);
                    }
                    delete_transient($lock_key);
                    $current_status_text = $this->get_status_text($review->status);
                    wp_send_json_error(sprintf(__('该审核记录状态不正确，当前状态：%s', 'product-review-system'), $current_status_text));
                }

                // 检查是否已经被其他审核员处理
                if (!empty($review->reviewer_id) && $review->reviewer_id != get_current_user_id()) {
                    delete_transient($lock_key);
                    $reviewer = get_user_by('id', $review->reviewer_id);
                    $reviewer_name = $reviewer ? $reviewer->display_name : '未知用户';
                    wp_send_json_error(sprintf(__('该审核已被 %s 处理', 'product-review-system'), $reviewer_name));
                }

                $update_data = array(
                    'reviewer_id' => get_current_user_id(),
                    'reviewer_status' => 'approved',
                    'status' => 'pending_admin',
                    'reviewer_date' => current_time('mysql'),
                    'reviewer_notes' => $notes
                );

                $result = PRS_Database::update_review($review_id, $update_data);

                if ($result) {
                    // 清除处理锁
                    delete_transient($lock_key);

                    // 记录处理时间（仅在调试模式下）
                    if (PRS_Security::is_debug_enabled()) {
                        $processing_time = round((microtime(true) - $start_time) * 1000, 2);
                        error_log("PRS: Reviewer approval completed in {$processing_time}ms");
                    }

                    // 异步发送通知（避免阻塞响应）
                    wp_schedule_single_event(time(), 'prs_send_admin_notification', array($review_id));

                    wp_send_json_success(__('审核通过，已提交给管理员进行最终审核', 'product-review-system'));
                } else {
                    delete_transient($lock_key);
                    wp_send_json_error(__('数据库更新失败，请重试', 'product-review-system'));
                }

            } elseif ($user_role === 'admin') {
                if (!PRS_Permissions::can_user_admin_review()) {
                    delete_transient($lock_key);
                    wp_send_json_error(__('您没有权限执行此操作', 'product-review-system'));
                }

                if ($review->status !== 'pending_admin') {
                    delete_transient($lock_key);
                    $current_status_text = $this->get_status_text($review->status);
                    wp_send_json_error(sprintf(__('该审核记录状态不正确，当前状态：%s', 'product-review-system'), $current_status_text));
                }

                // 应用产品修改
                $success = $this->apply_product_changes($review);

                if ($success) {
                    $update_data = array(
                        'admin_id' => get_current_user_id(),
                        'admin_status' => 'approved',
                        'status' => 'approved',
                        'admin_date' => current_time('mysql'),
                        'admin_notes' => $notes
                    );

                    $result = PRS_Database::update_review($review_id, $update_data);

                    if ($result) {
                        // 清除处理锁
                        delete_transient($lock_key);

                        // 记录处理时间（仅在调试模式下）
                        if (PRS_Security::is_debug_enabled()) {
                            $processing_time = round((microtime(true) - $start_time) * 1000, 2);
                            error_log("PRS: Admin approval completed in {$processing_time}ms");
                        }

                        // 异步发送通知
                        wp_schedule_single_event(time(), 'prs_send_submitter_notification', array($review_id, 'approved'));

                        wp_send_json_success(__('审核通过，产品修改已应用', 'product-review-system'));
                    } else {
                        delete_transient($lock_key);
                        wp_send_json_error(__('数据库更新失败，请重试', 'product-review-system'));
                    }
                } else {
                    delete_transient($lock_key);
                    wp_send_json_error(__('应用产品修改失败，请检查产品数据', 'product-review-system'));
                }
            } else {
                delete_transient($lock_key);
                wp_send_json_error(__('无效的用户角色', 'product-review-system'));
            }
        } catch (Exception $e) {
            // 清除处理锁
            delete_transient($lock_key);
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS AJAX: Exception in handle_approve_review: ' . $e->getMessage());
            }
            PRS_Security::log_security_event('ajax_exception', array(
                'action' => 'approve_review',
                'message' => $e->getMessage(),
                'review_id' => $review_id
            ));
            wp_send_json_error(__('处理过程中发生错误，请重试', 'product-review-system'));
        }
    }

    /**
     * 处理审核拒绝
     */
    public function handle_reject_review() {
        // 速率限制检查
        if (!PRS_Security::check_rate_limit('reject_review', get_current_user_id(), 10, 60)) {
            PRS_Security::log_security_event('rate_limit_exceeded', array('action' => 'reject_review'));
            wp_send_json_error(__('操作过于频繁，请稍后再试', 'product-review-system'));
        }

        // 验证nonce
        if (!PRS_Security::verify_csrf_token('prs_admin_nonce')) {
            PRS_Security::log_security_event('csrf_token_failed', array('action' => 'reject_review'));
            wp_send_json_error(__('安全验证失败', 'product-review-system'));
        }

        $review_id = intval($_POST['review_id']);
        // 修复权限绕过漏洞：服务器端验证用户角色
        $user_role = PRS_Permissions::get_user_review_role();
        $notes = PRS_Security::validate_input($_POST['notes'], 'textarea');

        // 验证用户权限
        if (!PRS_Security::verify_permission('review') && !PRS_Security::verify_permission('admin_review')) {
            PRS_Security::log_security_event('unauthorized_access', array('action' => 'reject_review', 'review_id' => $review_id));
            wp_send_json_error(__('您没有权限执行此操作', 'product-review-system'));
        }

        // 验证审核记录访问权限
        if (!PRS_Security::verify_review_ownership($review_id)) {
            PRS_Security::log_security_event('unauthorized_review_access', array('review_id' => $review_id));
            wp_send_json_error(__('您没有权限访问此审核记录', 'product-review-system'));
        }

        $review = PRS_Database::get_review($review_id);
        if (!$review) {
            wp_send_json_error(__('审核记录不存在', 'product-review-system'));
        }

        if ($user_role === 'reviewer') {
            if (!PRS_Permissions::can_user_review()) {
                wp_send_json_error(__('您没有权限执行此操作', 'product-review-system'));
            }

            if ($review->status !== 'pending_review') {
                wp_send_json_error(__('该审核记录状态不正确', 'product-review-system'));
            }

            $update_data = array(
                'reviewer_id' => get_current_user_id(),
                'reviewer_status' => 'rejected',
                'status' => 'rejected',
                'reviewer_date' => current_time('mysql'),
                'reviewer_notes' => $notes
            );

        } elseif ($user_role === 'admin') {
            if (!PRS_Permissions::can_user_admin_review()) {
                wp_send_json_error(__('您没有权限执行此操作', 'product-review-system'));
            }

            if ($review->status !== 'pending_admin') {
                wp_send_json_error(__('该审核记录状态不正确', 'product-review-system'));
            }

            $update_data = array(
                'admin_id' => get_current_user_id(),
                'admin_status' => 'rejected',
                'status' => 'rejected',
                'admin_date' => current_time('mysql'),
                'admin_notes' => $notes
            );
        } else {
            wp_send_json_error(__('无效的用户角色', 'product-review-system'));
        }

        $result = PRS_Database::update_review($review_id, $update_data);

        if ($result) {
            PRS_Notifications::notify_submitter($review_id, 'rejected');
            wp_send_json_success(__('审核已拒绝，已通知提交者', 'product-review-system'));
        } else {
            wp_send_json_error(__('操作失败，请重试', 'product-review-system'));
        }
    }

    /**
     * 获取审核详情
     */
    public function handle_get_review_details() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'], 'prs_admin_nonce')) {
            wp_send_json_error(__('安全验证失败', 'product-review-system'));
        }

        $review_id = intval($_POST['review_id']);

        $review = PRS_Database::get_review($review_id);
        if (!$review) {
            wp_send_json_error(__('审核记录不存在', 'product-review-system'));
        }

        $product = wc_get_product($review->product_id);
        $submitter = get_user_by('id', $review->submitter_id);

        $original_data = json_decode($review->original_data, true);
        $modified_data = json_decode($review->modified_data, true);

        $response_data = array(
            'id' => $review->id,
            'product_name' => $product ? $product->get_name() : __('未知产品', 'product-review-system'),
            'submitter_name' => $submitter ? $submitter->display_name : __('未知用户', 'product-review-system'),
            'status' => $review->status,
            'change_summary' => $review->change_summary,
            'created_at' => mysql2date('Y-m-d H:i:s', $review->created_at),
            'original_data' => $original_data,
            'modified_data' => $modified_data
        );

        wp_send_json_success($response_data);
    }

    /**
     * 处理批量操作
     */
    public function handle_bulk_action() {
        // 速率限制检查
        if (!PRS_Security::check_rate_limit('bulk_action', get_current_user_id(), 3, 60)) {
            PRS_Security::log_security_event('rate_limit_exceeded', array('action' => 'bulk_action'));
            wp_send_json_error(__('批量操作过于频繁，请稍后再试', 'product-review-system'));
        }

        // 验证nonce
        if (!PRS_Security::verify_csrf_token('prs_admin_nonce')) {
            PRS_Security::log_security_event('csrf_token_failed', array('action' => 'bulk_action'));
            wp_send_json_error(__('安全验证失败', 'product-review-system'));
        }

        $action = PRS_Security::validate_input($_POST['bulk_action'], 'text');
        $review_ids = array_map('intval', (array) $_POST['review_ids']);

        if (empty($review_ids)) {
            wp_send_json_error(__('请选择要操作的记录', 'product-review-system'));
        }

        // 限制批量操作的数量
        if (count($review_ids) > 50) {
            wp_send_json_error(__('一次最多只能操作50条记录', 'product-review-system'));
        }

        $success_count = 0;
        $error_count = 0;

        foreach ($review_ids as $review_id) {
            $review = PRS_Database::get_review($review_id);
            if (!$review) {
                $error_count++;
                continue;
            }

            switch ($action) {
                case 'delete':
                    if (current_user_can('delete_product_reviews')) {
                        if (PRS_Database::delete_review($review_id)) {
                            $success_count++;
                        } else {
                            $error_count++;
                        }
                    } else {
                        $error_count++;
                    }
                    break;

                default:
                    $error_count++;
                    break;
            }
        }

        $message = sprintf(__('成功处理 %d 条记录，失败 %d 条记录', 'product-review-system'), $success_count, $error_count);

        if ($error_count > 0) {
            wp_send_json_error($message);
        } else {
            wp_send_json_success($message);
        }
    }

    /**
     * 应用产品修改（使用统一的处理逻辑）
     */
    private function apply_product_changes($review) {
        $modified_data = json_decode($review->modified_data, true);
        if (!$modified_data) {
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS AJAX: Failed to decode modified_data JSON');
            }
            return false;
        }

        $product_id = $review->product_id;
        $product = wc_get_product($product_id);
        if (!$product) {
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS AJAX: Product not found with ID: ' . $product_id);
            }
            return false;
        }

        // 使用产品处理器的方法来应用修改
        if (class_exists('PRS_Product_Handler')) {
            $handler = new PRS_Product_Handler();
            $success = $handler->apply_approved_changes($product_id, $modified_data);

            if ($success) {
                // 额外的状态同步处理
                $this->sync_product_status_ajax($product_id, $modified_data);

                // 记录成功日志（仅在调试模式下）
                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS AJAX: Successfully applied product changes for product ID: ' . $product_id);
                }
                return true;
            } else {
                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS AJAX: Failed to apply product changes via handler for product ID: ' . $product_id);
                }
                return false;
            }
        }

        // 如果产品处理器不可用，返回失败
        if (PRS_Security::is_debug_enabled()) {
            error_log('PRS AJAX: PRS_Product_Handler class not available');
        }
        return false;
    }

    /**
     * AJAX环境下的产品状态同步
     */
    private function sync_product_status_ajax($product_id, $modified_data) {
        try {
            // 强制更新产品状态
            if (isset($modified_data['status'])) {
                $post_data = array(
                    'ID' => $product_id,
                    'post_status' => $modified_data['status']
                );
                wp_update_post($post_data);

                // 如果是发布状态，确保产品可见性正确
                if ($modified_data['status'] === 'publish') {
                    // 更新产品可见性
                    if (isset($modified_data['catalog_visibility'])) {
                        update_post_meta($product_id, '_visibility', $modified_data['catalog_visibility']);
                    }

                    // 确保产品在目录中可见
                    $visibility = get_post_meta($product_id, '_visibility', true);
                    if (empty($visibility)) {
                        update_post_meta($product_id, '_visibility', 'visible');
                    }
                }
            }

            // 清除所有相关缓存
            wp_cache_delete($product_id, 'posts');
            wp_cache_delete($product_id, 'post_meta');
            wc_delete_product_transients($product_id);

            // 清除WooCommerce对象缓存
            if (function_exists('wc_get_product')) {
                $product = wc_get_product($product_id);
                if ($product) {
                    $product->read_meta_data(true); // 强制重新读取元数据
                }
            }

            // 触发产品更新钩子
            do_action('woocommerce_product_object_updated_props', wc_get_product($product_id), array('status'));
            do_action('prs_product_status_synced_ajax', $product_id, $modified_data);

        } catch (Exception $e) {
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS AJAX: Failed to sync product status: ' . $e->getMessage());
            }
        }
    }

    /**
     * 安全验证：验证用户是否有权限执行特定操作
     */
    private function verify_user_permission($action, $review = null) {
        $user_role = PRS_Permissions::get_user_review_role();

        switch ($action) {
            case 'reviewer_approve':
            case 'reviewer_reject':
                if (!PRS_Permissions::can_user_review()) {
                    return false;
                }
                if ($review && $review->status !== 'pending_review') {
                    return false;
                }
                break;

            case 'admin_approve':
            case 'admin_reject':
                if (!PRS_Permissions::can_user_admin_review()) {
                    return false;
                }
                if ($review && $review->status !== 'pending_admin') {
                    return false;
                }
                break;

            default:
                return false;
        }

        return true;
    }

    /**
     * 安全验证：验证审核记录状态
     */
    private function verify_review_status($review, $expected_status) {
        if (!$review) {
            return false;
        }

        if (is_array($expected_status)) {
            return in_array($review->status, $expected_status);
        }

        return $review->status === $expected_status;
    }

    /**
     * 处理直接发布产品请求
     */
    public function handle_publish_product() {
        // 速率限制检查
        if (!PRS_Security::check_rate_limit('publish_product', get_current_user_id(), 5, 60)) {
            PRS_Security::log_security_event('rate_limit_exceeded', array('action' => 'publish_product'));
            wp_send_json_error('操作过于频繁，请稍后再试');
            return;
        }

        // 验证nonce
        if (!PRS_Security::verify_csrf_token('publish_product')) {
            PRS_Security::log_security_event('csrf_token_failed', array('action' => 'publish_product'));
            wp_send_json_error('安全验证失败');
            return;
        }

        $product_id = intval($_POST['product_id']);
        if (!$product_id) {
            wp_send_json_error('无效的产品ID');
            return;
        }

        // 验证产品访问权限
        if (!PRS_Security::verify_product_access($product_id, 'edit')) {
            PRS_Security::log_security_event('unauthorized_product_access', array('product_id' => $product_id));
            wp_send_json_error('您没有权限编辑此产品');
            return;
        }

        // 检查产品是否存在
        $product = wc_get_product($product_id);
        if (!$product) {
            wp_send_json_error('产品不存在');
            return;
        }

        try {
            // 更新产品状态为发布
            $result = wp_update_post(array(
                'ID' => $product_id,
                'post_status' => 'publish'
            ));

            if (is_wp_error($result)) {
                wp_send_json_error('更新失败: ' . $result->get_error_message());
                return;
            }

            // 确保产品可见性
            update_post_meta($product_id, '_visibility', 'visible');
            update_post_meta($product_id, '_catalog_visibility', 'visible');

            // 清除缓存
            wc_delete_product_transients($product_id);
            wp_cache_delete($product_id, 'posts');

            wp_send_json_success('产品已成功发布');

        } catch (Exception $e) {
            wp_send_json_error('发布失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取状态文本
     */
    private function get_status_text($status) {
        $status_texts = array(
            'pending_review' => __('待审核员审核', 'product-review-system'),
            'pending_admin' => __('待管理员审核', 'product-review-system'),
            'approved' => __('已通过', 'product-review-system'),
            'rejected' => __('已拒绝', 'product-review-system')
        );

        return isset($status_texts[$status]) ? $status_texts[$status] : $status;
    }

    /**
     * 异步发送管理员通知
     */
    public function send_admin_notification($review_id) {
        try {
            PRS_Notifications::notify_admins($review_id);
            if (PRS_Security::is_debug_enabled()) {
                error_log("PRS: Admin notification sent for review ID: {$review_id}");
            }
        } catch (Exception $e) {
            if (PRS_Security::is_debug_enabled()) {
                error_log("PRS: Failed to send admin notification: " . $e->getMessage());
            }
        }
    }

    /**
     * 异步发送提交者通知
     */
    public function send_submitter_notification($review_id, $status) {
        try {
            PRS_Notifications::notify_submitter($review_id, $status);
            if (PRS_Security::is_debug_enabled()) {
                error_log("PRS: Submitter notification sent for review ID: {$review_id}, status: {$status}");
            }
        } catch (Exception $e) {
            if (PRS_Security::is_debug_enabled()) {
                error_log("PRS: Failed to send submitter notification: " . $e->getMessage());
            }
        }
    }
}
