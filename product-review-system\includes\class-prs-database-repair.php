<?php
/**
 * 产品审核系统数据库修复工具
 * 
 * 基于第一性原理设计的数据库修复系统
 * 遵循SOLID原则，提供完整的数据库检测和修复功能
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 数据库修复工具主类
 * 
 * 职责：
 * 1. 检测数据库状态
 * 2. 识别问题
 * 3. 执行修复操作
 * 4. 提供修复报告
 */
class PRS_Database_Repair {
    
    /**
     * 修复结果常量
     */
    const REPAIR_SUCCESS = 'success';
    const REPAIR_WARNING = 'warning';
    const REPAIR_ERROR = 'error';
    const REPAIR_SKIPPED = 'skipped';
    
    /**
     * 数据库表定义
     */
    private static $table_definitions = array();
    
    /**
     * 修复日志
     */
    private $repair_log = array();
    
    /**
     * 初始化表定义
     */
    public function __construct() {
        $this->init_table_definitions();
    }
    
    /**
     * 初始化数据库表定义
     * 
     * 基于第一性原理，定义完整的表结构
     */
    private function init_table_definitions() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        // 产品审核主表
        self::$table_definitions['product_reviews'] = array(
            'table_name' => $wpdb->prefix . 'product_reviews',
            'sql' => "CREATE TABLE {$wpdb->prefix}product_reviews (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                product_id bigint(20) NOT NULL,
                original_data longtext NOT NULL,
                modified_data longtext NOT NULL,
                change_summary text DEFAULT '',
                submitter_id bigint(20) NOT NULL,
                reviewer_id bigint(20) DEFAULT NULL,
                admin_id bigint(20) DEFAULT NULL,
                status varchar(20) DEFAULT 'pending_review',
                reviewer_status varchar(20) DEFAULT 'pending',
                admin_status varchar(20) DEFAULT 'pending',
                reviewer_notes text DEFAULT '',
                admin_notes text DEFAULT '',
                reviewer_date datetime DEFAULT NULL,
                admin_date datetime DEFAULT NULL,
                is_new_product tinyint(1) DEFAULT 0,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY product_id (product_id),
                KEY submitter_id (submitter_id),
                KEY reviewer_id (reviewer_id),
                KEY admin_id (admin_id),
                KEY status (status),
                KEY reviewer_status (reviewer_status),
                KEY admin_status (admin_status),
                KEY is_new_product (is_new_product),
                KEY created_at (created_at)
            ) $charset_collate;",
            'required_columns' => array(
                'id', 'product_id', 'original_data', 'modified_data', 'change_summary',
                'submitter_id', 'reviewer_id', 'admin_id', 'status', 'reviewer_status',
                'admin_status', 'reviewer_notes', 'admin_notes', 'reviewer_date',
                'admin_date', 'is_new_product', 'created_at', 'updated_at'
            ),
            'required_indexes' => array(
                'PRIMARY', 'product_id', 'submitter_id', 'reviewer_id', 'admin_id',
                'status', 'reviewer_status', 'admin_status', 'is_new_product', 'created_at'
            )
        );
        
        // 审核历史表（可选）
        self::$table_definitions['product_review_history'] = array(
            'table_name' => $wpdb->prefix . 'product_review_history',
            'sql' => "CREATE TABLE {$wpdb->prefix}product_review_history (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                review_id bigint(20) NOT NULL,
                user_id bigint(20) NOT NULL,
                action varchar(50) NOT NULL,
                comment text,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY review_id (review_id),
                KEY user_id (user_id),
                KEY action (action),
                KEY created_at (created_at)
            ) $charset_collate;",
            'required_columns' => array(
                'id', 'review_id', 'user_id', 'action', 'comment', 'created_at'
            ),
            'required_indexes' => array(
                'PRIMARY', 'review_id', 'user_id', 'action', 'created_at'
            ),
            'optional' => true
        );
    }
    
    /**
     * 执行完整的数据库检测
     * 
     * @return array 检测结果
     */
    public function diagnose_database() {
        $this->log('开始数据库诊断', 'info');
        
        $diagnosis = array(
            'overall_status' => 'healthy',
            'issues' => array(),
            'tables' => array(),
            'recommendations' => array()
        );
        
        // 检查每个表
        foreach (self::$table_definitions as $table_key => $table_def) {
            $table_diagnosis = $this->diagnose_table($table_def);
            $diagnosis['tables'][$table_key] = $table_diagnosis;
            
            // 收集问题
            if (!empty($table_diagnosis['issues'])) {
                $diagnosis['issues'] = array_merge($diagnosis['issues'], $table_diagnosis['issues']);
                $diagnosis['overall_status'] = 'needs_repair';
            }
        }
        
        // 检查数据库权限
        $permission_check = $this->check_database_permissions();
        if (!$permission_check['success']) {
            $diagnosis['issues'][] = array(
                'type' => 'permission',
                'severity' => 'critical',
                'message' => '数据库权限不足',
                'details' => $permission_check['error']
            );
            $diagnosis['overall_status'] = 'critical';
        }
        
        // 生成修复建议
        $diagnosis['recommendations'] = $this->generate_recommendations($diagnosis['issues']);
        
        $this->log('数据库诊断完成', 'info');
        return $diagnosis;
    }
    
    /**
     * 诊断单个表
     * 
     * @param array $table_def 表定义
     * @return array 诊断结果
     */
    private function diagnose_table($table_def) {
        global $wpdb;
        
        $table_name = $table_def['table_name'];
        $diagnosis = array(
            'exists' => false,
            'columns' => array(),
            'indexes' => array(),
            'issues' => array(),
            'status' => 'unknown'
        );
        
        // 检查表是否存在
        $table_exists = $wpdb->get_var($wpdb->prepare(
            "SHOW TABLES LIKE %s",
            $table_name
        )) == $table_name;
        
        $diagnosis['exists'] = $table_exists;
        
        if (!$table_exists) {
            if (isset($table_def['optional']) && $table_def['optional']) {
                $diagnosis['status'] = 'optional_missing';
                $diagnosis['issues'][] = array(
                    'type' => 'table_missing',
                    'severity' => 'low',
                    'message' => "可选表 '$table_name' 不存在",
                    'table' => $table_name
                );
            } else {
                $diagnosis['status'] = 'missing';
                $diagnosis['issues'][] = array(
                    'type' => 'table_missing',
                    'severity' => 'critical',
                    'message' => "必需表 '$table_name' 不存在",
                    'table' => $table_name
                );
            }
            return $diagnosis;
        }
        
        // 检查列结构
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        $existing_columns = array_column($columns, 'Field');
        $diagnosis['columns'] = $existing_columns;
        
        $missing_columns = array_diff($table_def['required_columns'], $existing_columns);
        if (!empty($missing_columns)) {
            $diagnosis['issues'][] = array(
                'type' => 'missing_columns',
                'severity' => 'high',
                'message' => "表 '$table_name' 缺失字段",
                'table' => $table_name,
                'missing_columns' => $missing_columns
            );
        }
        
        // 检查索引
        $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
        $existing_indexes = array_unique(array_column($indexes, 'Key_name'));
        $diagnosis['indexes'] = $existing_indexes;
        
        $missing_indexes = array_diff($table_def['required_indexes'], $existing_indexes);
        if (!empty($missing_indexes)) {
            $diagnosis['issues'][] = array(
                'type' => 'missing_indexes',
                'severity' => 'medium',
                'message' => "表 '$table_name' 缺失索引",
                'table' => $table_name,
                'missing_indexes' => $missing_indexes
            );
        }
        
        // 确定表状态
        if (empty($diagnosis['issues'])) {
            $diagnosis['status'] = 'healthy';
        } else {
            $has_critical = false;
            foreach ($diagnosis['issues'] as $issue) {
                if ($issue['severity'] === 'critical' || $issue['severity'] === 'high') {
                    $has_critical = true;
                    break;
                }
            }
            $diagnosis['status'] = $has_critical ? 'needs_repair' : 'minor_issues';
        }
        
        return $diagnosis;
    }
    
    /**
     * 检查数据库权限
     * 
     * @return array 权限检查结果
     */
    private function check_database_permissions() {
        global $wpdb;
        
        try {
            // 测试创建表权限
            $test_table = $wpdb->prefix . 'prs_permission_test_' . time();
            $result = $wpdb->query("CREATE TABLE $test_table (id INT AUTO_INCREMENT PRIMARY KEY)");
            
            if ($result === false) {
                return array(
                    'success' => false,
                    'error' => '无法创建测试表: ' . $wpdb->last_error
                );
            }
            
            // 测试删除表权限
            $wpdb->query("DROP TABLE $test_table");
            
            return array('success' => true);
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => '权限检查异常: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * 生成修复建议
     * 
     * @param array $issues 问题列表
     * @return array 修复建议
     */
    private function generate_recommendations($issues) {
        $recommendations = array();
        
        foreach ($issues as $issue) {
            switch ($issue['type']) {
                case 'table_missing':
                    $recommendations[] = array(
                        'action' => 'create_table',
                        'priority' => $issue['severity'] === 'critical' ? 'high' : 'low',
                        'description' => "创建缺失的表: {$issue['table']}",
                        'table' => $issue['table']
                    );
                    break;
                    
                case 'missing_columns':
                    $recommendations[] = array(
                        'action' => 'add_columns',
                        'priority' => 'high',
                        'description' => "为表 {$issue['table']} 添加缺失字段",
                        'table' => $issue['table'],
                        'columns' => $issue['missing_columns']
                    );
                    break;
                    
                case 'missing_indexes':
                    $recommendations[] = array(
                        'action' => 'add_indexes',
                        'priority' => 'medium',
                        'description' => "为表 {$issue['table']} 添加缺失索引",
                        'table' => $issue['table'],
                        'indexes' => $issue['missing_indexes']
                    );
                    break;
                    
                case 'permission':
                    $recommendations[] = array(
                        'action' => 'fix_permissions',
                        'priority' => 'critical',
                        'description' => '修复数据库权限问题',
                        'manual' => true
                    );
                    break;
            }
        }
        
        return $recommendations;
    }
    
    /**
     * 记录日志
     * 
     * @param string $message 日志消息
     * @param string $level 日志级别
     */
    private function log($message, $level = 'info') {
        $this->repair_log[] = array(
            'timestamp' => current_time('mysql'),
            'level' => $level,
            'message' => $message
        );
        
        // 同时写入WordPress错误日志
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PRS Database Repair [$level]: $message");
        }
    }
    
    /**
     * 执行数据库修复
     *
     * @param array $options 修复选项
     * @return array 修复结果
     */
    public function repair_database($options = array()) {
        $this->log('开始数据库修复', 'info');

        $repair_results = array(
            'success' => true,
            'actions_performed' => array(),
            'errors' => array(),
            'warnings' => array()
        );

        // 首先诊断数据库
        $diagnosis = $this->diagnose_database();

        if ($diagnosis['overall_status'] === 'healthy') {
            $this->log('数据库状态正常，无需修复', 'info');
            return $repair_results;
        }

        // 执行修复操作
        foreach ($diagnosis['recommendations'] as $recommendation) {
            if (isset($recommendation['manual']) && $recommendation['manual']) {
                $repair_results['warnings'][] = $recommendation['description'] . ' (需要手动处理)';
                continue;
            }

            $action_result = $this->execute_repair_action($recommendation);

            if ($action_result['success']) {
                $repair_results['actions_performed'][] = $action_result['description'];
                $this->log($action_result['description'], 'success');
            } else {
                $repair_results['errors'][] = $action_result['error'];
                $repair_results['success'] = false;
                $this->log($action_result['error'], 'error');
            }
        }

        // 验证修复结果
        $post_repair_diagnosis = $this->diagnose_database();
        if ($post_repair_diagnosis['overall_status'] === 'healthy') {
            $this->log('数据库修复完成，状态正常', 'success');
        } else {
            $this->log('数据库修复完成，但仍有问题需要处理', 'warning');
            $repair_results['success'] = false;
        }

        return $repair_results;
    }

    /**
     * 执行单个修复操作
     *
     * @param array $recommendation 修复建议
     * @return array 操作结果
     */
    private function execute_repair_action($recommendation) {
        switch ($recommendation['action']) {
            case 'create_table':
                return $this->create_missing_table($recommendation['table']);

            case 'add_columns':
                return $this->add_missing_columns($recommendation['table'], $recommendation['columns']);

            case 'add_indexes':
                return $this->add_missing_indexes($recommendation['table'], $recommendation['indexes']);

            default:
                return array(
                    'success' => false,
                    'error' => '未知的修复操作: ' . $recommendation['action']
                );
        }
    }

    /**
     * 创建缺失的表
     *
     * @param string $table_name 表名
     * @return array 操作结果
     */
    private function create_missing_table($table_name) {
        global $wpdb;

        // 查找表定义
        $table_def = null;
        foreach (self::$table_definitions as $key => $def) {
            if ($def['table_name'] === $table_name) {
                $table_def = $def;
                break;
            }
        }

        if (!$table_def) {
            return array(
                'success' => false,
                'error' => "未找到表 '$table_name' 的定义"
            );
        }

        // 使用dbDelta创建表
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $result = dbDelta($table_def['sql']);

        if ($wpdb->last_error) {
            return array(
                'success' => false,
                'error' => "创建表 '$table_name' 失败: " . $wpdb->last_error
            );
        }

        // 验证表是否创建成功
        $table_exists = $wpdb->get_var($wpdb->prepare(
            "SHOW TABLES LIKE %s",
            $table_name
        )) == $table_name;

        if ($table_exists) {
            return array(
                'success' => true,
                'description' => "成功创建表: $table_name"
            );
        } else {
            return array(
                'success' => false,
                'error' => "表 '$table_name' 创建后验证失败"
            );
        }
    }

    /**
     * 添加缺失的字段
     *
     * @param string $table_name 表名
     * @param array $missing_columns 缺失的字段
     * @return array 操作结果
     */
    private function add_missing_columns($table_name, $missing_columns) {
        global $wpdb;

        // 获取表定义
        $table_def = null;
        foreach (self::$table_definitions as $key => $def) {
            if ($def['table_name'] === $table_name) {
                $table_def = $def;
                break;
            }
        }

        if (!$table_def) {
            return array(
                'success' => false,
                'error' => "未找到表 '$table_name' 的定义"
            );
        }

        // 使用dbDelta更新表结构
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $result = dbDelta($table_def['sql']);

        if ($wpdb->last_error) {
            return array(
                'success' => false,
                'error' => "更新表 '$table_name' 结构失败: " . $wpdb->last_error
            );
        }

        return array(
            'success' => true,
            'description' => "成功为表 '$table_name' 添加字段: " . implode(', ', $missing_columns)
        );
    }

    /**
     * 添加缺失的索引
     *
     * @param string $table_name 表名
     * @param array $missing_indexes 缺失的索引
     * @return array 操作结果
     */
    private function add_missing_indexes($table_name, $missing_indexes) {
        global $wpdb;

        $added_indexes = array();
        $errors = array();

        foreach ($missing_indexes as $index_name) {
            if ($index_name === 'PRIMARY') {
                continue; // 主键通常在表创建时已定义
            }

            // 根据索引名称确定字段
            $index_sql = $this->get_index_sql($table_name, $index_name);

            if ($index_sql) {
                $result = $wpdb->query($index_sql);

                if ($result !== false) {
                    $added_indexes[] = $index_name;
                } else {
                    $errors[] = "添加索引 '$index_name' 失败: " . $wpdb->last_error;
                }
            }
        }

        if (!empty($errors)) {
            return array(
                'success' => false,
                'error' => implode('; ', $errors)
            );
        }

        return array(
            'success' => true,
            'description' => "成功为表 '$table_name' 添加索引: " . implode(', ', $added_indexes)
        );
    }

    /**
     * 获取索引SQL语句
     *
     * @param string $table_name 表名
     * @param string $index_name 索引名
     * @return string|false SQL语句或false
     */
    private function get_index_sql($table_name, $index_name) {
        // 根据索引名称生成对应的SQL
        $index_definitions = array(
            'product_id' => "ALTER TABLE $table_name ADD INDEX product_id (product_id)",
            'submitter_id' => "ALTER TABLE $table_name ADD INDEX submitter_id (submitter_id)",
            'reviewer_id' => "ALTER TABLE $table_name ADD INDEX reviewer_id (reviewer_id)",
            'admin_id' => "ALTER TABLE $table_name ADD INDEX admin_id (admin_id)",
            'status' => "ALTER TABLE $table_name ADD INDEX status (status)",
            'reviewer_status' => "ALTER TABLE $table_name ADD INDEX reviewer_status (reviewer_status)",
            'admin_status' => "ALTER TABLE $table_name ADD INDEX admin_status (admin_status)",
            'is_new_product' => "ALTER TABLE $table_name ADD INDEX is_new_product (is_new_product)",
            'created_at' => "ALTER TABLE $table_name ADD INDEX created_at (created_at)",
            'review_id' => "ALTER TABLE $table_name ADD INDEX review_id (review_id)",
            'user_id' => "ALTER TABLE $table_name ADD INDEX user_id (user_id)",
            'action' => "ALTER TABLE $table_name ADD INDEX action (action)"
        );

        return isset($index_definitions[$index_name]) ? $index_definitions[$index_name] : false;
    }

    /**
     * 获取修复日志
     *
     * @return array 日志数组
     */
    public function get_repair_log() {
        return $this->repair_log;
    }
}
