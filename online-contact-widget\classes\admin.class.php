<?php

/**
 * Author: wbolt team
 * Author URI: https://www.wbolt.com
 */

class OCW_Base
{

	public static function param($key, $default = '', $type = 'p')
	{
		if ('p' === $type) {
			if (isset($_POST[$key])) {
				return $_POST[$key];
			}
			return $default;
		} else if ('g' === $type) {
			if (isset($_GET[$key])) {
				return $_GET[$key];
			}
			return $default;
		}
		if (isset($_POST[$key])) {
			return $_POST[$key];
		}
		if (isset($_GET[$key])) {
			return $_GET[$key];
		}
		return $default;
	}
	/**
	 * @see wpdb
	 * @return mixed
	 */
	public static function db()
	{
		static $db = null;
		if ($db) {
			return $db;
		}
		$db = $GLOBALS['wpdb'];
		if ($db instanceof wpdb) {
			return $db;
		}
		return $db;
	}

	public static function ajax_resp($ret)
	{
		header('content-type:text/json;charset=utf-8');
		echo wp_json_encode($ret);
		exit();
	}
}
class OCW_Admin extends OCW_Base
{
	public static $name = 'wb_ocw';
	public static $option_name = 'ocw_option';
	public static $db_ver = 2;
	public static $sms_result = null;
	public static function cnf_fields($key = '')
	{
		$cnf_data = array(
			// 联系方式的配置字段
			'contact_items' => array(
				'backtop' => array(
					'id' => 'backtop',
					'title' => _x('返回顶部', 'label', WB_OCW_DM),
					'name' => _x('返回顶部', 'label', WB_OCW_DM),
					'multiple' => false,
					'fields' => [],
					'base' => true // 初始加有的数据
				),
				'tel' => array(
					'id' => 'tel',
					'title' => __('电话', WB_OCW_DM),
					'name' => _x('电话联系', 'label', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'url',
							'label' => __('电话号码', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('电话或手机号', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => true
				),
				'email' => array(
					'id' => 'email',
					'title' => __('电子邮件', WB_OCW_DM),
					'name' => __('电子邮件', WB_OCW_DM),
					'placeholder' => __('邮箱地址', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'url',
							'label' => __('邮箱地址', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => true
				),
				'msg' => array(
					'id' => 'msg',
					'title' => __('留言', WB_OCW_DM),
					'name' => __('在线留言', WB_OCW_DM),
					'multiple' => false,
					'form_fields' => array(
						'name' => __('昵称', WB_OCW_DM),
						'content' => __('内容', WB_OCW_DM),
					),
					'proc' => array(
						_x('PHP', '邮局设置', WB_OCW_DM),
						_x('QQ邮箱', '邮局设置', WB_OCW_DM),
						_x('163邮箱', '邮局设置', WB_OCW_DM),
						_x('其他邮箱', '邮局设置', WB_OCW_DM)
					),
					'notify_type' => array(__('邮箱通知', WB_OCW_DM), __('短信通知', WB_OCW_DM)),
					'base' => true
				),
				'order' => array(
					'id' => 'order',
					'title' => __('我的订单', WB_OCW_DM),
					'name' => __('我的订单', WB_OCW_DM),
					'multiple' => false,
					'fields' => [],
					'base' => true
				),
				'custom' => array(
					'id' => '',
					'title' => __('自定义', WB_OCW_DM),
					'name' => '',
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('图片(如二维码图片)', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'icon',
							'label' => __('图标', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('链接/账号', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'wx' => array(
					'id' => 'wx',
					'title' => __('微信', WB_OCW_DM),
					'name' => __('关注我们', WB_OCW_DM),
					'placeholder' => __('微信号', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('微信号', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'nickname',
							'label' => __('联系人', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input',
							'remark' => array(
								'content' => __('* 如职称，职位等', WB_OCW_DM),
								'style' => 'block'
							)
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'qq' => array(
					'id' => 'qq',
					'title' => __('QQ客服', WB_OCW_DM),
					'name' => __('QQ客服', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'url',
							'label' => __('QQ号', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('QQ号', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'whatsapp' => array(
					'id' => 'whatsapp',
					'title' => __('WhatsApp', WB_OCW_DM),
					'name' => __('WhatsApp', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('Phone Number', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('全格式国际电话号码（需包含国家代码）', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'telegram' => array(
					'id' => 'telegram',
					'title' => __('Telegram', WB_OCW_DM),
					'name' => __('Telegram', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('Username', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('必须以@开头（如@mycompany）', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'messenger' => array(
					'id' => 'messenger',
					'title' => __('Messenger', WB_OCW_DM),
					'name' => __('Messenger', WB_OCW_DM),
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('Page User Name', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('Messenger用户名', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'line' => array(
					'id' => 'line',
					'title' => 'Line',
					'name' => 'Conatct Us',
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('Line ID', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('企业LINE官方账号ID（数字格式）', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'viber' => array(
					'id' => 'viber',
					'title' => 'Viber',
					'name' => 'Conatct Us',
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('Account', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('Viber账号名', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
				'signal' => array(
					'id' => 'signal',
					'title' => 'Signal',
					'name' => 'Conatct Us',
					'multiple' => true,
					'fields' => array(
						array(
							'slug' => 'img',
							'label' => __('二维码图片', WB_OCW_DM),
							'type' => 'img'
						),
						array(
							'slug' => 'url',
							'label' => __('Number', WB_OCW_DM),
							'type' => 'input',
							'attrs' => array(
								'placeholder' => __('完整国际格式号码（包含国家代码）, 如 +*************', WB_OCW_DM)
							)
						),
						array(
							'slug' => 'label',
							'label' => __('名称', WB_OCW_DM),
							'type' => 'input'
						),
						array(
							'slug' => 'item_divider',
							'label' => '',
							'type' => 'divider'
						)
					),
					'base' => false
				),
			),
			'position' => array(
				'lt' => array(
					'code' => 'lt',
					'label' => __('左上', WB_OCW_DM)
				),
				'lc' => array(
					'code' => 'lc',
					'label' => __('左中', WB_OCW_DM)
				),
				'lb' => array(
					'code' => 'lb',
					'label' => __('左下', WB_OCW_DM)
				),
				'rt' => array(
					'code' => 'rt',
					'label' => __('右上', WB_OCW_DM)
				),
				'rc' => array(
					'code' => 'lc',
					'label' => __('右中', WB_OCW_DM)
				),
				'rb' => array(
					'code' => 'lb',
					'label' => __('右下', WB_OCW_DM)
				),
			),
			//显示页面
			'active_page' => array(
				'0' => __('所有页面', WB_OCW_DM),
				'1' => __('指定页面', WB_OCW_DM),
				'2' => __('例外页面', WB_OCW_DM)
			),
			// 暗黑模式
			'dark_switch' => '0',
			// 圆角模式
			'fillet_select' => array(
				'0' => __('默认', WB_OCW_DM),
				'1' => __('圆角', WB_OCW_DM)
			),
			// 大尺寸模式
			'size_select' => array(
				'0' => __('默认', WB_OCW_DM),
				'1' => __('大尺寸', WB_OCW_DM)
			),
			// 名称显示
			'name_switch' => '0',
			// 作为自定义方式
			'custom_types' => array(
				'qq' => array(
					'label' => 'QQ',
					'value' => 'qq',
					'desktop' => 'http://wpa.qq.com/msgrd?v=3&uin=%s&site=qq&menu=yes',
					'mobile' => 'mqqwpa://im/chat?chat_type=wpa&uin=%s&version=1&src_type=web&web_src=%s'
				),
				'wx' => array(
					'label' => __('微信', WB_OCW_DM),
					'value' => 'wx',
					'desktop' => 'weixin://dl/chat?%s',
					'mobile' => 'weixin://dl/chat?%s'
				),
				'line' => array(
					'label' => 'LINE',
					'value' => 'line',
					'desktop' => 'https://line.me/ti/p/%s',
					'mobile' => 'https://line.me/ti/p/%s'
				),
				'whatsapp' => array(
					'label' => 'Whats App',
					'value' => 'whatsapp',
					'desktop' => 'https://api.whatsapp.com/send?phone=%s',
					'mobile' => 'https://wa.me/%s'
				),
				'telegram' => array(
					'label' => 'Telegram',
					'value' => 'telegram',
					'desktop' => 'https://t.me/%s',
					'mobile' => 'https://t.me/%s'
				),
				'messenger' => array(
					'label' => 'Messenger',
					'value' => 'messenger',
					'desktop' => 'https://m.me/%s',
					'mobile' => 'https://m.me/%s'
				),
				'viber' => array(
					'label' => 'Viber',
					'value' => 'viber',
					'desktop' => 'https://viber.com/%s',
					'mobile' => 'https://viber.com/%s'
				),
				'signal' => array(
					'label' => 'Signal',
					'value' => 'signal',
					'desktop' => 'https://signal.me/#p/+%s',
					'mobile' => 'https://signal.me/#p/+%s'
				),
				'custom' => array(
					'label' => __('其他', WB_OCW_DM),
					'value' => 'custom'
				),
			),
			// 自定义方式数据结构
			'custom_item_data' => array(
				'name' => '',
				'slug' => '',
				'data' => array(
					array(
						'img' => '',
						'icon' => '',
						'url' => '',
						'nickname' => '',
						'label' => ''
					)
				)
			),
			'contact_ways' => array(
				'mobile' => array(
					'label' => _x('手机', 'label', WB_OCW_DM),
					'value' => 'mobile'
				),
				'email' => array(
					'label' => _x('邮箱', 'label', WB_OCW_DM),
					'value' => 'email'
				),
				'wx' => array(
					'label' => _x('微信', 'label', WB_OCW_DM),
					'value' => 'wx'
				),
				'qq' => array(
					'label' => _x('QQ', 'label', WB_OCW_DM),
					'value' => 'qq'
				),
				'line' => array(
					'label' => _x('LINE', 'label', WB_OCW_DM),
					'value' => 'line'
				),
				'whatsapp' => array(
					'label' => _x('Whats App', 'label', WB_OCW_DM),
					'value' => 'whatsapp'
				),
				'telegram' => array(
					'label' => _x('Telegram', 'label', WB_OCW_DM),
					'value' => 'telegram'
				),
				'messenger' => array(
					'label' => _x('Messenger', 'label', WB_OCW_DM),
					'value' => 'messenger'
				),
				'viber' => array(
					'label' => _x('Viber', 'label', WB_OCW_DM),
					'value' => 'viber'
				),
				'signal' => array(
					'label' => _x('Signal', 'label', WB_OCW_DM),
					'value' => 'signal'
				),
			)
		);

		if ($key) {
			return $cnf_data[$key];
		} else {
			return $cnf_data;
		}
	}


	public static function init()
	{
		register_activation_hook(ONLINE_CONTACT_WIDGET_FILE, [__CLASS__, 'plugin_activate']);
		register_deactivation_hook(ONLINE_CONTACT_WIDGET_FILE, [__CLASS__, 'plugin_deactivate']);

		if (is_admin()) {
			//插件设置连接
			add_filter('plugin_action_links', array(__CLASS__, 'action_links'), 10, 2);
			add_action('admin_menu', array(__CLASS__, 'admin_menu_handler'));

			add_action('admin_enqueue_scripts', array(__CLASS__, 'admin_enqueue_scripts'), 1);

			add_filter('plugin_row_meta', array(__CLASS__, 'plugin_row_meta'), 10, 2);

			add_action('wp_ajax_wb_ocw_options', array(__CLASS__, 'wb_options_ajax_handler'));
		}
	}

	/**
	 * 插件设置
	 */
	public static function admin_menu_handler()
	{
		global $submenu;
		add_menu_page(
			__('多合一在线客服插件', WB_OCW_DM),
			__('多合一客服', WB_OCW_DM),
			'administrator',
			self::$name,
			array(__CLASS__, 'render_views'),
			plugin_dir_url(ONLINE_CONTACT_WIDGET_FILE) . 'assets/icon_for_menu.svg'
		);

		add_submenu_page(
			self::$name,
			__('多合一在线客服插件', WB_OCW_DM),
			__('插件设置', WB_OCW_DM),
			'administrator',
			self::$name . '#/setting-base',
			array(__CLASS__, 'render_views')
		);

		unset($submenu[self::$name][0]);
	}

	public static function render_views()
	{
		if (!current_user_can('administrator')) {
			wp_die(__('You do not have sufficient permissions to access this page.'));
		}

		echo '<div id="app"></div>';
	}

	// 设置连接方法
	public static function action_links($links, $file)
	{

		if ($file != plugin_basename(ONLINE_CONTACT_WIDGET_FILE))
			return $links;

		if (!get_option('wb_ocw_ver', 0)) {
			$a_link = '<a href="https://www.wbolt.com/plugins/wb-contact-widget" target="_blank"><span style="color: #FCB214;">' . __('升至Pro版', WB_OCW_DM) . '</span></a>';
			array_unshift($links, $a_link);
		}

		$settings_link = '<a href="' . menu_page_url(self::$name, false) . '">' . __('设置', WB_OCW_DM) . '</a>';

		array_unshift($links, $settings_link);

		return $links;
	}

	public static function admin_enqueue_scripts($hook)
	{
		if (!preg_match('#' . self::$name . '#', $hook)) return;

		$prompt_items = [];
		if (file_exists(__DIR__ . '/_prompt.php')) {
			include __DIR__ . '/_prompt.php';
		}

		$wb_ocw_ajax_nonce = wp_create_nonce('wb_ocw_admin_ajax');
		$wbm_api_ajax_nonce = wp_create_nonce('wbm_api_ajax');
		$wb_cnf = array(
			'wbp_security' => $wb_ocw_ajax_nonce,
			'_wbm_api_ajax_nonce' => $wbm_api_ajax_nonce,
			'base_url' => admin_url(),
			'home_url' => home_url(),
			'ajax_url' => admin_url('admin-ajax.php'),
			'dir_url' => ONLINE_CONTACT_WIDGET_URL,
			'pd_code' => ONLINE_CONTACT_WIDGET_CODE,
			'wb_vue_path' => ONLINE_CONTACT_WIDGET_URL . '/tpl/',
			'doc_url' => "https://www.wbolt.com/ocw-plugin-documentation.html",
			'pd_title' => __('多合一在线客服插件', WB_OCW_DM),
			'login_url' => wp_login_url(),
			'pd_version' => ONLINE_CONTACT_WIDGET_VERSION,
			'is_pro' => intval(get_option('wb_ocw_ver', 0)),
			'use_theme_mail' => false,
			'action' => array(
				'act' => 'wb_ocw_options',
				'fetch' => 'get_setting',
				'push' => 'set_setting'
			),
			'subject_type' => OCW_Admin::opt('items_data.msg.subject_type'),
			'wbm_url' => home_url('?wbp=member&slug=ocw'),
			'locale' => get_locale(),
			'actpanel_visible' => in_array(get_locale(), ['zh_CN', 'zh_TW'], true),
			'prompt' => $prompt_items,

		);
		if (OCW_Mail::use_theme_mail()) {
			$wb_cnf['use_theme_mail'] = true;
		}
		wp_register_script('wbs-inline-js', false, null, false);
		wp_enqueue_script('wbs-inline-js');
		wp_add_inline_script(
			'wbs-inline-js',
			' var wbp_js_cnf=' . wp_json_encode($wb_cnf, JSON_UNESCAPED_UNICODE) . ';',
			'before'
		);

		echo WB_Vite::vite('src/main.js', ONLINE_CONTACT_WIDGET_PATH . '/assets/wbp/', ONLINE_CONTACT_WIDGET_URL . '/assets/wbp/');

		wp_enqueue_media();

		self::update_table();
	}

	public static function update_table()
	{
		// global $wpdb;

		//error_log("update db \n",3,__DIR__.'/log.txt');
		$ver = get_option('ocw_db_ver', '');
		if (!$ver) {
			$ver = 1;
		}
		$ver = intval($ver);
		do {
			if ($ver >= self::$db_ver) {
				break;
			}

			if ($ver < 2) {
				$db = self::db();
				$t = $db->prefix . 'ocw_contact';
				$sql = $db->get_var('SHOW CREATE TABLE `' . $t . '`', 1);
				if (!preg_match('#`wx`#is', $sql)) {
					$db->query("ALTER TABLE $t ADD `wx` varchar(64) DEFAULT NULL AFTER `qq` ");
				}
				$ver = 2;
				update_option('ocw_db_ver', 2, false);
				//error_log("update db v1 \n",3,__DIR__.'/log.txt');
			}
		} while (0);
	}

	public static function plugin_row_meta($links, $file)
	{

		$base = plugin_basename(ONLINE_CONTACT_WIDGET_FILE);
		if ($file == $base) {
			$links[] = '<a href="https://www.wbolt.com/plugins/ocw" target="_blank">' . __('插件主页', WB_OCW_DM) . '</a>';
			$links[] = '<a href="https://www.wbolt.com/ocw-plugin-documentation.html" target="_blank">' . __('说明文档', WB_OCW_DM) . '</a>';
			$links[] = '<a href="https://www.wbolt.com/plugins/ocw#J_commentsSection" target="_blank">' . __('反馈', WB_OCW_DM) . '</a>';
		}
		return $links;
	}

	public static function wb_options_ajax_handler()
	{

		// $op = isset($_REQUEST['op']) ? sanitize_text_field($_REQUEST['op']) : '';
		$op = self::param('op');
		if (!$op) {
			$op = self::param('op', '', 'g');
		}
		if (!$op) {
			return;
		}
		$allow = [
			'chk_ver',
			'promote',
			'verify',
			'options',
			'get_setting',
			'set_setting',
			'get_item',
			'new_item',
			'set_item',
			'del_item',
			'test_sms',
			'mail_test',
			'get_localize'
		];
		if (!in_array($op, $allow, true)) {
			return;
		}
		if (!current_user_can('manage_options')) {
			return;
		}
		$nonce = self::param('_ajax_nonce');
		if (!$nonce) {
			$nonce = self::param('_ajax_nonce', '', 'g');
		}
		if (!wp_verify_nonce($nonce, 'wb_ocw_admin_ajax')) {
			return;
		}

		/*$ajax_nonce = isset($_POST['_ajax_nonce']) ? sanitize_text_field($_POST['_ajax_nonce']) : '';
				if (!wp_verify_nonce($ajax_nonce, 'wb_ocw_admin_ajax')) {

					echo wp_json_encode(array('code' => 1, 'data' => __('Illegal operation.', 'online-contact-widget')));
					exit(0);
				}
				if (!current_user_can('manage_options')) {
					echo wp_json_encode(array('code' => 1, 'data' => __('No permission.', 'online-contact-widget')));
					exit(0);
				}*/
		switch ($op) {
			case 'chk_ver':
				$http = wp_remote_get('https://www.wbolt.com/wb-api/v1/themes/checkver?code=' . ONLINE_CONTACT_WIDGET_CODE . '&ver=' . ONLINE_CONTACT_WIDGET_VERSION . '&chk=1', array('sslverify' => false, 'headers' => array('referer' => home_url()),));;
				if (wp_remote_retrieve_response_code($http) == 200) {
					echo esc_html(wp_remote_retrieve_body($http));
				}
				exit();
				break;

			case 'promote':
				$ret = ['code' => 0, 'desc' => 'success', 'data' => ''];
				$data = [];
				$expired = 0;
				$update_cache = false;
				do {
					$option = get_option('wb_ocw_promote', null);
					do {
						if (!$option || !is_array($option)) {
							break;
						}

						if (!isset($option['expired']) || empty($option['expired'])) {
							break;
						}

						$expired = intval($option['expired']);
						if ($expired < current_time('U')) {
							$expired = 0;
							break;
						}

						if (!isset($option['data']) || empty($option['data'])) {
							break;
						}

						$data = $option['data'];
					} while (0);

					if ($data) {
						$ret['data'] = $data;
						break;
					}
					if ($expired) {
						break;
					}

					$update_cache = true;
					$param = ['c' => 'wcw', 'h' => $_SERVER['HTTP_HOST']];
					$http = wp_remote_post('https://www.wbolt.com/wb-api/v1/promote', array('sslverify' => false, 'body' => $param, 'headers' => array('referer' => home_url()),));

					if (is_wp_error($http)) {
						$ret['error'] = $http->get_error_message();
						break;
					}
					if (wp_remote_retrieve_response_code($http) !== 200) {
						$ret['error-code'] = '201';
						break;
					}
					$body = trim(wp_remote_retrieve_body($http));
					if (!$body) {
						$ret['empty'] = 1;
						break;
					}
					$data = json_decode($body, true);
					if (!$data) {
						$ret['json-error'] = 1;
						$ret['body'] = $body;
						break;
					}
					//data = [title=>'',image=>'','expired'=>'2021-05-12','url=>'']
					$ret['data'] = $data;
					if (isset($data['expired']) && $data['expired'] && preg_match('#^\d{4}-\d{2}-\d{2}$#', $data['expired'])) {
						$expired = strtotime($data['expired'] . ' 23:50:00');
					}
				} while (0);
				if ($update_cache) {
					if (!$expired) {
						$expired = current_time('U') + 21600;
					}
					update_option('wb_ocw_promote', ['data' => $ret['data'], 'expired' => $expired], false);
				}

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			case 'verify':

				$key = sanitize_text_field(self::param('key'));
				$host = sanitize_text_field(self::param('host'));

				$param = array(
					'code' => $key,
					'host' => $host,
					'ver' => 'ocw',
				);
				$err = '';
				do {
					if (empty($param['code']) || empty($param['host'])) {
						$err = 'empty param';
						break;
					}
					$http = wp_remote_post('https://www.wbolt.com/wb-api/v1/verify', array('sslverify' => false, 'body' => $param, 'headers' => array('referer' => home_url()),));
					if (is_wp_error($http)) {
						$err = __('Verification failed, please try again later', 'online-contact-widget')
							. '[error code001 ' . $http->get_error_message() . '])';
						break;
					}

					if ($http['response']['code'] != 200) {
						$err = __('Verification failed, please try again later', 'online-contact-widget')
							. '[error code001 ' . $http['response']['code'] . '])';
						break;
					}

					$body = $http['body'];


					$data = json_decode($body, true);
					if (!$data || $data['code']) {
						$err_code = $data['data'] ? $data['data'] : '';
						switch ($err_code) {
							case 100:
							case 101:
							case 102:
							case 103:
								$err = __('Configuration error, contact for', 'online-contact-widget')
									. '<a href="https://www.wbolt.com/?wb=member#/contact" target="_blank">'
									. __('technical support', 'online-contact-widget') . '</a>('
									. __('error code', 'online-contact-widget') . $err_code . ')';
								break;
							case 200:
								$err = __('Invalid key, please enter a correct key. (error code 200)', 'online-contact-widget');
								break;
							case 201:
								$err = __('Key usage out of limit. (error code 201)', 'online-contact-widget');
								break;
							case 202:
							case 203:
							case 204:
								$err = __('Verification server exception, contact for', 'online-contact-widget')
									. '<a href="https://www.wbolt.com/?wb=member#/contact" target="_blank">'
									. __('technical support', 'online-contact-widget') . '</a>('
									. __('error code', 'online-contact-widget') . $err_code . ')';
								break;
							default:
								$err = __('Unexpected error, contact for', 'online-contact-widget')
									. '<a href="https://www.wbolt.com/?wb=member#/contact" target="_blank">'
									. __('technical support', 'online-contact-widget') . '</a>('
									. __('error code', 'online-contact-widget') . $err_code . ')';
						}
						break;
					}
					if (!$data['data']) {
						$err = __('Verification failed, please try again later. (error code 004)', 'online-contact-widget');
						break;
					}
					update_option('wb_ocw_ver', $data['v'], false);
					update_option('wb_ocw_cnf_' . $data['v'], $data['data'], false);

					echo wp_json_encode(array('code' => 0, 'data' => 'success'));
					exit(0);
				} while (false);
				echo wp_json_encode(array('code' => 1, 'data' => $err));
				exit(0);
				break;

			case 'options':
				/*if (!current_user_can('manage_options') || !wp_verify_nonce($_GET['_ajax_nonce'], 'wb_ocw_admin_ajax')) {
					echo wp_json_encode(array('o' => '', 'err' => 'no auth'));
					exit(0);
				}*/

				$ver = get_option('wb_ocw_ver', 0);
				$cnf = '';
				if ($ver) {
					$cnf = get_option('wb_ocw_cnf_' . $ver, '');
				}
				$list = array('o' => $cnf);
				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($list);
				exit();
				break;

			case 'get_setting':
				$key = sanitize_text_field(self::param('key'));

				$ret = array('code' => 0, 'desc' => 'success');
				$ret['data'] = self::get_opt_data($key);
				$ret['data']['cnf'] = self::cnf_fields();
				if (!empty($ret['data']['opt'])) {
					$ret['data']['opt'] = apply_filters('get_wbm_cnf', $ret['data']['opt']);
				}
				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			case 'set_setting':
				do {
					$opt = self::param('opt');
					if (empty($opt)) {
						break;
					}
					$key = sanitize_text_field(self::param('tab'));
					$key2 = implode('', ['re', 'set']);
					if ($key2 === $key) {
						$w_key = implode('_', ['wb', 'ocw', '']);
						$u_uid = get_option($w_key . 'ver', 0);
						if ($u_uid) {
							update_option($w_key . 'ver', 0);
							update_option($w_key . 'cnf_' . $u_uid, '');
						}
						break;
					}
					$opt_data = $opt;
					$opt_data = apply_filters('set_wbm_cnf', $opt_data);
					self::wb_set_setting($opt_data);
				} while (0);


				$ret = array('code' => 0, 'desc' => 'success');

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			//读取单个组件设置
			case 'get_item':
				$ret = array('code' => 1, 'data' => [], 'desc' => 'success');

				if (self::param('id')) {
					$id = trim(self::param('id'));
					$opt_items_data = self::opt('items_data');
					$opt_data = !empty($opt_items_data[$id]) ? $opt_items_data[$id] : ['name' => '', 'data' => []];
					$contact_items_fields = self::cnf_fields('contact_items');
					$cnf_data = $contact_items_fields[$id] ?? $contact_items_fields['custom'];

					$ret['code'] = 0;
					$ret['data'] = [
						'opt' => $opt_data,
						'cnf' => $cnf_data,
						'custom_types' => self::cnf_fields('custom_types'),
						'contact_types' => self::cnf_fields('contact_ways')
					];
				}

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			// 新增单个组件设置
			case 'new_item':
				$ret = array('code' => 1, 'data' => [], 'desc' => 'success');
				$opt_items_data = self::opt('items_data');

				if ($type = self::param('type')) {
					$opt_data = ['name' => '', 'data' => []];
					$contact_items_fields = self::cnf_fields('contact_items');
					$cnf_data = $contact_items_fields[$type] ?? $contact_items_fields['custom'];
					$items_orders = self::opt('items_orders');
					$custom_types = [];

					$custom_types_fields = self::cnf_fields('custom_types');
					foreach ($custom_types_fields as $key => $item) {
						$item['disabled'] = in_array($item['value'], $items_orders) ? true : false;
						$custom_types[$key] = $item;
					}

					$ret['code'] = 0;
					$ret['data'] = [
						'opt' => $opt_data,
						'cnf' => $cnf_data,
						'custom_types' => $custom_types,
					];
				}

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			// 保存单个组件设置
			case 'set_item':
				$ret = array('code' => 1, 'desc' => 'success');
				$opt = self::opt();
				$id = self::param('id');
				$val = self::param('opt');

				// 新增组件处理
				if (!in_array($id, $opt['items_orders'])) {
					array_push($opt['items_orders'], $id);
					array_push($opt['items'], $id);
				}

				if (!empty($id) && !empty($val)) {
					$opt['items_data'][$id] = $val;
					//					self::wb_set_setting($opt);
					$ret['data'] = $opt;
					$ret['save'] = self::wb_set_setting($opt);
					$ret['code'] = 0;
				}

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			case 'del_item':
				$ret = array('code' => 1, 'desc' => 'success');
				$opt = self::opt();
				$id = self::param('id');
				$val = self::param('opt');

				if ($id) {
					unset($opt['items_data'][$id]);
					if (($key = array_search($id, $opt['items'])) !== false) {
						unset($opt['items'][$key]);
					}
					if (($key = array_search($id, $opt['items_orders'])) !== false) {
						unset($opt['items_orders'][$key]);
					}

					$ret['data'] = $opt;
					$ret['save'] = self::wb_set_setting($opt);
					$ret['code'] = 0;
				}

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				exit();
				break;

			case 'test_sms':
				add_filter('ocw_sms_send_result', function ($result) {
					self::$sms_result = $result;
				});
				$ret = array('code' => 0, 'desc' => 'success');
				$mobile = sanitize_text_field(self::param('mobile'));;
				$cnf = self::param('opt', []);
				if ($mobile && $cnf) {
					$msg = __('测试短信', WB_OCW_DM);
					do_action('ocw_send_sms_test', $mobile, $cnf, $msg);
					if (isset(self::$sms_result)) {
						$ret = self::$sms_result;
					}
				}

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				break;

			case 'mail_test':
				$ret = OCW_Mail::do_mail_test();

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				break;

			case 'get_localize':
				$ret = [
					'code' => 0,
					'desc' => 'success'
				];

				$ret['data'] = self::localize_ajax_handle();

				header('content-type:text/json;charset=utf-8');
				echo wp_json_encode($ret);
				break;
		}

		exit();
	}

	public static function get_opt_data($key = '')
	{

		$ret = [];

		switch ($key) {
			case 'base':
				// $ret['opt']['login_url'] = self::opt('login_url');
				// $ret['opt']['need_login'] = self::opt('need_login');
				$ret['opt']['position'] = self::opt('position');
				$ret['opt']['dark_mode_class'] = self::opt('dark_mode_class');
				$ret['opt']['other_tool_name'] = self::opt('other_tool_name');
				break;

			case 'items':
				$ret['opt']['items'] = self::opt('items');
				$ret['opt']['items_data'] = self::opt('items_data');
				$ret['opt']['items_orders'] = self::opt('items_orders');
				break;

			case 'pro':
				$ret['opt']['active_page'] = self::opt('active_page');
				$ret['opt']['active_device'] = self::opt('active_device');
				$ret['opt']['appoint_urls'] = self::opt('appoint_urls');
				$ret['opt']['exception_urls'] = self::opt('exception_urls');
				$ret['opt']['custom_style'] = self::opt('custom_style');
				$ret['opt']['color_head'] = self::opt('color_head');
				$ret['opt']['color_icon'] = self::opt('color_icon');
				$ret['opt']['fold_label'] = self::opt('fold_label');
				break;

			default:
				$ret['opt'] = self::opt($key);

				break;
		}

		return $ret;
	}

	/**
	 * 扩展属性
	 * @param $cnf array 当前属性
	 * @param $conf array 扩展属性
	 * @param array $skip 跳过
	 */
	public static function extend_conf(&$cnf, $conf, $skip = [])
	{
		if (is_array($conf)) foreach ($conf as  $k => $v) {
			if (!isset($cnf[$k])) {
				$cnf[$k] = $v;
				continue;
			}
			if (!empty($skip) && in_array($k, $skip)) {
				continue;
			}
			if (is_array($v)) {
				if (!is_array($cnf[$k])) {
					$cnf[$k] = [];
				}
				self::extend_conf($cnf[$k], $v, $skip);
			}
		}
	}

	/**
	 * 合并属性
	 * @param $old array 原属性
	 * @param $new array 新属性
	 */
	public static function combine_conf(&$old, $new)
	{
		if (is_array($new)) foreach ($new as  $k => $v) {
			if (!isset($old[$k])) {
				$old[$k] = $v;
			} else if (is_array($v)) {
				self::combine_conf($old[$k], $v);
			} else {
				$old[$k] = $v;
			}
		}
	}

	/**
	 * 默认设置值
	 * @return array
	 */
	public static function def_opt()
	{
		$contact_items_fields = self::cnf_fields('contact_items');
		$def_items = array_filter($contact_items_fields, function ($item) {
			return isset($item['base']) && $item['base'] === true;
		});
		$def_items_key = array_keys($def_items);
		$msg_def_subject_types = array(
			__('业务咨询', WB_OCW_DM),
			__('市场合作', WB_OCW_DM),
			__('其他事宜', WB_OCW_DM)
		);
		$cnf = array(
			'items' => array('backtop', 'msg'), //选择中的选项
			'items_data' => array(
				'backtop' => '',
				'tel' => [],
				'email' => [],
				'msg' => array(
					'name' => __('在线留言', WB_OCW_DM),
					'form_contact_ways' => ['mobile', 'email'], //联系方式
					'subject_type' => $msg_def_subject_types,
					'auto_reply_msg' => __('您的工单我们已经收到，我们将会尽快跟您联系！', WB_OCW_DM),
					'notify_type' => '0',
					'captcha' => [
						'type' => 'none',
						'google' => [
							'public' => '',
							'private' => '',
							'score' => 0.5,
						],
					],
					'notice' => [],
					'mail' => [
						'mailer' => '0',
						'to' => '',
						'from' => '',
						'name' => '',
						'proc' => array(
							array(
								'id' => 'php',
								'name' => _x('PHP', '邮局设置', WB_OCW_DM),
							),
							array(
								'id' => 'qq',
								'name' => _x('QQ邮箱', '邮局设置', WB_OCW_DM),
								'host' => 'smtp.qq.com',
								'secure' => '',
								'port' => '25',
								'user' => '',
								'password' => '',
							),
							array(
								'id' => '163',
								'name' => _x('163邮箱', '邮局设置', WB_OCW_DM),
								'host' => 'smtp.163.com',
								'secure' => '',
								'port' => '25',
								'user' => '',
								'password' => '',
							),
							array(
								'id' => 'other',
								'name' => _x('其他邮箱', '邮局设置', WB_OCW_DM),
								'host' => '',
								'secure' => '',
								'port' => '',
								'user' => '',
								'password' => '',
							),
						),
					],
					'sms' => [
						'to' => '',
						'vendor' => 'upyun',
						'upyun' => [
							'id' => '',
							'secret' => '',
							'tpl' => '',
							'sign' => '',
						],
						'aliyun' => [
							'id' => '',
							'secret' => '',
							'tpl' => '',
							'sign' => '',
						],
						'huawei' => [
							'api' => '',
							'id' => '',
							'secret' => '',
							'tpl' => '',
							'sign' => '',
							'channel' => '',
						],
					],
					'need_login' => '0',
					'login_url' => '',
				)
			),
			'items_orders' => $def_items_key,
			'contact_color' => '',
			'position' => 'rb', //右下
			'position_offset_x' => 0,
			'position_offset_y' => 0,
			'is_fold' => '0', //是否收起
			'fold_icon' => '1',
			'fold_label' => _x('在线客服', '浮标label', WB_OCW_DM),
			'avatar_url' => '',
			'contact_name' => '',
			'contact_msg' => __('我们将24小时内回复。', WB_OCW_DM),
			'open_msg' => __('您好，有任何疑问请与我们联系！', WB_OCW_DM),
			'mode_tips' => __('选择聊天工具：', WB_OCW_DM),
			'active_device' => array('0', '1'),
			'active_page' => '0',
			'appoint_urls' => '',
			'exception_urls' => '',
			'custom_style' => '',
			'color_head' => '',
			'color_icon' => '',
			'panel_hd_fcolor' => '', // 顶部文字颜色
			'custom_theme_color' => '',
			'dark_switch' => '0', // 是否黑暗模式 仅展开显示
			'fillet_select' => '0', // 展示默认、圆角
			'size_select' => '0', // 尺寸展示
			'name_switch' => '0', // 是否显示组件名称
			'other_tool_name' => '.tool-bar', // 隐藏主题原有tool class name
			'unfold_size' => '38',
			'unfold_radius' => '0',
			'panel_width' => '320',
			'buoy_icon_size' => '24',
			'buoy_icon_custom' => '',
			'buoy_animation' => '1',
			'base_font_size' => '',
			'buoy_animation_interval' => 5,
			'dark_mode_class' => '.dark-mode',
			'wb_member' => 0,
			'cache_switch' => 0
		);
		//error_log(print_r($cnf,true),3,__DIR__.'/log.txt');
		return $cnf;
	}


	/**
	 * 读取设置值
	 * @param string $name
	 * @param bool $default
	 *
	 * @return bool|mixed|null
	 */
	public static function opt($name = '', $default = false)
	{
		static $options = null;

		if (null == $options) {
			$options = get_option(self::$option_name, []);

			/*$is_new = false;
			if (!$options) {
				$is_new = true;
			}*/

			//, ['subject_type','items_orders','active_device']
			self::extend_conf($options, self::def_opt(), ['subject_type']);

			/*if ($is_new) {
				$new_default = [];
				foreach ($new_default as $k => $v) {
					if (is_array($v)) {
						$options[$k] = array_merge($options[$k], $v);
					} else {
						$options[$k] = $v;
					}
				}
			}*/
			if (!class_exists('WP_VK') && in_array('order', $options['items_orders'])) {
				$k = array_search('order', $options['items_orders']);
				unset($options['items_orders'][$k]);
			}
		}

		$return = null;
		do {

			if (!$name) {
				$return = $options;
				break;
			}

			$ret = $options;
			$ak = explode('.', $name);

			foreach ($ak as $sk) {
				if (isset($ret[$sk])) {
					$ret = $ret[$sk];
				} else {
					$ret = $default;
					break;
				}
			}

			$return = $ret;
		} while (0);

		return apply_filters('wb_ocw_get_conf', $return, $name, $default);
	}

	/**
	 * 保存设置值
	 * @param $data
	 *
	 * @return bool
	 */
	public static function wb_set_setting($data)
	{

		$opt = self::opt();

		// 去重处理items_orders并重置数字索引
		$items_orders = array_values(array_unique($data['items_orders']));
		$items_orders = array_filter($items_orders);
		$data['items_orders'] = $items_orders;

		// 处理items数组并重置数字索引
		if (isset($data['items'])) {
			$data['items'] = array_values(array_filter($data['items']));
		}

		foreach ($data as $key => $value) {
			$opt[$key] = self::stripslashes_deep($value);
		}

		update_option(self::$option_name, $opt);
		WBP::cache_reset('wb_cache_ocw', 'wb_cache_ocw'); // 更新后清除缓存
		return true;
	}

	public static function stripslashes_deep($value)
	{
		if (empty($value)) {
			return $value;
		}

		if (is_array($value)) {

			foreach ($value as $k => $v) {
				$value[$k] = self::stripslashes_deep($v);
			}
		} else {
			$value = stripslashes($value);
		}
		return $value;
	}


	public static function plugin_deactivate() {}

	public static function plugin_activate()
	{

		// global $wpdb;
		$db = self::db();
		$prefix = $db->prefix;
		$charset_collate = $db->get_charset_collate();

		$sql = 'CREATE TABLE IF NOT EXISTS `' . $prefix . 'ocw_contact` (
				`id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
				`sn` varchar(16) NOT NULL,
				`name` varchar(100) DEFAULT NULL,
				`email` varchar(64) DEFAULT NULL,
				`qq` varchar(32) DEFAULT NULL,
				`wx` varchar(64) DEFAULT NULL,
				`mobile` varchar(32) DEFAULT NULL,
				`title` varchar(200) DEFAULT NULL,
				`create_date` datetime DEFAULT NULL,
				`update_time` datetime DEFAULT NULL,
				`status` tinyint(3) UNSIGNED NOT NULL DEFAULT \'1\',
				`uid` bigint(20) UNSIGNED DEFAULT \'0\',
				`type` varchar(32) DEFAULT \'\',
				`pid` int(10) UNSIGNED NOT NULL DEFAULT \'0\',
				`is_read` tinyint(1) UNSIGNED NOT NULL DEFAULT \'1\',
				`is_new` tinyint(1) UNSIGNED NOT NULL DEFAULT \'0\',
				PRIMARY KEY (`id`),
				KEY `pid` (`pid`),
				KEY `uid` (`uid`),
				KEY `type` (`type`),
				KEY `status` (`status`)
			) ' . $charset_collate;
		$db->query($sql);



		$sql = 'CREATE TABLE IF NOT EXISTS `' . $prefix . 'ocw_contact_content` (
				`cid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
				`pid` int(10) UNSIGNED NOT NULL DEFAULT \'0\',
				`uid` bigint(20) UNSIGNED NOT NULL DEFAULT \'0\',
				`pics` varchar(250) DEFAULT NULL,
				`create_date` datetime DEFAULT NULL,
				`ip` varchar(32) DEFAULT NULL,
				`content` mediumtext,
				PRIMARY KEY (`cid`),
				KEY `pid` (`pid`),
				KEY `uid` (`uid`)
			) ' . $charset_collate;
		$db->query($sql);

		update_option('ocw_db_ver', self::$db_ver, false);
	}

	/**
	 * 后台本地化语言包数据
	 *
	 * @return []
	 */
	public static function localize_ajax_handle()
	{
		$locale = get_locale();
		$cache_key = 'wb_cache_localize_' . $locale . '_' . WB_OCW_DM . '_' . ONLINE_CONTACT_WIDGET_VERSION;
		$cache_data = get_transient($cache_key);
		if ($cache_data) {
			return $cache_data;
		}

		$lang_data = [];
		if (file_exists(__DIR__ . '/_localize.php')) {
			include __DIR__ . '/_localize.php';
		}

		apply_filters('wb_ocw_locales_data', $lang_data);

		$format_data = [];
		foreach ($lang_data as $k => $v) {
			$format_data[WBP::set_localize_key($k)] = $v;
		}
		set_transient($cache_key, $format_data, DAY_IN_SECONDS);

		return $format_data;
	}
}
