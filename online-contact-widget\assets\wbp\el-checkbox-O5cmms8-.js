import{ch as e,ci as a,bs as l,cj as t,ck as n,cl as u,bA as o,bJ as r,bB as i,bC as s,bt as c,aa as d,a$ as v,g as b,aR as m,aS as f,aQ as h,i as p,h as g,b1 as k,aV as x,aU as y,F as C,W as L,D as V,ag as _,a as B,a9 as S,bj as w,bS as j,c6 as E,ac as F,aW as I,bk as z,_ as O,d as N,al as U,j as D,l as A,m as R,w as $,J as G,q as P,n as J,s as K,u as M,N as W,aK as q,bl as Q,cm as H,r as T,R as X,v as Y,t as Z,ap as ee,a8 as ae,c as le,e as te,p as ne,cn as ue,ad as oe,ar as re,y as ie}from"./wbs-Dtem2-xP.js";function se(a){var l=-1,t=null==a?0:a.length;for(this.__data__=new e;++l<t;)this.add(a[l])}function ce(e,a){for(var l=-1,t=null==e?0:e.length;++l<t;)if(a(e[l],l,e))return!0;return!1}se.prototype.add=se.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},se.prototype.has=function(e){return this.__data__.has(e)};function de(e,a,l,t,n,u){var o=1&l,r=e.length,i=a.length;if(r!=i&&!(o&&i>r))return!1;var s=u.get(e),c=u.get(a);if(s&&c)return s==a&&c==e;var d=-1,v=!0,b=2&l?new se:void 0;for(u.set(e,a),u.set(a,e);++d<r;){var m=e[d],f=a[d];if(t)var h=o?t(f,m,d,a,e,u):t(m,f,d,e,a,u);if(void 0!==h){if(h)continue;v=!1;break}if(b){if(!ce(a,(function(e,a){if(o=a,!b.has(o)&&(m===e||n(m,e,l,t,u)))return b.push(a);var o}))){v=!1;break}}else if(m!==f&&!n(m,f,l,t,u)){v=!1;break}}return u.delete(e),u.delete(a),v}function ve(e){var a=-1,l=Array(e.size);return e.forEach((function(e,t){l[++a]=[t,e]})),l}function be(e){var a=-1,l=Array(e.size);return e.forEach((function(e){l[++a]=e})),l}var me=a?a.prototype:void 0,fe=me?me.valueOf:void 0;var he=Object.prototype.hasOwnProperty;var pe="[object Arguments]",ge="[object Array]",ke="[object Object]",xe=Object.prototype.hasOwnProperty;function ye(e,a,c,d,v,b){var m=s(e),f=s(a),h=m?ge:u(e),p=f?ge:u(a),g=(h=h==pe?ke:h)==ke,k=(p=p==pe?ke:p)==ke,x=h==p;if(x&&o(e)){if(!o(a))return!1;m=!0,g=!1}if(x&&!g)return b||(b=new r),m||i(e)?de(e,a,c,d,v,b):function(e,a,n,u,o,r,i){switch(n){case"[object DataView]":if(e.byteLength!=a.byteLength||e.byteOffset!=a.byteOffset)return!1;e=e.buffer,a=a.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=a.byteLength||!r(new t(e),new t(a)));case"[object Boolean]":case"[object Date]":case"[object Number]":return l(+e,+a);case"[object Error]":return e.name==a.name&&e.message==a.message;case"[object RegExp]":case"[object String]":return e==a+"";case"[object Map]":var s=ve;case"[object Set]":var c=1&u;if(s||(s=be),e.size!=a.size&&!c)return!1;var d=i.get(e);if(d)return d==a;u|=2,i.set(e,a);var v=de(s(e),s(a),u,o,r,i);return i.delete(e),v;case"[object Symbol]":if(fe)return fe.call(e)==fe.call(a)}return!1}(e,a,h,c,d,v,b);if(!(1&c)){var y=g&&xe.call(e,"__wrapped__"),C=k&&xe.call(a,"__wrapped__");if(y||C){var L=y?e.value():e,V=C?a.value():a;return b||(b=new r),v(L,V,c,d,b)}}return!!x&&(b||(b=new r),function(e,a,l,t,u,o){var r=1&l,i=n(e),s=i.length;if(s!=n(a).length&&!r)return!1;for(var c=s;c--;){var d=i[c];if(!(r?d in a:he.call(a,d)))return!1}var v=o.get(e),b=o.get(a);if(v&&b)return v==a&&b==e;var m=!0;o.set(e,a),o.set(a,e);for(var f=r;++c<s;){var h=e[d=i[c]],p=a[d];if(t)var g=r?t(p,h,d,a,e,o):t(h,p,d,e,a,o);if(!(void 0===g?h===p||u(h,p,l,t,o):g)){m=!1;break}f||(f="constructor"==d)}if(m&&!f){var k=e.constructor,x=a.constructor;k==x||!("constructor"in e)||!("constructor"in a)||"function"==typeof k&&k instanceof k&&"function"==typeof x&&x instanceof x||(m=!1)}return o.delete(e),o.delete(a),m}(e,a,c,d,v,b))}function Ce(e,a,l,t,n){return e===a||(null==e||null==a||!c(e)&&!c(a)?e!=e&&a!=a:ye(e,a,l,t,Ce,n))}function Le(e,a){return Ce(e,a)}const Ve={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},value:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueValue:{type:[String,Number],default:void 0},falseValue:{type:[String,Number],default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},border:Boolean,size:f,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0},...m(["ariaControls"])},_e={[h]:e=>d(e)||v(e)||b(e),change:e=>d(e)||v(e)||b(e)},Be=Symbol("checkboxGroupContextKey"),Se=(e,{model:a,isLimitExceeded:l,hasOwnLabel:t,isDisabled:n,isLabeledByFormItem:u})=>{const o=p(Be,void 0),{formItem:r}=y(),{emit:i}=C();function s(a){var l,t,n,u;return[!0,e.trueValue,e.trueLabel].includes(a)?null==(t=null!=(l=e.trueValue)?l:e.trueLabel)||t:null!=(u=null!=(n=e.falseValue)?n:e.falseLabel)&&u}const c=g((()=>(null==o?void 0:o.validateEvent)||e.validateEvent));return L((()=>e.modelValue),(()=>{c.value&&(null==r||r.validate("change").catch((e=>_())))})),{handleChange:function(e){if(l.value)return;const a=e.target;i("change",s(a.checked),e)},onClickRoot:async function(o){if(!l.value&&!t.value&&!n.value&&u.value){o.composedPath().some((e=>"LABEL"===e.tagName))||(a.value=s([!1,e.falseValue,e.falseLabel].includes(a.value)),await V(),function(e,a){i("change",s(e),a)}(a.value,o))}}}},we=(e,a)=>{const{formItem:l}=y(),{model:t,isGroup:n,isLimitExceeded:u}=(e=>{const a=B(!1),{emit:l}=C(),t=p(Be,void 0),n=g((()=>!1===k(t))),u=B(!1),o=g({get(){var l,u;return n.value?null==(l=null==t?void 0:t.modelValue)?void 0:l.value:null!=(u=e.modelValue)?u:a.value},set(e){var r,i;n.value&&S(e)?(u.value=void 0!==(null==(r=null==t?void 0:t.max)?void 0:r.value)&&e.length>(null==t?void 0:t.max.value)&&e.length>o.value.length,!1===u.value&&(null==(i=null==t?void 0:t.changeEvent)||i.call(t,e))):(l(h,e),a.value=e)}});return{model:o,isGroup:n,isLimitExceeded:u}})(e),{isFocused:o,isChecked:r,checkboxButtonSize:i,checkboxSize:s,hasOwnLabel:c,actualValue:d}=((e,a,{model:l})=>{const t=p(Be,void 0),n=B(!1),u=g((()=>w(e.value)?e.label:e.value)),o=g((()=>{const a=l.value;return b(a)?a:S(a)?j(u.value)?a.map(E).some((e=>Le(e,u.value))):a.map(E).includes(u.value):null!=a?a===e.trueValue||a===e.trueLabel:!!a}));return{checkboxButtonSize:F(g((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value})),{prop:!0}),isChecked:o,isFocused:n,checkboxSize:F(g((()=>{var e;return null==(e=null==t?void 0:t.size)?void 0:e.value}))),hasOwnLabel:g((()=>!!a.default||!w(u.value))),actualValue:u}})(e,a,{model:t}),{isDisabled:v}=(({model:e,isChecked:a})=>{const l=p(Be,void 0),t=g((()=>{var t,n;const u=null==(t=null==l?void 0:l.max)?void 0:t.value,o=null==(n=null==l?void 0:l.min)?void 0:n.value;return!k(u)&&e.value.length>=u&&!a.value||!k(o)&&e.value.length<=o&&a.value}));return{isDisabled:x(g((()=>(null==l?void 0:l.disabled.value)||t.value))),isLimitDisabled:t}})({model:t,isChecked:r}),{inputId:m,isLabeledByFormItem:f}=I(e,{formItemContext:l,disableIdGeneration:c,disableIdManagement:n}),{handleChange:L,onClickRoot:V}=Se(e,{model:t,isLimitExceeded:u,hasOwnLabel:c,isDisabled:v,isLabeledByFormItem:f});var _,O;return e.checked&&(S(t.value)&&!t.value.includes(d.value)?t.value.push(d.value):t.value=null==(O=null!=(_=e.trueValue)?_:e.trueLabel)||O),z({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},g((()=>n.value&&w(e.value)))),z({from:"true-label",replacement:"true-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},g((()=>!!e.trueLabel))),z({from:"false-label",replacement:"false-value",version:"3.0.0",scope:"el-checkbox",ref:"https://element-plus.org/en-US/component/checkbox.html"},g((()=>!!e.falseLabel))),{inputId:m,isLabeledByFormItem:f,isChecked:r,isDisabled:v,isFocused:o,checkboxButtonSize:i,checkboxSize:s,hasOwnLabel:c,model:t,actualValue:d,handleChange:L,onClickRoot:V}},je=N({name:"ElCheckbox"});var Ee=O(N({...je,props:Ve,emits:_e,setup(e){const a=e,l=U(),{inputId:t,isLabeledByFormItem:n,isChecked:u,isDisabled:o,isFocused:r,checkboxSize:i,hasOwnLabel:s,model:c,actualValue:d,handleChange:v,onClickRoot:b}=we(a,l),m=D("checkbox"),f=g((()=>[m.b(),m.m(i.value),m.is("disabled",o.value),m.is("bordered",a.border),m.is("checked",u.value)])),h=g((()=>[m.e("input"),m.is("disabled",o.value),m.is("checked",u.value),m.is("indeterminate",a.indeterminate),m.is("focus",r.value)]));return(e,a)=>(R(),A(ee(!M(s)&&M(n)?"span":"label"),{class:K(M(f)),"aria-controls":e.indeterminate?e.ariaControls:null,onClick:M(b)},{default:$((()=>{var a,l,n,u;return[G("span",{class:K(M(h))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?W((R(),P("input",{key:0,id:M(t),"onUpdate:modelValue":e=>Q(c)?c.value=e:null,class:K(M(m).e("original")),type:"checkbox",indeterminate:e.indeterminate,name:e.name,tabindex:e.tabindex,disabled:M(o),"true-value":null==(l=null!=(a=e.trueValue)?a:e.trueLabel)||l,"false-value":null!=(u=null!=(n=e.falseValue)?n:e.falseLabel)&&u,onChange:M(v),onFocus:e=>r.value=!0,onBlur:e=>r.value=!1,onClick:q((()=>{}),["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[H,M(c)]]):W((R(),P("input",{key:1,id:M(t),"onUpdate:modelValue":e=>Q(c)?c.value=e:null,class:K(M(m).e("original")),type:"checkbox",indeterminate:e.indeterminate,disabled:M(o),value:M(d),name:e.name,tabindex:e.tabindex,onChange:M(v),onFocus:e=>r.value=!0,onBlur:e=>r.value=!1,onClick:q((()=>{}),["stop"])},null,42,["id","onUpdate:modelValue","indeterminate","disabled","value","name","tabindex","onChange","onFocus","onBlur","onClick"])),[[H,M(c)]]),G("span",{class:K(M(m).e("inner"))},null,2)],2),M(s)?(R(),P("span",{key:0,class:K(M(m).e("label"))},[T(e.$slots,"default"),e.$slots.default?J("v-if",!0):(R(),P(X,{key:0},[Y(Z(e.label),1)],64))],2)):J("v-if",!0)]})),_:3},8,["class","aria-controls","onClick"]))}}),[["__file","checkbox.vue"]]);const Fe=N({name:"ElCheckboxButton"});var Ie=O(N({...Fe,props:Ve,emits:_e,setup(e){const a=e,l=U(),{isFocused:t,isChecked:n,isDisabled:u,checkboxButtonSize:o,model:r,actualValue:i,handleChange:s}=we(a,l),c=p(Be,void 0),d=D("checkbox"),v=g((()=>{var e,a,l,t;const n=null!=(a=null==(e=null==c?void 0:c.fill)?void 0:e.value)?a:"";return{backgroundColor:n,borderColor:n,color:null!=(t=null==(l=null==c?void 0:c.textColor)?void 0:l.value)?t:"",boxShadow:n?`-1px 0 0 0 ${n}`:void 0}})),b=g((()=>[d.b("button"),d.bm("button",o.value),d.is("disabled",u.value),d.is("checked",n.value),d.is("focus",t.value)]));return(e,a)=>{var l,o,c,m;return R(),P("label",{class:K(M(b))},[e.trueValue||e.falseValue||e.trueLabel||e.falseLabel?W((R(),P("input",{key:0,"onUpdate:modelValue":e=>Q(r)?r.value=e:null,class:K(M(d).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:M(u),"true-value":null==(o=null!=(l=e.trueValue)?l:e.trueLabel)||o,"false-value":null!=(m=null!=(c=e.falseValue)?c:e.falseLabel)&&m,onChange:M(s),onFocus:e=>t.value=!0,onBlur:e=>t.value=!1,onClick:q((()=>{}),["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","true-value","false-value","onChange","onFocus","onBlur","onClick"])),[[H,M(r)]]):W((R(),P("input",{key:1,"onUpdate:modelValue":e=>Q(r)?r.value=e:null,class:K(M(d).be("button","original")),type:"checkbox",name:e.name,tabindex:e.tabindex,disabled:M(u),value:M(i),onChange:M(s),onFocus:e=>t.value=!0,onBlur:e=>t.value=!1,onClick:q((()=>{}),["stop"])},null,42,["onUpdate:modelValue","name","tabindex","disabled","value","onChange","onFocus","onBlur","onClick"])),[[H,M(r)]]),e.$slots.default||e.label?(R(),P("span",{key:2,class:K(M(d).be("button","inner")),style:ae(M(n)?M(v):void 0)},[T(e.$slots,"default",{},(()=>[Y(Z(e.label),1)]))],6)):J("v-if",!0)],2)}}}),[["__file","checkbox-button.vue"]]);const ze=le({modelValue:{type:te(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:f,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0},...m(["ariaLabel"])}),Oe={[h]:e=>S(e),change:e=>S(e)},Ne=N({name:"ElCheckboxGroup"});var Ue=O(N({...Ne,props:ze,emits:Oe,setup(e,{emit:a}){const l=e,t=D("checkbox"),{formItem:n}=y(),{inputId:u,isLabeledByFormItem:o}=I(l,{formItemContext:n}),r=async e=>{a(h,e),await V(),a("change",e)},i=g({get:()=>l.modelValue,set(e){r(e)}});return ne(Be,{...ue(oe(l),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:i,changeEvent:r}),L((()=>l.modelValue),(()=>{l.validateEvent&&(null==n||n.validate("change").catch((e=>_())))})),(e,a)=>{var l;return R(),A(ee(e.tag),{id:M(u),class:K(M(t).b("group")),role:"group","aria-label":M(o)?void 0:e.ariaLabel||"checkbox-group","aria-labelledby":M(o)?null==(l=M(n))?void 0:l.labelId:void 0},{default:$((()=>[T(e.$slots,"default")])),_:3},8,["id","class","aria-label","aria-labelledby"])}}}),[["__file","checkbox-group.vue"]]);const De=ie(Ee,{CheckboxButton:Ie,CheckboxGroup:Ue});re(Ie);const Ae=re(Ue);export{De as E,Ae as a,Ce as b,Le as i};
