.rsssl{
  // rsssl bullets
  .rsssl-bullet {
	height: 13px;
	width: 13px;
	flex: 0 0 13px;
	border-radius: 50%;
	display: inline-block;
  	background-color: var(--rsp-grey-300);
	&.rsp-yellow {
	  background-color: var(--rsp-yellow);
	}

	&.rsp-blue {
	  background-color: var(--rsp-blue);
	}

	&.rsp-pink {
	  background-color: var(--rsp-pink);
	}

	&.rsp-red, &.rsssl-bullet-error {
	  background-color: var(--rsp-red);
	}

	&.rsp-green, &.rsssl-bullet-success  {
	  background-color: var(--rsp-green);
	}
	&.rsp-blue-yellow {
	  background: var(--rsp-blue);
	  background: linear-gradient(77deg, rgba(0,159,255, 1) 0%, rgba(0,159,255, 1) 30%, rgba(244, 191, 62, 1) 70%, rgba(244, 191, 62, 1) 100%);
	  animation: gradient 2s ease infinite;
	  background-size: 200% 200%;
	}
  }
	.rsssl-legend{
		display: flex;

		width: max-content;
		color: var(--rsp-text-color-light);
		align-items: center;
		min-width: 0;
		gap: var(--rsp-spacing-xxs);
		text-decoration: none;
		&:first-of-type{
			margin-left: auto;
		}
		.rsssl-progress-footer-link {
			a {
				color: inherit;
				text-decoration: none;

				&:hover {
					text-decoration: underline;
				}
			}
		}
	}

  @keyframes gradient {
	0% {
	  background-position: 0% 50%;
	}
	50% {
	  background-position: 100% 50%;
	}
	100% {
	  background-position: 0% 50%;
	}
  }
}
