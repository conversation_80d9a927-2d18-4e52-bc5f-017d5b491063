@import '../../../../assets/css/variables.scss';
.rsssl-modal {
  overflow: visible;
  width: 700px;
  max-width: 700px;
  //background:var(--rsp-grey-100);
  padding:var(--rsp-spacing-m) var(--rsp-spacing-l);
  box-shadow:var(--rsp-box-shadow);
  border-radius:var(--rsp-border-radius) var(--rsp-border-radius);
  border-top:1px solid var(--rsp-grey-300);
  max-height: calc(100% - 120px); //the default wp modal limits this to minimum 600px, but this creates an ugly modal.
  margin:auto;//the default wp modal limits this to minimum 600px, but this creates an ugly modal.
  @media (max-width: 768px) {
    width: 100%;
  }

  .components-modal__content {
    min-height: 250px;
    //reset default padding so we can add a footer with background
    padding:0;
    margin-top:45px;

    & > div:nth-child(2) {
      display: flex;
      height: 100%;
      flex-direction: column;
    }
  }

  .rsssl-modal-body {
    flex-grow: 1;
    padding: 12px 8px 80px 8px; //80 is for the complete footer height + margin
    @media (max-width: 768px) {
      padding: 12px 8px 180px 8px;

    }
    p {
      margin-top:0;
    }
    ul {
      column-count: 2;
      column-gap: 20px;
      margin-bottom:0;
      li {
        display: flex;
        padding-bottom: 5px;

        .rsssl-icon {
          margin-top: 2px;
          margin-right: 10px;
        }
      }
    }
  }

  .rsssl-modal-footer {
    background-color: var(--rsp-grey-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--rsp-grey-400);
    padding: 20px 32px;
    //ensure the footer doesn't scroll out of sight on smaller screens.
    bottom: 0;
    position: absolute;
    left: 0;
    right: 0;
    .rsssl-modal-footer-buttons {
      display: flex;
      .rsssl-activate-license-text {
        margin-right: 10px;
        color: var(--rsp-red);
      }
    }
    border-bottom-left-radius: var(--rsp-border-radius) var(--rsp-border-radius);
    border-bottom-right-radius: var(--rsp-border-radius) var(--rsp-border-radius);
    img {
      width: 150px;

    }
    @media (max-width: 768px) {
      .rsssl-modal-footer-image {
        display:none;
      }
      .rsssl-modal-footer-buttons {
        width:100%;
        display:flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        button {
          width: 100%;
          margin:0;
        }
      }
    }

    button + button {
      margin-left: 10px;
    }
    button + a {
      margin-left: 10px;
    }

    button {
      &:not(.is-primary) {
        border: 1px solid var(--wp-admin-theme-color, #3858e9);
        color: var(--wp-admin-theme-color, #3858e9);
        background-color: transparent;
      }

      &.rsssl-modal-warning {
        border: 1px solid var(--rsp-red);
        color: var(--rsp-red);
        background-color: transparent;
      }
    }
  }
}

