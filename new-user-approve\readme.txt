=== New User Approve ===
Contributors: wpexpertsio
Donate link: https://newuserapprove.com
Tags: User Management, User Registration, Registration, users, user approval
Requires at least: 4.0
Tested up to: 6.8.1
Stable tag: 3.0.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

New User Approve allows a site administrator to approve a user before they
are able to login to the site.

== Description ==

[Documentation](https://newuserapprove.com/docs/pro-and-free/?utm_source=wp_org&utm_medium=read_me) | [New User Approve PRO](https://newuserapprove.com/pricing/?utm_source=wp_org&utm_medium=read_me) | [Contact Us](https://newuserapprove.com/get-in-touch/?utm_source=wp_org&utm_medium=read_me)

https://www.youtube.com/watch?v=6qFhsEwh6ec

Running a WordPress site is exciting but it also means you’re constantly battling spam registrations, fake accounts, and unwanted users. New User Approve works as a default WordPress registration system that does not let anyone sign up and instantly gain access. Because its your responsibility as a site owner as exactly who’s allowed into your site before any user log in.

New User Approve comes is a powerful WordPress user approval plugin that puts you in full control of your community, membership site, online store, or private portal. With this manual user approval plugin, you can moderate user registration, protect your content, and keep your website free from unwanted registration requests.
Whether you want to restrict user access before approval, prevent fake user signups, or simply make sure every new member is legit, New User Approve makes it easy, professional, and efficient. 

==⭐ Why You Need New User Approve ⭐==
Imagine running a membership site, community forum, or private business portal where quality and security matter. The last thing you want is spam bots flooding your database or strangers sneaking into confidential areas.

**New User Approve helps** you:
⚡ **Stop spam registrations** and fake accounts before they ever log in
⚡ **Verify user identities** by reviewing each signup manually
⚡ Maintain **complete control** over who becomes part of your community
⚡ Create a **safe, trusted space** for your members or customers

Unlike generic WordPress plugins that simply hide login pages, New User Approve adds a true **user verification before approval** process. It gives you confidence to grow your site without worrying about who’s lurking behind those new user accounts. 
  
==🛠 How It Works==
Here’s how **New User Approve**, your go-to **member approval plugin**, transforms your registration flow:

When someone registers on your WordPress site, you’ll receive an email alert. You can then decide to approve or deny their account. The plugin automatically emails the user to let them know the outcome.

👁If approved, the user receives their login details and can access your site immediately.
👁If denied, they’ll be kept out—and can’t even log in.
👁Pending users stay locked out until you make a decision.

This makes it simple to **restrict user access before approval** and ensure only the right people become part of your online community.

Already have users on your website? No problem. 

Existing users stay approved automatically when you install New User Approve. You can also change someone’s approval status at any time, with easy search tools for managing pending, approved, or denied users.

==⌛ Save Time with Zapier Automation==
Want to work smarter? Connect **New User Approve** to Zapier to automate routine tasks.
For example:

* Trigger a Slack message when a user is approved
* Add approved users to a Google Sheet
* Send emails through Gmail when someone’s denied

With Zapier, you can integrate the plugin with thousands of apps without writing a single line of code. It’s perfect for businesses looking to streamline their processes while maintaining strict **user verification before approval.**
 
==🤝Invite Trusted Users Instantly==
Sometimes you want trusted people—like staff, VIPs, or clients—to skip the approval queue. With New User Approve’s invitation codes, you can:

* 📜 Generate unique codes manually or automatically
* 💳 Give those codes to users so they’re **auto-approved** upon registration
* 💻 Manage, edit, or disable codes anytime
* 🛒 Seamlessly integrate codes with WooCommerce registrations

This feature makes it easier than ever to onboard important members without sacrificing security.
 
==☀ A Fresh New Interface==
**New User Approve** has been redesigned with a crisp, modern look that’s clean and user-friendly. The updated interface is intuitive for admins and gives users confidence in your website’s professionalism.

Whether you’re a WordPress beginner or seasoned WordPress user, managing new user approvals has never been easier or looked this good.

==⭐ Compatible with Top Plugins==
New User Approve integrates beautifully with popular WordPress plugins, including:

* WooCommerce
* MemberPress
* WP-Foro
* LearnDash
* Ultimate Member
* BuddyPress

So, whether you’re running a store, a membership site, or a thriving online community, this user approval plugin fits right in.
 
==🖍 Customize Everything==
For those who want complete flexibility, New User Approve lets you customize nearly every step of the user approval process:

* Tailor the welcome message above the login or registration form
* Personalize messages for pending or denied users
* Craft unique notification emails for users and admins
* Suppress denial notifications if preferred
* Use HTML formatting in emails for a branded, professional look

There’s even a commercial add-on available at [New User Approve](https://newuserapprove.com/pricing/?utm_source=wp_org&utm_medium=read_me) that unlocks additional powerful features for businesses and growing sites. 
 
==📌 Documentation==
Need help getting started? [View the detailed technical documentation here](https://newuserapprove.com/?utm_source=wp_org&utm_medium=read_me). It walks you through every step of installing and configuring **New User Approve**, plus troubleshooting tips if you get stuck.

==🔥 New User Approve Pro Features==
Upgrade to the premium version for advanced features like:

✔ Customizable Email Notifications
✔ Invite-Only Registration
✔ Bulk Invitation Code Generator
✔ Email Invitation Codes
✔ Import Invitation Codes
✔ Auto-Approve Trusted Email Domains
✔ Blacklist Generic or Suspicious Email Domains
✔ Registration Deadlines
✔ Auto-Approve Selected User Roles
✔ User Role Change Requests
✔ Extended Zapier Triggers

==✨ Translations==
**New User Approve** is already available in many languages thanks to an amazing community of translators. You can help expand translations further with tools like Poedit.

Current supported languages include:

Belarusian, Brazilian Portuguese, Bulgarian, Catalan, Croatian, Czech, Danish, Dutch, Estonian, Finnish, French, German, Greek, Hebrew, Hungarian, Italian, Lithuanian, Persian, Polish, Romanian, Russian, Serbo-Croatian, Slovak, Spanish, Swedish.
 
**New User Approve** isn’t just a plugin. It’s your ultimate solution for user verification before approval, helping you moderate user registration, prevent fake user signups, and stop spam registrations for good. 
If you need a reliable manual user approval plugin for WordPress, get it now! 

== Installation ==

1. Upload new-user-approve to the wp-content/plugins directory or download from the WordPress backend (Plugins -> Add New -> search for 'new user approve')
2. Activate the plugin through the Plugins menu in WordPress
3. No configuration necessary.

== Frequently Asked Questions ==

= Why am I not getting the emails when a new user registers? =

The New User Approve plugin uses the functions provided by WordPress to send email. Make sure your host is setup correctly to send email if this happens.

= What happens to the user's status after the plugin is deactivated? =

If you deactivate the plugin, their status doesn't matter. The status that the plugin uses is only used by the plugin. All users will be allowed to login as long as they have their username and passwords.

== Screenshots ==

1. New & Improved Dashboard.
2. Real Time Dashboard Alerts.
3. Manual User Approval.
4. Invitation Codes.
5. Add Invitation Code.
6. Integrations.
7. Settings.

== Changelog ==

= 3.0.0 =
* Improvement - Imporved overall UI
* Added - React base dashboard
* Added - Recent activity in dashboard.
* Added - Functionality to logged out user when admin decline.
* Fixed - Bypass user login from woocommerce.
* Improvement - Code Optimization

= ******* =
* Tweak – Tested Upto WordPress Latest Version 6.8

= ******* =
* Tweak – Security Fixes.

= 2.6.5 =
* Fixed - Freemius SDK Update.

= 2.6.4 =
* Fixed - Minor bug fix and improvements.
* Fixed - Security issues.

= 2.6.2 =
* Fixed - Minor bug fix and improvements.

= 2.6.1 =
* TWEAK - Compatible with WordPress Version 6.6.x.

= 2.6 =
* NEW: Pagination has been added to the Approved/Denied/Pending users list.
* NEW: "Pending user" trigger has been added to the Zapier Integration.
* Improvement: Code has been optimized and improved.
* Improvement: Action buttons alignment has been adjusted in the Approved/Denied/Pending tabs.
* Fixed: The Welcome/Pending Email does not fire when the user registers through the WooCommerce Registration Form.
* Fixed: Translation issues with Admin and User Email Notification Strings have been resolved.
* Fixed: Issues with the NUA filter at the bottom of the user's list page have been addressed.

= 2.5.3 =
* Tested and Compatible up to WordPress v6.4.2

= 2.5.2 =
* Fixed - Bug preventing the notice from showing on admin page.

= 2.5.1 =
* Improvement - Updated Feedback library to the latest version.

= 2.5 =
* Updated localization - pot file updated, French and Italian po mo updated.
* Fixed - Approve button color issue.
* Fixed - Duplicate text message on user registration by invitation code.
* Fixed - Rest API notice.
* Improvement - When using invitation code user will not be created if invition code is not given or incorrect invitation code is used.
* Improvement - When user successfully registers via invitation code the welcome email not sent, instead Approve email is sent and success message appears. 

= 2.4.1 =
* Tweak – Security Fixes

= 2.4 =
* Tweak – Code improvement

= 2.3 =
* Added - Zapier Integration
* Added - Filter Hook to Filter password before user creation.
* Added - Search approve, deny and pending users.
* Improvement - Code Optimization.

= 2.1 = 
* Updated Freemius SDK Version 2.4.3

= 2.0 = 
* Updated- Plugin menu.
* Added- the invitation code functionality.
* Improved backend UI.

= 1.9.1 = 
* Added filter to enable/disable auto login on WooCommerce checkout, by default it will be enabled.

= 1.8.9 = 
* Issue in user search functionality.

= 1.8.8 =
* Freemius Library Updated.
* 'View all user' filter not working in users page.
* Disable auto login on WooCommerce checkout.

= 1.8.7 =
* Upgrade to pro menu fixed.

= 1.8.6 =
* Code optimization.

= 1.8.5 =
* Added: Support for reCaptcha on default Login and Registration page.

= 1.8.4 =
* Added: User registeration welcome email
* Added: Action Hook - new_user_approve_after_registration
* Added: Filter Hook for modify welcome email subject - new_user_approve_welcome_user_subject
* Added: Filter Hook for modify welcome email message - new_user_approve_welcome_user_message

= 1.8.3 =
* Updated Freemius SDK Version 2.4.1

= 1.8.2 =
* Code Optimization

= 1.8.1 =
* Tested upto WordPress version 5.5
* Tested for compatibility with [Memberpress](https://memberpress.com/)
* Added: Compatibility for [WooCoommerce](https://woocommerce.com/)

= 1.8 =
* Tested with WordPress 5.4
* Code Optimization

= 1.7.6 =
* Fixed: Formatting of readme.txt had line breaks where they should have been
* Fixed: Fix how deny_user() gets user_email
  * Courtesy of [jrequiroso](https://github.com/jrequiroso)
  * https://github.com/picklewagon/new-user-approve/pull/22
* Fixed: Show unapproved user error message when the user attempts to reset password
* Updated: Swedish translations
  * Courtesy of [adevade](https://github.com/adevade)
  * https://github.com/picklewagon/new-user-approve/pull/59
* Updated: Updates to admin approval screen
  * Courtesy of [adevade](https://github.com/adevade)
  * https://github.com/picklewagon/new-user-approve/pull/60
* Added: Don't allow a super admin to be denied or approved
  * https://github.com/picklewagon/new-user-approve/pull/19
* Added: readme.md to show content in github

= 1.7.5 =
* Fixed: User status filter in admin was not using database prefix
  * Courtesy of [Oizopower](https://github.com/Oizopower)
  * https://github.com/picklewagon/new-user-approve/pull/50
* Fixed: Optimize user status list so it can be used with many users
* Fixed: Updated transient to populate with user counts instead of user list
* Updated: Modify output of user counts on dashboard
* Updated: Polish translations
  * Courtesy of [pik256](http://wordpress.org/support/profile/1271256)
* Added: Missing string to translation file
  * Courtesy of [spaszs](https://profiles.wordpress.org/spaszs/)
* Added: Bulgarian translation
  * Courtesy of [spaszs](https://profiles.wordpress.org/spaszs/)

= 1.7.4 =
* Fixed: Corrected erroneous SQL query when filtering users
* Fixed: User filters
  * Courtesy of [julmuell](https://github.com/julmuell)
  * https://github.com/picklewagon/new-user-approve/pull/44
* Fixed: Show a user status in the filters only if at least one user has that status

= 1.7.3 =
* place content in metaboxes in place of dynamically pulling from website
* tested with WordPress 4.3.1

= 1.7.2 =
* tested with WordPress 4.1
* fix translation bug
* add bubble to user menu for pending users
 * Courtesy of [howdy_mcgee](https://wordpress.org/support/profile/howdy_mcgee)
 * https://wordpress.org/support/topic/get-number-of-pending-users#post-5920371

= 1.7.1 =
* fix code causing PHP notices
* don't show admin notice for registration setting if S2Member plugin is active
* fix issue causing empty password in approval email
* update translation files

= 1.7 =
* email/message tags
* refactor messages
* send admin approval email after the user has been created
* tested with WordPress 4.0
* finish updates in preparation of option addon plugin

= 1.6 =
* improve actions and filters
* refactor messages to make them easier to override
* show admin notice if the membership setting is turned off
* fix bug preventing approvals/denials when using filter
* add sidebar in admin to help with support
* unit tests
* shake the login form when attempting to login as unapproved user
* updated French translation

= 1.5.8 =
* tested with WordPress 3.9
* fix bug preventing the notice from hiding on legacy page

= 1.5.7 =
* fix bug that was preventing bulk approval/denials

= 1.5.6 =
* add more translations

= 1.5.5 =
* allow approval from legacy page

= 1.5.4 =
* fix bug that prevents emails from being sent to admins

= 1.5.3 =
* add filter for link to approve/deny users
* add filter for adding more email addresses to get notifications
* fix bug that prevents users to be approved and denied when requested
* fix bug that prevents the new user email from including a password
* fix bug that prevents search results from showing when searching users

= 1.5.2 =
* fix link to approve new users in email to admin
* fix bug with sending emails to new approved users

= 1.5.1 =
* fix bug when trying to install on a site with WP 3.5.1

= 1.5 =
* add more logic to prevent unwanted password resets
* add more translations
* minor bug fixes
* use core definition of tabs
* user query updates (requires 3.5)
* add status attribute to user profile page
* integration with core user table (bulk approve, filtering, etc.)
* tested with WordPress 3.6
* set email header when sending email
* more filters and actions

= 1.4.2 =
* fix password recovery bug if a user does not have an approve-status meta field
* add more translations
* tested with WordPress 3.5

= 1.4.1 =
* delete transient of user statuses when a user is deleted

= 1.4 =
* add filters
* honor the redirect if there is one set when registering
* add actions for when a user is approved or denied
* add a filter to bypass password reset
* add more translations
* add user counts by status to dashboard
* store the users by status in a transient

= 1.3.4 =
* remove unused screen_layout_columns filter
* tested with WordPress 3.4

= 1.3.3 =
* fix bug showing error message permanently on login page

= 1.3.2 =
* fix bug with allowing wrong passwords

= 1.3.1 =
* add czech, catalan, romanian translations
* fix formatting issues in readme.txt
* add a filter to modify who has access to approve and deny users
* remove deprecated function calls when a user resets a password
* don't allow a user to login without a password

= 1.3 =
* use the User API to retrieve a user instead of querying the db
* require at least WordPress 3.1
* add validate_user function to fix authentication problems
* add new translations
* get rid of plugin errors with WP_DEBUG set to true

= 1.2.6 =
* fix to include the deprecated code for user search

= 1.2.5 =
* add french translation

= 1.2.4 =
* add greek translation

= 1.2.3 =
* add danish translation

= 1.2.2 =
* fix localization to work correctly
* add polish translation

= 1.2.1 =
* check for the existence of the login_header function to make compatible with functions that remove it
* added "Other Notes" page in readme.txt with localization information.
* added belarusian translation files

= 1.2 =
* add localization support
* add a changelog to readme.txt
* remove plugin constants that have been defined since 2.6
* correct the use of db prepare statements/use prepare on all SQL statements
* add wp_enqueue_style for the admin style sheet

= 1.1.3 =
* replace calls to esc_url() with clean_url() to make plugin compatible with versions less than 2.8

= 1.1.2 =
* fix the admin ui tab interface for 2.8
* add a link to the users profile in the admin interface
* fix bug when using email address to retrieve lost password
* show blog title correctly on login screen
* use get_option() instead of get_settings()

= 1.1.1 =
* fix approve/deny links
* fix formatting issue with email to admin to approve user

= 1.1 =
* correctly display error message if registration is empty
* add a link to the options page from the plugin dashboard
* clean up code
* style updates
* if a user is created through the admin interface, set the status as approved instead of pending
* add avatars to user management admin page
* improvements to SQL used
* verify the user does not already exist before the process is started
* add nonces to approve and deny actions
* temporary fix for pagination bug

== Upgrade Notice ==

= 1.5.3 =
Download version 1.5.3 immediately! Some bugs have been fixed that have been affecting how the plugin worked.

= 1.5 =
A long awaited upgrade that includes better integration with WordPress core. Requires at least WordPress 3.5.

= 1.3 =
This version fixes some issues when authenticating users. Requires at least WordPress 3.1.

= 1.3.1 =
Download version 1.3.1 immediately! A bug was found in version 1.3 that allows a user to login without using password.

= 1.3.2 =
Download version 1.3.2 immediately! A bug was found in version 1.3 that allows a user to login using any password.
