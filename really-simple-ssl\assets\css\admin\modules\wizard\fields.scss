html {
  scroll-behavior: smooth;
}
.rsssl{
  div[class^="rsssl-wizard-"]{ // starts with selector
    .components-flex{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .components-flex-item label{
        margin: 0;
      }
      .rsssl-tooltip {
        flex-grow:1;
      }
      .components-input-control__container {
        max-width: max-content;
      }
    }

    .rsssl-grid-item-content label{
      font-size: var(--rsp-fs-300);
      font-weight: 400;
      line-height: 1.5;
      text-transform: unset;
      box-sizing: border-box;
      display: inherit;
      max-width: 100%;
      //z-index: 1; => this conflicts with the tooltip z-index
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .components-base-control__field{
      margin-bottom: 0;
    }
    .components-toggle-control{
      .components-base-control__field, .components-flex{
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        justify-content: space-between;
        width:100%;
        .components-form-toggle{
            margin-right: 0;

        }
      }
    }
    select option {
      font-size: var(--rsp-fs-300);
    }
    .components-form-toggle.is-checked .components-form-toggle__track{
      background-color: var(--rsp-dark-blue);
    }
    input.components-form-toggle__input[type=checkbox]:disabled, {
      background: var(--rsp-grey-100);
      opacity:0.7
    }
    .rsssl-grid-item{
      .rsssl-grid-item-content{
        padding: var(--rsp-spacing-xs) 0;
      }
    }

    .rsssl-field-wrap {
      .rsssl-comment {
        font-style:italic;
      }
    }
    .rsssl-field-wrap, .rsssl-settings-block-intro{
      padding-block: var(--rsp-spacing-s);
      @include rsssl-inline-block-padding;
      overflow: hidden;
      & > .rsssl-field-wrap{
        padding: 0;
      }
    }
    .rsssl-field-button{
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: center;
    }
  }

  .rsssl-highlight {
    background-color: transparent;
    border-left: 4px solid var(--rsp-green);
    -o-animation: fadeIt 3s ease-in-out;
    animation: fadeIt 3s ease-in-out;
    scroll-margin-top: 100px;
    scroll-padding-top:100px;
  }

  .rsssl-block-highlight {
    border: 0;
    -o-animation: pulse 2s infinite;
    animation: pulse 2s infinite;}

  @-webkit-keyframes pulse {
    0% {
      -webkit-box-shadow: 0 0 0 0 var(--rsp-green);
    }
    70% {
      -webkit-box-shadow: 0 0 0 10px var(--rsp-green-faded);
    }
    100% {
      -webkit-box-shadow: 0 0 0 0 var(--rsp-green-faded);;
    }
  }
  @keyframes pulse {
    0% {
      -moz-box-shadow: 0 0 0 0 var(--rsp-green);
      box-shadow: 0 0 0 0 var(--rsp-green);
    }
    70% {
      -moz-box-shadow: 0 0 0 10px var(--rsp-green-faded);
      box-shadow: 0 0 0 10px var(--rsp-green-faded);
    }
    100% {
      -moz-box-shadow: 0 0 0 0 var(--rsp-green-faded);
      box-shadow: 0 0 0 0 var(--rsp-green-faded);
    }
  }

  @-o-keyframes fadeIt {
    0%   { background-color: transparent; }
    30%  { background-color: var(--rsp-green-faded); }
    100% {
      border-right: 0;
      background-color: inherit;
    }
  }
  @keyframes fadeIt {
    0%   {
      background-color: transparent;
      border-right: 0;
    }
    30%  { background-color: var(--rsp-green-faded); }
    100% { background-color: inherit; }
  }
}

.rsssl-email-verified {
 position: absolute;
  bottom: 23px;
  right: 35px;
}

.rsssl-ip-verified {
  position: absolute;
  bottom: 8px;
  right: 6px;
}

//switch padding to top, to prevent tasks from getting stuck to text field
.rsssl-license {
  .rsssl-task-element {
    padding-top: var(--rsp-spacing-s);
    padding-bottom: 0;
  }
  .rsssl-license-field{
    display:flex;
  }
}

.input-container {
  position: relative;
  display: inline-block;
    width: 100%;
}

input.rsssl-input {
  &.full {
    width: 100%;
    padding-right: 40px;
  }
  &.rsssl-error {
    border: 2px solid var(--rsp-red);
    outline: none;
    &:focus {
      border-color: var(--rsp-red) !important;
    }
  }
  &.rsssl-success {
    border: 2px solid var(--rsp-green);
    outline: none;
    &:focus {
      border-color: var(--rsp-green) !important;
    }
  }
}
.wp-core-ui select {
  /* Your styles here */
  flex-grow: 1 !important;
  max-width: 33%;
}
.icon-button {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: none;
  cursor: pointer;
}

.rsssl-group-filter {
  min-width: 25%;
  max-width: 100%!important;
}