# 分类和品牌变更检测修复

## 问题描述

当产品分类和品牌没有实际变更时，变更对比中仍然显示这些字段有变更。这导致：

1. 变更对比中显示了不必要的分类和品牌变更
2. 审核人员难以识别真正的变更内容
3. 变更摘要中包含了误导性的信息

## 问题原因

在 `class-prs-product-handler.php` 的 `generate_change_summary()` 方法中，数组比较逻辑存在问题：

1. 使用了 `!==` 严格比较操作符，但没有考虑数组元素类型差异
2. 分类和品牌ID可能在不同阶段以不同类型存储（字符串ID vs 数字ID）
3. 即使内容相同，由于类型不同，比较结果也会显示为"有变更"

## 修复内容

### 1. 修复变更摘要生成逻辑 (class-prs-product-handler.php)

修改 `generate_change_summary()` 方法中的数组比较逻辑，为分类、标签和品牌添加特殊处理：

```php
// 修复前
$should_show_array_change = ($original_value !== $modified_value);

// 修复后
if (in_array($field, array('categories', 'tags', 'brands'))) {
    // 将所有元素转换为整数，确保类型一致
    $original_value_int = array_map('intval', $original_value);
    $modified_value_int = array_map('intval', $modified_value);
    
    // 重新排序
    sort($original_value_int);
    sort($modified_value_int);
    
    // 使用序列化后的值进行比较，避免类型问题
    $should_show_array_change = (serialize($original_value_int) !== serialize($modified_value_int));
} else {
    // 其他数组使用标准比较
    $should_show_array_change = ($original_value !== $modified_value);
}
```

### 2. 修复详细变更对比逻辑 (class-prs-admin.php)

修改 `display_change_comparison()` 方法中的字段比较逻辑，为分类、标签和品牌添加特殊比较方法：

```php
// 修复前
if ($original_normalized !== $modified_normalized) {

// 修复后
$has_field_change = false;
if (in_array($field, array('categories', 'tags', 'brands'))) {
    $has_field_change = $this->compare_taxonomy_arrays($original_normalized, $modified_normalized);
} else {
    $has_field_change = ($original_normalized !== $modified_normalized);
}

if ($has_field_change) {
```

新增 `compare_taxonomy_arrays()` 方法：

```php
private function compare_taxonomy_arrays($original, $modified) {
    // 确保输入是数组
    $original = is_array($original) ? $original : array();
    $modified = is_array($modified) ? $modified : array();

    // 将所有元素转换为整数，确保类型一致
    $original_int = array_map('intval', $original);
    $modified_int = array_map('intval', $modified);

    // 重新排序
    sort($original_int);
    sort($modified_int);

    // 使用序列化后的值进行比较，避免类型问题
    return (serialize($original_int) !== serialize($modified_int));
}
```

### 3. 主要改进点

1. **类型统一**：将所有分类、标签和品牌ID转换为整数，确保类型一致
2. **序列化比较**：使用序列化后的值进行比较，避免类型差异导致的误判
3. **特殊处理**：只对分类、标签和品牌应用特殊比较逻辑，其他数组保持原有比较方式
4. **双重修复**：同时修复变更摘要和详细对比两个层面的问题

## 修复效果

修复后，当产品分类和品牌没有实际内容变更时，变更对比中将不再显示这些字段有变更。这将：

1. 减少变更对比中的噪音信息
2. 帮助审核人员更容易识别真正的变更内容
3. 提高变更摘要的准确性

## 测试方法

1. 编辑一个产品，不更改分类和品牌
2. 保存产品
3. 查看审核记录中的变更对比
4. 确认分类和品牌不再显示在变更中

## 相关文件

- `includes/class-prs-product-handler.php` - 变更摘要修复
- `includes/class-prs-admin.php` - 详细对比修复

## 注意事项

1. 此修复不会影响实际有变更的分类和品牌的检测
2. 修复向后兼容，不会影响现有功能
3. 修复不会影响其他类型数组的比较逻辑

## 版本信息

- 修复版本：1.0.3
- 修复日期：2025-01-16
- 影响范围：分类和品牌变更检测
