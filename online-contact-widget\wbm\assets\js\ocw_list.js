import{e as r,f as x,k as j,g as c,h as t,i as p,t as s,u,F as h,j as q,o as i}from"./vendor.js";import{u as B,a as D}from"./useI18N.js";const N={class:"wbm-hd"},V={key:0,class:"mb-content-table"},C={key:0,class:"table-data-list"},F={class:"wbm-rec-type"},E={class:"wbm-rec-subject"},G={class:"wbm-pd-title"},I={class:"wbm-wk"},M={key:1,class:"com-empty-rec"},O={class:"cer-inner"},P=t("svg",{class:"wb-icon wbm-sico-nodata"},[t("use",{"xlink:href":"#wbm-sico-nodata"})],-1),z={__name:"ocw_list",setup(R){const{$api:w,$cnf:m}=B(),{wb_e:l}=D(m),y=j(),_=r([]),b=r(!1),f=r(0),v=r(1),o=r({}),k=()=>{w.getData({_ajax_nonce:m.ocw_ajax_nonce,action:"wb_ocw_api",op:"my_wo"}).then(e=>{_.value=e.data.items,v.value=e.data.total,o.value=e.data.cnf,b.value=!0})},g=e=>{const d=["qq","mobile","wx"];if(e.email){const a=e.email;if(a.indexOf(":")>-1){const n=a.split(":");return`${o.value.contact_ways[n[0]].label}: ${n[1]}`}else return`${o.value.contact_ways.email.label}: ${e.email}`}else for(let a in d){const n=d[a];if(e[n])return`${o.value.contact_ways[n].label}: ${e[n]}`}return l("未填写")},L=e=>o.value.type[e.type]?o.value.type[e.type]:e.type;return x(()=>{f.value=y.query.status?y.query.status:0,k()}),(e,d)=>(i(),c(h,null,[t("div",N,[t("h1",null,s(u(l)("我的咨询")),1)]),b.value?(i(),c("div",V,[_.value.length?(i(),c("table",C,[t("thead",null,[t("tr",null,[t("th",null,s(u(l)("咨询类型")),1),t("th",null,s(u(l)("联系人")),1),t("th",null,s(u(l)("联系方式")),1),t("th",null,s(u(l)("时间")),1)])]),t("tbody",null,[(i(!0),c(h,null,q(_.value,a=>(i(),c("tr",{key:"wo"+a.sn},[t("td",null,[t("span",F,s(L(a)),1)]),t("td",null,[t("div",E,s(a.name),1)]),t("td",null,[t("div",G,s(g(a)),1)]),t("td",null,[t("span",I,s(a.update_time),1)])]))),128))])])):p("",!0),_.value.length?p("",!0):(i(),c("div",M,[t("div",O,[P,t("p",null,s(u(l)("暂无记录")),1)])]))])):p("",!0)],64))}};export{z as default};
