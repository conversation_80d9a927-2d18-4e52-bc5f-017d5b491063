<?php

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 产品审核系统安全中间件
 */
class PRS_Security_Middleware {
    
    /**
     * 初始化安全中间件
     */
    public static function init() {
        // 添加安全头
        add_action('send_headers', array(__CLASS__, 'add_security_headers'));

        // IP白名单检查 - 仅在配置了白名单时启用
        if (!empty(PRS_Security_Config::get_ip_whitelist())) {
            add_action('init', array(__CLASS__, 'check_ip_whitelist'));
        }

        // 清理过期的安全日志
        add_action('prs_daily_cleanup', array(__CLASS__, 'cleanup_security_logs'));

        // 监控可疑活动
        add_action('wp_login_failed', array(__CLASS__, 'log_failed_login'));
        add_action('wp_login', array(__CLASS__, 'log_successful_login'), 10, 2);
    }
    
    /**
     * 添加安全头
     */
    public static function add_security_headers() {
        if (!headers_sent()) {
            $headers = PRS_Security_Config::get_security_headers();
            foreach ($headers as $header => $value) {
                header($header . ': ' . $value);
            }
        }
    }
    
    /**
     * 检查IP白名单
     */
    public static function check_ip_whitelist() {
        $current_ip = PRS_Security::get_client_ip();
        
        if (!PRS_Security_Config::is_ip_whitelisted($current_ip)) {
            PRS_Security::log_security_event('ip_blocked', array(
                'ip' => $current_ip,
                'reason' => 'not_in_whitelist'
            ));
            
            wp_die(__('访问被拒绝：您的IP地址不在允许列表中。', 'product-review-system'), 
                   __('访问被拒绝', 'product-review-system'), 
                   array('response' => 403));
        }
    }
    
    /**
     * 验证AJAX请求的安全性
     */
    public static function validate_ajax_request($action, $required_capability = null) {
        // 检查是否为AJAX请求
        if (!wp_doing_ajax()) {
            return false;
        }
        
        // 检查用户是否登录
        if (!is_user_logged_in()) {
            PRS_Security::log_security_event('ajax_unauthorized', array('action' => $action));
            return false;
        }
        
        // 检查权限
        if ($required_capability && !current_user_can($required_capability)) {
            PRS_Security::log_security_event('ajax_insufficient_permissions', array(
                'action' => $action,
                'required_capability' => $required_capability
            ));
            return false;
        }
        
        // 检查速率限制
        $rate_config = PRS_Security_Config::get_rate_limit($action);
        if (!PRS_Security::check_rate_limit($action, get_current_user_id(), 
                                           $rate_config['limit'], $rate_config['window'])) {
            PRS_Security::log_security_event('ajax_rate_limit', array('action' => $action));
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证表单提交的安全性
     */
    public static function validate_form_submission($nonce_action, $required_capability = null) {
        // 检查nonce
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], $nonce_action)) {
            PRS_Security::log_security_event('form_invalid_nonce', array('action' => $nonce_action));
            return false;
        }
        
        // 检查用户权限
        if ($required_capability && !current_user_can($required_capability)) {
            PRS_Security::log_security_event('form_insufficient_permissions', array(
                'action' => $nonce_action,
                'required_capability' => $required_capability
            ));
            return false;
        }
        
        // 检查速率限制
        if (!PRS_Security::check_rate_limit('form_submission', get_current_user_id(), 20, 300)) {
            PRS_Security::log_security_event('form_rate_limit', array('action' => $nonce_action));
            return false;
        }
        
        return true;
    }
    
    /**
     * 清理过期的安全日志
     */
    public static function cleanup_security_logs() {
        $log_config = PRS_Security_Config::get_log_config();
        $retention_days = $log_config['retention_days'];
        
        // 这里可以实现清理逻辑，如果有数据库存储的安全日志
        do_action('prs_cleanup_security_logs', $retention_days);
    }
    
    /**
     * 记录登录失败
     */
    public static function log_failed_login($username) {
        PRS_Security::log_security_event('login_failed', array(
            'username' => sanitize_user($username),
            'ip' => PRS_Security::get_client_ip()
        ));
    }
    
    /**
     * 记录成功登录
     */
    public static function log_successful_login($user_login, $user) {
        PRS_Security::log_security_event('login_success', array(
            'user_id' => $user->ID,
            'username' => $user_login,
            'ip' => PRS_Security::get_client_ip()
        ));
    }
    
    /**
     * 检查可疑活动模式
     */
    public static function detect_suspicious_activity($user_id, $action) {
        $suspicious_patterns = array(
            'rapid_actions' => self::check_rapid_actions($user_id, $action),
            'unusual_hours' => self::check_unusual_hours(),
            'multiple_failures' => self::check_multiple_failures($user_id),
            'privilege_escalation' => self::check_privilege_escalation($user_id, $action)
        );
        
        $suspicious_count = array_sum($suspicious_patterns);
        
        if ($suspicious_count >= 2) {
            PRS_Security::log_security_event('suspicious_activity_detected', array(
                'user_id' => $user_id,
                'action' => $action,
                'patterns' => $suspicious_patterns,
                'score' => $suspicious_count
            ));
            
            // 可以在这里添加额外的安全措施，如临时锁定账户
            do_action('prs_suspicious_activity_detected', $user_id, $action, $suspicious_patterns);
        }
        
        return $suspicious_count;
    }
    
    /**
     * 检查快速连续操作
     */
    private static function check_rapid_actions($user_id, $action) {
        $key = 'prs_action_count_' . $user_id . '_' . $action;
        $count = get_transient($key);
        
        if ($count === false) {
            set_transient($key, 1, 60);
            return false;
        }
        
        set_transient($key, $count + 1, 60);
        return $count > 10; // 1分钟内超过10次相同操作
    }
    
    /**
     * 检查异常时间访问
     */
    private static function check_unusual_hours() {
        $current_hour = (int) current_time('H');
        // 凌晨2点到6点被认为是异常时间
        return ($current_hour >= 2 && $current_hour <= 6);
    }
    
    /**
     * 检查多次失败操作
     */
    private static function check_multiple_failures($user_id) {
        $key = 'prs_failures_' . $user_id;
        $failures = get_transient($key);
        
        return $failures && $failures > 3;
    }
    
    /**
     * 检查权限提升尝试
     */
    private static function check_privilege_escalation($user_id, $action) {
        $sensitive_actions = PRS_Security_Config::get_sensitive_actions();
        
        if (!in_array($action, $sensitive_actions)) {
            return false;
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return true; // 无效用户尝试敏感操作
        }
        
        // 检查用户是否有执行此操作的权限
        $required_caps = array(
            'approve_review' => 'review_products',
            'reject_review' => 'review_products',
            'delete_review' => 'delete_product_reviews',
            'bulk_action' => 'manage_product_reviews',
            'publish_product' => 'edit_products',
            'change_settings' => 'manage_product_review_settings'
        );
        
        $required_cap = isset($required_caps[$action]) ? $required_caps[$action] : 'manage_options';
        
        return !user_can($user, $required_cap);
    }
}
