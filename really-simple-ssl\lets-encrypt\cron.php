<?php
defined( 'ABSPATH' ) or die();

add_action( 'plugins_loaded', 'rsssl_le_schedule_cron' );
function rsssl_le_schedule_cron() {
	//only run if SSL is enabled.
	if ( !rsssl_get_option('ssl_enabled') ) {
		return;
	}

	//only if generated by RSSSL.
	if ( ! get_option( 'rsssl_le_certificate_generated_by_rsssl' ) ) {
		return;
	}

	add_action( 'rsssl_week_cron', 'rsssl_le_cron_maybe_start_renewal' );
	add_action( 'rsssl_daily_cron', 'rsssl_le_check_renewal_status' );
}

/**
 * Check if the certificate is generated by RSSSL. If so, renew if necessary
 */
function rsssl_le_cron_maybe_start_renewal(){
	if ( !rsssl_generated_by_rsssl() ) {
		return;
	}

	if ( RSSSL_LE()->letsencrypt_handler->cron_certificate_needs_renewal() ) {
		update_option("rsssl_le_start_renewal", true, false);
	}

	if ( RSSSL_LE()->letsencrypt_handler->certificate_install_required() ) {
		update_option("rsssl_le_start_installation", true, false);
	}
}

function rsssl_le_check_renewal_status(){
	if ( !rsssl_generated_by_rsssl() ) {
		return;
	}

	//when DNS validated, without api, we cannot autorenew
	if ( !RSSSL_LE()->letsencrypt_handler->ssl_generation_can_auto_renew() ) {
		return;
	}

	$renewal_active = get_option("rsssl_le_start_renewal");
	$installation_active = get_option("rsssl_le_start_installation");

	if ( $renewal_active ) {
		RSSSL_LE()->letsencrypt_handler->create_bundle_or_renew();
	} else if ( $installation_active ) {
		RSSSL_LE()->letsencrypt_handler->cron_renew_installation();
	}
}




