<?php
/**
 * 产品处理类 - 拦截产品修改并创建审核流程
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class PRS_Product_Handler {

    /**
     * 原始数据缓存
     */
    private $original_data_cache = array();

    /**
     * 是否正在处理审核流程
     */
    private $processing_review = false;

    /**
     * 构造函数
     */
    public function __construct() {
        // 使用更精确的钩子拦截产品保存
        add_action('wp_loaded', array($this, 'setup_product_save_intercept'));

        // 拦截产品删除操作 - 在init钩子中注册，确保更早执行
        add_action('init', array($this, 'setup_product_delete_intercept'));

        // 添加产品编辑页面的提示
        add_action('add_meta_boxes', array($this, 'add_review_status_meta_box'));

        // 添加产品列表列
        add_filter('manage_edit-product_columns', array($this, 'add_review_status_column'));
        add_action('manage_product_posts_custom_column', array($this, 'display_review_status_column'), 10, 2);

        // 添加列样式修复
        add_action('admin_head', array($this, 'add_column_styles'));

        // 添加备用重定向处理
        add_action('admin_init', array($this, 'handle_pending_redirect'));

        // 添加删除成功后的JavaScript重定向
        add_action('admin_footer', array($this, 'add_delete_redirect_script'));

        // 存储原始数据的临时变量
        $this->original_data_cache = array();
    }

    /**
     * 设置产品保存拦截
     */
    public function setup_product_save_intercept() {
        // 只在管理后台处理
        if (!is_admin()) {
            return;
        }

        // 检查当前页面
        global $pagenow;
        if ($pagenow === 'post.php' || $pagenow === 'post-new.php') {
            add_action('admin_init', array($this, 'intercept_product_save_early'), 1);
        }
    }

    /**
     * 设置产品删除拦截
     */
    public function setup_product_delete_intercept() {
        // 只在管理后台处理
        if (!is_admin()) {
            return;
        }

        // 拦截产品删除操作
        add_filter('pre_trash_post', array($this, 'intercept_product_delete'), 10, 2);
    }

    /**
     * 处理待处理的重定向
     */
    public function handle_pending_redirect() {
        $user_id = get_current_user_id();

        // 检查强制重定向（最高优先级）
        $force_redirect_url = get_transient('prs_force_redirect_' . $user_id);
        if ($force_redirect_url) {
            delete_transient('prs_force_redirect_' . $user_id);
            wp_redirect($force_redirect_url);
            exit;
        }

        // 检查修改审核的重定向
        $redirect_url = get_transient('prs_redirect_after_submit_' . $user_id);
        if ($redirect_url) {
            // 删除临时重定向URL
            delete_transient('prs_redirect_after_submit_' . $user_id);
            // 执行重定向
            wp_redirect($redirect_url);
            exit;
        }

        // 检查删除审核的重定向 - 优先级更高
        $delete_redirect_url = get_transient('prs_redirect_after_delete_' . $user_id);
        if ($delete_redirect_url) {
            // 删除临时重定向URL
            delete_transient('prs_redirect_after_delete_' . $user_id);

            // 如果是AJAX请求，不执行重定向
            if (defined('DOING_AJAX') && DOING_AJAX) {
                return;
            }

            // 执行重定向
            wp_redirect($delete_redirect_url);
            exit;
        }

        // 检查并显示删除成功消息
        $delete_success_message = get_transient('prs_delete_success_' . $user_id);
        if ($delete_success_message) {
            delete_transient('prs_delete_success_' . $user_id);
            add_action('admin_notices', function() use ($delete_success_message) {
                echo '<div class="notice notice-success is-dismissible"><p>' . esc_html($delete_success_message) . '</p></div>';
            });
        }

        // 检查并显示删除错误消息
        $delete_error_message = get_transient('prs_delete_error_' . $user_id);
        if ($delete_error_message) {
            delete_transient('prs_delete_error_' . $user_id);
            add_action('admin_notices', function() use ($delete_error_message) {
                echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($delete_error_message) . '</p></div>';
            });
        }
    }

    /**
     * 早期拦截产品保存
     */
    public function intercept_product_save_early() {
        // 防止重复执行
        static $already_processed = false;
        if ($already_processed) {
            return;
        }

        // 只在产品编辑页面处理
        global $pagenow;
        if ($pagenow !== 'post.php' && $pagenow !== 'post-new.php') {
            return;
        }

        // 检查是否是AJAX请求 - 如果是AJAX请求，不拦截（避免影响文件上传等功能）
        if (defined('DOING_AJAX') && DOING_AJAX) {
            return;
        }

        // 检查是否是产品保存请求
        if (!isset($_POST['action']) || $_POST['action'] !== 'editpost') {
            return;
        }

        if (!isset($_POST['post_type']) || $_POST['post_type'] !== 'product') {
            return;
        }

        if (!isset($_POST['post_ID'])) {
            return;
        }

        // 额外检查：确保这是来自产品编辑页面的正常保存请求
        // 排除其他可能的POST请求（如文件上传、快速编辑等）
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'update-post_' . $_POST['post_ID'])) {
            return;
        }

        // 检查是否是媒体库相关的请求 - 如果是，不拦截
        if (isset($_POST['action']) && in_array($_POST['action'], array('upload-attachment', 'save-attachment', 'save-attachment-compat'))) {
            return;
        }

        // 检查是否包含文件上传相关的字段 - 如果是纯文件上传，不拦截
        if (isset($_FILES) && !empty($_FILES) && !isset($_POST['post_title'])) {
            return;
        }

        // 标记已处理
        $already_processed = true;

        $post_id = intval($_POST['post_ID']);

        // 检查产品是否存在
        $existing_post = get_post($post_id);
        if (!$existing_post) {
            return;
        }

        // 对于新产品（auto-draft状态或空标题），我们仍然需要审核
        // 只有在没有实质性内容时才跳过审核
        $is_likely_new_product = ($existing_post->post_status === 'auto-draft') ||
                                 (empty($existing_post->post_title) || $existing_post->post_title === 'AUTO-DRAFT');

        // 如果是新产品但没有填写任何内容，跳过审核
        if ($is_likely_new_product) {
            $has_content = !empty($_POST['post_title']) ||
                          !empty($_POST['post_content']) ||
                          !empty($_POST['post_excerpt']) ||
                          !empty($_POST['_regular_price']) ||
                          !empty($_POST['_sku']) ||
                          (isset($_POST['tax_input']['product_cat']) && !empty($_POST['tax_input']['product_cat'])) ||
                          (isset($_POST['tax_input']['product_tag']) && !empty($_POST['tax_input']['product_tag'])) ||
                          (isset($_POST['tax_input']['product_brand']) && !empty($_POST['tax_input']['product_brand']));

            if (!$has_content) {
                return; // 没有内容的新产品，跳过审核
            }
        }

        // 移除绕过审核选项 - 所有产品更改都需要审核
        // 注释掉原来的绕过逻辑
        /*
        if (current_user_can('final_approve_product_changes') &&
            isset($_POST['prs_bypass_review']) && $_POST['prs_bypass_review'] === '1') {
            return;
        }
        */

        try {
            // 跳过审核系统内部操作
            if ($this->processing_review) {
                return;
            }

            // 检查是否已有待审核记录
            $existing_review = PRS_Database::get_product_pending_review($post_id);
            if ($existing_review) {
                wp_die(__('该产品已有待审核的修改，请等待审核完成后再进行修改。', 'product-review-system'));
            }

            // 获取原始产品数据
            $original_product = wc_get_product($post_id);
            if (!$original_product) {
                return;
            }

            $original_data = $this->get_product_data($original_product);

            // 获取修改后的数据，传入原始数据作为参考
            $modified_data = $this->get_modified_product_data($_POST, $original_data);

            // 比较数据，生成变更摘要
            $change_summary = $this->generate_change_summary($original_data, $modified_data);

            // 如果没有实质性变更，允许保存
            if (empty($change_summary) || trim($change_summary) === '') {
                return;
            }

            // 移除新产品检测逻辑，所有产品都按现有产品处理
            // 创建审核记录
            $review_data = array(
                'product_id' => $post_id,
                'original_data' => json_encode($original_data),
                'modified_data' => json_encode($modified_data),
                'change_summary' => $change_summary,
                'submitter_id' => get_current_user_id(),
                'is_new_product' => 0  // 固定为0，不再显示新产品标签
            );

            $review_id = PRS_Database::add_review($review_data);

            if ($review_id) {
                // 发送通知给审核员
                PRS_Notifications::notify_reviewers($review_id);

                // 使用更可靠的重定向方式
                $redirect_url = admin_url('admin.php?page=product-review-system&action=view&id=' . $review_id . '&submitted=1');

                // 方法1：存储重定向URL，在下次页面加载时处理
                set_transient('prs_redirect_after_submit_' . get_current_user_id(), $redirect_url, 60);

                // 方法2：使用JavaScript重定向作为备用
                add_action('admin_footer', function() use ($redirect_url) {
                    echo '<script type="text/javascript">
                        setTimeout(function() {
                            window.location.href = "' . esc_url($redirect_url) . '";
                        }, 100);
                    </script>';
                });

                // 方法3：立即重定向（如果headers还没发送）
                if (!headers_sent()) {
                    wp_redirect($redirect_url);
                    exit;
                } else {
                    // 如果headers已发送，使用wp_die显示成功消息和链接
                    wp_die(
                        sprintf(
                            __('产品修改已提交审核成功！<br><br><a href="%s" class="button button-primary">查看审核详情</a>', 'product-review-system'),
                            esc_url($redirect_url)
                        ),
                        __('提交成功', 'product-review-system'),
                        array('response' => 200, 'back_link' => false)
                    );
                }
            } else {
                // 记录详细错误信息
                global $wpdb;
                $error_msg = $wpdb->last_error ? $wpdb->last_error : '未知数据库错误';
                error_log('PRS: Failed to create review record. Error: ' . $error_msg);
                error_log('PRS: Review data: ' . print_r($review_data, true));

                wp_die(sprintf(__('创建审核记录失败：%s。请重试或联系管理员。', 'product-review-system'), $error_msg));
            }

        } catch (Exception $e) {
            // 捕获任何异常，记录错误并显示友好的错误信息
            error_log('PRS: Exception in handle_product_save: ' . $e->getMessage());
            error_log('PRS: Exception trace: ' . $e->getTraceAsString());

            // 显示友好的错误信息，而不是白屏
            wp_die(sprintf(__('产品审核系统遇到错误：%s。请刷新页面重试或联系管理员。', 'product-review-system'), $e->getMessage()));
        }
    }



    /**
     * 获取产品数据
     */
    private function get_product_data($product) {
        // 改进新产品检测逻辑 - 使用多重验证避免误判
        $post = get_post($product->get_id());
        $is_new_product = false;

        if ($post) {
            // 初步检查：只有auto-draft状态才可能是新产品
            if ($post->post_status === 'auto-draft') {
                // 进一步验证：检查产品是否真的是空的
                $product_name = trim($product->get_name());
                $product_sku = trim($product->get_sku());
                $product_price = $product->get_regular_price();
                $product_categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'ids'));
                $product_downloads = $product->get_downloads();

                // 只有当产品真正为空时才认为是新产品
                if (empty($product_name) && empty($product_sku) && empty($product_price) &&
                    empty($product_categories) && empty($product_downloads)) {
                    $is_new_product = true;
                    error_log('PRS: Confirmed new product (auto-draft + empty content) for ID: ' . $product->get_id());
                } else {
                    $is_new_product = false;
                    error_log('PRS: Override new product detection - auto-draft but has content for ID: ' . $product->get_id());
                }
            } else {
                $is_new_product = false;
                error_log('PRS: Detected existing product (status: ' . $post->post_status . ') for ID: ' . $product->get_id());
            }
        }

        $data = array(
            'name' => $product->get_name(),
            'description' => $product->get_description(),
            'short_description' => $product->get_short_description(),
            'sku' => $product->get_sku(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price(),
            'stock_quantity' => $product->get_stock_quantity(),
            'stock_status' => $product->get_stock_status(),
            'weight' => $product->get_weight(),
            'length' => $product->get_length(),
            'width' => $product->get_width(),
            'height' => $product->get_height(),
            'categories' => wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'ids')),
            'tags' => wp_get_post_terms($product->get_id(), 'product_tag', array('fields' => 'ids')),
            'brands' => wp_get_post_terms($product->get_id(), 'product_brand', array('fields' => 'ids')),
            'status' => $product->get_status(),
            'featured' => $product->get_featured(),
            'catalog_visibility' => $product->get_catalog_visibility(),
            'virtual' => $product->get_virtual(),
            'downloadable' => $product->get_downloadable(),
            'product_type' => $product->get_type(), // 添加产品类型
        );

        // 记录虚拟和可下载状态的原始值用于调试（仅在调试模式下）
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PRS: Original product data - Virtual: ' . ($data['virtual'] ? 'yes' : 'no') . ', Downloadable: ' . ($data['downloadable'] ? 'yes' : 'no') . ' for product ID: ' . $product->get_id());
        }

        // 如果是新产品，将所有字段设为空值以便正确显示变更
        // 但保留关键的判定字段，避免循环依赖问题
        if ($is_new_product) {
            // 保存关键字段，避免被清空
            $preserved_fields = array(
                'status' => $data['status'], // 状态字段是判定的关键，不能清空
                'product_type' => $data['product_type'] // 产品类型也很重要
            );

            foreach ($data as $key => $value) {
                // 跳过需要保留的关键字段
                if (isset($preserved_fields[$key])) {
                    continue;
                }

                if (is_array($value)) {
                    $data[$key] = array();
                } elseif (is_bool($value)) {
                    $data[$key] = false;
                } else {
                    $data[$key] = '';
                }
            }

            // 确保关键字段不被清空
            foreach ($preserved_fields as $key => $value) {
                $data[$key] = $value;
            }
        }

        // 获取自定义字段
        $meta_keys = array(
            '_product_attributes',
            '_default_attributes',
            '_product_image_gallery',
            '_thumbnail_id'
        );

        foreach ($meta_keys as $key) {
            $meta_value = get_post_meta($product->get_id(), $key, true);
            $data[$key] = $is_new_product ? '' : $meta_value;
        }

        // 处理可下载文件数据
        $data['downloadable_files'] = array();

        if (!$is_new_product && $product->get_downloadable()) {
            // 获取产品的下载文件
            $downloads = $product->get_downloads();

            if (!empty($downloads)) {
                // 将WooCommerce下载对象转换为数组格式
                $data['downloadable_files'] = $this->convert_downloads_to_array($downloads);
                error_log('PRS: Retrieved downloadable files for existing product - ' . count($data['downloadable_files']) . ' files');
            } else {
                // 如果通过产品对象获取不到，尝试直接从元数据获取
                $downloadable_files = get_post_meta($product->get_id(), '_downloadable_files', true);
                if (!empty($downloadable_files) && is_array($downloadable_files)) {
                    $data['downloadable_files'] = $downloadable_files;
                    error_log('PRS: Retrieved downloadable files from meta data - ' . count($data['downloadable_files']) . ' files');
                }
            }
        }

        return $data;
    }

    /**
     * 从POST数据获取修改后的产品数据
     *
     * @param array $post_data POST数据
     * @param array $original_data 原始产品数据，用于复选框状态的参考
     */
    private function get_modified_product_data($post_data, $original_data = array()) {
        // 处理描述字段的多种可能字段名
        $description = '';
        if (isset($post_data['post_content'])) {
            $description = $post_data['post_content'];
        } elseif (isset($post_data['content'])) {
            $description = $post_data['content'];
        }

        $short_description = '';
        if (isset($post_data['post_excerpt'])) {
            $short_description = $post_data['post_excerpt'];
        } elseif (isset($post_data['excerpt'])) {
            $short_description = $post_data['excerpt'];
        }

        // 增强的状态字段获取逻辑 - 检查多个可能的字段名
        $status = '';
        if (isset($post_data['post_status'])) {
            $status = $post_data['post_status'];
        } elseif (isset($post_data['product_status'])) {
            $status = $post_data['product_status'];
        } elseif (isset($post_data['_status'])) {
            $status = $post_data['_status'];
        } elseif (isset($post_data['status'])) {
            $status = $post_data['status'];
        }

        // 获取产品类型 - 检查多个可能的字段名
        $product_type = '';
        if (isset($post_data['product-type'])) {
            $product_type = $post_data['product-type'];
        } elseif (isset($post_data['product_type'])) {
            $product_type = $post_data['product_type'];
        } elseif (isset($post_data['_product_type'])) {
            $product_type = $post_data['_product_type'];
        }

        // 记录状态和产品类型获取过程用于调试
        error_log('PRS: Status field detection - post_status: ' . (isset($post_data['post_status']) ? $post_data['post_status'] : 'not set'));
        error_log('PRS: Status field detection - final status: ' . $status);
        error_log('PRS: Product type field detection - product-type: ' . (isset($post_data['product-type']) ? $post_data['product-type'] : 'not set'));
        error_log('PRS: Product type field detection - final product_type: ' . $product_type);

        // 记录虚拟和可下载状态的POST数据用于调试（仅在调试模式下）
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('PRS: Virtual field detection - _virtual: ' . (isset($post_data['_virtual']) ? $post_data['_virtual'] : 'not set'));
            error_log('PRS: Downloadable field detection - _downloadable: ' . (isset($post_data['_downloadable']) ? $post_data['_downloadable'] : 'not set'));
        }

        $data = array(
            'name' => isset($post_data['post_title']) ? $post_data['post_title'] : '',
            'description' => $description,
            'short_description' => $short_description,
            'status' => $status,
            'product_type' => $product_type,
            'sku' => isset($post_data['_sku']) ? $post_data['_sku'] : '',
            'regular_price' => isset($post_data['_regular_price']) ? $post_data['_regular_price'] : '',
            'sale_price' => isset($post_data['_sale_price']) ? $post_data['_sale_price'] : '',
            'stock_quantity' => isset($post_data['_stock']) ? $post_data['_stock'] : '',
            'stock_status' => isset($post_data['_stock_status']) ? $post_data['_stock_status'] : '',
            'weight' => isset($post_data['_weight']) ? $post_data['_weight'] : '',
            'length' => isset($post_data['_length']) ? $post_data['_length'] : '',
            'width' => isset($post_data['_width']) ? $post_data['_width'] : '',
            'height' => isset($post_data['_height']) ? $post_data['_height'] : '',
            'catalog_visibility' => isset($post_data['_visibility']) ? $post_data['_visibility'] : '',
        );

        // 处理复选框字段 - 改进逻辑以正确处理现有产品的状态
        // 对于复选框，我们需要区分"未设置"和"明确设置为否"

        // 特色产品
        if (isset($post_data['_featured'])) {
            $data['featured'] = $post_data['_featured'] === 'yes';
        } else {
            // 如果POST数据中没有这个字段，使用原始数据的值
            $data['featured'] = isset($original_data['featured']) ? $original_data['featured'] : false;
        }

        // 虚拟产品 - 修复复选框状态获取逻辑
        // WooCommerce复选框：选中时发送 '_virtual' => 'on' 或 '1'，未选中时不发送该字段
        if (isset($post_data['_virtual'])) {
            // 复选框被选中
            $data['virtual'] = true;
        } else {
            // 复选框未选中，设置为false
            $data['virtual'] = false;
        }

        // 可下载产品 - 修复复选框状态获取逻辑
        // WooCommerce复选框：选中时发送 '_downloadable' => 'on' 或 '1'，未选中时不发送该字段
        if (isset($post_data['_downloadable'])) {
            // 复选框被选中
            $data['downloadable'] = true;
        } else {
            // 复选框未选中，但如果有下载文件，可能需要自动设置为可下载
            $has_download_files = $this->check_has_download_files($post_data);

            if ($has_download_files) {
                // 如果有下载文件但复选框未选中，可能是用户忘记选中，自动设置为可下载
                $data['downloadable'] = true;
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('PRS: Auto-setting downloadable to true based on file presence (checkbox not checked)');
                }
            } else {
                // 没有下载文件且复选框未选中，设置为false
                $data['downloadable'] = false;
            }
        }

        // 处理分类、标签和品牌 - 检查多种可能的字段名
        $data['categories'] = array();
        $data['tags'] = array();
        $data['brands'] = array();

        // 检查直接的字段名
        if (isset($post_data['product_cat'])) {
            $data['categories'] = $post_data['product_cat'];
        }
        if (isset($post_data['product_tag'])) {
            $data['tags'] = $post_data['product_tag'];
        }
        if (isset($post_data['product_brand'])) {
            $data['brands'] = $post_data['product_brand'];
        }

        // 检查 tax_input 字段
        if (isset($post_data['tax_input'])) {
            if (isset($post_data['tax_input']['product_cat'])) {
                $data['categories'] = $post_data['tax_input']['product_cat'];
            }
            if (isset($post_data['tax_input']['product_tag'])) {
                $data['tags'] = $post_data['tax_input']['product_tag'];
            }
            if (isset($post_data['tax_input']['product_brand'])) {
                $data['brands'] = $post_data['tax_input']['product_brand'];
            }
        }

        // 确保是数组格式
        $data['categories'] = is_array($data['categories']) ? $data['categories'] : array();
        $data['tags'] = is_array($data['tags']) ? $data['tags'] : array();
        $data['brands'] = is_array($data['brands']) ? $data['brands'] : array();

        // 处理自定义字段
        $meta_keys = array(
            '_product_attributes',
            '_default_attributes',
            '_product_image_gallery',
            '_thumbnail_id'
        );

        foreach ($meta_keys as $key) {
            $data[$key] = isset($post_data[$key]) ? $post_data[$key] : '';
        }

        // 处理可下载文件 - 支持WooCommerce标准格式
        $data['downloadable_files'] = array();

        // 首先检查是否有直接的 _downloadable_files 数据（用于现有数据兼容）
        if (isset($post_data['_downloadable_files']) && is_array($post_data['_downloadable_files'])) {
            $data['downloadable_files'] = $post_data['_downloadable_files'];
            error_log('PRS: Found direct _downloadable_files data - ' . count($data['downloadable_files']) . ' files');
        }
        // 否则检查WooCommerce标准的三个数组格式
        elseif (isset($post_data['_wc_file_names']) || isset($post_data['_wc_file_urls']) || isset($post_data['_wc_file_hashes'])) {
            $file_names = isset($post_data['_wc_file_names']) ? $post_data['_wc_file_names'] : array();
            $file_urls = isset($post_data['_wc_file_urls']) ? $post_data['_wc_file_urls'] : array();
            $file_hashes = isset($post_data['_wc_file_hashes']) ? $post_data['_wc_file_hashes'] : array();

            // 确保所有数组都是数组格式
            if (!is_array($file_names)) $file_names = array();
            if (!is_array($file_urls)) $file_urls = array();
            if (!is_array($file_hashes)) $file_hashes = array();

            // 获取最大数组长度，以防数组长度不一致
            $max_count = max(count($file_names), count($file_urls), count($file_hashes));

            if ($max_count > 0) {
                for ($i = 0; $i < $max_count; $i++) {
                    $name = isset($file_names[$i]) ? trim($file_names[$i]) : '';
                    $url = isset($file_urls[$i]) ? trim($file_urls[$i]) : '';
                    $hash = isset($file_hashes[$i]) ? trim($file_hashes[$i]) : '';

                    // 如果没有hash，生成一个
                    if (empty($hash) && !empty($url)) {
                        $hash = md5($url . time() . $i);
                    }

                    // 如果没有名称，从URL提取
                    if (empty($name) && !empty($url)) {
                        $name = basename(parse_url($url, PHP_URL_PATH));
                        if (empty($name)) {
                            $name = '下载文件 ' . ($i + 1);
                        }
                    }

                    // 只要有URL就添加文件
                    if (!empty($url) && !empty($hash)) {
                        $data['downloadable_files'][$hash] = array(
                            'name' => wp_strip_all_tags($name), // 使用wp_strip_all_tags代替sanitize_text_field，保持中文字符
                            'file' => esc_url_raw($url)
                        );
                    }
                }

                error_log('PRS: Converted WooCommerce download files format - ' . count($data['downloadable_files']) . ' files processed from ' . $max_count . ' entries');
            }
        }
        // 最后检查其他可能的文件格式
        else {
            // 检查是否有其他格式的文件数据
            $other_file_fields = array('downloadable_files', 'wc_downloadable_files', 'product_downloadable_files');
            foreach ($other_file_fields as $field) {
                if (isset($post_data[$field]) && is_array($post_data[$field]) && !empty($post_data[$field])) {
                    $data['downloadable_files'] = $post_data[$field];
                    error_log('PRS: Found downloadable files in field: ' . $field . ' - ' . count($data['downloadable_files']) . ' files');
                    break;
                }
            }
        }

        return $data;
    }

    /**
     * 检查POST数据中是否有下载文件
     */
    private function check_has_download_files($post_data) {
        // 检查直接的下载文件数据
        if (isset($post_data['_downloadable_files']) && !empty($post_data['_downloadable_files'])) {
            return true;
        }

        // 检查WooCommerce格式的文件URL
        if (isset($post_data['_wc_file_urls']) && !empty($post_data['_wc_file_urls'])) {
            $file_urls = is_array($post_data['_wc_file_urls']) ? $post_data['_wc_file_urls'] : array();
            foreach ($file_urls as $url) {
                if (!empty(trim($url))) {
                    return true;
                }
            }
        }

        // 检查其他可能的文件字段
        $other_file_fields = array('downloadable_files', 'wc_downloadable_files', 'product_downloadable_files');
        foreach ($other_file_fields as $field) {
            if (isset($post_data[$field]) && !empty($post_data[$field])) {
                return true;
            }
        }

        return false;
    }

    /**
     * 应用审核通过的修改（供审核系统调用）
     */
    public function apply_approved_changes($product_id, $modified_data) {
        // 设置标志，避免再次触发审核
        $this->processing_review = true;

        try {
            // 记录开始处理
            error_log('PRS: Starting to apply approved changes for product ID: ' . $product_id);

            // 获取当前产品对象
            $product = wc_get_product($product_id);
            if (!$product) {
                error_log('PRS: Product not found with ID: ' . $product_id);
                return false;
            }

            // 确定最终的产品状态
            $final_status = $this->determine_final_product_status($product, $modified_data);

            // 更新基本信息
            $post_data = array(
                'ID' => $product_id,
                'post_title' => $modified_data['name'],
                'post_content' => $modified_data['description'],
                'post_excerpt' => $modified_data['short_description'],
                'post_status' => $final_status
            );

            error_log('PRS: Updating product with final status: ' . $final_status);

            $update_result = wp_update_post($post_data);
            if (is_wp_error($update_result)) {
                error_log('PRS: Failed to update post: ' . $update_result->get_error_message());
                return false;
            }

            // 更新产品元数据
            $meta_fields = array(
                '_sku' => 'sku',
                '_regular_price' => 'regular_price',
                '_sale_price' => 'sale_price',
                '_stock' => 'stock_quantity',
                '_stock_status' => 'stock_status',
                '_weight' => 'weight',
                '_length' => 'length',
                '_width' => 'width',
                '_height' => 'height',
                '_featured' => 'featured',
                '_visibility' => 'catalog_visibility',
                '_virtual' => 'virtual',
                '_downloadable' => 'downloadable'
            );

            foreach ($meta_fields as $meta_key => $data_key) {
                if (isset($modified_data[$data_key])) {
                    $value = $modified_data[$data_key];
                    // 处理布尔值
                    if (in_array($data_key, array('featured', 'virtual', 'downloadable'))) {
                        $value = $value ? 'yes' : 'no';
                    }
                    update_post_meta($product_id, $meta_key, $value);
                }
            }

            // 特别处理价格字段 - 确保 _price 字段正确设置
            $this->update_product_price_fields($product_id, $modified_data);

            // 更新产品类型（非常重要！）
            if (isset($modified_data['product_type']) && !empty($modified_data['product_type'])) {
                $new_product_type = $modified_data['product_type'];
                $current_product_type = $product->get_type();

                if ($current_product_type !== $new_product_type) {
                    // 设置产品类型分类法
                    wp_set_object_terms($product_id, $new_product_type, 'product_type');

                    // 清理可变产品的变体（如果从可变产品改为其他类型）
                    if ($current_product_type === 'variable' && $new_product_type !== 'variable') {
                        $this->cleanup_variable_product_data($product_id);
                    }

                    error_log('PRS: Updated product type from ' . $current_product_type . ' to ' . $new_product_type . ' for product ID: ' . $product_id);
                }
            }

            // 更新分类、标签和品牌
            if (isset($modified_data['categories'])) {
                wp_set_post_terms($product_id, $modified_data['categories'], 'product_cat');
            }

            if (isset($modified_data['tags'])) {
                wp_set_post_terms($product_id, $modified_data['tags'], 'product_tag');
            }

            if (isset($modified_data['brands'])) {
                wp_set_post_terms($product_id, $modified_data['brands'], 'product_brand');
            }

            // 处理可下载文件
            if (isset($modified_data['downloadable']) && $modified_data['downloadable']) {
                // 产品是可下载的
                if (isset($modified_data['downloadable_files']) && !empty($modified_data['downloadable_files'])) {
                    update_post_meta($product_id, '_downloadable_files', $modified_data['downloadable_files']);
                    error_log('PRS: Updated downloadable files for product ID: ' . $product_id . ' - ' . count($modified_data['downloadable_files']) . ' files');
                } else {
                    // 可下载产品但没有文件，清除现有文件
                    delete_post_meta($product_id, '_downloadable_files');
                    error_log('PRS: Cleared downloadable files for product ID: ' . $product_id . ' (no files provided)');
                }

                // 确保下载相关的元数据正确设置
                $this->ensure_download_metadata($product_id, $modified_data);
            } else {
                // 产品不是可下载的，清除所有下载相关数据
                delete_post_meta($product_id, '_downloadable_files');
                delete_post_meta($product_id, '_download_limit');
                delete_post_meta($product_id, '_download_expiry');
                error_log('PRS: Cleared all download metadata for non-downloadable product ID: ' . $product_id);
            }

            // 强制同步产品状态和可见性
            $this->force_sync_product_status($product_id, $modified_data);

            // 清除所有相关缓存
            $this->clear_all_product_caches($product_id);

            // 触发WooCommerce产品更新钩子
            do_action('woocommerce_update_product', $product_id);
            do_action('prs_product_changes_applied', $product_id, $modified_data);

            error_log('PRS: Successfully applied all changes for product ID: ' . $product_id);
            return true;

        } catch (Exception $e) {
            error_log('PRS: Failed to apply approved changes: ' . $e->getMessage());
            return false;
        } finally {
            // 重置标志
            $this->processing_review = false;
        }
    }

    /**
     * 更新产品价格字段
     */
    private function update_product_price_fields($product_id, $modified_data) {
        try {
            $regular_price = isset($modified_data['regular_price']) ? $modified_data['regular_price'] : '';
            $sale_price = isset($modified_data['sale_price']) ? $modified_data['sale_price'] : '';

            // 更新常规价格 - 特殊处理价格为0的情况
            if ($regular_price !== '' && $regular_price !== null) {
                update_post_meta($product_id, '_regular_price', $regular_price);
                error_log('PRS: Updated regular price to: ' . $regular_price);
            }

            // 更新促销价格 - 特殊处理价格为0的情况
            if ($sale_price !== '' && $sale_price !== null) {
                update_post_meta($product_id, '_sale_price', $sale_price);
                error_log('PRS: Updated sale price to: ' . $sale_price);
            }

            // 确定当前价格（_price字段）- 特殊处理价格为0的情况
            $current_price = '';
            if ($sale_price !== '' && $sale_price !== null && is_numeric($sale_price)) {
                // 如果有促销价格，使用促销价格
                $current_price = $sale_price;
            } elseif ($regular_price !== '' && $regular_price !== null && is_numeric($regular_price)) {
                // 否则使用常规价格
                $current_price = $regular_price;
            }

            // 更新当前价格（这是WooCommerce用于显示和购买逻辑的关键字段）
            if ($current_price !== '' && $current_price !== null) {
                update_post_meta($product_id, '_price', $current_price);
                error_log('PRS: Updated current price to: ' . $current_price);
            } else {
                // 如果没有有效价格，清除_price字段
                delete_post_meta($product_id, '_price');
                error_log('PRS: Cleared current price');
            }

            // 强制刷新产品对象
            $product = wc_get_product($product_id);
            if ($product) {
                $product->read_meta_data(true);
                // 触发价格更新钩子
                do_action('woocommerce_product_object_updated_props', $product, array('regular_price', 'sale_price', 'price'));
            }

        } catch (Exception $e) {
            error_log('PRS: Failed to update product price fields: ' . $e->getMessage());
        }
    }

    /**
     * 确定最终的产品状态
     */
    private function determine_final_product_status($product, $modified_data) {
        $current_status = $product->get_status();
        $requested_status = isset($modified_data['status']) ? $modified_data['status'] : '';

        error_log('PRS: Determining final status - Current: ' . $current_status . ', Requested: ' . $requested_status);
        error_log('PRS: Modified data: ' . print_r($modified_data, true));

        // 对于审核通过的情况，我们总是要发布产品，忽略 modified_data 中的 status
        // 检查是否是新产品或待审核产品（需要发布的状态）
        $should_publish_statuses = array('draft', 'auto-draft', 'pending');

        if (in_array($current_status, $should_publish_statuses)) {
            error_log('PRS: Product in publishable status (' . $current_status . '), setting to publish');
            return 'publish';
        }

        // 如果当前是已发布状态，保持发布
        if ($current_status === 'publish') {
            error_log('PRS: Product already published, keeping publish status');
            return 'publish';
        }

        // 如果有明确的状态请求且不是待发布状态，使用请求的状态
        if (!empty($requested_status) && !in_array($requested_status, $should_publish_statuses)) {
            error_log('PRS: Using requested status: ' . $requested_status);
            return $requested_status;
        }

        // 默认发布
        error_log('PRS: Defaulting to publish status');
        return 'publish';
    }

    /**
     * 强制同步产品状态
     */
    private function force_sync_product_status($product_id, $modified_data) {
        try {
            $current_status = get_post_status($product_id);
            $product = wc_get_product($product_id);

            // 重新确定最终状态（以防第一次更新失败）
            $final_status = $this->determine_final_product_status($product, $modified_data);

            error_log('PRS: Force sync - Current status: ' . $current_status . ', Final status: ' . $final_status);

            if ($current_status !== $final_status) {
                wp_update_post(array(
                    'ID' => $product_id,
                    'post_status' => $final_status
                ));

                error_log('PRS: Force updated product status from ' . $current_status . ' to ' . $final_status);
            }

            // 如果是发布状态，确保产品可见性正确
            if ($final_status === 'publish') {
                // 确保产品在目录中可见
                $visibility = get_post_meta($product_id, '_visibility', true);
                if (empty($visibility) || $visibility === 'hidden') {
                    update_post_meta($product_id, '_visibility', 'visible');
                    error_log('PRS: Set product visibility to visible for published product');
                }

                // 确保目录可见性正确
                $catalog_visibility = get_post_meta($product_id, '_catalog_visibility', true);
                if (empty($catalog_visibility) || $catalog_visibility === 'hidden') {
                    update_post_meta($product_id, '_catalog_visibility', 'visible');
                    error_log('PRS: Set catalog visibility to visible for published product');
                }

                // 确保产品在搜索中可见
                update_post_meta($product_id, '_search_visibility', 'visible');
            }

            // 最终验证状态
            $final_check_status = get_post_status($product_id);
            error_log('PRS: Final status verification: ' . $final_check_status);

        } catch (Exception $e) {
            error_log('PRS: Failed to force sync product status: ' . $e->getMessage());
        }
    }

    /**
     * 清理可变产品数据（当从可变产品改为其他类型时）
     */
    private function cleanup_variable_product_data($product_id) {
        try {
            // 删除所有变体
            $variations = get_posts(array(
                'post_type' => 'product_variation',
                'post_parent' => $product_id,
                'numberposts' => -1,
                'post_status' => 'any'
            ));

            foreach ($variations as $variation) {
                wp_delete_post($variation->ID, true);
                error_log('PRS: Deleted variation ID: ' . $variation->ID);
            }

            // 删除可变产品相关的元数据
            $variable_meta_keys = array(
                '_product_attributes',
                '_default_attributes',
                '_children'
            );

            foreach ($variable_meta_keys as $meta_key) {
                delete_post_meta($product_id, $meta_key);
            }

            error_log('PRS: Cleaned up variable product data for product ID: ' . $product_id);

        } catch (Exception $e) {
            error_log('PRS: Failed to cleanup variable product data: ' . $e->getMessage());
        }
    }

    /**
     * 清除所有产品相关缓存
     */
    private function clear_all_product_caches($product_id) {
        try {
            // 清除WordPress缓存
            wp_cache_delete($product_id, 'posts');
            wp_cache_delete($product_id, 'post_meta');

            // 清除WooCommerce缓存
            wc_delete_product_transients($product_id);

            // 清除价格相关的缓存
            wp_cache_delete('wc_product_' . $product_id, 'products');
            wp_cache_delete('woocommerce_product_' . $product_id, 'products');

            // 清除对象缓存
            if (function_exists('wp_cache_delete_group')) {
                wp_cache_delete_group('woocommerce-product-' . $product_id);
            }

            // 强制重新加载产品对象
            $product = wc_get_product($product_id);
            if ($product) {
                $product->read_meta_data(true);
                // 强制重新计算价格
                $product->get_price('edit');
            }

            // 清除WooCommerce的产品查询缓存
            if (function_exists('wc_delete_shop_order_transients')) {
                wc_delete_shop_order_transients();
            }

            error_log('PRS: Cleared all caches for product ID: ' . $product_id);
        } catch (Exception $e) {
            error_log('PRS: Failed to clear product caches: ' . $e->getMessage());
        }
    }

    /**
     * 生成变更摘要
     */
    private function generate_change_summary($original, $modified) {
        $changes = array();

        // 移除新产品检测逻辑，避免误判问题
        // 所有产品修改都按现有产品处理，只显示实际变更内容
        $is_new_product = false;
        error_log('PRS: Treating all submissions as existing product modifications');

        $field_labels = array(
            'name' => __('产品名称', 'product-review-system'),
            'description' => __('产品描述', 'product-review-system'),
            'short_description' => __('简短描述', 'product-review-system'),
            'sku' => __('SKU', 'product-review-system'),
            'regular_price' => __('常规价格', 'product-review-system'),
            'sale_price' => __('促销价格', 'product-review-system'),
            'stock_quantity' => __('库存数量', 'product-review-system'),
            'stock_status' => __('库存状态', 'product-review-system'),
            'weight' => __('重量', 'product-review-system'),
            'length' => __('长度', 'product-review-system'),
            'width' => __('宽度', 'product-review-system'),
            'height' => __('高度', 'product-review-system'),
            'categories' => __('产品分类', 'product-review-system'),
            'tags' => __('产品标签', 'product-review-system'),
            'brands' => __('产品品牌', 'product-review-system'),
            'status' => __('产品状态', 'product-review-system'),
            'product_type' => __('产品类型', 'product-review-system'),
            'featured' => __('特色产品', 'product-review-system'),
            'catalog_visibility' => __('目录可见性', 'product-review-system'),
            'virtual' => __('虚拟产品', 'product-review-system'),
            'downloadable' => __('可下载产品', 'product-review-system'),
            'downloadable_files' => __('下载文件', 'product-review-system'),
            // 添加图片字段的标签映射
            '_thumbnail_id' => __('产品主图', 'product-review-system'),
            '_product_image_gallery' => __('产品图库', 'product-review-system'),
            '_product_attributes' => __('产品属性', 'product-review-system'),
            '_default_attributes' => __('默认属性', 'product-review-system'),
        );

        foreach ($field_labels as $field => $label) {
            $original_value = isset($original[$field]) ? $original[$field] : '';
            $modified_value = isset($modified[$field]) ? $modified[$field] : '';

            // 标准化空值处理
            $original_value = $this->normalize_value($original_value);
            $modified_value = $this->normalize_value($modified_value);

            // 处理数组比较
            if (is_array($original_value) || is_array($modified_value)) {
                $original_value = is_array($original_value) ? $original_value : array();
                $modified_value = is_array($modified_value) ? $modified_value : array();

                // 排序数组以确保比较准确性
                sort($original_value);
                sort($modified_value);

                $should_show_array_change = false;

                if ($is_new_product) {
                    // 新产品：只要修改值不为空就显示
                    $should_show_array_change = !empty($modified_value);
                } else {
                    // 现有产品：比较数组内容
                    // 对于分类、标签和品牌，需要特殊处理
                    if (in_array($field, array('categories', 'tags', 'brands'))) {
                        // 清理数组，移除空值和无效值
                        $original_clean = $this->clean_taxonomy_array($original_value);
                        $modified_clean = $this->clean_taxonomy_array($modified_value);

                        // 如果两个数组都为空，则认为无变更
                        if (empty($original_clean) && empty($modified_clean)) {
                            $should_show_array_change = false;
                        } else {
                            // 比较清理后的数组
                            $should_show_array_change = !$this->arrays_equal($original_clean, $modified_clean);
                        }
                    } else {
                        // 其他数组使用标准比较
                        $should_show_array_change = ($original_value !== $modified_value);
                    }
                }

                if ($should_show_array_change) {
                    // 对于分类和标签，转换ID为名称
                    if ($field === 'categories') {
                        $original_display = $this->convert_category_ids_to_names($original_value);
                        $modified_display = $this->convert_category_ids_to_names($modified_value);
                    } elseif ($field === 'tags') {
                        $original_display = $this->convert_tag_ids_to_names($original_value);
                        $modified_display = $this->convert_tag_ids_to_names($modified_value);
                    } elseif ($field === 'brands') {
                        $original_display = $this->convert_brand_ids_to_names($original_value);
                        $modified_display = $this->convert_brand_ids_to_names($modified_value);
                    } elseif ($field === 'downloadable_files') {
                        $original_display = $this->format_downloadable_files_display($original_value);
                        $modified_display = $this->format_downloadable_files_display($modified_value);
                    } else {
                        $original_display = empty($original_value) ? __('无', 'product-review-system') : implode(', ', $original_value);
                        $modified_display = empty($modified_value) ? __('无', 'product-review-system') : implode(', ', $modified_value);
                    }

                    $changes[] = sprintf(__('%s: "%s" → "%s"', 'product-review-system'),
                        $label, $original_display, $modified_display);
                }
            } else {
                // 对于新产品，只要修改值不为空就显示变更
                // 对于现有产品，严格比较原始值和修改值
                $should_show_change = false;

                if ($is_new_product) {
                    // 新产品：只要修改值不为空就显示
                    if (is_array($modified_value)) {
                        $should_show_change = !empty($modified_value);
                    } else {
                        $should_show_change = ($modified_value !== '' && $modified_value !== null);
                    }
                } else {
                    // 现有产品：严格比较，避免类型转换导致的误判
                    $should_show_change = ($original_value !== $modified_value);
                }

                if ($should_show_change) {
                    // 特殊处理布尔值显示
                    if (in_array($field, array('featured', 'virtual', 'downloadable'))) {
                        $original_display = $original_value ? __('是', 'product-review-system') : __('否', 'product-review-system');
                        $modified_display = $modified_value ? __('是', 'product-review-system') : __('否', 'product-review-system');
                    } elseif ($field === 'product_type') {
                        // 特殊处理产品类型显示
                        $original_display = $this->localize_product_type($original_value);
                        $modified_display = $this->localize_product_type($modified_value);
                    } elseif (in_array($field, array('regular_price', 'sale_price'))) {
                        // 特殊处理价格字段显示 - 价格为0时应显示"0"而不是"无"
                        $original_display = $this->format_price_display($original_value);
                        $modified_display = $this->format_price_display($modified_value);
                    } elseif ($field === '_thumbnail_id') {
                        // 特殊处理产品主图显示
                        $original_display = $this->format_image_display($original_value);
                        $modified_display = $this->format_image_display($modified_value);
                    } elseif ($field === '_product_image_gallery') {
                        // 特殊处理产品图库显示
                        $original_display = $this->format_gallery_display($original_value);
                        $modified_display = $this->format_gallery_display($modified_value);
                    } else {
                        // 限制显示长度，避免过长的内容
                        $original_display = $this->truncate_for_display($original_value);
                        $modified_display = $this->truncate_for_display($modified_value);

                        // 如果原始值为空，显示为"无"
                        if (empty($original_display)) {
                            $original_display = __('无', 'product-review-system');
                        }
                        if (empty($modified_display)) {
                            $modified_display = __('无', 'product-review-system');
                        }

                        // 特殊值的中文化处理
                        $original_display = $this->localize_value($original_display);
                        $modified_display = $this->localize_value($modified_display);
                    }

                    $changes[] = sprintf(__('%s: "%s" → "%s"', 'product-review-system'),
                        $label, $original_display, $modified_display);
                }
            }
        }

        return implode("\n", $changes);
    }

    /**
     * 检查是否是新产品数据
     *
     * 改进的判断逻辑：
     * 1. 最高优先级：检查产品状态 - 已发布的产品绝对不是新产品
     * 2. 检查是否有产品ID信息
     * 3. 检查关键字段的组合情况
     * 4. 避免误判现有产品为新产品
     * 5. 更严格的验证标准，只有真正空白的产品才被认为是新产品
     */
    private function is_new_product_data($data) {
        // 最高优先级：检查产品状态
        // 任何已发布、私密、待审核等状态的产品都绝对不是新产品
        if (isset($data['status']) && !empty($data['status'])) {
            $status = $data['status'];
            // 只有 draft 和 auto-draft 状态才可能是新产品
            if ($status !== 'draft' && $status !== 'auto-draft') {
                error_log('PRS: Product status is "' . $status . '" - definitely not a new product');
                return false;
            }
        }

        // 如果数据中包含产品ID相关信息，通常不是新产品
        if (isset($data['id']) && !empty($data['id'])) {
            return false;
        }

        // 更严格的检查：如果有产品名称，几乎肯定不是新产品
        if (isset($data['name']) && !empty(trim($data['name']))) {
            return false;
        }

        // 检查关键字段组合 - 降低阈值，只要有一个关键字段不为空就不是新产品
        $key_fields = array('sku', 'regular_price');
        foreach ($key_fields as $field) {
            $value = isset($data[$field]) ? $data[$field] : '';
            $normalized = $this->normalize_value($value);

            if (!empty($normalized)) {
                return false; // 有任何关键字段不为空就不是新产品
            }
        }

        // 特殊检查：如果有分类、标签或品牌信息，通常不是新产品
        $taxonomy_fields = array('categories', 'tags', 'brands');
        foreach ($taxonomy_fields as $field) {
            if (isset($data[$field]) && is_array($data[$field]) && !empty($data[$field])) {
                return false;
            }
        }

        // 如果有下载文件信息，通常不是新产品
        if (isset($data['downloadable_files']) && is_array($data['downloadable_files']) && !empty($data['downloadable_files'])) {
            return false;
        }

        // 检查描述字段
        if (isset($data['description']) && !empty(trim($data['description']))) {
            return false;
        }

        if (isset($data['short_description']) && !empty(trim($data['short_description']))) {
            return false;
        }

        // 只有在所有关键信息都为空的情况下，才认为是新产品
        return true;
    }

    /**
     * 本地化产品类型显示
     */
    private function localize_product_type($type) {
        if (empty($type)) {
            return __('无', 'product-review-system');
        }

        $type_labels = array(
            'simple' => __('单一产品', 'product-review-system'),
            'variable' => __('可变产品', 'product-review-system'),
            'grouped' => __('组合产品', 'product-review-system'),
            'external' => __('外部/联盟产品', 'product-review-system'),
            'variation' => __('产品变体', 'product-review-system')
        );

        return isset($type_labels[$type]) ? $type_labels[$type] : $type;
    }

    /**
     * 转换分类ID为名称
     */
    private function convert_category_ids_to_names($ids) {
        if (empty($ids)) {
            return __('无', 'product-review-system');
        }

        $names = array();
        foreach ($ids as $id) {
            $term = get_term($id, 'product_cat');
            if ($term && !is_wp_error($term)) {
                $names[] = $term->name;
            }
        }

        return empty($names) ? __('无', 'product-review-system') : implode(', ', $names);
    }

    /**
     * 转换标签ID为名称
     */
    private function convert_tag_ids_to_names($ids) {
        if (empty($ids)) {
            return __('无', 'product-review-system');
        }

        $names = array();
        foreach ($ids as $id) {
            $term = get_term($id, 'product_tag');
            if ($term && !is_wp_error($term)) {
                $names[] = $term->name;
            }
        }

        return empty($names) ? __('无', 'product-review-system') : implode(', ', $names);
    }

    /**
     * 转换品牌ID为名称
     */
    private function convert_brand_ids_to_names($ids) {
        if (empty($ids)) {
            return __('无', 'product-review-system');
        }

        $names = array();
        foreach ($ids as $id) {
            $term = get_term($id, 'product_brand');
            if ($term && !is_wp_error($term)) {
                $names[] = $term->name;
            }
        }

        return empty($names) ? __('无', 'product-review-system') : implode(', ', $names);
    }

    /**
     * 标准化值，处理空值和类型转换
     */
    private function normalize_value($value) {
        // 处理null和空字符串
        if (is_null($value) || $value === '' || $value === false) {
            return '';
        }

        // 处理数组
        if (is_array($value)) {
            return $value;
        }

        // 处理数字字符串，保持原始格式
        if (is_numeric($value)) {
            return (string) $value;
        }

        // 处理布尔值
        if (is_bool($value)) {
            return $value;
        }

        // 处理字符串，去除首尾空格
        if (is_string($value)) {
            $trimmed = trim($value);
            // 如果trim后为空，返回空字符串
            return $trimmed === '' ? '' : $trimmed;
        }

        return $value;
    }

    /**
     * 清理分类数组，移除空值和无效值，并标准化类型
     */
    private function clean_taxonomy_array($array) {
        // 确保输入是数组
        if (!is_array($array)) {
            return array();
        }

        // 过滤空值和无效值
        $array = array_filter($array, function($v) {
            return $v !== '' && $v !== null && $v !== 0 && $v !== '0';
        });

        // 将所有元素转换为整数，确保类型一致
        $array = array_map('intval', $array);

        // 移除0值（转换后的无效值）
        $array = array_filter($array, function($v) { return $v > 0; });

        // 重新排序
        sort($array);

        return $array;
    }

    /**
     * 比较两个数组是否相等（忽略顺序）
     */
    private function arrays_equal($array1, $array2) {
        if (count($array1) !== count($array2)) {
            return false;
        }

        // 如果两个数组都为空，则认为相同
        if (empty($array1) && empty($array2)) {
            return true;
        }

        // 排序后比较
        sort($array1);
        sort($array2);

        return $array1 === $array2;
    }

    /**
     * 截断显示内容，避免过长
     */
    private function truncate_for_display($value, $length = 50) {
        if (!is_string($value)) {
            return $value;
        }

        if (mb_strlen($value) > $length) {
            return mb_substr($value, 0, $length) . '...';
        }

        return $value;
    }

    /**
     * 本地化值显示
     */
    private function localize_value($value) {
        // 库存状态中文化
        $stock_status_map = array(
            'instock' => __('有库存', 'product-review-system'),
            'outofstock' => __('缺货', 'product-review-system'),
            'onbackorder' => __('延期交货', 'product-review-system'),
        );

        // 产品状态中文化
        $post_status_map = array(
            'publish' => __('已发布', 'product-review-system'),
            'draft' => __('草稿', 'product-review-system'),
            'private' => __('私密', 'product-review-system'),
            'pending' => __('待审核', 'product-review-system'),
            'trash' => __('回收站', 'product-review-system'),
        );

        // 目录可见性中文化
        $visibility_map = array(
            'visible' => __('可见', 'product-review-system'),
            'catalog' => __('仅目录', 'product-review-system'),
            'search' => __('仅搜索', 'product-review-system'),
            'hidden' => __('隐藏', 'product-review-system'),
        );

        // 检查并转换值
        if (isset($stock_status_map[$value])) {
            return $stock_status_map[$value];
        }

        if (isset($post_status_map[$value])) {
            return $post_status_map[$value];
        }

        if (isset($visibility_map[$value])) {
            return $visibility_map[$value];
        }

        return $value;
    }

    /**
     * 添加审核状态元框
     */
    public function add_review_status_meta_box() {
        add_meta_box(
            'prs-review-status',
            __('产品审核状态', 'product-review-system'),
            array($this, 'display_review_status_meta_box'),
            'product',
            'side',
            'high'
        );
    }

    /**
     * 显示审核状态元框
     */
    public function display_review_status_meta_box($post) {
        $review = PRS_Database::get_product_pending_review($post->ID);
        
        if ($review) {
            $status_options = PRS_Database::get_status_options();
            $status_label = isset($status_options[$review->status]) ? $status_options[$review->status] : $review->status;
            
            echo '<div class="prs-review-status">';
            echo '<p><strong>' . __('当前状态:', 'product-review-system') . '</strong> ' . esc_html($status_label) . '</p>';
            echo '<p><strong>' . __('提交时间:', 'product-review-system') . '</strong> ' . esc_html($review->created_at) . '</p>';
            
            if ($review->change_summary) {
                echo '<p><strong>' . __('变更摘要:', 'product-review-system') . '</strong></p>';
                echo '<div class="prs-change-summary">' . nl2br(esc_html($review->change_summary)) . '</div>';
            }
            
            echo '<p><a href="' . admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id) . '" class="button">' . __('查看详情', 'product-review-system') . '</a></p>';
            echo '</div>';
            
            echo '<style>
                .prs-review-status { padding: 10px; background: #f9f9f9; border-left: 4px solid #ffb900; }
                .prs-change-summary { background: white; padding: 8px; border: 1px solid #ddd; margin-top: 5px; font-size: 12px; }
            </style>';
        } else {
            echo '<p>' . __('该产品当前没有待审核的修改。', 'product-review-system') . '</p>';
            echo '<p><small>' . __('注意：所有产品修改都需要经过审核流程。', 'product-review-system') . '</small></p>';
        }
    }

    /**
     * 添加审核状态列
     */
    public function add_review_status_column($columns) {
        $new_columns = array();
        
        foreach ($columns as $key => $title) {
            $new_columns[$key] = $title;
            
            // 在产品名称后添加审核状态列
            if ($key === 'name') {
                $new_columns['review_status'] = __('审核状态', 'product-review-system');
            }
        }
        
        return $new_columns;
    }

    /**
     * 显示审核状态列内容
     */
    public function display_review_status_column($column, $post_id) {
        if ($column === 'review_status') {
            $review = PRS_Database::get_product_pending_review($post_id);

            if ($review) {
                $status_options = PRS_Database::get_status_options();
                $status_label = isset($status_options[$review->status]) ? $status_options[$review->status] : $review->status;

                // 获取操作类型
                $operation_type = isset($review->operation_type) ? $review->operation_type : 'modify';
                $operation_types = PRS_Database::get_operation_type_options();
                $operation_label = isset($operation_types[$operation_type]) ? $operation_types[$operation_type] : $operation_type;

                echo '<div class="prs-review-info">';
                echo '<span class="prs-status prs-status-' . esc_attr($review->status) . '">' . esc_html($status_label) . '</span>';
                echo '<br><small class="prs-operation-type ' . esc_attr($operation_type) . '">' . esc_html($operation_label) . '</small>';
                echo '</div>';
            } else {
                echo '<span class="prs-status prs-status-normal">' . __('正常', 'product-review-system') . '</span>';
            }
        }
    }

    /**
     * 添加列样式修复
     */
    public function add_column_styles() {
        global $pagenow, $typenow;

        // 只在产品列表页面添加样式
        if ($pagenow === 'edit.php' && $typenow === 'product') {
            ?>
            <style type="text/css">
                /* 强制审核状态列横向显示 */
                .wp-list-table th.column-review_status,
                .wp-list-table td.column-review_status {
                    writing-mode: horizontal-tb !important;
                    text-orientation: mixed !important;
                    transform: none !important;
                    -webkit-transform: none !important;
                    -moz-transform: none !important;
                    -ms-transform: none !important;
                    -o-transform: none !important;
                    width: 120px !important;
                    min-width: 120px !important;
                    text-align: center !important;
                    white-space: nowrap !important;
                }

                /* 确保列标题文字正常显示 */
                .wp-list-table th.column-review_status span,
                .wp-list-table th.column-review_status a {
                    writing-mode: horizontal-tb !important;
                    text-orientation: mixed !important;
                    transform: none !important;
                    display: inline-block !important;
                }

                /* 审核状态标签样式 */
                .prs-status {
                    display: inline-block !important;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: 500;
                    text-align: center;
                    min-width: 60px;
                    white-space: nowrap;
                }

                .prs-status-normal {
                    background: #e8f5e8;
                    color: #2e7d32;
                    border: 1px solid #4caf50;
                }

                .prs-status-pending_review {
                    background: #fff3cd;
                    color: #856404;
                    border: 1px solid #ffc107;
                }

                .prs-status-pending_admin {
                    background: #cce5ff;
                    color: #0066cc;
                    border: 1px solid #007cba;
                }

                .prs-status-approved {
                    background: #d4edda;
                    color: #155724;
                    border: 1px solid #28a745;
                }

                .prs-status-rejected {
                    background: #f8d7da;
                    color: #721c24;
                    border: 1px solid #dc3545;
                }

                /* 操作类型样式 */
                .prs-operation-type {
                    font-size: 11px;
                    font-weight: normal;
                }

                .prs-operation-type.delete {
                    color: #d63384;
                }

                .prs-operation-type.modify {
                    color: #0d6efd;
                }

                .prs-review-info {
                    text-align: center;
                }
            </style>
            <?php
        }
    }

    /**
     * 将WooCommerce下载对象转换为数组格式
     */
    private function convert_downloads_to_array($downloads) {
        $files_array = array();

        if (empty($downloads) || !is_array($downloads)) {
            return $files_array;
        }

        foreach ($downloads as $download) {
            if (is_object($download) && method_exists($download, 'get_id')) {
                $files_array[$download->get_id()] = array(
                    'name' => $download->get_name(),
                    'file' => $download->get_file()
                );
            }
        }

        return $files_array;
    }

    /**
     * 确保下载相关的元数据正确设置
     */
    private function ensure_download_metadata($product_id, $modified_data) {
        try {
            // 设置下载限制（如果没有设置的话）
            $download_limit = get_post_meta($product_id, '_download_limit', true);
            if (empty($download_limit) || $download_limit === '') {
                update_post_meta($product_id, '_download_limit', '-1'); // 无限制
                error_log('PRS: Set download limit to unlimited for product ID: ' . $product_id);
            }

            // 设置下载过期时间（如果没有设置的话）
            $download_expiry = get_post_meta($product_id, '_download_expiry', true);
            if (empty($download_expiry) || $download_expiry === '') {
                update_post_meta($product_id, '_download_expiry', '-1'); // 永不过期
                error_log('PRS: Set download expiry to never for product ID: ' . $product_id);
            }

            // 确保可下载标志正确设置
            if (isset($modified_data['downloadable']) && $modified_data['downloadable']) {
                update_post_meta($product_id, '_downloadable', 'yes');
                error_log('PRS: Set downloadable flag to yes for product ID: ' . $product_id);
            }

            // 确保虚拟产品标志正确设置
            if (isset($modified_data['virtual']) && $modified_data['virtual']) {
                update_post_meta($product_id, '_virtual', 'yes');
                error_log('PRS: Set virtual flag to yes for product ID: ' . $product_id);
            }

            // 强制刷新产品对象以确保下载文件被正确识别
            $product = wc_get_product($product_id);
            if ($product) {
                $product->read_meta_data(true);

                // 触发 WooCommerce 下载文件相关的钩子
                do_action('woocommerce_product_object_updated_props', $product, array('downloadable', 'virtual', 'downloadable_files'));

                // 验证下载文件是否正确设置
                $downloads = $product->get_downloads();
                if (!empty($downloads)) {
                    error_log('PRS: Verified ' . count($downloads) . ' download files for product ID: ' . $product_id);
                } else {
                    error_log('PRS: Warning - No download files found after metadata update for product ID: ' . $product_id);
                }
            }

            error_log('PRS: Ensured download metadata for product ID: ' . $product_id);

        } catch (Exception $e) {
            error_log('PRS: Failed to ensure download metadata: ' . $e->getMessage());
        }
    }

    /**
     * 格式化价格显示 - 特殊处理价格为0的情况
     */
    private function format_price_display($price) {
        // 如果价格是数字0或字符串"0"，显示为"0"
        if (is_numeric($price) && floatval($price) == 0) {
            return '0';
        }

        // 如果价格为空或null，显示为"无"
        if (empty($price) && $price !== '0' && $price !== 0) {
            return __('无', 'product-review-system');
        }

        // 其他情况直接返回价格值
        return (string) $price;
    }

    /**
     * 格式化下载文件显示
     */
    private function format_downloadable_files_display($files) {
        if (empty($files) || !is_array($files)) {
            return __('无', 'product-review-system');
        }

        $file_names = array();
        foreach ($files as $file) {
            if (is_array($file) && isset($file['name'])) {
                $file_names[] = $file['name'];
            }
        }

        if (empty($file_names)) {
            return __('无', 'product-review-system');
        }

        return implode(', ', $file_names) . ' (' . count($file_names) . ' 个文件)';
    }

    /**
     * 格式化产品主图显示
     */
    private function format_image_display($image_id) {
        if (empty($image_id)) {
            return __('无', 'product-review-system');
        }

        $image = wp_get_attachment_image_src($image_id, 'thumbnail');
        if ($image) {
            $image_name = get_the_title($image_id);
            if (empty($image_name)) {
                $image_name = basename($image[0]);
            }
            return sprintf(__('图片: %s (ID: %d)', 'product-review-system'), $image_name, $image_id);
        }

        return sprintf(__('图片 ID: %d (文件不存在)', 'product-review-system'), $image_id);
    }

    /**
     * 格式化产品图库显示
     */
    private function format_gallery_display($gallery_ids) {
        if (empty($gallery_ids)) {
            return __('无', 'product-review-system');
        }

        // 处理字符串格式的图库ID（逗号分隔）
        if (is_string($gallery_ids)) {
            $gallery_ids = array_filter(explode(',', $gallery_ids));
        }

        if (!is_array($gallery_ids) || empty($gallery_ids)) {
            return __('无', 'product-review-system');
        }

        $image_count = count($gallery_ids);
        $image_names = array();

        // 最多显示前3个图片的名称
        $display_count = min(3, $image_count);
        for ($i = 0; $i < $display_count; $i++) {
            $image_id = intval($gallery_ids[$i]);
            if ($image_id > 0) {
                $image_name = get_the_title($image_id);
                if (empty($image_name)) {
                    $image = wp_get_attachment_image_src($image_id, 'thumbnail');
                    if ($image) {
                        $image_name = basename($image[0]);
                    } else {
                        $image_name = 'ID:' . $image_id;
                    }
                }
                $image_names[] = $image_name;
            }
        }

        if (empty($image_names)) {
            return sprintf(__('%d 张图片 (无法获取详情)', 'product-review-system'), $image_count);
        }

        $display_text = implode(', ', $image_names);
        if ($image_count > 3) {
            $display_text .= sprintf(__(' 等 %d 张图片', 'product-review-system'), $image_count);
        } else {
            $display_text .= sprintf(__(' (%d 张图片)', 'product-review-system'), $image_count);
        }

        return $display_text;
    }

    /**
     * 拦截产品删除操作
     */
    public function intercept_product_delete($delete, $post) {
        // 记录调试信息
        error_log('PRS: intercept_product_delete called with post ID: ' . ($post ? $post->ID : 'null'));

        // 只处理产品类型
        if (!$post || $post->post_type !== 'product') {
            error_log('PRS: Not a product, allowing delete. Post type: ' . ($post ? $post->post_type : 'null'));
            return $delete;
        }

        $product_id = $post->ID;
        error_log('PRS: Processing delete request for product ID: ' . $product_id);

        // 检查用户权限 - 是否应该绕过审核
        if ($this->should_bypass_delete_review($product_id)) {
            error_log('PRS: Bypassing delete review for product ID: ' . $product_id);
            return $delete; // 允许直接删除
        }

        error_log('PRS: Delete review required for product ID: ' . $product_id);

        // 检查是否已有待审核记录
        $existing_review = PRS_Database::get_product_pending_review($product_id);
        if ($existing_review) {
            error_log('PRS: Product already has pending review, blocking delete');
            // 如果已有待审核记录，阻止删除并显示友好的错误消息
            $this->show_pending_review_error($product_id, $existing_review);
            return false; // 阻止删除
        }

        // 创建删除审核记录
        try {
            error_log('PRS: Creating delete review for product ID: ' . $product_id);
            $review_id = $this->create_delete_review($product_id);
            error_log('PRS: Delete review created successfully with ID: ' . $review_id . ', blocking delete');

            // 立即设置重定向，使用多种方法确保重定向成功
            $redirect_url = admin_url('admin.php?page=product-review-system&action=view&id=' . $review_id);
            $this->force_redirect_to_review($redirect_url);

            return false; // 阻止删除，因为我们已经创建了审核记录
        } catch (Exception $e) {
            // 如果创建审核记录失败，记录错误并允许删除继续
            error_log('PRS: Failed to create delete review: ' . $e->getMessage());
            $this->set_delete_error_message(__('创建删除审核记录失败，请重试。', 'product-review-system'));
            return false;
        }
    }

    /**
     * 显示产品待审核状态的友好错误页面
     */
    private function show_pending_review_error($product_id, $existing_review) {
        $product = wc_get_product($product_id);
        $product_name = $product ? $product->get_name() : '产品';

        // 获取操作类型
        $operation_type = isset($existing_review->operation_type) ? $existing_review->operation_type : 'modify';
        $operation_types = PRS_Database::get_operation_type_options();
        $operation_label = isset($operation_types[$operation_type]) ? $operation_types[$operation_type] : '修改';

        // 构建友好的错误消息
        $title = __('操作被阻止', 'product-review-system');
        $message = sprintf(
            __('产品"%s"当前处于待审核状态（%s），请等待审核完成后再次操作。', 'product-review-system'),
            $product_name,
            $operation_label
        );

        // 添加审核详情链接
        $review_url = admin_url('admin.php?page=product-review-system&action=view&id=' . $existing_review->id);
        $back_url = admin_url('edit.php?post_type=product');

        // 使用wp_die显示友好的错误页面
        $error_html = '<div style="max-width: 600px; margin: 50px auto; padding: 30px; border: 1px solid #ddd; border-radius: 8px; background: #fff;">';
        $error_html .= '<div style="text-align: center; margin-bottom: 30px;">';
        $error_html .= '<div style="width: 80px; height: 80px; margin: 0 auto 20px; background: #ff6b6b; border-radius: 50%; display: flex; align-items: center; justify-content: center;">';
        $error_html .= '<span style="color: white; font-size: 36px;">⚠</span>';
        $error_html .= '</div>';
        $error_html .= '<h2 style="color: #333; margin: 0 0 10px 0;">' . esc_html($title) . '</h2>';
        $error_html .= '<p style="color: #666; font-size: 16px; line-height: 1.5; margin: 0;">' . esc_html($message) . '</p>';
        $error_html .= '</div>';

        $error_html .= '<div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 30px;">';
        $error_html .= '<h3 style="margin: 0 0 15px 0; color: #333;">您可以：</h3>';
        $error_html .= '<ul style="margin: 0; padding-left: 20px; color: #666;">';
        $error_html .= '<li style="margin-bottom: 8px;"><a href="' . esc_url($review_url) . '" style="color: #0073aa; text-decoration: none;">查看审核详情</a></li>';
        $error_html .= '<li style="margin-bottom: 8px;">等待审核员处理当前审核</li>';
        $error_html .= '<li style="margin-bottom: 8px;">联系管理员加快审核进度</li>';
        $error_html .= '</ul>';
        $error_html .= '</div>';

        $error_html .= '<div style="text-align: center;">';
        $error_html .= '<a href="' . esc_url($back_url) . '" style="display: inline-block; padding: 12px 24px; background: #0073aa; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px;">返回产品列表</a>';
        $error_html .= '<a href="' . esc_url($review_url) . '" style="display: inline-block; padding: 12px 24px; background: #46b450; color: white; text-decoration: none; border-radius: 4px;">查看审核详情</a>';
        $error_html .= '</div>';
        $error_html .= '</div>';

        wp_die($error_html, $title, array('response' => 403));
    }

    /**
     * 设置删除错误消息
     */
    private function set_delete_error_message($message) {
        // 使用transient来存储错误消息，这样可以在重定向后显示
        $user_id = get_current_user_id();
        set_transient('prs_delete_error_' . $user_id, $message, 30);

        // 同时添加admin_notices钩子
        add_action('admin_notices', function() use ($message) {
            echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($message) . '</p></div>';
        });
    }

    /**
     * 检查是否应该绕过删除审核
     */
    private function should_bypass_delete_review($product_id) {
        // 检查产品状态 - 如果产品已经是草稿状态，可能不需要审核
        $product = get_post($product_id);
        if ($product && $product->post_status === 'auto-draft') {
            return true;
        }

        // 所有用户删除产品都需要审核，包括管理员
        return false;
    }

    /**
     * 创建删除审核记录
     */
    private function create_delete_review($product_id) {
        // 获取产品完整数据
        $product = wc_get_product($product_id);
        if (!$product) {
            throw new Exception(__('产品不存在，无法创建删除审核。', 'product-review-system'));
        }

        $original_data = $this->get_product_data($product);

        // 创建审核记录
        $review_data = array(
            'product_id' => $product_id,
            'original_data' => wp_json_encode($original_data),
            'modified_data' => '', // 删除操作不需要修改数据
            'change_summary' => sprintf(__('请求删除产品: %s', 'product-review-system'), $product->get_name()),
            'submitter_id' => get_current_user_id(),
            'operation_type' => 'delete'
        );

        $review_id = PRS_Database::add_review($review_data);

        if ($review_id) {
            // 标记产品为待删除审核状态
            update_post_meta($product_id, '_product_under_review', $review_id);
            update_post_meta($product_id, '_product_review_status', 'pending_delete');

            // 发送通知给审核员
            PRS_Notifications::notify_reviewers($review_id);

            // 设置成功消息
            $current_user = wp_get_current_user();
            $is_admin = current_user_can('manage_options');

            if ($is_admin) {
                $message = __('删除请求已提交审核。作为管理员，您可以在审核系统中快速处理此请求。', 'product-review-system');
            } else {
                $message = __('删除请求已提交审核，请等待审核员处理。', 'product-review-system');
            }

            // 使用transient存储成功消息和重定向URL
            $user_id = get_current_user_id();
            set_transient('prs_delete_success_' . $user_id, $message, 30);
            set_transient('prs_redirect_after_delete_' . $user_id, admin_url('admin.php?page=product-review-system&action=view&id=' . $review_id), 30);

            return $review_id;
        } else {
            throw new Exception(__('创建删除审核记录失败，请重试。', 'product-review-system'));
        }
    }

    /**
     * 强制重定向到审核页面
     */
    private function force_redirect_to_review($redirect_url) {
        $user_id = get_current_user_id();

        // 方法1: 设置transient用于后续重定向
        set_transient('prs_force_redirect_' . $user_id, $redirect_url, 60);

        // 方法2: 输出JavaScript立即重定向
        add_action('admin_footer', function() use ($redirect_url) {
            ?>
            <script type="text/javascript">
            (function() {
                // 立即重定向，不等待页面加载完成
                window.location.href = '<?php echo esc_js($redirect_url); ?>';
            })();
            </script>
            <?php
        }, 1); // 使用优先级1确保最早执行

        // 方法3: 设置HTTP头重定向（如果还没有输出内容）
        if (!headers_sent()) {
            wp_redirect($redirect_url);
            exit;
        }
    }

    /**
     * 添加删除重定向脚本
     */
    public function add_delete_redirect_script() {
        // 只在管理后台添加
        if (!is_admin()) {
            return;
        }

        $user_id = get_current_user_id();
        $redirect_url = get_transient('prs_redirect_after_delete_' . $user_id);

        if ($redirect_url) {
            delete_transient('prs_redirect_after_delete_' . $user_id);
            ?>
            <script type="text/javascript">
            (function() {
                // 确保在页面加载完成后执行重定向
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', function() {
                        setTimeout(function() {
                            window.location.href = '<?php echo esc_js($redirect_url); ?>';
                        }, 500);
                    });
                } else {
                    // 页面已经加载完成
                    setTimeout(function() {
                        window.location.href = '<?php echo esc_js($redirect_url); ?>';
                    }, 500);
                }
            })();
            </script>
            <?php
        }
    }
}
