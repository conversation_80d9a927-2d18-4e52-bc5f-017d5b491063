import{K as l,L as e,a,U as t,l as s,m as o,w as u,n,u as d,q as m,J as i,t as p,O as r,P as c,R as h,S as V,v as w,a3 as b,N as f,Z as y,Q as v,a0 as U,$ as _}from"./wbs-Dtem2-xP.js";import{E as k,a as z}from"./el-tab-pane-DsCLh1_f.js";import{E as g,a as M}from"./el-radio-CJI16kOl.js";import"./strings-BZz7wQvk.js";const S={key:0,class:"wbs-form-table-sub"},P={class:"w8em row"},C={class:"description"},x={key:1,class:"wbs-form-table-sub"},A={class:"w8em row"},T={class:"description"},$={class:"w8em row"},O={class:"description"},D={class:"row"},K={class:"row"},j={class:"description"},I=["href"],E={key:0},L={class:"row"},Q={class:"wb-ib-label"},N={class:"wb-ib-lable"},W={class:"mt"},q={class:"mt"},J={class:"mt"},R={class:"mt"},Z={style:{"line-height":"1.1"}},B={class:"row"},F={class:"description"},G={class:"wbs-form-table-sub"},H={class:"row w8em"},X={class:"description"},Y={class:"row w8em"},ll={key:0,class:"with-sub-form-table"},el={class:"wbs-form-table-sub"},al={class:"w8em"},tl={class:"info description"},sl={class:"info description"},ol={class:"info description"},ul={class:"info description"},nl={key:0,class:"description mt"},dl={key:1,class:"with-sub-form-table"},ml={class:"wbs-form-table-sub"},il={class:"w8em"},pl={style:{"line-height":"1.1"}},rl={class:"info description"},cl={class:"info description"},hl={class:"info description"},Vl={class:"info description"},wl={key:0,class:"description mt"},bl={key:2,class:"with-sub-form-table"},fl={class:"wbs-form-table-sub"},yl={class:"w8em"},vl={class:"w8em"},Ul={style:{"line-height":"1.1"}},_l={style:{"line-height":"1.1"}},kl={class:"info description"},zl={class:"info description"},gl={class:"info description"},Ml={class:"info description"},Sl={key:0,class:"description mt"},Pl={__name:"NoticeOptions",props:{cnf:Object,opt:Object},setup(Pl){const{wbsCnf:Cl}=l(),{wb_e:xl}=e(Cl),Al=Pl,Tl=a(""),$l=a(""),Ol=t({to:""}),Dl=()=>{$l.value=""},Kl=async()=>{if(Tl.value&&/^1\d{10}$/.test(Tl.value))try{const l=await _.saveData({op:"test_sms",mobile:Tl.value,opt:Al.opt.sms});l.code?($l.value=l,U.info(l.desc)):U.success(xl("短信发送成功"))}catch(l){U.error(xl("短信发送失败"))}},jl=l=>{/^1\d{10}$/.test(l)||U.warning(xl("请确认手机号码格式"))},Il=()=>{if(Al.opt.mail.mailer>0){const l=Al.opt.mail.proc[Al.opt.mail.mailer].secure,e={ssl:465,tls:587};Al.opt.mail.proc[Al.opt.mail.mailer].port=e[l]||25}};return(l,e)=>{const a=c,t=M,El=g,Ll=v,Ql=k,Nl=z;return o(),s(Nl,{type:"border-card",class:"mt"},{default:u((()=>[Pl.opt.notice.indexOf("mail")>-1?(o(),s(Ql,{key:"mail",label:d(xl)("邮局设置")},{default:u((()=>[d(Cl).use_theme_mail?(o(),m("table",S,[i("tbody",null,[i("tr",null,[i("th",P,p(d(xl)("通知邮箱")),1),i("td",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:Pl.opt.mail.to,"onUpdate:modelValue":e[0]||(e[0]=l=>Pl.opt.mail.to=l),modelModifiers:{trim:!0}},null,8,["modelValue"]),i("p",C,p(d(xl)("* 多个邮箱用英文逗号(,)分隔")),1)])]),i("tr",null,[i("th",null,p(d(xl)("邮件服务")),1),i("td",null,p(d(xl)("检测到当前正在使用闪电博主题，将使用主题邮件服务设置。")),1)])])])):(o(),m("table",x,[i("tbody",null,[i("tr",null,[i("th",A,p(d(xl)("通知邮箱")),1),i("td",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:Pl.opt.mail.to,"onUpdate:modelValue":e[1]||(e[1]=l=>Pl.opt.mail.to=l),modelModifiers:{trim:!0}},null,8,["modelValue"]),i("p",T,p(d(xl)("* 多个邮箱用英文逗号(,)分隔")),1)])]),i("tr",null,[i("th",$,p(d(xl)("发件邮箱")),1),i("td",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:Pl.opt.mail.from,"onUpdate:modelValue":e[2]||(e[2]=l=>Pl.opt.mail.from=l),modelModifiers:{trim:!0}},null,8,["modelValue"]),i("p",O,p(d(xl)("* 如果启用SMTP邮局，发件邮箱必须与SMTP邮局邮箱保持一致。")),1)])]),i("tr",null,[i("th",D,p(d(xl)("发件人名称")),1),i("td",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:Pl.opt.mail.name,"onUpdate:modelValue":e[3]||(e[3]=l=>Pl.opt.mail.name=l),modelModifiers:{trim:!0}},null,8,["modelValue"])])]),i("tr",null,[i("th",K,p(d(xl)("邮件程序")),1),i("td",null,[r(El,{modelValue:Pl.opt.mail.mailer,"onUpdate:modelValue":e[4]||(e[4]=l=>Pl.opt.mail.mailer=l)},{default:u((()=>[(o(!0),m(h,null,V(Pl.cnf.proc,((l,e)=>(o(),s(t,{key:"mail"+e,value:e},{default:u((()=>[i("span",null,p(l),1)])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"]),i("div",j,[w(p(d(xl)("* 由于大部分国内服务器禁用了邮局端口，无法通过WP默认邮局发信。建议使用QQ、163或者其他SMTP邮局执行WP发信任务。"))+" ",1),i("a",{href:d(b)("https://www.wbolt.com/how-to-set-smtp-email.html",d(Cl).locale),target:"_blank"},p(d(xl)("查看SMTP邮局设置教程")),9,I)])])]),Pl.opt.mail.mailer>0?(o(),m("tr",E,[i("th",L,[i("span",Q,p(d(xl)("SMTP配置")),1)]),i("td",null,[(o(!0),m(h,null,V(Pl.opt.mail.proc,((l,e)=>f((o(),m("dl",{class:"info",key:"mailer"+e},[i("dt",null,[i("span",N,p(d(xl)("SMTP服务器")),1)]),i("dd",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:l.host,"onUpdate:modelValue":e=>l.host=e,modelModifiers:{trim:!0}},null,8,["modelValue","onUpdate:modelValue"])]),i("dt",W,p(d(xl)("加密方式")),1),i("dd",null,[r(El,{modelValue:l.secure,"onUpdate:modelValue":e=>l.secure=e,onChange:Il},{default:u((()=>[(o(!0),m(h,null,V({"":d(xl)("无"),ssl:"SSL",tls:"TLS"},((l,e)=>(o(),s(t,{key:"mail"+e,value:e},{default:u((()=>[i("span",null,p(l),1)])),_:2},1032,["value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])]),i("dt",q,p(d(xl)("端口")),1),i("dd",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:l.port,"onUpdate:modelValue":e=>l.port=e,modelModifiers:{trim:!0}},null,8,["modelValue","onUpdate:modelValue"])]),i("dt",J,p(d(xl)("SMTP用户名")),1),i("dd",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:l.user,"onUpdate:modelValue":e=>l.user=e,modelModifiers:{trim:!0}},null,8,["modelValue","onUpdate:modelValue"])]),i("dt",R,[i("div",Z,p(d(xl)("SMTP密码/授权码")),1)]),i("dd",null,[r(a,{size:"small",class:"wbs-input-short",modelValue:l.password,"onUpdate:modelValue":e=>l.password=e,modelModifiers:{trim:!0}},null,8,["modelValue","onUpdate:modelValue"])])])),[[y,Pl.opt.mail.mailer==e]]))),128))])])):n("",!0),i("tr",null,[i("th",B,p(d(xl)("发送测试邮件")),1),i("td",null,[r(a,{class:"wbs-input-short",type:"mail",modelValue:Ol.to,"onUpdate:modelValue":e[6]||(e[6]=l=>Ol.to=l),modelModifiers:{trim:!0},placeholder:d(xl)("<EMAIL>")},{append:u((()=>[r(Ll,{onClick:e[5]||(e[5]=l=>(async()=>{if(!Ol.to)return U.info(xl("请输入测试收件箱。")),!1;try{const l=await _.saveData({op:"mail_test",to:Ol.to,mail:{mailer:Al.opt.mail.mailer,smtp:Al.opt.mail.proc[Al.opt.mail.mailer],from:Al.opt.mail.from,name:Al.opt.mail.name}});U.info(l.desc)}catch(l){U.warning(xl("邮件发送失败"))}})())},{default:u((()=>[w(p(d(xl)("发送")),1)])),_:1})])),_:1},8,["modelValue","placeholder"]),i("p",F,p(d(xl)("* 测试邮件收件人必须与发件人邮箱地址保持一致，否则测试邮件将会发送失败。")),1)])])])]))])),_:1},8,["label"])):n("",!0),Pl.opt.notice.indexOf("sms")>-1?(o(),s(Ql,{key:"sms",label:d(xl)("短信设置")},{default:u((()=>[i("table",G,[i("tbody",null,[i("tr",null,[i("th",H,p(d(xl)("通知手机号")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.to,"onUpdate:modelValue":e[7]||(e[7]=l=>Pl.opt.sms.to=l),modelModifiers:{trim:!0}},null,8,["modelValue"]),i("p",X,p(d(xl)("* 多个手机用英文逗号(,)分隔")),1)])]),i("tr",null,[i("th",Y,p(d(xl)("服务商")),1),i("td",null,[r(El,{modelValue:Pl.opt.sms.vendor,"onUpdate:modelValue":e[8]||(e[8]=l=>Pl.opt.sms.vendor=l),onClick:Dl},{default:u((()=>[r(t,{value:"upyun"},{default:u((()=>[i("span",null,p(d(xl)("又拍云")),1)])),_:1}),r(t,{value:"aliyun"},{default:u((()=>[i("span",null,p(d(xl)("阿里云")),1)])),_:1}),r(t,{value:"huawei"},{default:u((()=>[i("span",null,p(d(xl)("华为云")),1)])),_:1})])),_:1},8,["modelValue"])])]),i("tr",null,[i("th",null,p(d(xl)("参数")),1),i("td",null,["upyun"==Pl.opt.sms.vendor?(o(),m("div",ll,[i("table",el,[i("tbody",null,[i("tr",null,[i("th",al,p(d(xl)("账号ID")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.upyun.id,"onUpdate:modelValue":e[9]||(e[9]=l=>Pl.opt.sms.upyun.id=l),placeholder:d(xl)("进入又拍云短信服务控制台>短信发送>API发送，点击流程步骤2-获取账号token获取")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("Token")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.upyun.secret,"onUpdate:modelValue":e[10]||(e[10]=l=>Pl.opt.sms.upyun.secret=l),placeholder:d(xl)("账号token或者模板token（推荐后者）")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("模板编号")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.upyun.tpl,"onUpdate:modelValue":e[11]||(e[11]=l=>Pl.opt.sms.upyun.tpl=l),placeholder:d(xl)("进入又拍云短信服务控制台>配置中心>模板配置获取")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("短信类型")),1),i("td",tl,p(d(xl)("行业短信-通用")),1)]),i("tr",null,[i("th",null,p(d(xl)("模板名称")),1),i("td",sl,p(d(xl)("自定义命名")),1)]),i("tr",null,[i("th",null,p(d(xl)("短信内容")),1),i("td",ol,p(d(xl)("自定义。模板文本变量{$var1}。例如：收到{$var1}的留言。")),1)]),i("tr",null,[i("th",null,p(d(xl)("签名")),1),i("td",ul,p(d(xl)("签名")),1)]),i("tr",null,[i("th",null,p(d(xl)("测试短信")),1),i("td",null,[r(a,{class:"wbs-input-short",size:"small",type:"tel",maxlength:"11",placeholder:d(xl)("输入手机号"),modelValue:Tl.value,"onUpdate:modelValue":e[12]||(e[12]=l=>Tl.value=l),onChange:jl},{append:u((()=>[r(Ll,{slot:"append",onClick:Kl},{default:u((()=>[w(p(d(xl)("发送")),1)])),_:1})])),_:1},8,["placeholder","modelValue"]),$l.value?(o(),m("div",nl,p(d(xl)("测试短信返回结果："))+p($l.value),1)):n("",!0)])])])])])):n("",!0),"aliyun"==Pl.opt.sms.vendor?(o(),m("div",dl,[i("table",ml,[i("tbody",null,[i("tr",null,[i("th",il,p(d(xl)("Access Key Id")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.aliyun.id,"onUpdate:modelValue":e[13]||(e[13]=l=>Pl.opt.sms.aliyun.id=l),placeholder:d(xl)("登录阿里云工作台>点击主账号头像>AccessKey管理，创建生成。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,[i("div",pl,p(d(xl)("Access Key Secret")),1)]),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.aliyun.secret,"onUpdate:modelValue":e[14]||(e[14]=l=>Pl.opt.sms.aliyun.secret=l),placeholder:d(xl)("登录阿里云工作台>点击主账号头像>AccessKey管理，创建生成。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("短信签名")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.aliyun.sign,"onUpdate:modelValue":e[15]||(e[15]=l=>Pl.opt.sms.aliyun.sign=l),placeholder:d(xl)("登录阿里云短信服务工作台>国内消息>模板管理>查看短信模板详情>关联签名。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("模板CODE")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.aliyun.tpl,"onUpdate:modelValue":e[16]||(e[16]=l=>Pl.opt.sms.aliyun.tpl=l),placeholder:d(xl)("登录阿里云短信服务工作台>国内消息>模板管理，填入对应模板的CODE。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("模板类型")),1),i("td",rl,p(d(xl)("通知短信")),1)]),i("tr",null,[i("th",null,p(d(xl)("模板名称")),1),i("td",cl,p(d(xl)("自定义命名")),1)]),i("tr",null,[i("th",null,p(d(xl)("模版内容")),1),i("td",hl,p(d(xl)("自定义，建议使用阿里云提供的常用模版库修改。变量${name}。如：收到${name}的留言。")),1)]),i("tr",null,[i("th",null,p(d(xl)("申请说明")),1),i("td",Vl,p(d(xl)("使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。")),1)]),i("tr",null,[i("th",null,p(d(xl)("测试短信")),1),i("td",null,[r(a,{class:"wbs-input-short",size:"small",type:"tel",maxlength:"11",placeholder:d(xl)("输入手机号"),modelValue:Tl.value,"onUpdate:modelValue":e[17]||(e[17]=l=>Tl.value=l),onChange:jl},{append:u((()=>[r(Ll,{slot:"append",onClick:Kl},{default:u((()=>[w(p(d(xl)("发送")),1)])),_:1})])),_:1},8,["placeholder","modelValue"]),$l.value?(o(),m("div",wl,p(d(xl)("测试短信返回结果："))+p($l.value),1)):n("",!0)])])])])])):n("",!0),"huawei"==Pl.opt.sms.vendor?(o(),m("div",bl,[i("table",fl,[i("tbody",null,[i("tr",null,[i("th",yl,p(d(xl)("接入地址")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.huawei.api,"onUpdate:modelValue":e[18]||(e[18]=l=>Pl.opt.sms.huawei.api=l),placeholder:d(xl)("登录华为云消息&短信控制台>国内短信>应用管理，填入对应的APP接入地址。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",vl,[i("div",Ul,p(d(xl)("Application Key")),1)]),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.huawei.id,"onUpdate:modelValue":e[19]||(e[19]=l=>Pl.opt.sms.huawei.id=l),placeholder:d(xl)("登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Key。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,[i("div",_l,p(d(xl)("Application Secret")),1)]),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.huawei.secret,"onUpdate:modelValue":e[20]||(e[20]=l=>Pl.opt.sms.huawei.secret=l),placeholder:d(xl)("登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Secret。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("短信签名")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.huawei.sign,"onUpdate:modelValue":e[21]||(e[21]=l=>Pl.opt.sms.huawei.sign=l),placeholder:d(xl)("登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名名称。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("签名通道号")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.huawei.channel,"onUpdate:modelValue":e[22]||(e[22]=l=>Pl.opt.sms.huawei.channel=l),placeholder:d(xl)("登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名通道号。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("模板ID")),1),i("td",null,[r(a,{size:"small",modelValue:Pl.opt.sms.huawei.tpl,"onUpdate:modelValue":e[23]||(e[23]=l=>Pl.opt.sms.huawei.tpl=l),placeholder:d(xl)("登录华为云消息&短信控制台>国内短信>模板管理，填入对应的模板ID。")},null,8,["modelValue","placeholder"])])]),i("tr",null,[i("th",null,p(d(xl)("模板类型")),1),i("td",kl,p(d(xl)("通知短信")),1)]),i("tr",null,[i("th",null,p(d(xl)("模板名称")),1),i("td",zl,p(d(xl)("自定义命名")),1)]),i("tr",null,[i("th",null,p(d(xl)("模版内容")),1),i("td",gl,p(d(xl)("自定义。使用变量${1}。如：收到${1}的留言。")),1)]),i("tr",null,[i("th",null,p(d(xl)("申请说明")),1),i("td",Ml,p(d(xl)("使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。。")),1)]),i("tr",null,[i("th",null,p(d(xl)("测试短信")),1),i("td",null,[r(a,{class:"wbs-input-short",size:"small",type:"tel",maxlength:"11",placeholder:d(xl)("输入手机号"),modelValue:Tl.value,"onUpdate:modelValue":e[24]||(e[24]=l=>Tl.value=l),onChange:jl},{append:u((()=>[r(Ll,{slot:"append",onClick:Kl},{default:u((()=>[w(p(d(xl)("发送")),1)])),_:1})])),_:1},8,["placeholder","modelValue"]),$l.value?(o(),m("div",Sl,p(d(xl)("测试短信返回结果："))+p($l.value),1)):n("",!0)])])])])])):n("",!0)])])])])])),_:1},8,["label"])):n("",!0)])),_:1})}}};export{Pl as default};
