.rsssl-wizard-menu{
  height: fit-content;
  background: none !important;
  box-shadow: none !important;
  .rsssl-grid-item-header {
    padding-left: var(--rsp-spacing-xs);
  }
  .rsssl-grid-item-content{
    padding: 0;
    padding-bottom: var(--rsp-spacing-l);
  }
}
.rsssl-wizard-menu-items > .rsssl-menu-item > a {
  font-weight: 600 !important;
  padding-inline: var(--rsp-spacing-xs) !important;
}

.rsssl-wizard-menu-items {
  .rsssl-main-menu {
    .rsssl-active {
      border-radius: var(--rsp-border-radius-s);
      background: var(--rsp-yellow-faded);
      box-shadow: var(--rsp-box-shadow);
      a:hover {
        text-decoration: none;
      }
    }

    .rsssl-menu-item {
      a {
        span {
          font-weight: 600;
        }
      }
    }
  }
  .rsssl-menu-item{
    a{
      display: flex;
      align-items: center;
      gap: var(--rsp-spacing-xs);
      text-decoration: none;
      color: var(--rsp-text-color);
      font-size: var(--rsp-fs-400);
      padding-block: var(--rsp-spacing-xs);
      transition: all 0.2s ease-in-out;
      padding-left: var(--rsp-spacing-xs);
      &:hover {
        text-decoration: underline;
      }
    }
    &.rsssl-active{
      > a{
        font-weight: 600;
      }
      a {
        &:focus {
          box-shadow: none !important;
        }
      }
    }
    &.rsssl-featured{

      &.rsssl-active {
        .rsssl-menu-item-beta-pill {
          color: var(--rsp-dark-blue);
        }
      }
      a{
        flex-wrap: wrap;
        .rsssl-menu-item-featured-pill{
            background: var(--rsp-green);
            color: var(--rsp-text-color-white);
            padding: 4px 8px;
            border-radius: var(--rsp-border-radius-xs);
            font-size: var(--rsp-fs-100);
        }
        .rsssl-menu-item-beta-pill{
          color: var(--rsp-dark-blue);
        }
      }
    }
    &.rsssl-new{

      .rsssl-menu-item-new-pill{
        background: var(--rsp-yellow);
        color: var(--rsp-text-color-dark);
        padding: 4px 8px;
        border-radius: var(--rsp-border-radius-xs);
        font-size: var(--rsp-fs-100);
      }

      &.rsssl-active {
        .rsssl-menu-item-new-pill {
          color: var(--rsp-text-color-dark);
        }
      }
      a{
        flex-wrap: wrap;
      }
    }


    &.rsssl-premium{
      a{
        flex-wrap: wrap;
        .rsssl-menu-item-featured-pill{
          background: var(--rsp-dark-blue);
          color: var(--rsp-text-color-white);
          padding: 2px 9px;
          border-radius: var(--rsp-border-radius);
          font-size: var(--rsp-fs-100);
        }
      }
    }
  }

  .rsssl-submenu-item{
    a{
      padding-left: calc(var(--rsp-spacing-xs) + var(--rsp-spacing-s)) !important;
      font-size: var(--rsp-fs-300);
    }
    .rsssl-active {
      a {
        text-decoration: none;
        position: relative;
        span {
          color: initial;
        }

        &::before {
          content: '\2022';
          color: var(--rsp-dark-blue);
          font-size: 3em;
          position: absolute;
          left: var(--rsp-spacing-xxs) !important;
          margin-bottom: 7px;
        }
      }
    }
  }
}