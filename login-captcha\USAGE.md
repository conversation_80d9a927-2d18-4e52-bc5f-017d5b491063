# 使用指南

## 快速开始

### 1. 激活插件
1. 上传插件到 `/wp-content/plugins/login-captcha/` 目录
2. 在WordPress后台激活插件
3. 访问 "设置" > "登录验证码" 进行配置

### 2. 基本配置
- **启用验证码**: 勾选启用选项
- **验证码类型**: 选择数字、字母或混合
- **验证码长度**: 建议设置为4位
- **图片尺寸**: 根据主题调整

### 3. 测试功能
- 退出登录
- 访问登录页面
- 查看验证码是否正常显示
- 测试验证码验证功能

## 详细功能

### 验证码类型

**纯数字 (numbers)**
- 字符集: 0-9
- 适合: 简单快速输入
- 安全性: 中等

**纯字母 (letters)**
- 字符集: A-Z
- 适合: 避免数字混淆
- 安全性: 中等

**数字字母混合 (mixed)**
- 字符集: 0-9, A-Z
- 适合: 平衡安全性和可用性
- 安全性: 高 (推荐)

### 自定义设置

#### 验证码长度
```
最小值: 3位
最大值: 8位
推荐值: 4位
```

#### 图片尺寸
```
宽度: 80-300px (推荐120px)
高度: 30-100px (推荐40px)
```

### 高级功能

#### 键盘快捷键
- **F5**: 刷新验证码
- **Tab**: 在字段间导航
- **Enter**: 提交表单

#### 无障碍支持
- 屏幕阅读器兼容
- 键盘导航支持
- 高对比度模式
- 语义化HTML结构

#### 响应式设计
- 自动适配移动设备
- 触摸友好的界面
- 灵活的布局系统

## 开发者接口

### 钩子 (Hooks)

#### 动作钩子 (Actions)
```php
// 验证码生成前
do_action('login_captcha_before_generate', $captcha_id);

// 验证码生成后
do_action('login_captcha_after_generate', $captcha_id, $code);

// 验证码验证前
do_action('login_captcha_before_verify', $captcha_id, $user_input);

// 验证码验证后
do_action('login_captcha_after_verify', $captcha_id, $result);
```

#### 过滤器 (Filters)
```php
// 自定义字符集
add_filter('login_captcha_characters', function($chars, $type) {
    if ($type === 'custom') {
        return '23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
    }
    return $chars;
}, 10, 2);

// 自定义验证逻辑
add_filter('login_captcha_validate', function($is_valid, $input, $stored) {
    // 自定义验证逻辑
    return $is_valid;
}, 10, 3);

// 自定义图片样式
add_filter('login_captcha_image_style', function($style) {
    $style['background_color'] = [255, 255, 255];
    $style['text_color'] = [0, 0, 0];
    return $style;
});
```

### JavaScript API

#### 事件监听
```javascript
// 验证码就绪
$(document).on('loginCaptchaReady', function() {
    console.log('验证码功能已初始化');
});

// 验证码刷新
$(document).on('loginCaptchaRefreshed', function() {
    console.log('验证码已刷新');
});

// 验证码验证
$(document).on('loginCaptchaValidated', function(e, result) {
    console.log('验证结果:', result);
});
```

#### 方法调用
```javascript
// 手动刷新验证码
LoginCaptcha.refreshCaptcha();

// 验证输入
LoginCaptcha.validateInput($('#login_captcha'));

// 获取统计信息
var stats = LoginCaptcha.getStats();
```

## 主题集成

### CSS自定义

#### 基本样式覆盖
```css
/* 自定义验证码容器 */
.login-captcha-field {
    margin-bottom: 20px;
}

/* 自定义验证码图片 */
#captcha-image {
    border: 2px solid #your-color;
    border-radius: 8px;
}

/* 自定义输入框 */
#login_captcha {
    font-size: 16px;
    padding: 10px;
}

/* 自定义刷新链接 */
#refresh-captcha {
    color: #your-brand-color;
}
```

#### 深色主题支持
```css
@media (prefers-color-scheme: dark) {
    .login-captcha-field label {
        color: #f0f0f1;
    }
    
    #captcha-image {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    #login_captcha {
        background: #1d2327;
        color: #f0f0f1;
        border-color: #3c434a;
    }
}
```

### 模板修改

#### 自定义登录表单
```php
// 在主题的functions.php中
add_action('login_form', 'custom_captcha_position', 25);
function custom_captcha_position() {
    // 移除默认位置的验证码
    remove_action('login_form', array(Login_Captcha::get_instance(), 'add_captcha_to_login_form'));
    
    // 在自定义位置添加验证码
    if (class_exists('Login_Captcha')) {
        $captcha = Login_Captcha::get_instance();
        $captcha->add_captcha_to_login_form();
    }
}
```

## 故障排除

### 常见问题

#### 验证码不显示
**可能原因:**
- GD扩展未安装
- 文件权限问题
- 内存限制不足

**解决方案:**
```bash
# 检查GD扩展
php -m | grep -i gd

# 设置文件权限
chmod 755 /path/to/wp-content/plugins/login-captcha/
chmod 644 /path/to/wp-content/plugins/login-captcha/*.php

# 增加内存限制 (wp-config.php)
ini_set('memory_limit', '256M');
```

#### 验证码总是错误
**可能原因:**
- 服务器时间不同步
- 缓存插件干扰
- Session问题

**解决方案:**
```php
// 在wp-config.php中添加调试
define('LOGIN_CAPTCHA_DEBUG', true);

// 清除所有缓存
wp_cache_flush();

// 检查服务器时间
echo date('Y-m-d H:i:s');
```

#### 样式冲突
**可能原因:**
- 主题CSS冲突
- 插件CSS冲突
- 缓存问题

**解决方案:**
```css
/* 提高CSS优先级 */
.login form .login-captcha-field {
    /* 你的样式 */
}

/* 或使用!important */
#login_captcha {
    font-size: 14px !important;
}
```

### 调试模式

#### 启用调试
```php
// wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('LOGIN_CAPTCHA_DEBUG', true);
```

#### 查看日志
```bash
# 查看WordPress错误日志
tail -f /path/to/wp-content/debug.log

# 查看服务器错误日志
tail -f /var/log/apache2/error.log
```

## 性能优化

### 缓存策略
- 验证码图片不缓存
- CSS/JS文件版本控制
- 过期验证码自动清理

### 资源优化
- 图片尺寸合理设置
- 最小化HTTP请求
- 异步加载非关键资源

### 数据库优化
- 使用transient API
- 定期清理过期数据
- 避免永久存储验证码

---

**提示**: 如需更多帮助，请查看 README.md 和 INSTALL.md 文件。
