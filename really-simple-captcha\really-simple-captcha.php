<?php
/*
 * Plugin Name: Really Simple CAPTCHA
 * Plugin URI: https://contactform7.com/captcha/
 * Description: Really Simple CAPTCHA is a CAPTCHA module intended to be called from other plugins. It is originally created for my Contact Form 7 plugin.
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://ideasilo.wordpress.com/
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Version: 2.4
 * Requires at least: 6.6
 * Requires PHP: 7.4
 */

define( 'REALLYSIMPLECAPTCHA_VERSION', '2.4' );

require_once __DIR__ . '/includes/filesystem.php';

class ReallySimpleCaptcha {

	use ReallySimpleCaptcha_Filesystem;

	/**
	 * Characters available in a CAPTCHA image.
	 *
	 * @var string All characters that can be used in a CAPTCHA image.
	 */
	public $chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';

	/**
	 * Number of characters that are displayed in a CAPTCHA image.
	 *
	 * @var int
	 */
	public $char_length = 4;

	/**
	 * Font paths. Randomly picked up from the list per character.
	 *
	 * @var array
	 */
	public $fonts = array(
		__DIR__ . '/gentium/GenBkBasR.ttf',
		__DIR__ . '/gentium/GenBkBasI.ttf',
		__DIR__ . '/gentium/GenBkBasBI.ttf',
		__DIR__ . '/gentium/GenBkBasB.ttf',
	);

	/**
	 * Temp directory for CAPTCHA images and text files.
	 *
	 * @var string
	 */
	public $tmp_dir = __DIR__ . '/tmp';

	/**
	 * Array of CAPTCHA image size.
	 *
	 * @var array 0: Width, 1: Height (in pixels)
	 */
	public $img_size = array( 72, 24 );

	/**
	 * Background color of a CAPTCHA image in RGB-notation.
	 *
	 * @var array 0: R, 1: G, 2: B (each within 0-255)
	 */
	public $bg = array( 255, 255, 255 );

	/**
	 * Foreground (character) color of a CAPTCHA image in RGB-notation.
	 *
	 * @var array 0: R, 1: G, 2: B (each within 0-255)
	 */
	public $fg = array( 0, 0, 0 );

	/**
	 * Coordinates for a text in an image. I don't know the meaning. Just adjust.
	 *
	 * @var array
	 */
	public $base = array( 6, 18 );

	/**
	 * Font size.
	 *
	 * @var int
	 */
	public $font_size = 14;

	/**
	 * Width of a character.
	 *
	 * @var int
	 */
	public $font_char_width = 15;

	/**
	 * Image type.
	 *
	 * @var string png, gif, or jpeg.
	 */
	public $img_type = 'png';

	/**
	 * File mode set for a CAPTCHA image.
	 *
	 * @var int
	 */
	public $file_mode = 0644;

	/**
	 * File mode set for an answer text file.
	 *
	 * @var int
	 */
	public $answer_file_mode = 0640;


	/**
	 * Constructor.
	 */
	public function __construct() {
		$this->connect();
	}


	/**
	 * Generate and return a random word.
	 *
	 * @return string Random word with $chars characters x $char_length length
	 */
	public function generate_random_word() {
		$word = '';

		for ( $i = 0; $i < $this->char_length; $i++ ) {
			$pos = wp_rand( 0, strlen( $this->chars ) - 1 );
			$char = $this->chars[$pos];
			$word .= $char;
		}

		return $word;
	}


	/**
	 * Generate CAPTCHA image and corresponding answer file.
	 *
	 * @param string $prefix File prefix used for both files
	 * @param string $word Random word generated by generate_random_word()
	 * @return string|bool The file name of the CAPTCHA image. Return false if temp directory is not available.
	 */
	public function generate_image( $prefix, $word ) {
		if ( ! $this->make_tmp_dir() ) {
			return false;
		}

		$this->cleanup();

		$dir = trailingslashit( $this->tmp_dir );
		$filename = null;

		$im = imagecreatetruecolor(
			$this->img_size[0],
			$this->img_size[1]
		);

		if ( $im ) {
			$bg = imagecolorallocate( $im, $this->bg[0], $this->bg[1], $this->bg[2] );
			$fg = imagecolorallocate( $im, $this->fg[0], $this->fg[1], $this->fg[2] );

			imagefill( $im, 0, 0, $bg );

			$x = $this->base[0] + wp_rand( -2, 2 );

			for ( $i = 0; $i < strlen( $word ); $i++ ) {
				$font = $this->fonts[array_rand( $this->fonts )];
				$font = wp_normalize_path( $font );

				imagettftext(
					$im, $this->font_size, wp_rand( -12, 12 ), $x,
					$this->base[1] + wp_rand( -2, 2 ), $fg, $font, $word[$i]
				);

				$x += $this->font_char_width;
			}

			switch ( $this->img_type ) {
				case 'jpeg':
					$filename = sanitize_file_name( $prefix . '.jpeg' );
					$file = wp_normalize_path( path_join( $dir, $filename ) );
					imagejpeg( $im, $file );
					break;
				case 'gif':
					$filename = sanitize_file_name( $prefix . '.gif' );
					$file = wp_normalize_path( path_join( $dir, $filename ) );
					imagegif( $im, $file );
					break;
				case 'png':
				default:
					$filename = sanitize_file_name( $prefix . '.png' );
					$file = wp_normalize_path( path_join( $dir, $filename ) );
					imagepng( $im, $file );
			}

			imagedestroy( $im );

			$this->chmod( $file, $this->file_mode );
		}

		$this->generate_answer_file( $prefix, $word );

		return $filename;
	}


	/**
	 * Generate answer file corresponding to CAPTCHA image.
	 *
	 * @param string $prefix File prefix used for answer file
	 * @param string $word Random word generated by generate_random_word()
	 */
	public function generate_answer_file( $prefix, $word ) {
		$dir = trailingslashit( $this->tmp_dir );
		$answer_file = path_join( $dir, sanitize_file_name( $prefix . '.txt' ) );
		$answer_file = wp_normalize_path( $answer_file );

		$word = strtoupper( $word );
		$salt = wp_generate_password( 64 );
		$hash = hash_hmac( 'sha256', $word, $salt );
		$code = $salt . '|' . $hash;

		$this->put_contents( $answer_file, $code, $this->answer_file_mode );
	}


	/**
	 * Check a response against the code kept in the temporary file.
	 *
	 * @param string $prefix File prefix used for both files
	 * @param string $response CAPTCHA response
	 * @return bool Return true if the two match, otherwise return false.
	 */
	public function check( $prefix, $response ) {
		if ( 0 === strlen( $prefix ) ) {
			return false;
		}

		$response = str_replace( array( " ", "\t" ), '', $response );
		$response = strtoupper( $response );

		$dir = trailingslashit( $this->tmp_dir );
		$filename = sanitize_file_name( $prefix . '.txt' );
		$file = wp_normalize_path( path_join( $dir, $filename ) );

		if ( is_readable( $file ) and $code = $this->get_contents( $file ) ) {
			$code = explode( '|', $code, 2 );
			$salt = $code[0];
			$hash = $code[1];

			return hash_equals( $hash, hash_hmac( 'sha256', $response, $salt ) );
		}

		return false;
	}


	/**
	 * Remove temporary files with given prefix.
	 *
	 * @param string $prefix File prefix
	 */
	public function remove( $prefix ) {
		$dir = trailingslashit( $this->tmp_dir );
		$suffixes = array( '.jpeg', '.gif', '.png', '.php', '.txt' );

		foreach ( $suffixes as $suffix ) {
			$filename = sanitize_file_name( $prefix . $suffix );
			$file = wp_normalize_path( path_join( $dir, $filename ) );

			if ( is_file( $file ) ) {
				$this->delete( $file );
			}
		}
	}


	/**
	 * Clean up dead files older than given length of time.
	 *
	 * @param int $minutes Consider older files than this time as dead files
	 * @return int|bool The number of removed files. Return false if error occurred.
	 */
	public function cleanup( $minutes = 60, $max = 100 ) {
		$dir = trailingslashit( $this->tmp_dir );
		$dir = wp_normalize_path( $dir );

		if (
			! is_dir( $dir ) or
			! is_readable( $dir ) or
			! wp_is_writable( $dir )
		) {
			return false;
		}

		$count = 0;

		if ( $handle = opendir( $dir ) ) {
			while ( false !== ( $filename = readdir( $handle ) ) ) {
				if ( ! preg_match( '/^[0-9]+\.(php|txt|png|gif|jpeg)$/', $filename ) ) {
					continue;
				}

				$file = wp_normalize_path( path_join( $dir, $filename ) );

				if ( ! file_exists( $file ) or ! $stat = stat( $file ) ) {
					continue;
				}

				if ( ( $stat['mtime'] + $minutes * MINUTE_IN_SECONDS ) < time() ) {
					if ( ! $this->delete( $file ) ) {
						$this->chmod( $file, 0644 );
						$this->delete( $file );
					}

					$count += 1;
				}

				if ( $max <= $count ) {
					break;
				}
			}

			closedir( $handle );
		}

		return $count;
	}


	/**
	 * Make a temporary directory and generate .htaccess file in it.
	 *
	 * @return bool True on successful create, false on failure.
	 */
	public function make_tmp_dir() {
		$dir = trailingslashit( $this->tmp_dir );
		$dir = wp_normalize_path( $dir );

		if ( ! wp_mkdir_p( $dir ) ) {
			return false;
		}

		$htaccess_file = wp_normalize_path( path_join( $dir, '.htaccess' ) );

		if ( file_exists( $htaccess_file ) ) {
			list( $first_line_comment ) = (array) file(
				$htaccess_file,
				FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES
			);

			if ( '# Apache 2.4+' === $first_line_comment ) {
				return true;
			}
		}

		$htaccess_body = '
# Apache 2.4+
<IfModule authz_core_module>
    Require all denied
    <FilesMatch "^\w+\.(jpe?g|gif|png)$">
        Require all granted
    </FilesMatch>
</IfModule>

# Apache 2.2
<IfModule !authz_core_module>
    Order deny,allow
    Deny from all
    <FilesMatch "^\w+\.(jpe?g|gif|png)$">
        Allow from all
    </FilesMatch>
</IfModule>
';

		return $this->put_contents( $htaccess_file, ltrim( $htaccess_body ), 0644 );
	}

}
