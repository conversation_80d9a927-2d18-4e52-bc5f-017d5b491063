=== Online Contact Widget-多合一在线客服插件 ===
Contributors: wbolt,mrkwong
Donate link: https://www.wbolt.com/
Tags: WeChat, QQ, WhatsApp, Telegram, Line, Messenger, Viber, Signal
Requires at least: 5.6
Tested up to: 6.8
Stable tag: 1.2.0
License: GNU General Public License v2.0 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Online Contact Widget（多合一在线客服插件），旨在为WordPress网站提供一系列可配置在线客服支持，包括QQ、微信（微信号、公众号和小程序QR-code）、电话、Email和工单等。

== Description ==

Online Contact Widget，针对WordPress开发的多合一在线客服插件。该插件旨在方便WordPress网站与访客建立多路径沟通渠道，包括QQ、微信、电话、邮箱和留言等（海外站点还可以选WhatsApp，Telegram，Line，Messenger,Viber和Signal）。并且该插件支持自定义外观样式，以符合不同的WordPress主题设计风格。

### 1.基本设置

支持对不同组件进行自定义配置，支持站长配置不同的客服联系方式，包括QQ、微信、电话、邮箱和留言。并且像QQ、微信、电话支持设置多个，比如售前QQ、售后QQ，微信号、微信公众号和微信小程序，销售电话、售后电话、技术支持电话等。
站长可以根据自身网站情况设置组件显示位置，包括左上，左中和左下；右上，右中和右下。一般建议设置为右下比较合适。

独立站或者海外站点可以选用WhatsApp，Telegram，Line，Messenger,Viber和Signal等作为消息联系工具。

### 2.外观设置
插件支持不同的外观设置，比如默认展开客服联系组件，浮标形式或者浮标加窗口形式（注：移动端仅使用浮标形式）。并且可以设置联系人头像、联系人昵称及客服响应文字；欢迎语等。

### 3.高级设置
Pro版本支持更多配置选项，包括：
* **显示页面**-支持所有页面、指定页面或者例外页面显示在线客服组件；
* **浮标及文字**-支持更多的浮标图标及可设置浮标文字；
* **自定义颜色**-可自定义客服图标及客服弹窗窗口头部颜色。
* **自定义颜色**-支持自定义插件额外CSS，以便于有自主开发能力的站长进一步调整插件前端外观。

### 4.工单管理
插件提供访客留言管理工单，以便于站长或者网站运营人员根据线索，及时跟进客户访客留言。

== 其他WP插件 ==

Online Contact Widget是一款专门为WordPress开发的<a href="https://www.wbolt.com/plugins/online-contact-widget?utm_source=wp&utm_medium=link&utm_campaign=ocw" rel="friend" title="WP多合一在线客服插件插件">多合一在线客服插件插件插件</a>. 

闪电博（<a href='https://www.wbolt.com/?utm_source=wp&utm_medium=link&utm_campaign=ocw' rel='friend' title='闪电博官网'>wbolt.com</a>）专注于原创<a href='https://www.wbolt.com/themes' rel='friend' title='WordPress主题'>WordPress主题</a>和<a href='https://www.wbolt.com/plugins' rel='friend' title='WordPress插件'>WordPress插件</a>开发，为中文博客提供更多优质和符合国内需求的主题和插件。此外我们也会分享WordPress相关技巧和教程。

除了多合一在线客服插件插件插件外，目前我们还开发了以下WordPress插件：

- [多合一搜索自动推送管理插件-历史下载安装数200,000+](https://wordpress.org/plugins/baidu-submit-link/)
- [热门关键词推荐插件-最佳关键词布局插件](https://wordpress.org/plugins/smart-keywords-tool/)
- [Smart SEO Tool-高效便捷的WP搜索引擎优化插件](https://wordpress.org/plugins/smart-seo-tool/)
- [Spider Analyser – WordPress搜索引擎蜘蛛分析插件](https://wordpress.org/plugins/spider-analyser/)
- [MagicPost – WordPress文章管理功能增强插件](https://wordpress.org/plugins/magicpost/)
- [IMGspider-轻量外链图片采集插件](https://wordpress.org/plugins/imgspider/)
- [WPTurbo -WordPress性能优化插件](https://wordpress.org/plugins/wpturbo/)
- 更多主题和插件，请访问<a href="https://www.wbolt.com/?utm_source=wp&utm_medium=link&utm_campaign=ocw" rel="friend" title="闪电博官网">wbolt.com</a>!

如果你在WordPress主题和插件上有更多的需求，也希望您可以向我们提出意见建议，我们将会记录下来并根据实际情况，推出更多符合大家需求的主题和插件。

== WordPress资源 ==

由于我们是WordPress重度爱好者，在WordPress主题插件开发之余，我们还独立开发了一系列的在线工具及分享大量的WordPress教程，供国内的WordPress粉丝和站长使用和学习，其中包括：

**<a href="https://www.wbolt.com/learn?utm_source=wp&utm_medium=link&utm_campaign=ocw" target="_blank">1. Wordpress学院</a>:** 这里将整合全面的WordPress知识和教程，帮助您深入了解WordPress的方方面面，包括基础、开发、优化、电商及SEO等。WordPress大师之路，从这里开始。

**<a href="https://www.wbolt.com/tools/keyword-finder?utm_source=wp&utm_medium=link&utm_campaign=ocw" target="_blank">2. 关键词查找工具</a>:** 选择符合搜索用户需求的关键词进行内容编辑，更有机会获得更好的搜索引擎排名及自然流量。使用我们的关键词查找工具，以获取主流搜索引擎推荐关键词。

**<a href="https://www.wbolt.com/tools/wp-fixer?utm_source=wp&utm_medium=link&utm_campaign=ocw">3. WOrdPress错误查找</a>:** 我们搜集了大部分WordPress最为常见的错误及对应的解决方案。您只需要在下方输入所遭遇的错误关键词或错误码，即可找到对应的处理办法。

**<a href="https://www.wbolt.com/tools/seo-toolbox?utm_source=wp&utm_medium=link&utm_campaign=ocw">4. SEO工具箱</a>:** 收集整理国内外诸如链接建设、关键词研究、内容优化等不同类型的SEO工具。善用工具，往往可以达到事半功倍的效果。

**<a href="https://www.wbolt.com/tools/seo-topic?utm_source=wp&utm_medium=link&utm_campaign=ocw">5. SEO优化中心</a>:** 无论您是 SEO 初学者，还是想学习高级SEO 策略，这都是您的 SEO 知识中心。

**<a href="https://www.wbolt.com/tools/spider-tool?utm_source=wp&utm_medium=link&utm_campaign=ocw">6. 蜘蛛查询工具</a>:** 网站每日都可能会有大量的蜘蛛爬虫访问，或者搜索引擎爬虫，或者安全扫描，或者SEO检测……满目琳琅。借助我们的蜘蛛爬虫检测工具，让一切假蜘蛛爬虫无处遁形！

**<a href="https://www.wbolt.com/tools/wp-codex?utm_source=wp&utm_medium=link&utm_campaign=ocw">7. WP开发宝典</a>:** WordPress作为全球市场份额最大CMS，也为众多企业官网、个人博客及电商网站的首选。使用我们的开发宝典，快速了解其函数、过滤器及动作等作用和写法。

**<a href="https://www.wbolt.com/tools/robots-tester?utm_source=wp&utm_medium=link&utm_campaign=ocw">8. robots.txt测试工具</a>:** 标准规范的robots.txt能够正确指引搜索引擎蜘蛛爬取网站内容。反之，可能让蜘蛛晕头转向。借助我们的robots.txt检测工具，校正您所写的规则。

**<a href="https://www.wbolt.com/tools/theme-detector?utm_source=wp&utm_medium=link&utm_campaign=ocw">9. WordPress主题检测器</a>:** 有时候，看到一个您为之着迷的WordPress网站。甚是想知道它背后的主题。查看源代码定可以找到蛛丝马迹，又或者使用我们的小工具，一键查明。

== Installation ==

方式1：在线安装(推荐)
1. 进入WordPress仪表盘，点击`插件-安装插件`，关键词搜索`多合一在线客服插件`，找搜索结果中找到`多合一在线客服插件`插件，点击`现在安装`；
2. 安装完毕后，启用`多合一在线客服插件`插件.
3. 通过`多合一客服`->`插件设置` 进行插件设置。

方式2：上传安装

FTP上传安装
1. 解压插件压缩包`online-contact-widget.zip`，将解压获得文件夹上传至wordpress安装目录下的 `/wp-content/plugins/` 目录.
2. 访问WordPress仪表盘，进入“插件”-“已安装插件”，在插件列表中找到“多合一在线客服插件”插件，点击“启用”.
3. 通过`多合一客服`->`插件设置` 进行插件设置。

仪表盘上传安装
1. 进入WordPress仪表盘，点击`插件-安装插件`；
2. 点击界面左上方的`上传按钮`，选择本地提前下载好的插件压缩包`online-contact-widget.zip`，点击`现在安装`；
3. 安装完毕后，启用`多合一在线客服插件插件`插件；
4. 通过`多合一客服`->`插件设置` 进行插件设置。

关于本插件，你可以通过阅读<a href="https://www.wbolt.com/ocw-plugin-documentation.html?utm_source=wp&utm_medium=link&utm_campaign=ocw" rel="friend" title="插件教程">多合一在线客服插件插件插件教程</a>学习了解插件安装、设置等详细内容。

== Frequently Asked Questions ==

= 目前插件都支持哪些客服联系方式？ =
目前仅支持QQ、微信、邮箱、电话及留言客服联系方式。后续根据情况再考虑更多的客服联系方式，比如Facebook, WhatsApp, Telegram, Viber和Line等。

= 插件是否支持自定义外观CSS样式？ =
你可以通过外观设置或者高级版本设置对插件进行外观样式设置或者自主加入额外CSS配置插件前端外观。

= 插件是否为响应式设计？ =
是的。我们针对PC端和手机端分别做了响应，以适配不同设备。

== Screenshots ==

1. 多合一在线客服插件基本设置截图.
2. 多合一在线客服插件外观设置截图.
3. 多合一在线客服插件高级设置截图.
4. 多合一在线客服插件PC端界面截图.
5. 多合一在线客服插件移动端界面截图.
6. 多合一在线客服插件留言工单管理截图.

== Changelog ==

= 1.2.0 =
* 插件前后端实现支持本地语言（目前除默认简中，预设英文和繁体两种语言）。
* 组件新增预设Line, Whatsapp, Messenger等主流即时通讯工具可供配置调用；并实现自定义功能未涵盖的其他方式。
* 联系工单提供更多联系方式可选择。
* 提供前端组件静态化缓存功能可选择。
* 插件设置结构调整：组件设置作为独立列表，基本设置和外观设置合并。
* 设置技术升级，完善优化交互细节。

= 1.1.0 =
* 增加前端界面语言包支持（仅预翻译了中文语言，旧版本用户切换英文可能存在中英混合情况，需手动改为英文）。
* 修正咨询类型后台设置值保存异常的问题。

= 1.0.11 =
* 修复更换域名插件无法使用问题。

= 1.0.10 =
* 修复组件设置无法保存的bug。

= 1.0.9 =
* 基于编码规范进一步优化PHP代码；
* 优化PHP代码以提升性能；
* 优化PHP代码以增强代码安全性。

= 1.0.8 =
* 新增会员中心支持；
* 新增付费内容插件我的订单入口支持。

= 1.0.7 =
* 修复移动端弹窗错位bug。

= 1.0.6 =
* 新增登录留言支持；
* 新增在线留言短信通知支持；
* 新增微信ID字段，以便于移动端微信联系直接调用微信APP；
* 新增位置可微调(Pro版)；
* 优化移动端界面样式及交互。

= 1.0.5 =
* 优化插件部分PHP代码。

= 1.0.4 =

* 新增对主题或其他插件类似组件可隐藏配置；
* 新增单浮标模式下，焦点动效（可配置开关及轮换时长）；
* 新增在线留言邮件和短信通知支持；
* 新增欢迎语字段自定义支持；
* 新增工单管理列表状态项及操作项；
* 优化在线留言字段及前端交互体验；
* 优化插件前端界面UI；
* 优化在特定页面显示插件逻辑；
* 优化浮标模式下不同联系方式切换的交互效果；
* 优化对暗黑模式支持，配置主题相应暗黑模式即可实现跟随主题切换显示插件配色风格；
* PRO版：支持插件主色调可配置；
* PRO版：支持自定义浮标图标；相应图标配色及大小支持可配置；
* PRO版：支持展开模式的尺寸，圆角，留言窗口宽度可配置；
* 完善插件设置即时预览效果。

= 1.0.3 =

* 新增桌面端/移动端显示设备选项支持；
* 新增组件设置拖拽排序支持；
* 优化前端页面例外页面逻辑；
* 优化前端聊天窗口换行文字行宽；
* 修复提交成功提示语设置无效bug；
* 修复暗黑模式和浮标名称开关无效bug。

= 1.0.2 =

* 修复前端CSS样式与部分主题不兼容问题。

= 1.0.1 =
* 修复部分主题移动端在线客服窗口样式异常bug；
* 优化PC端微信组件展开尺寸大小。

= 1.0.0 =
* 新增插件基本设置；
* 新增插件外观设置；
* 新增插件高级设置；
* 新增留言工单管理功能；
* 新增PC端和移动端前端交互。