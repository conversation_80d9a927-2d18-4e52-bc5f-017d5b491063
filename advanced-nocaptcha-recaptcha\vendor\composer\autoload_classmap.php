<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'C4WP\\C4WP_Captcha_Class' => $baseDir . '/includes/class-c4wp-captcha-class.php',
    'C4WP\\C4WP_Functions' => $baseDir . '/includes/class-c4wp-functions.php',
    'C4WP\\Methods\\C4WP_Method_Loader' => $baseDir . '/includes/methods/class-c4wp-method-loader.php',
    'C4WP\\Methods\\Captcha' => $baseDir . '/includes/methods/class-captcha.php',
    'C4WP\\Methods\\Cloudflare' => $baseDir . '/includes/methods/class-cloudflare.php',
    'C4WP\\Methods\\HCaptcha' => $baseDir . '/includes/methods/class-hcaptcha.php',
    'C4WP\\PluginUpdatedNotice' => $baseDir . '/admin/admin-notices/class-plugin-updated-notice.php',
    'C4WP_Settings' => $baseDir . '/admin/class-c4wp-settings.php',
);
