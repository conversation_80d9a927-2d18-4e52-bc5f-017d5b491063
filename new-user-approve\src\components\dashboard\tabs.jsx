import React, { Component, useEffect, useState } from 'react';
import { styled } from '@mui/system';
import { Tabs} from '@mui/base/Tabs';
import { TabsList  } from '@mui/base/TabsList';
import { TabPanel  } from '@mui/base/TabPanel';
import { Tab , tabClasses } from '@mui/base/Tab';
// import { get_general_settings } from '../../functions';
import { update_general_settings } from '../../functions';
import Add_Code_SubTabs from '../invitation-code/add-code-subtabs';
import Recent_Users from './recent-users';
import Users_SubTabs from './users-sub-tabs';
// import Users_SubTabs from './tes';
import Users from './fetch_users/users';
// import Auto_Approve from './auto-approve';
import Auto_Approve from './auto-approve';
import Integrations from './integrations/integrations';
// import Invitation from '../invitation-code/invitation-code-main-tabs';
import Settings from '../settings/tabs/general';
import { <PERSON>rowserRouter as Router, Routes, Route, Link, useNavigate, useLocation } from 'react-router-dom';

// wordpress i18n
import { sprintf, __ } from '@wordpress/i18n';
const icons = require.context('../../assets/icons', false, /\.svg$/);





const NUA_Dash_Tabs = () => {
// const [showInvitationTab, setShowInvitationTab] = useState(false);   
 useEffect(() => {
    
    // const general_settings = async () => {

    //     const response = await get_general_settings();
    //     if(response.data.data.nua_invitation_code === true){
    //         setShowInvitationTab(true);
    //     }
  
    // }
    // general_settings();
}, [])

    
        let dash_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 6C2 4.11438 2 3.17157 2.58579 2.58579C3.17157 2 4.11438 2 6 2C7.88562 2 8.82843 2 9.41421 2.58579C10 3.17157 10 4.11438 10 6V8C10 9.88562 10 10.8284 9.41421 11.4142C8.82843 12 7.88562 12 6 12C4.11438 12 3.17157 12 2.58579 11.4142C2 10.8284 2 9.88562 2 8V6Z" fill="#242424"/>
            <path d="M2 19C2 18.0681 2 17.6022 2.15224 17.2346C2.35523 16.7446 2.74458 16.3552 3.23463 16.1522C3.60218 16 4.06812 16 5 16H7C7.93188 16 8.39782 16 8.76537 16.1522C9.25542 16.3552 9.64477 16.7446 9.84776 17.2346C10 17.6022 10 18.0681 10 19C10 19.9319 10 20.3978 9.84776 20.7654C9.64477 21.2554 9.25542 21.6448 8.76537 21.8478C8.39782 22 7.93188 22 7 22H5C4.06812 22 3.60218 22 3.23463 21.8478C2.74458 21.6448 2.35523 21.2554 2.15224 20.7654C2 20.3978 2 19.9319 2 19Z" fill="#242424"/>
            <path d="M14 16C14 14.1144 14 13.1716 14.5858 12.5858C15.1716 12 16.1144 12 18 12C19.8856 12 20.8284 12 21.4142 12.5858C22 13.1716 22 14.1144 22 16V18C22 19.8856 22 20.8284 21.4142 21.4142C20.8284 22 19.8856 22 18 22C16.1144 22 15.1716 22 14.5858 21.4142C14 20.8284 14 19.8856 14 18V16Z" fill="#242424"/>
            <path d="M14 5C14 4.06812 14 3.60218 14.1522 3.23463C14.3552 2.74458 14.7446 2.35523 15.2346 2.15224C15.6022 2 16.0681 2 17 2H19C19.9319 2 20.3978 2 20.7654 2.15224C21.2554 2.35523 21.6448 2.74458 21.8478 3.23463C22 3.60218 22 4.06812 22 5C22 5.93188 22 6.39782 21.8478 6.76537C21.6448 7.25542 21.2554 7.64477 20.7654 7.84776C20.3978 8 19.9319 8 19 8H17C16.0681 8 15.6022 8 15.2346 7.84776C14.7446 7.64477 14.3552 7.25542 14.1522 6.76537C14 6.39782 14 5.93188 14 5Z" fill="#242424"/>
            </svg>

        );
        let users_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" > <path d="M6.57757 15.1335C5.1628 15.8917 1.45336 17.4398 3.71266 19.377C4.81631 20.3233 6.04549 21.0001 7.59087 21.0001H16.4091C17.9545 21.0001 19.1837 20.3233 20.2873 19.377C22.5466 17.4398 18.8372 15.8917 17.4224 15.1335C14.1048 13.3556 9.89519 13.3556 6.57757 15.1335Z" fill="#242424" /> <path d="M16.5 7.5C16.5 9.98528 14.4853 12 12 12C9.51472 12 7.5 9.98528 7.5 7.5C7.5 5.01472 9.51472 3 12 3C14.4853 3 16.5 5.01472 16.5 7.5Z" fill="#242424" /> </svg>
        );
        let user_roles_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15.5 14.5C18.8137 14.5 21.5 11.8137 21.5 8.5C21.5 5.18629 18.8137 2.5 15.5 2.5C12.1863 2.5 9.5 5.18629 9.5 8.5C9.5 9.38041 9.68962 10.2165 10.0303 10.9697L2.5 18.5V21.5H5.5V19.5H7.5V17.5H9.5L13.0303 13.9697C13.7835 14.3104 14.6196 14.5 15.5 14.5Z" fill="#242424"/>
            <path d="M17.5 6.5L16.5 7.5L17.5 6.5Z" fill="white"/>
            <path d="M17.5 6.5L16.5 7.5" stroke="white" stroke-width="1.54" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        );
        let invitation_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 9.36L12 14.36L20 9.36L12 4.36L4 9.36ZM22 9.36V19.36C22 19.8904 21.7893 20.3991 21.4142 20.7742C21.0391 21.1493 20.5304 21.36 20 21.36H4C3.46957 21.36 2.96086 21.1493 2.58579 20.7742C2.21071 20.3991 2 19.8904 2 19.36V9.36C2 8.63 2.39 8 2.97 7.65L12 2L21.03 7.65C21.61 8 22 8.63 22 9.36Z" fill="#242424"/>
            </svg>

        );
        let auto_approve_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14.9258 12.7775L12.7775 10.6292C12.4847 10.3364 12.3383 10.19 12.1803 10.1117C11.8798 9.96277 11.527 9.96277 11.2264 10.1117C11.0685 10.19 10.9221 10.3364 10.6292 10.6292C10.3364 10.9221 10.19 11.0685 10.1117 11.2264C9.96277 11.527 9.96277 11.8798 10.1117 12.1803C10.19 12.3383 10.3364 12.4847 10.6292 12.7775L12.7775 14.9258M14.9258 12.7775L21.3708 19.2225C21.6636 19.5153 21.81 19.6617 21.8883 19.8197C22.0372 20.1202 22.0372 20.473 21.8883 20.7736C21.81 20.9315 21.6636 21.0779 21.3708 21.3708C21.0779 21.6636 20.9315 21.81 20.7736 21.8883C20.473 22.0372 20.1202 22.0372 19.8197 21.8883C19.6617 21.81 19.5153 21.6636 19.2225 21.3708L12.7775 14.9258M14.9258 12.7775L12.7775 14.9258L14.9258 12.7775Z" fill="#242424"/>
            <path d="M18 2L18.2948 2.7966C18.6813 3.84117 18.8746 4.36345 19.2556 4.74445C19.6366 5.12545 20.1588 5.31871 21.2034 5.70523L22 6L21.2034 6.29477C20.1588 6.68129 19.6366 6.87456 19.2556 7.25555C18.8746 7.63655 18.6813 8.15883 18.2948 9.2034L18 10L17.7052 9.2034C17.3187 8.15884 17.1254 7.63655 16.7444 7.25555C16.3634 6.87455 15.8412 6.68129 14.7966 6.29477L14 6L14.7966 5.70523C15.8412 5.31871 16.3634 5.12545 16.7444 4.74445C17.1254 4.36345 17.3187 3.84117 17.7052 2.7966L18 2Z" fill="#242424"/>
            <path d="M5 4L5.22108 4.59745C5.51097 5.38087 5.65592 5.77259 5.94167 6.05834C6.22741 6.34408 6.61913 6.48903 7.40255 6.77892L8 7L7.40255 7.22108C6.61913 7.51097 6.22741 7.65592 5.94166 7.94167C5.65592 8.22741 5.51097 8.61913 5.22108 9.40255L5 10L4.77892 9.40255C4.48903 8.61913 4.34408 8.22741 4.05833 7.94167C3.77259 7.65592 3.38087 7.51097 2.59745 7.22108L2 7L2.59745 6.77892C3.38087 6.48903 3.77259 6.34408 4.05833 6.05833C4.34408 5.77259 4.48903 5.38087 4.77892 4.59745L5 4Z" fill="#242424"/>
            </svg>

        );
        let integration_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16.5 2C15.668 1.99982 14.8468 2.18841 14.0982 2.55157C13.3496 2.91472 12.6932 3.44298 12.1783 4.09656C11.6635 4.75014 11.3036 5.51202 11.1258 6.32482C10.9481 7.13762 10.957 7.98016 11.152 8.789L2.841 17.1C2.56998 17.3656 2.3543 17.6823 2.20643 18.0318C2.05857 18.3812 1.98145 18.7565 1.97953 19.136C1.97761 19.5155 2.05094 19.8915 2.19527 20.2425C2.3396 20.5934 2.55207 20.9123 2.82039 21.1806C3.08871 21.4489 3.40756 21.6614 3.75851 21.8057C4.10946 21.9501 4.48553 22.0234 4.86499 22.0215C5.24445 22.0196 5.61977 21.9424 5.96924 21.7946C6.31871 21.6467 6.6354 21.431 6.901 21.16L11.016 17.047C11.0888 16.0003 11.414 14.9869 11.9636 14.0932C12.5133 13.1995 13.2711 12.4521 14.1723 11.9149C15.0735 11.3777 16.0914 11.0667 17.139 11.0084C18.1866 10.95 19.2327 11.1462 20.188 11.58C20.7585 11.0644 21.2145 10.4349 21.5266 9.73211C21.8387 9.02935 22 8.26895 22 7.5C22.0007 6.90693 21.9055 6.31766 21.718 5.755C21.6774 5.63378 21.6066 5.52493 21.5121 5.43879C21.4177 5.35266 21.3028 5.29208 21.1784 5.26283C21.054 5.23357 20.9241 5.23661 20.8012 5.27164C20.6783 5.30668 20.5663 5.37255 20.476 5.463L18.032 7.907C17.8914 8.04745 17.7007 8.12634 17.502 8.12634C17.3032 8.12634 17.1126 8.04745 16.972 7.907L16.093 7.029C15.9525 6.88838 15.8737 6.69775 15.8737 6.499C15.8737 6.30025 15.9525 6.10963 16.093 5.969L18.538 3.524C18.6282 3.4336 18.6938 3.32166 18.7286 3.1988C18.7635 3.07594 18.7664 2.94621 18.737 2.82193C18.7077 2.69764 18.6471 2.5829 18.561 2.48859C18.4749 2.39428 18.3661 2.32351 18.245 2.283C17.6824 2.09519 17.0931 1.99963 16.5 2ZM14.277 13.976C14.3516 14.2345 14.3733 14.5055 14.3407 14.7726C14.3082 15.0397 14.222 15.2975 14.0875 15.5306C13.953 15.7636 13.7728 15.9671 13.5577 16.1289C13.3427 16.2906 13.0972 16.4073 12.836 16.472L12.252 16.616C12.1578 17.2151 12.1598 17.8255 12.258 18.424L12.798 18.554C13.0616 18.6175 13.3095 18.7339 13.5267 18.8962C13.7439 19.0585 13.9258 19.2632 14.0614 19.4979C14.1971 19.7327 14.2835 19.9926 14.3156 20.2618C14.3477 20.531 14.3247 20.804 14.248 21.064L14.061 21.695C14.501 22.081 15.001 22.394 15.546 22.617L16.039 22.098C16.2258 21.9015 16.4507 21.745 16.6998 21.6381C16.949 21.5312 17.2173 21.476 17.4885 21.476C17.7597 21.476 18.028 21.5312 18.2772 21.6381C18.5263 21.745 18.7512 21.9015 18.938 22.098L19.437 22.623C19.9779 22.4024 20.4787 22.0939 20.919 21.71L20.721 21.024C20.6464 20.7654 20.6248 20.4944 20.6574 20.2272C20.69 19.96 20.7762 19.7022 20.9109 19.4692C21.0455 19.2361 21.2258 19.0326 21.441 18.8709C21.6561 18.7092 21.9017 18.5925 22.163 18.528L22.746 18.384C22.8402 17.7849 22.8382 17.1745 22.74 16.576L22.2 16.446C21.9365 16.3824 21.6887 16.2659 21.4716 16.1036C21.2545 15.9413 21.0727 15.7365 20.9372 15.5018C20.8017 15.267 20.7153 15.0072 20.6833 14.738C20.6513 14.4688 20.6743 14.196 20.751 13.936L20.937 13.306C20.4967 12.9187 19.9953 12.6071 19.453 12.384L18.96 12.902C18.7732 13.0987 18.5483 13.2553 18.299 13.3623C18.0497 13.4693 17.7813 13.5245 17.51 13.5245C17.2387 13.5245 16.9703 13.4693 16.721 13.3623C16.4717 13.2553 16.2468 13.0987 16.06 12.902L15.562 12.377C15.018 12.597 14.518 12.907 14.079 13.289L14.277 13.976ZM17.5 19C16.7 19 16.05 18.328 16.05 17.5C16.05 16.672 16.7 16 17.5 16C18.3 16 18.95 16.672 18.95 17.5C18.95 18.328 18.3 19 17.5 19Z" fill="#242424"/>
            </svg>

        );
        let setting_icon = (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21.7138 10.4801L19.4805 9.81344C19.3258 9.2744 19.1135 8.75361 18.8471 8.2601L19.9405 6.21343C19.9815 6.13618 19.9965 6.04776 19.9833 5.96129C19.9701 5.87482 19.9294 5.79492 19.8671 5.73344L18.2738 4.13344C18.2123 4.07121 18.1324 4.03048 18.0459 4.01727C17.9595 4.00406 17.8711 4.01908 17.7938 4.0601L15.7605 5.14677C15.2623 4.86711 14.7346 4.64352 14.1871 4.4801L13.5205 2.27344C13.4923 2.19211 13.439 2.12183 13.3683 2.07272C13.2976 2.0236 13.2132 1.99817 13.1271 2.0001H10.8738C10.7872 2.00051 10.703 2.02853 10.6335 2.08008C10.564 2.13164 10.5127 2.20405 10.4871 2.28677L9.82047 4.48677C9.26842 4.64926 8.73624 4.87287 8.2338 5.15344L6.2338 4.07344C6.15655 4.03241 6.06812 4.01739 5.98166 4.0306C5.89519 4.04381 5.81528 4.08455 5.7538 4.14677L4.1338 5.72677C4.07158 5.78825 4.03084 5.86815 4.01763 5.95462C4.00442 6.04109 4.01944 6.12951 4.06047 6.20677L5.14047 8.20677C4.86036 8.70709 4.63676 9.23702 4.4738 9.78677L2.26714 10.4534C2.18442 10.479 2.11201 10.5303 2.06045 10.5998C2.00889 10.6693 1.98087 10.7535 1.98047 10.8401V13.0934C1.98087 13.18 2.00889 13.2642 2.06045 13.3337C2.11201 13.4033 2.18442 13.4546 2.26714 13.4801L4.48714 14.1468C4.65193 14.6873 4.87549 15.2082 5.1538 15.7001L4.06047 17.7934C4.01944 17.8707 4.00442 17.9591 4.01763 18.0456C4.03084 18.132 4.07158 18.212 4.1338 18.2734L5.72714 19.8668C5.78862 19.929 5.86852 19.9697 5.95499 19.9829C6.04146 19.9961 6.12988 19.9811 6.20714 19.9401L8.26713 18.8401C8.75455 19.1028 9.26856 19.3129 9.80047 19.4668L10.4671 21.7134C10.4927 21.7962 10.544 21.8686 10.6135 21.9201C10.683 21.9717 10.7672 21.9997 10.8538 22.0001H13.1071C13.1937 21.9997 13.2779 21.9717 13.3474 21.9201C13.417 21.8686 13.4683 21.7962 13.4938 21.7134L14.1605 19.4601C14.688 19.3058 15.1975 19.0957 15.6805 18.8334L17.7538 19.9401C17.8311 19.9811 17.9195 19.9961 18.0059 19.9829C18.0924 19.9697 18.1723 19.929 18.2338 19.8668L19.8271 18.2734C19.8894 18.212 19.9301 18.132 19.9433 18.0456C19.9565 17.9591 19.9415 17.8707 19.9005 17.7934L18.7938 15.7268C19.0588 15.242 19.2712 14.7302 19.4271 14.2001L21.6738 13.5334C21.7565 13.5079 21.8289 13.4566 21.8805 13.3871C21.932 13.3175 21.9601 13.2333 21.9605 13.1468V10.8734C21.9644 10.7905 21.9429 10.7084 21.8988 10.6381C21.8547 10.5678 21.7901 10.5127 21.7138 10.4801ZM12.0005 15.6668C11.2753 15.6668 10.5664 15.4517 9.96338 15.0488C9.3604 14.6459 8.89043 14.0733 8.61291 13.4033C8.33539 12.7333 8.26278 11.996 8.40426 11.2848C8.54573 10.5735 8.89495 9.92017 9.40774 9.40738C9.92054 8.89458 10.5739 8.54537 11.2851 8.40389C11.9964 8.26241 12.7336 8.33502 13.4036 8.61254C14.0736 8.89007 14.6463 9.36003 15.0492 9.96301C15.4521 10.566 15.6671 11.2749 15.6671 12.0001C15.6671 12.9726 15.2808 13.9052 14.5932 14.5928C13.9056 15.2805 12.9729 15.6668 12.0005 15.6668Z" fill="#242424"/>
            </svg>
        );
        const navigate = useNavigate();
        const location = useLocation();
        const currentTab =  location.pathname === '/'  ? 'dashboard' : location.pathname.split('/')[1];
       
        const handleTabChange = (event, newTab) => {
            if(newTab == 'dashboard') {
                navigate('/');
            }
            else {
            navigate(`/${newTab}`)
            }
        }

        return (
            
            <Tabs className = "nua_dash_parent_tabs" defaultValue={'dashboard'} value={currentTab} onChange={handleTabChange}>
                <TabsList className="nua_dash_parent_tablist">
                <Tab 
                    value={'dashboard'} 
                    component={Link} 
                    to={'/dashboard'} 
                    className={currentTab == 'dashboard' ? 'nua_active_tab' : ''}
                    datatarget={'dashboard'}
                >
                    <span>
                        {dash_icon}
                        {__('Dashboard', 'new-user-approve')}
                    </span>
                </Tab>
                <Tab 
                    value={'action=users'} 
                    component={Link} 
                    to={'/action=users'} 
                    className={currentTab == 'action=users' ? 'nua_active_tab' : ''}
                    onClick={() => window.location.hash = '#/action=users/tab=all-users'}
                    datatarget={'users'}
                >
                    <span>
                        {users_icon}
                        {__('Users', 'new-user-approve')}
                    </span>
                </Tab>
            
              
                <Tab
                    value={'action=inv-codes'}
                    component={Link}
                    to={'/action=inv-codes'}
                    className={currentTab === 'action=inv-codes' ? 'nua_active_tab' : ''}
                    onClick={() => window.location.hash = '#/action=inv-codes/tab=all-codes'}
                    datatarget={'inv code'}
                >
                    <span>
                    {invitation_icon}
                    {__('Invitation Code', 'new-user-approve')}
                    </span>
                </Tab>
               

                <Tab 
                    value={'action=auto-approve'} 
                    component={Link} 
                    to={'/action=auto-approve'} 
                    className={currentTab == 'action=auto-approve' ? 'nua_active_tab' : ''}
                    onClick={() => window.location.hash = '#/action=auto-approve/tab=whitelist'}
                    datatarget={'auto approve'}
                >
                    <span>
                        {auto_approve_icon}
                        {__('Auto Approve', 'new-user-approve')}
                    </span>
                </Tab>
                <Tab 
                    value={'action=integrations'} 
                    component={Link} 
                    to={'/action=integrations'} 
                    className={currentTab == 'action=integrations' ? 'nua_active_tab' : ''}
                    onClick={() => window.location.hash = '#/action=integrations'}
                >
                    <span>
                    {integration_icon}
                        {__('Integration', 'new-user-approve')}
                    </span>
                </Tab>
                <Tab 
                    value={'action=settings'} 
                    component={Link} 
                    to={'/action=settings'} 
                    className={currentTab == 'action=settings' ? 'nua_active_tab' : ''}
                    onClick={() => window.location.hash = '#/action=settings/tab=general'}
                >
                    <span>
                    {setting_icon}
                        {__('Settings', 'new-user-approve')}
                    </span>
                </Tab>
                </TabsList>

                <TabPanel className ="dash-tabPanel" value={"dashboard"} index="dashboard">
                    <Recent_Users/>
                </TabPanel>

                <TabPanel className="users-main-tabpanel" value={'action=users'} index="users">
                    <Users_SubTabs/>
                </TabPanel>

                <TabPanel className ="invitation-main-tabpanel" value={'action=inv-codes'} index='inv-codes'>
                        <Add_Code_SubTabs/>
                </TabPanel>

                <TabPanel className='auto-approve-main-tabpanel' value={"action=auto-approve"} index="auto-approve">
                   <Auto_Approve/>
                </TabPanel>

                <TabPanel className='integration-main-tabpanel' value={"action=integrations"}>
                   <Integrations/>
                </TabPanel>

                <TabPanel className='setting-main-tabpanel generalTabPage' value={"action=settings"}>
                   <Settings/>
                </TabPanel>

            
            </Tabs>

        );
    }       
      



export default NUA_Dash_Tabs;