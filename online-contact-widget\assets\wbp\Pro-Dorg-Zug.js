import{c as l,aF as e,aG as a,d as s,al as o,j as t,a as u,h as n,l as i,m as c,w as d,N as m,J as r,s as p,u as _,n as b,az as f,ap as v,q as w,r as h,v as V,t as y,R as k,O as g,aH as x,Z as U,aI as z,_ as C,y as j,K as S,L as P,U as B,V as E,o as T,aJ as O,W as R,X as $,Y as D,S as I,$ as L,D as A,a0 as F,Q as H,P as J,a2 as K,aK as M,aL as q,ay as G,aA as N,a1 as Q,a4 as W,a5 as X,a6 as Y}from"./wbs-Dtem2-xP.js";import{a as Z,E as ll}from"./el-tab-pane-DsCLh1_f.js";import{E as el,a as al,b as sl}from"./el-switch-D8RcE-lN.js";import{E as ol,a as tl}from"./el-radio-CJI16kOl.js";/* empty css                          */import{a as ul,E as nl}from"./el-checkbox-O5cmms8-.js";import il from"./Preview-cssRhrhC.js";import{S as cl}from"./Svg-23wbG-YI.js";import"./strings-BZz7wQvk.js";import"./index-CfGGe3vq.js";const dl=l({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:e(a),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:["light","dark"],default:"light"}}),ml={close:l=>l instanceof MouseEvent},rl=s({name:"ElAlert"});const pl=j(C(s({...rl,props:dl,emits:ml,setup(l,{emit:e}){const s=l,{Close:C}=x,j=o(),S=t("alert"),P=u(!0),B=n((()=>a[s.type])),E=n((()=>!(!s.description&&!j.default))),T=l=>{P.value=!1,e("close",l)};return(l,e)=>(c(),i(z,{name:_(S).b("fade"),persisted:""},{default:d((()=>[m(r("div",{class:p([_(S).b(),_(S).m(l.type),_(S).is("center",l.center),_(S).is(l.effect)]),role:"alert"},[l.showIcon&&_(B)?(c(),i(_(f),{key:0,class:p([_(S).e("icon"),{[_(S).is("big")]:_(E)}])},{default:d((()=>[(c(),i(v(_(B))))])),_:1},8,["class"])):b("v-if",!0),r("div",{class:p(_(S).e("content"))},[l.title||l.$slots.title?(c(),w("span",{key:0,class:p([_(S).e("title"),{"with-description":_(E)}])},[h(l.$slots,"title",{},(()=>[V(y(l.title),1)]))],2)):b("v-if",!0),_(E)?(c(),w("p",{key:1,class:p(_(S).e("description"))},[h(l.$slots,"default",{},(()=>[V(y(l.description),1)]))],2)):b("v-if",!0),l.closable?(c(),w(k,{key:2},[l.closeText?(c(),w("div",{key:0,class:p([_(S).e("close-btn"),_(S).is("customed")]),onClick:T},y(l.closeText),3)):(c(),i(_(f),{key:1,class:p(_(S).e("close-btn")),onClick:T},{default:d((()=>[g(_(C))])),_:1},8,["class"]))],64)):b("v-if",!0)],2)],2),[[U,P.value]])])),_:3},8,["name"]))}}),[["__file","alert.vue"]])),_l={class:"wbs-content"},bl={class:"wbs-main"},fl={class:"align-right mt"},vl={class:"ml"},wl={key:1,class:"wbs-form-table"},hl={class:"row"},Vl={class:"row w8em"},yl={key:0,class:"with-sub-form-table mt"},kl={key:1,class:"with-sub-form-table mt"},gl={class:"row"},xl={class:"row"},Ul={class:"ml description"},zl={class:"row"},Cl={class:"wbs-form-table-sub"},jl={class:"row w8em"},Sl={class:"row"},Pl={class:"row"},Bl={class:"wb-icon"},El=["xlink:href"],Tl={key:0},Ol=["src"],Rl={key:0},$l={class:"row"},Dl={class:"row"},Il={class:"row"},Ll={class:"wb-ib-label"},Al={class:"row"},Fl={class:"wb-ib-label"},Hl={class:"ib"},Jl={class:"ib"},Kl={class:"wbs-form-table-sub"},Ml={class:"row w8em"},ql={class:"row"},Gl={class:"wb-ib-label"},Nl={class:"row"},Ql={class:"wb-ib-label"},Wl={class:"wbs-form-table-sub"},Xl={class:"row w8em"},Yl={class:"row"},Zl={class:"row"},le={class:"row w8em"},ee={__name:"Pro",setup(l){const{wbsCnf:e}=S(),{wb_i18n:a,wb_e:s}=P(e),o=u(0),t=u(!1),n=u(e.is_pro),f=B({}),v=B({}),h=u(!1),x=u([]),z=u([]),C=B([{name:E("左中"),value:"lc"},{name:E("左下"),value:"lb"},{name:E("右中"),value:"rc"},{name:E("右下"),value:"rb"}]),j=async l=>{if(n.value){f.appoint_urls=dl(x.value),f.exception_urls=dl(z.value);try{await L.saveData({op:e.action.push,opt:f}),F.success(s("设置保存成功")),o.value=0,l&&l()}catch(a){F.error(s("保存失败"))}}},dl=l=>l.filter((l=>l.url)).map((l=>l.url)).join(","),ml=l=>{t.value=!1,"appoint"===l?x.value.push({url:""}):z.value.push({url:""}),t.value=!0},rl=(l,e)=>{Y.confirm(s("确认删除?")).then((()=>{t.value=!1,x.value.splice(e,1),t.value=!0}))};return T((()=>{e.is_pro&&O(),(async()=>{var l,a;try{const s=await L.getData({op:e.action.fetch});Object.assign(f,s.data.opt),Object.assign(v,s.data.cnf),n.value&&(x.value=(null==(l=f.appoint_urls)?void 0:l.split(",").map((l=>({url:l}))))||[],z.value=(null==(a=f.exception_urls)?void 0:a.split(",").map((l=>({url:l}))))||[]),t.value=!0,A((()=>{o.value=0}))}catch(u){F.error(s("数据加载失败"))}})()})),R(f,(()=>o.value++),{deep:!0}),$(((l,e,a)=>{n.value&&o.value>0?Y({dangerouslyUseHTMLString:!0,confirmButtonText:s("保存并离开"),cancelButtonText:s("放弃修改"),showCancelButton:!0,message:s("您修改的设置尚未保存，确定离开此页面吗？"),beforeClose:async(l,e,s)=>{if("confirm"===l)try{await j(),s(),a()}catch(o){s(),a(!1)}else s(),a()}}):a()})),(l,u)=>{const S=H,P=pl,B=el,E=nl,T=ul,O=tl,R=ol,$=J,L=al,A=sl,F=Q,Y=ll,dl=Z,ee=W,ae=X,se=K("wbs-ctrl-bar"),oe=D;return c(),w("div",_l,[m((c(),w("div",{class:p(["wbs-content-inner",{"wb-page-loaded":t.value}])},[r("div",bl,[n.value?b("",!0):(c(),i(P,{key:0,type:"warning","show-icon":""},{default:d((()=>[r("span",null,y(_(s)("当前为Free版，高级功能需")),1),_(e).is_mobile?(c(),w("a",{key:0,class:"ml",onClick:u[0]||(u[0]=M(((...l)=>_(q)&&_(q)(...l)),["stop"]))},y(_(s)("升级为PRO版")),1)):(c(),i(S,{key:1,type:"primary",size:"small",class:"ml",onClick:M(_(q),["stop"])},{default:d((()=>[V(y(_(s)("升级为PRO版")),1)])),_:1},8,["onClick"]))])),_:1})),r("div",fl,[g(B,{"active-value":"1","inactive-value":"0",modelValue:h.value,"onUpdate:modelValue":u[1]||(u[1]=l=>h.value=l)},null,8,["modelValue"]),r("span",vl,y(_(s)("预览")),1)]),t.value?(c(),w("table",wl,[r("tbody",null,[r("tr",null,[r("th",hl,y(_(s)("显示设备")),1),r("td",null,[g(T,{modelValue:f.active_device,"onUpdate:modelValue":u[2]||(u[2]=l=>f.active_device=l)},{default:d((()=>[g(E,{value:"0"},{default:d((()=>[V(y(_(s)("桌面端")),1)])),_:1}),g(E,{value:"1"},{default:d((()=>[V(y(_(s)("移动端")),1)])),_:1})])),_:1},8,["modelValue"])])]),r("tr",null,[r("th",Vl,y(_(s)("显示页面")),1),r("td",null,[g(R,{modelValue:f.active_page,"onUpdate:modelValue":u[3]||(u[3]=l=>f.active_page=l)},{default:d((()=>[g(O,{value:"0"},{default:d((()=>[V(y(_(s)("全部页面")),1)])),_:1}),g(O,{value:"1"},{default:d((()=>[V(y(_(s)("指定页面")),1)])),_:1}),g(O,{value:"2"},{default:d((()=>[V(y(_(s)("例外页面")),1)])),_:1})])),_:1},8,["modelValue"]),"1"===f.active_page?(c(),w("div",yl,[(c(!0),w(k,null,I(x.value,((l,e)=>(c(),w("div",{class:p([{mt:e>0},"ml"]),key:e},[g($,{modelValue:l.url,"onUpdate:modelValue":e=>l.url=e,size:"small"},{append:d((()=>[g(S,{slot:"append",icon:_(G),onClick:l=>rl(0,e)},null,8,["icon","onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue"])],2)))),128)),r("div",{class:p(["mt",f.appoint_urls?"align-right":"align-center"])},[g(S,{plain:"",size:"small",icon:_(N),onClick:u[4]||(u[4]=l=>ml("appoint"))},{default:d((()=>[V(y(_(s)("添加")),1)])),_:1},8,["icon"])],2)])):b("",!0),"2"===f.active_page?(c(),w("div",kl,[(c(!0),w(k,null,I(z.value,((l,e)=>(c(),w("div",{class:p([{mt:e>0},"ml"]),key:e},[g($,{modelValue:l.url,"onUpdate:modelValue":e=>l.url=e,size:"small"},{append:d((()=>[g(S,{slot:"append",icon:_(G),onClick:l=>rl(0,e)},null,8,["icon","onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue"])],2)))),128)),r("div",{class:p(["mt",f.exception_urls?"align-right":"align-center"])},[g(S,{plain:"",size:"small",icon:_(N),onClick:u[5]||(u[5]=l=>ml("exception"))},{default:d((()=>[V(y(_(s)("添加")),1)])),_:1},8,["icon"])],2)])):b("",!0)])]),r("tr",null,[r("th",gl,y(_(s)("主色调")),1),r("td",null,[g(L,{modelValue:f.custom_theme_color,"onUpdate:modelValue":u[6]||(u[6]=l=>f.custom_theme_color=l),"show-alpha":"",predefine:["#0066cc","#a02533","#ca891e","#6bb020","#8b572a"]},null,8,["modelValue"])])]),r("tr",null,[r("th",xl,y(_(s)("缓存功能")),1),r("td",null,[g(B,{"active-value":"1","inactive-value":"0",modelValue:f.cache_switch,"onUpdate:modelValue":u[7]||(u[7]=l=>f.cache_switch=l)},null,8,["modelValue"]),r("span",Ul,[r("b",null,y(1==f.cache_switch?_(s)("已开启，"):_(s)("未启用，")),1),V(y(_(s)("该功能会将模块静态化处理（适用于性能优化）。")),1)])])]),r("tr",null,[r("th",zl,y(_(s)("细节定制")),1),r("td",null,[g(dl,{type:"border-card"},{default:d((()=>[g(Y,{label:_(s)("浮标模式")},{default:d((()=>[r("table",Cl,[r("tbody",null,[r("tr",null,[r("th",jl,y(_(s)("浮标图标尺寸")),1),r("td",null,[g(A,{modelValue:f.buoy_icon_size,"onUpdate:modelValue":u[8]||(u[8]=l=>f.buoy_icon_size=l),min:16,max:50,label:"px",size:"small"},null,8,["modelValue"])])]),r("tr",null,[r("th",Sl,y(_(s)("浮标文字")),1),r("td",null,[g($,{class:"w8em",size:"small",modelValue:f.fold_label,"onUpdate:modelValue":u[9]||(u[9]=l=>f.fold_label=l)},null,8,["modelValue"])])]),r("tr",null,[r("th",Pl,y(_(s)("浮标图标")),1),r("td",null,[g(R,{class:"inline-selector-group",modelValue:f.fold_icon,"onUpdate:modelValue":u[10]||(u[10]=l=>f.fold_icon=l)},{default:d((()=>[(c(),w(k,null,I([1,2,3,4,5],((l,e)=>g(O,{key:e,class:p(l>1?"ml":""),value:l},{default:d((()=>[r("span",{class:p(["fold-icon",{selected:f.fold_icon==l}])},[(c(),w("svg",Bl,[r("use",{"xlink:href":"#ocw-buoy-"+l},null,8,El)]))],2)])),_:2},1032,["class","value"]))),64)),g(O,{value:"6"},{default:d((()=>[r("span",{class:p(["fold-icon",{selected:6==f.fold_icon}])},[V(y(_(s)("自定义"))+" ",1),f.buoy_icon_custom?(c(),w("span",Tl,[u[25]||(u[25]=V(": ")),f.buoy_icon_custom?(c(),w("img",{key:0,class:"wb-icon",src:f.buoy_icon_custom},null,8,Ol)):b("",!0)])):b("",!0)],2)])),_:1})])),_:1},8,["modelValue"])])]),6==f.fold_icon?(c(),w("tr",Rl,[r("th",$l,y(_(s)("自定义浮标")),1),r("td",null,[g(F,{modelValue:f.buoy_icon_custom,"onUpdate:modelValue":u[11]||(u[11]=l=>f.buoy_icon_custom=l)},null,8,["modelValue"])])])):b("",!0),r("tr",null,[r("th",Dl,y(_(s)("基础字号")),1),r("td",null,[g(A,{modelValue:f.base_font_size,"onUpdate:modelValue":u[12]||(u[12]=l=>f.base_font_size=l),min:12,max:16,label:"px",size:"small"},null,8,["modelValue"])])]),r("tr",null,[r("th",Il,[r("span",Ll,y(_(s)("窗口宽度")),1)]),r("td",null,[g(A,{modelValue:f.panel_width,"onUpdate:modelValue":u[13]||(u[13]=l=>f.panel_width=l),min:200,max:600,label:"px",size:"small"},null,8,["modelValue"])])]),r("tr",null,[r("th",Al,[r("span",Fl,y(_(s)("窗口头部")),1)]),r("td",null,[g(L,{class:"ib",modelValue:f.color_head,"onUpdate:modelValue":u[14]||(u[14]=l=>f.color_head=l),"show-alpha":"",predefine:["#0066cc","#a02533","#ca891e","#6bb020","#8b572a"]},null,8,["modelValue"]),r("span",Hl,y(_(s)("背景颜色")),1),g(L,{class:"ib ml",modelValue:f.panel_hd_fcolor,"onUpdate:modelValue":u[15]||(u[15]=l=>f.panel_hd_fcolor=l),"show-alpha":"",predefine:["#fff","#666","#333","#999"]},null,8,["modelValue"]),r("span",Jl,y(_(s)("文字颜色")),1)])])])])])),_:1},8,["label"]),g(Y,{label:_(s)("展开模式")},{default:d((()=>[r("table",Kl,[r("tbody",null,[r("tr",null,[r("th",Ml,y(_(s)("尺寸")),1),r("td",null,[g(A,{modelValue:f.unfold_size,"onUpdate:modelValue":u[16]||(u[16]=l=>f.unfold_size=l),min:38,max:68,label:"px",size:"small"},null,8,["modelValue"])])]),r("tr",null,[r("th",ql,[r("span",Gl,y(_(s)("圆角")),1)]),r("td",null,[g(A,{modelValue:f.unfold_radius,"onUpdate:modelValue":u[17]||(u[17]=l=>f.unfold_radius=l),min:0,max:34,label:"px",size:"small"},null,8,["modelValue"])])]),r("tr",null,[r("th",Nl,[r("span",Ql,y(_(s)("留言窗口宽度")),1)]),r("td",null,[g(A,{modelValue:f.panel_width,"onUpdate:modelValue":u[18]||(u[18]=l=>f.panel_width=l),min:200,max:600,label:"px",size:"small"},null,8,["modelValue"])])])])])])),_:1},8,["label"]),g(Y,{label:_(s)("位置调整")},{default:d((()=>[r("table",Wl,[r("tbody",null,[r("tr",null,[r("th",Xl,y(_(s)("展示位置")),1),r("td",null,[g(R,{class:"inline-selector-group",modelValue:f.position,"onUpdate:modelValue":u[19]||(u[19]=l=>f.position=l)},{default:d((()=>[(c(!0),w(k,null,I(C,((l,e)=>(c(),i(O,{class:p(["pst-selector",l.value]),value:l.value,key:"position"+e},{default:d((()=>[r("span",null,y(_(a)[l.name]),1),u[26]||(u[26]=r("div",{class:"position-img"},null,-1))])),_:2},1032,["value","class"])))),128))])),_:1},8,["modelValue"])])]),r("tr",null,[r("th",Yl,y(_(s)("横向偏移值")),1),r("td",null,[g(A,{modelValue:f.position_offset_x,"onUpdate:modelValue":u[20]||(u[20]=l=>f.position_offset_x=l),label:"px",size:"small"},null,8,["modelValue"]),u[27]||(u[27]=V(" px "))])]),r("tr",null,[r("th",Zl,y(_(s)("纵向偏移值")),1),r("td",null,[g(A,{modelValue:f.position_offset_y,"onUpdate:modelValue":u[21]||(u[21]=l=>f.position_offset_y=l),label:"px",size:"small"},null,8,["modelValue"]),u[28]||(u[28]=V(" px "))])])])])])),_:1},8,["label"])])),_:1})])]),r("tr",null,[r("th",le,y(_(s)("自定义CSS样式")),1),r("td",null,[g($,{type:"textarea",autosize:{minRows:2,maxRows:10},modelValue:f.custom_style,"onUpdate:modelValue":u[22]||(u[22]=l=>f.custom_style=l)},null,8,["modelValue"])])])])])):b("",!0),1==h.value?(c(),i(il,{key:2,opt:f,cnf:v,onClosePreview:u[23]||(u[23]=l=>h.value="0")},null,8,["opt","cnf"])):b("",!0),m(g(ee,{class:"mt"},null,512),[[U,t.value]])]),n.value?b("",!0):(c(),i(ae,{key:0}))],2)),[[oe,!t.value]]),n.value?(c(),i(se,{key:0,changed:o.value,onSubmit:u[24]||(u[24]=l=>j())},null,8,["changed"])):(c(),i(se,{key:1},{default:d((()=>[g(S,{type:"primary",onClick:_(q)},{default:d((()=>[V(y(_(s)("升级为Pro版")),1)])),_:1},8,["onClick"])])),_:1})),g(cl)])}}};export{ee as default};
