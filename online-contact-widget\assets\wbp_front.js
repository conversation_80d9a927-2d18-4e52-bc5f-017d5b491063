(()=>{"use strict";function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function e(e,n){return function(t){if(Array.isArray(t))return t}(e)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,c=[],i=!0,a=!1;try{for(n=n.call(t);!(i=(r=n.next()).done)&&(c.push(r.value),!e||c.length!==e);i=!0);}catch(t){a=!0,o=t}finally{try{i||null==n.return||n.return()}finally{if(a)throw o}}return c}}(e,n)||function(e,n){if(e){if("string"==typeof e)return t(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}const r=(...t)=>{let e=t;3===e.length&&(e=[[document.documentElement],...e]),"String"===e[0].constructor.name?e[0]=[...document.querySelectorAll(e[0])]:e[0].forEach?e[0]=[...e[0]]:e[0]=[e[0]],"String"!==e[2].constructor.name&&(e[2].forEach?e[2]=[...e[2]]:e[2]=[e[2]]);const[n,r,o,c]=e,i=r.split(" "),a=t=>{let e;"String"===o.constructor.name?e=t.target.closest(o):[e]=o.filter((e=>e===t.target||e.contains(t.target))),e&&(t.relatedElements=n,c.call(e,t,e))};return n.forEach((t=>{i.forEach((e=>{t.addEventListener(e,a,!0)}))})),a};function o(t,e,n,r,o,c,i){try{var a=t[c](i),s=a.value}catch(t){return void n(t)}a.done?e(s):Promise.resolve(s).then(r,o)}function c(){c=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function l(t,e,n,r){var o=e&&e.prototype instanceof h?e:h,c=Object.create(o.prototype),i=new x(r||[]);return c._invoke=function(t,e,n){var r="suspendedStart";return function(o,c){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw c;return T()}for(n.method=o,n.arg=c;;){var i=n.delegate;if(i){var a=L(i,n);if(a){if(a===d)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=f(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===d)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}(t,n,i),c}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=l;var d={};function h(){}function v(){}function m(){}var y={};u(y,i,(function(){return this}));var p=Object.getPrototypeOf,g=p&&p(p(O([])));g&&g!==e&&r.call(g,i)&&(y=g);var w=m.prototype=h.prototype=Object.create(y);function b(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function o(c,i,a,s){var u=f(t[c],t,i);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==n(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,s)}),(function(t){o("throw",t,a,s)})):e.resolve(d).then((function(t){l.value=t,a(l)}),(function(t){return o("throw",t,a,s)}))}s(u.arg)}var c;this._invoke=function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return c=c?c.then(r,r):r()}}function L(t,e){var n=t.iterator[e.method];if(void 0===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,L(t,e),"throw"===e.method))return d;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var r=f(n,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,d;var o=r.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,d):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function _(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(_,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return v.prototype=m,u(w,"constructor",m),u(m,"constructor",v),v.displayName=u(m,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,u(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},b(S.prototype),u(S.prototype,a,(function(){return this})),t.AsyncIterator=S,t.async=function(e,n,r,o,c){void 0===c&&(c=Promise);var i=new S(l(e,n,r,o),c);return t.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},b(w),u(w,s,"Generator"),u(w,i,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){for(;e.length;){var r=e.pop();if(r in t)return n.value=r,n.done=!1,n}return n.done=!0,n}},t.values=O,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,r){return i.type="throw",i.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var c=this.tryEntries[o],i=c.completion;if("root"===c.tryLoc)return n("end");if(c.tryLoc<=this.prev){var a=r.call(c,"catchLoc"),s=r.call(c,"finallyLoc");if(a&&s){if(this.prev<c.catchLoc)return n(c.catchLoc,!0);if(this.prev<c.finallyLoc)return n(c.finallyLoc)}else if(a){if(this.prev<c.catchLoc)return n(c.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<c.finallyLoc)return n(c.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var c=o;break}}c&&("break"===t||"continue"===t)&&c.tryLoc<=e&&e<=c.finallyLoc&&(c=null);var i=c?c.completion:{};return i.type=t,i.arg=e,c?(this.method="next",this.next=c.finallyLoc,d):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),d},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),d}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:O(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},t}var i={stringify:function(t){if("object"!=n(t))return"";var r=[];return Object.entries(t).forEach((function(t){var n=e(t,2),o=n[0],c=n[1];r.push("".concat(o,"=").concat(c))})),r.join("&")},postData:function(t,e){return(n=c().mark((function n(){var r,o;return c().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r={method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:i.stringify(e)},n.prev=1,n.next=4,fetch(t,r);case 4:return o=n.sent,n.abrupt("return",o.json());case 8:n.prev=8,n.t0=n.catch(1),Promise.reject(n.t0);case 11:case"end":return n.stop()}}),n,null,[[1,8]])})),function(){var t=this,e=arguments;return new Promise((function(r,c){var i=n.apply(t,e);function a(t){o(i,r,c,a,s,"next",t)}function s(t){o(i,r,c,a,s,"throw",t)}a(void 0)}))})();var n}},a=i.postData,s=function(t){return function(t){for(var e=(new TextEncoder).encode(t),n=[],r=0;r<256;r++){for(var o=r,c=0;c<8;c++)o=1&o?3988292384^o>>>1:o>>>1;n[r]=o}for(var i=-1,a=0;a<e.length;a++)i=i>>>8^n[255&(i^e[a])];return(-1^i)>>>0}(t).toString(16).padStart(8,"0")},u=function(){return window.wb_i18n_ocw||{}},l=function(t){return u()[s(t)]||"{".concat(t,"}")},f={olfTO:null,mainTO:null,panelHeight:240,isMobile:/mobile/i.test(navigator.userAgent),st:0,init:function(){var t=this,e=window.wb_ocw_cnf;void 0!==n(e)&&function(t){var e=t.ver,n=t.dir+"/assets/images/wb_svg.html?v="+e,r="ocwSVGdata_widget",o="ocwSVGrev_widget";if(!document.createElementNS||!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect)return!0;var c,i,a="localStorage"in window&&null!==window.localStorage,s=function(){document.body.insertAdjacentHTML("afterbegin",i)},u=function(){document.body?s():document.addEventListener("DOMContentLoaded",s)};if(a&&localStorage.getItem(o)===e&&(i=localStorage.getItem(r)))return u(),!0;try{(c=new XMLHttpRequest).open("GET",n,!0),c.onload=function(){c.status>=200&&c.status<400&&(i='<div style="display:none;position:absolute;z-index:-1;">'+c.responseText+"</div>",u(),a&&(localStorage.setItem(r,i),localStorage.setItem(o,e)))},c.send()}catch(t){console.log(t)}}(e);var o=document.querySelector("#OCW_Wp"),c=document.querySelector(".ocw-mask"),i=o&&o.querySelector(".ocw-panel-main"),s=o&&o.querySelector(".ocw-panel-head"),u=document.querySelector("#OCW_btnItems"),d=o.querySelector("#OCW_afterSetMsgBox"),h="active-panel",v="wbocw-actived-panel",m=document.querySelector("body"),y=document.querySelector(".ocw-animated-circles"),p="ocw-hide-anim",g=function(e){var n=o.classList.contains(h),r=!!t.closest(e.target,".wb-ocw");return!(!n||r)&&(w(),!1)};r("click",".ocw-buoy-btn",(function(){o&&o.classList.toggle(h),y&&y.classList.add(p),t.isMobile?(t.st=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop,m.classList.add(v),m.insertAdjacentElement("beforeend",o),c&&m.insertAdjacentElement("beforeend",c)):document.addEventListener("click",g)})),r(o,"click",".list-wx .ocw-link, .ocw-el-item.wx .ocw-link",(function(t,e){var n=e.getAttribute("href");if(n)return f.copyToClipboard(n,l("微信号已复制")),t.preventDefault(),!1})),e.anim_interval&&y&&setInterval((function(){y.classList.toggle("ocw-animated")}),1e3*e.anim_interval);var w=function(){document.querySelector("#OCW_Wp").classList.remove(h),y&&y.classList.remove(p),t.isMobile?(m.classList.remove(v),window.scrollTo({top:t.st,left:0})):document.removeEventListener("click",g)};r("click",".ocw-btn-close, .ocw-mask",(function(){w()}));var b=function(){if(!t.isMobile&&s&&i){var e=s.offsetHeight,n=i.offsetHeight;setTimeout((function(){t.wpHeight=e+n,o.style.height=t.wpHeight+"px"}),50)}};b();var S=o.querySelector(".buoy-msg"),L=function(){i?(S.classList.remove("ocw-type-active"),S.style.display="",i.classList.remove("active-msg"),b()):o.classList.remove(h)},_=o.querySelector(".j-cancel-form");_&&_.addEventListener("click",(function(){L()}));var E="OCW_LOC_MSG_REC",x=function(e){var n="\n         ".concat(l("姓名：")).concat(e.name," <br>\n         ").concat(l("联系方式："),"[").concat(e.contact_type,"] ").concat(e.contact,"<br>\n         ").concat(l("咨询类型：")).concat(e.type,"<br>\n         ").concat(l("咨询内容：")).concat(e.message,"\n         ");d.style.display="block",d.classList.add("ocw-type-active"),o.querySelector("#OCW_replyCont").innerHTML=n,t.setSessionStorage(E,e),b()},O=t.getSessionStorage(E);O&&i&&x(O);var T=document.querySelector("#OCW_submitBtn"),k=/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;T&&T.addEventListener("click",(function(){var n=document.querySelector("#J_OCWForm"),o=n.querySelector('[name="name"]'),c=n.querySelector('[name="contact"]'),s=n.querySelector('[name="message"]'),u=n.querySelector("#OCW_msg"),f=new FormData(n);if(r(n,"change","input",(function(t,e){e.classList.contains("ocw-warning")&&""!=e.value&&(e.classList.remove("ocw-warning"),u.innerHTML="")})),!f.get("name"))return o.classList.add("ocw-warning"),void o.select();if(!f.get("contact"))return c.classList.add("ocw-warning"),void c.select();if("email"===f.get("contact_type")&&!k.test(f.get("contact")))return c.classList.add("ocw-warning"),c.select(),void t.toast(l("请输入正确格式的邮箱地址"));if("zh_CN"===e.locale&&"mobile"===f.get("contact_type")&&!/^1\d{10}$/.test(f.get("contact")))return c.classList.add("ocw-warning"),c.select(),void t.toast(l("请输入正确格式的11位手机号"));if(!f.get("message"))return s.classList.add("ocw-warning"),void s.select();if(f.get("message").length>500)return s.classList.add("ocw-warning"),s.select(),void t.toast(l("咨询内容过长，可简单描述，我们联系您进一步沟通。"));var d=function(){var e={};f.forEach((function(t,n){e[n]=t})),e._ajax_nonce=wb_ocw_cnf.ajax_nonce,a(wb_ocw_cnf.ajax_url+"?action=wb_ocw_api",e).then((function(e){0==e.code?(n.reset(),i?(L(),x(e.data)):t.toast(e.desc,(function(){L()}))):t.toast(e.desc)}))};window.wb_ocw_cnf.captcha_key?grecaptcha.ready((function(){try{grecaptcha.execute(window.wb_ocw_cnf.captcha_key,{action:"submit"}).then((function(t){console.log(t),f.append("ocw_captcha",t),d()})).catch((function(e){console.log(e),t.toast(l("智能验证不通过，若有疑问可联系站点管理员。"))}))}catch(e){console.log(e),t.toast(l("智能验证异常，请联系站点管理员。")+"("+e+")")}})):d()})),u&&r(u,"click",".ocw-btn-tool",(function(e,n){var r=n.dataset.target,o=i.querySelectorAll(".ocw-type-item");[].forEach.call(o,(function(t){t.style.display="none",t.classList.remove("ocw-type-active")})),i.classList["msg"===r?"add":"remove"]("active-"+r);var c=i.querySelector(".buoy-"+r);c&&(c.style.display="block",b(),i.classList.add("active-"+r),t.olfTO=setTimeout((function(){clearTimeout(t.olfTO),c.classList.add("ocw-type-active")}),50))}));var q=o.querySelector(".ocw-msg-btn");q&&q.addEventListener("click",(function(){o&&o.classList.toggle(h),document.addEventListener("click",g)}));var j=o.querySelector(".ocw-captcha .captcha_img");j&&j.addEventListener("click",(function(){var t=j.getAttribute("src");j.setAttribute("src",t+"&_t="+Math.random())}));var C=o.querySelector(".backtop");C&&t.backTop(C)},autoTime:function(t){var e=document.querySelector(".ocw-now-time")&&document.querySelector(".ocw-now-time").querySelector(".ocw-time")||t,n=new Date,r=n.getHours()<10?"0"+n.getHours():n.getHours(),o=n.getMinutes()<10?"0"+n.getMinutes():n.getMinutes();e.innerText="".concat(r,":").concat(o)},toast:function(t,e){if(t){var n=document.querySelector("#OCW_msg");if(!n){var r=document.createElement("div");r.id="OCW_msg",r.setAttribute("class","ocw-msg-bar"),n=document.querySelector("#OCW_msg")}n.style.display="block",n.innerHTML=t,setTimeout((function(){n.style.display="none",n.innerHTML="",e&&e()}),1500)}},backTop:function(t){t&&t.addEventListener("click",(function(){window.scrollTo({top:0,left:0,behavior:"smooth"})}))},getSessionStorage:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=sessionStorage.getItem(t);if(!n)return!1;try{return e?JSON.parse(n):JSON.parse(n).data}catch(e){console.log("getSessionStorage: ",e),sessionStorage.removeItem(t)}},setSessionStorage:function(t,e){console.log("setSessionStorage",t,e);var n={ver:(new Date).getTime(),data:e},r=JSON.stringify(n);sessionStorage.setItem(t,r)},stringify:function(t){if("object"!=n(t))return"";var r=[];return Object.entries(t).forEach((function(t){var n=e(t,2),o=n[0],c=n[1];r.push("".concat(o,"=").concat(c))})),r.join("&")},closest:function(t,e){for(var n=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector;t&&!n.call(t,e);)t=t.parentElement;return t},copyToClipboard:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=document.createElement("textarea");document.body.appendChild(r),r.value=t,r.select(),document.execCommand("copy"),document.body.removeChild(r);var o=document.querySelector("#OCW_toast");o||(document.body.insertAdjacentHTML("beforeend",'<div id="OCW_toast" class="ocw-msg-bar"></div>'),o=document.querySelector("#OCW_toast")),o.style.display="block",o.innerHTML=e,setTimeout((function(){o.style.display="none",o.innerHTML="",n&&n()}),1500)}};document.addEventListener("DOMContentLoaded",(function(){f.init()}))})();