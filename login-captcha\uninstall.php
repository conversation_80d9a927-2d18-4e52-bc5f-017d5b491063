<?php
/**
 * 插件卸载文件
 * 当插件被删除时执行清理操作
 */

// 防止直接访问
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * 清理插件数据
 */
function login_captcha_uninstall_cleanup() {
    // 删除插件选项
    delete_option('login_captcha_enabled');
    delete_option('login_captcha_type');
    delete_option('login_captcha_length');
    delete_option('login_captcha_width');
    delete_option('login_captcha_height');

    // 清理所有验证码相关的transient
    global $wpdb;
    
    $wpdb->query(
        "DELETE FROM {$wpdb->options} 
         WHERE option_name LIKE '_transient_login_captcha_%' 
         OR option_name LIKE '_transient_timeout_login_captcha_%'"
    );

    // 清理临时文件
    $plugin_dir = dirname(__FILE__);
    $temp_dir = $plugin_dir . '/temp/';
    
    if (file_exists($temp_dir)) {
        $files = glob($temp_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        
        // 尝试删除临时目录
        if (is_dir($temp_dir)) {
            rmdir($temp_dir);
        }
    }

    // 清理用户元数据（如果有的话）
    $wpdb->query(
        "DELETE FROM {$wpdb->usermeta} 
         WHERE meta_key LIKE 'login_captcha_%'"
    );

    // 清理缓存
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
}

// 执行清理
login_captcha_uninstall_cleanup();
