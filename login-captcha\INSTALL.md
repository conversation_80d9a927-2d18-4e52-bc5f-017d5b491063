# 登录验证码插件安装指南

## 快速安装

### 方法一：通过WordPress管理后台

1. 将整个 `login-captcha` 文件夹上传到 `/wp-content/plugins/` 目录
2. 登录WordPress管理后台
3. 进入 "插件" > "已安装的插件"
4. 找到 "登录验证码" 插件并点击 "启用"
5. 进入 "设置" > "登录验证码" 配置选项

### 方法二：FTP上传

1. 使用FTP客户端连接到您的网站
2. 导航到 `/wp-content/plugins/` 目录
3. 上传整个 `login-captcha` 文件夹
4. 确保文件权限正确（通常为644）
5. 在WordPress后台激活插件

## 系统要求检查

在安装前，请确保您的服务器满足以下要求：

### 必需要求
- WordPress 5.0 或更高版本
- PHP 7.4 或更高版本
- GD 图像处理扩展

### 推荐要求
- PHP 8.0 或更高版本
- 足够的内存限制（至少128MB）
- 启用文件写入权限

### 检查方法

您可以通过以下方式检查系统要求：

1. **PHP版本检查**
   ```php
   <?php echo PHP_VERSION; ?>
   ```

2. **GD扩展检查**
   ```php
   <?php var_dump(extension_loaded('gd')); ?>
   ```

3. **使用插件测试文件**
   - 访问 `your-site.com/wp-content/plugins/login-captcha/test-captcha.php?test=captcha`
   - 查看系统环境检查结果

## 安装步骤详解

### 1. 文件上传

确保以下文件结构正确：

```
wp-content/plugins/login-captcha/
├── login-captcha.php          # 主插件文件
├── README.md                  # 说明文档
├── INSTALL.md                 # 安装指南
├── uninstall.php             # 卸载清理
├── index.php                 # 安全防护
├── includes/                 # 核心类文件
│   ├── class-captcha-generator.php
│   ├── class-admin-settings.php
│   └── index.php
├── assets/                   # 静态资源
│   ├── css/
│   │   ├── login-captcha.css
│   │   └── index.php
│   ├── js/
│   │   ├── login-captcha.js
│   │   └── index.php
│   └── index.php
├── languages/                # 语言文件
│   ├── login-captcha.pot
│   └── index.php
└── temp/                     # 临时目录（自动创建）
```

### 2. 权限设置

设置正确的文件权限：

```bash
# 文件权限
find /path/to/wp-content/plugins/login-captcha/ -type f -exec chmod 644 {} \;

# 目录权限
find /path/to/wp-content/plugins/login-captcha/ -type d -exec chmod 755 {} \;

# 临时目录需要写入权限
chmod 755 /path/to/wp-content/plugins/login-captcha/temp/
```

### 3. 插件激活

1. 登录WordPress管理后台
2. 导航到 "插件" > "已安装的插件"
3. 找到 "登录验证码" 插件
4. 点击 "启用" 按钮

### 4. 基本配置

激活后进行基本配置：

1. 进入 "设置" > "登录验证码"
2. 启用验证码功能
3. 选择验证码类型（推荐：数字字母混合）
4. 设置验证码长度（推荐：4位）
5. 调整图片尺寸（默认：120x40）
6. 保存设置

## 故障排除

### 常见问题

**问题1：验证码图片不显示**
- 检查GD扩展是否安装
- 确认temp目录权限
- 查看PHP错误日志

**问题2：插件激活失败**
- 检查PHP版本是否满足要求
- 确认WordPress版本兼容性
- 检查文件完整性

**问题3：验证码总是错误**
- 检查服务器时间设置
- 确认没有缓存插件干扰
- 查看浏览器控制台错误

### 调试模式

启用调试模式获取更多信息：

在 `wp-config.php` 中添加：

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('LOGIN_CAPTCHA_DEBUG', true);
```

### 手动测试

运行测试文件检查功能：

```
访问：your-site.com/wp-content/plugins/login-captcha/test-captcha.php?test=captcha
```

## 安全建议

1. **删除测试文件**
   - 生产环境中删除 `test-captcha.php`

2. **定期更新**
   - 保持插件版本最新

3. **备份配置**
   - 记录重要设置选项

4. **监控日志**
   - 定期检查错误日志

## 卸载说明

如需完全卸载插件：

1. 在插件页面停用插件
2. 点击 "删除" 按钮
3. 系统会自动清理所有数据

手动清理（如果需要）：

```sql
-- 清理选项
DELETE FROM wp_options WHERE option_name LIKE 'login_captcha_%';

-- 清理transient
DELETE FROM wp_options WHERE option_name LIKE '_transient_login_captcha_%';
```

## 技术支持

如果遇到问题，请提供以下信息：

- WordPress版本
- PHP版本
- 插件版本
- 错误消息
- 服务器环境信息

---

**注意**：安装前建议先在测试环境中验证功能，确保与现有主题和插件兼容。
