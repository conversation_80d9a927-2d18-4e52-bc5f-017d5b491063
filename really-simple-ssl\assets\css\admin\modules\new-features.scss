.rsssl{
  .rsssl-new-features-block{
    .rsssl-grid-item-content{
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
  .rsssl-new-features{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--rsp-spacing-xs);
    @media only screen and (max-width: $rsp-break-xxl) and (min-width: $rsp-break-m)  {
      gap: var(--rsp-spacing-xxs);
    }
  }
  .rsssl-new-feature{
    width: 100%;
    color: var(--rsp-text-color-light);
    display: flex;
    align-items: flex-start;
    min-width: 0;
    gap: var(--rsp-spacing-xs);
    text-decoration: none;
    .rsssl-icon{
      margin-top: 2px;
    }
    .rsssl-new-feature-desc {
      p {
        font-size:var(--rsp-fs-300);
      }
    }
  }
}