# WooCommerce 产品修改审核系统

一个为WooCommerce产品修改提供两级审核流程的WordPress插件，集成Members插件权限系统。

## 功能特点

### 🔄 两级审核流程
- **第一级**：审核员审核产品修改申请
- **第二级**：管理员进行最终审核并发布
- **状态保持**：产品在审核期间保持原始状态，不影响前端显示

### 🛡️ 权限管理
- 深度集成Members插件
- 细粒度权限控制
- 自定义用户角色（产品审核员）
- 统一审核策略（所有用户包括管理员）

### 📊 完整的审核记录
- 详细的变更对比
- 审核时间线
- 审核员和管理员备注
- 邮件通知系统

### 🎯 智能拦截
- 自动拦截产品修改和删除操作
- 生成变更摘要
- 防止重复提交
- 统一审核流程（包括管理员）

## 安装要求

- WordPress 5.0+
- WooCommerce 5.0+
- Members插件
- PHP 7.4+

## 安装步骤

1. 将插件文件夹上传到 `/wp-content/plugins/` 目录
2. 在WordPress管理后台激活插件
3. 确保已安装并激活WooCommerce和Members插件
4. 插件会自动创建数据库表和注册权限

## 使用说明

### 权限配置

插件激活后会自动创建以下权限：

#### 基础权限
- `manage_product_reviews` - 管理产品审核
- `view_product_reviews` - 查看产品审核

#### 审核员权限
- `review_product_changes` - 审核产品修改
- `approve_product_changes_reviewer` - 审核员通过产品修改
- `reject_product_changes_reviewer` - 审核员拒绝产品修改

#### 管理员权限
- `approve_product_changes_admin` - 管理员通过产品修改
- `reject_product_changes_admin` - 管理员拒绝产品修改
- `final_approve_product_changes` - 最终批准产品修改

#### 高级权限
- `view_all_product_reviews` - 查看所有产品审核
- `edit_all_product_reviews` - 编辑所有产品审核
- `delete_product_reviews` - 删除产品审核记录
- `manage_product_review_settings` - 管理产品审核设置

### 用户角色

#### 产品审核员 (product_reviewer)
- 可以审核产品修改申请
- 可以通过或拒绝第一级审核
- 只能查看分配给自己的审核任务

#### 管理员 (administrator)
- 拥有所有权限
- 可以进行最终审核
- 可以绕过审核直接修改产品
- 可以管理系统设置

### 审核流程

1. **提交修改**
   - 用户修改产品信息
   - 系统自动拦截并创建审核记录
   - 发送通知给审核员

2. **审核员审核**
   - 审核员查看修改详情
   - 可以通过或拒绝修改
   - 添加审核备注

3. **管理员审核**
   - 如果审核员通过，提交给管理员
   - 管理员进行最终审核
   - 通过后自动应用产品修改

4. **结果通知**
   - 系统自动发送邮件通知
   - 更新审核状态
   - 记录完整的审核历史

### 管理界面

#### 主要菜单
- **产品审核** - 主页面，显示统计和待处理审核
- **待审核列表** - 当前用户需要处理的审核
- **审核历史** - 所有审核记录
- **审核设置** - 系统设置（仅管理员）

#### 产品编辑页面
- 显示当前产品的审核状态
- 提供审核详情链接
- 管理员可选择绕过审核

#### 产品列表
- 新增审核状态列
- 快速查看产品审核状态

## 数据库结构

### product_reviews 表

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint(20) | 主键 |
| product_id | bigint(20) | 产品ID |
| original_data | longtext | 原始产品数据 |
| modified_data | longtext | 修改后数据 |
| change_summary | text | 变更摘要 |
| submitter_id | bigint(20) | 提交者ID |
| reviewer_id | bigint(20) | 审核员ID |
| admin_id | bigint(20) | 管理员ID |
| status | varchar(20) | 总体状态 |
| reviewer_status | varchar(20) | 审核员状态 |
| admin_status | varchar(20) | 管理员状态 |
| reviewer_notes | text | 审核员备注 |
| admin_notes | text | 管理员备注 |
| reviewer_date | datetime | 审核员审核时间 |
| admin_date | datetime | 管理员审核时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 状态说明

### 总体状态 (status)
- `pending_review` - 等待审核员审核
- `pending_admin` - 等待管理员审核
- `approved` - 已通过
- `rejected` - 已拒绝

### 审核员状态 (reviewer_status)
- `pending` - 待审核
- `approved` - 已通过
- `rejected` - 已拒绝

### 管理员状态 (admin_status)
- `pending` - 待审核
- `approved` - 已通过
- `rejected` - 已拒绝

## 邮件通知

系统会在以下情况发送邮件通知：

1. **新审核请求** - 通知所有审核员
2. **审核员通过** - 通知所有管理员
3. **最终结果** - 通知提交者

邮件模板支持自定义，包含详细的审核信息和操作链接。

## 安全特性

- Nonce验证
- 权限检查
- SQL注入防护
- XSS防护
- 数据验证和清理

## 开发者接口

### 钩子 (Hooks)

```php
// 审核创建前
do_action('prs_before_create_review', $review_data);

// 审核创建后
do_action('prs_after_create_review', $review_id, $review_data);

// 审核状态更改前
do_action('prs_before_update_review_status', $review_id, $old_status, $new_status);

// 审核状态更改后
do_action('prs_after_update_review_status', $review_id, $old_status, $new_status);

// 产品修改应用前
do_action('prs_before_apply_changes', $review_id, $product_id);

// 产品修改应用后
do_action('prs_after_apply_changes', $review_id, $product_id, $success);
```

### 过滤器 (Filters)

```php
// 修改邮件模板
apply_filters('prs_email_template', $template, $template_name, $variables);

// 修改变更摘要
apply_filters('prs_change_summary', $summary, $original_data, $modified_data);

// 修改权限检查
apply_filters('prs_user_can_review', $can_review, $user_id, $review_id);
```

## 故障排除

### 常见问题

1. **审核记录不显示**
   - 检查用户权限
   - 确认Members插件已激活
   - 查看错误日志

2. **产品修改被阻止**
   - 检查是否有待审核记录
   - 确认用户权限
   - 尝试管理员绕过选项

3. **邮件通知不发送**
   - 检查WordPress邮件配置
   - 确认收件人邮箱地址
   - 查看邮件日志

### 调试模式

在 `wp-config.php` 中启用调试：

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

查看日志文件：`/wp-content/debug.log`

## 更新日志

### 1.0.0
- 初始版本发布
- 基础审核流程
- Members插件集成
- 邮件通知系统

## 支持

如有问题或建议，请联系开发团队。

## 许可证

本插件遵循 GPL v2 或更高版本许可证。
