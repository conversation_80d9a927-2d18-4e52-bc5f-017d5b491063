<?php
/*
* Deactivation page to simply deactivate the plugin when backend is not accessible anymore
* To deactivate:
*       1) rename this file to force-deactivate.php
*       2) Go in your browser to (note use of http, not https) http://yourdomain.com/wp-content/plugins/really-simple-ssl/force-deactivate.php.
*       3) IMPORTANT! On execution, this file will automatically get renamed to .txt. If you do not run it, don't forget to change it back.
*/

?>
<html>
<body>

<?php
# No need for the template engine
define( 'WP_USE_THEMES', false );

#find the base path
define( 'BASE_PATH', find_wordpress_base_path() . "/" );
# Load WordPress Core
if ( !file_exists(BASE_PATH . 'wp-load.php') ) {
	die("WordPress not installed here");
}
//make sure the files are loaded
if (!defined('RSSSL_DOING_SYSTEM_STATUS')) define( 'RSSSL_DOING_SYSTEM_STATUS' , true);
define('RSSSL_LEARNING_MODE', true);
# Load WordPress Core
require_once( BASE_PATH . 'wp-load.php' );
require_once( ABSPATH . 'wp-admin/includes/plugin.php' );

rsssl_run_force_deactivate();
function rsssl_run_force_deactivate() {
	$core_plugin = 'really-simple-ssl/rlrsssl-really-simple-ssl.php';
	if ( ! is_plugin_active( $core_plugin ) ) {
		echo "<h1>Really Simple Security is already deactivated!</h1>";
		exit;
	}

	$step = 1;
	echo "<h1>Force deactivation of Really Simple Security</h1>";
	echo $step . ". Resetting options" . "<br>";
	//ensure we can run the code
	define( 'WP_CLI',true );
	RSSSL()->admin->deactivate();
	$step ++;

	echo $step . ". Deactivating plugin" . "<br>";
	rl_deactivate_plugin( RSSSL()->admin->plugin_dir . "/"
	                      . RSSSL()->admin->plugin_filename );

	$step ++;
	echo $step . ". Completed<b>";
	rename('force-deactivate.php' , 'force-deactivate.txt');
}

function rl_remove_plugin_from_array( $plugin, $current ) {
	$key = array_search( $plugin, $current );
	if ( false !== $key ) {
		unset( $current[ $key ] );
	}

	return $current;
}

function rl_deactivate_plugin( $plugin ) {
	$plugin = plugin_basename( trim( $plugin ) );

	if ( is_multisite() ) {

		$network_current = get_site_option( 'active_sitewide_plugins', array() );
		if ( is_plugin_active_for_network( $plugin ) ) {
			unset( $network_current[ $plugin ] );
		}
		update_site_option( 'active_sitewide_plugins', $network_current );

		//remove plugin one by one on each site
		$args = array(
			'public' => 1,
		);
		$sites = get_sites($args);
		foreach ( $sites as $site ) {
			switch_to_blog($site->blog_id);
			$current = get_option( 'active_plugins', array() );
			$current = rl_remove_plugin_from_array( $plugin, $current );
			update_option( 'active_plugins', $current );
			restore_current_blog(); //switches back to previous blog, not current, so we have to do it each loop
		}

	} else {
		$current = get_option( 'active_plugins', array() );
		$current = rl_remove_plugin_from_array( $plugin, $current );
		update_option( 'active_plugins', $current );
	}
	update_option( 'active_plugins', $current );
}

/**
 * Helper function to find Wordpress base path.
 */
function find_wordpress_base_path()
{
	$path = __DIR__;

	do {
		if (file_exists($path . "/wp-config.php")) {
			//check if the wp-load.php file exists here. If not, we assume it's in a subdir.
			if ( file_exists( $path . '/wp-load.php') ) {
				return $path;
			} else {
				//wp not in this directory. Look in each folder to see if it's there.
				if ( file_exists( $path ) && $handle = opendir( $path ) ) {
					while ( false !== ( $file = readdir( $handle ) ) ) {
						if ( $file != "." && $file != ".." ) {
							$file = $path .'/' . $file;
							if ( is_dir( $file ) && file_exists( $file . '/wp-load.php') ) {
								$path = $file;
								break;
							}
						}
					}
					closedir( $handle );
				}
			}

			return $path;
		}
	} while ($path = realpath("$path/.."));

	return false;
}



?>
</body>
</html>
