import{c as e,f as s,_ as a,d as t,j as i,h as n,q as l,m as d,l as r,n as o,r as f,w as c,ap as u,u as p,az as y,s as b,y as g}from"./wbs-Dtem2-xP.js";const k=e({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:Boolean,href:{type:String,default:""},target:{type:String,default:"_self"},icon:{type:s}}),m={click:e=>e instanceof MouseEvent},v=t({name:"ElLink"});const h=g(a(t({...v,props:k,emits:m,setup(e,{emit:s}){const a=e,t=i("link"),g=n((()=>[t.b(),t.m(a.type),t.is("disabled",a.disabled),t.is("underline",a.underline&&!a.disabled)]));function k(e){a.disabled||s("click",e)}return(e,s)=>(d(),l("a",{class:b(p(g)),href:e.disabled||!e.href?void 0:e.href,target:e.disabled||!e.href?void 0:e.target,onClick:k},[e.icon?(d(),r(p(y),{key:0},{default:c((()=>[(d(),r(u(e.icon)))])),_:1})):o("v-if",!0),e.$slots.default?(d(),l("span",{key:1,class:b(p(t).e("inner"))},[f(e.$slots,"default")],2)):o("v-if",!0),e.$slots.icon?f(e.$slots,"icon",{key:2}):o("v-if",!0)],10,["href","target"]))}}),[["__file","link.vue"]]));export{h as E};
