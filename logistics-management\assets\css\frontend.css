/**
 * 物流管理前端样式
 */

/* 通知消息样式 */
.logistics-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: #fff;
    font-weight: bold;
    z-index: 9999;
    max-width: 300px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    display: none;
}

.logistics-notification-success {
    background-color: #00a32a;
}

.logistics-notification-error {
    background-color: #d63638;
}

.logistics-notification-info {
    background-color: #0073aa;
}

/* 物流追踪页面样式 */
.woocommerce-MyAccount-content .logistics-tracking-list {
    margin: 0;
}

.woocommerce-logistics-tracking-page {
    margin: 2em 0;
}

.logistics-tracking-list {
    display: flex;
    flex-direction: column;
    gap: 2em;
}

.logistics-tracking-item {
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 1.5em;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.logistics-tracking-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.tracking-order-info {
    margin-bottom: 1.5em;
    padding-bottom: 1em;
    border-bottom: 1px solid #f0f0f0;
}

.tracking-order-info h3 {
    margin: 0 0 0.5em 0;
    font-size: 1.2em;
    color: #333;
}

.tracking-order-info h3 a {
    color: #0073aa;
    text-decoration: none;
    transition: color 0.3s ease;
}

.tracking-order-info h3 a:hover {
    color: #005a87;
    text-decoration: underline;
}

.tracking-order-info p {
    margin: 0.25em 0;
    color: #666;
    font-size: 0.9em;
}

/* 物流信息网格 */
.tracking-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin-bottom: 1em;
}

.tracking-field {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
}

.field-label {
    font-weight: bold;
    color: #666;
    font-size: 0.85em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.field-value {
    color: #333;
    font-size: 1em;
    line-height: 1.4;
}

.field-value.tracking-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #0073aa;
    cursor: pointer;
    transition: color 0.3s ease;
    user-select: all;
}

.field-value.tracking-number:hover {
    color: #005a87;
    text-decoration: underline;
}

/* 状态颜色 */
.field-value.status-pending,
.tracking-value.status-pending,
.logistics-status.status-pending {
    color: #f56e28;
    font-weight: bold;
}

.field-value.status-shipped,
.tracking-value.status-shipped,
.logistics-status.status-shipped {
    color: #ffb900;
    font-weight: bold;
}

.field-value.status-in_transit,
.tracking-value.status-in_transit,
.logistics-status.status-in_transit {
    color: #0073aa;
    font-weight: bold;
}

.field-value.status-delivered,
.tracking-value.status-delivered,
.logistics-status.status-delivered {
    color: #00a32a;
    font-weight: bold;
}

.field-value.status-exception,
.tracking-value.status-exception,
.logistics-status.status-exception {
    color: #d63638;
    font-weight: bold;
}

.field-value.status-returned,
.tracking-value.status-returned,
.logistics-status.status-returned {
    color: #8f98a1;
    font-weight: bold;
}

/* 操作按钮 */
.tracking-actions {
    margin-bottom: 1em;
}

.tracking-actions .button {
    margin-right: 0.5em;
    margin-bottom: 0.5em;
    transition: all 0.3s ease;
}

.tracking-actions .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.tracking-actions .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 物流详情 */
.tracking-details {
    border-top: 1px solid #f0f0f0;
    padding-top: 1em;
    margin-top: 1em;
}

.tracking-details h4 {
    margin: 0 0 1em 0;
    color: #333;
    font-size: 1.1em;
}

.tracking-timeline {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    background: #fafafa;
}

.logistics-timeline {
    padding: 1em;
}

.timeline-item {
    display: flex;
    padding: 0.75em 0;
    border-bottom: 1px solid #e1e1e1;
    position: relative;
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 110px;
    top: 1em;
    width: 8px;
    height: 8px;
    background: #0073aa;
    border-radius: 50%;
    z-index: 1;
}

.timeline-item:first-child::before {
    background: #00a32a;
}

.timeline-time {
    flex: 0 0 120px;
    font-size: 0.85em;
    color: #666;
    font-weight: bold;
    padding-right: 1em;
}

.timeline-content {
    flex: 1;
    padding-left: 1em;
}

.timeline-status {
    font-weight: bold;
    margin-bottom: 0.25em;
    color: #333;
}

.timeline-location {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 0.25em;
}

.timeline-description {
    font-size: 0.9em;
    color: #555;
    line-height: 1.4;
}

/* 加载和错误消息 */
.loading-message,
.error-message {
    text-align: center;
    padding: 2em;
    color: #666;
}

.error-message {
    color: #d63638;
}

/* 无物流信息 */
.no-tracking-info {
    text-align: center;
    padding: 2em;
    color: #666;
    background: #f9f9f9;
    border-radius: 4px;
}

.no-tracking-info .note {
    font-size: 0.9em;
    font-style: italic;
    margin-top: 0.5em;
}

/* 订单详情页面物流信息 */
.woocommerce-logistics-tracking {
    margin: 2em 0;
    padding: 1.5em;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    background: #f9f9f9;
}

.woocommerce-logistics-tracking .woocommerce-order-details__title {
    margin-top: 0;
    margin-bottom: 1em;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 0.5em;
}

.logistics-tracking-info .tracking-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
    margin-bottom: 1.5em;
}

.tracking-item {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
}

.tracking-label {
    font-weight: bold;
    color: #666;
    font-size: 0.9em;
}

.tracking-value {
    font-size: 1em;
    color: #333;
}

.tracking-value.tracking-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #0073aa;
    cursor: pointer;
    user-select: all;
}

/* 感谢页面物流信息 */
.woocommerce-order-tracking-thankyou {
    margin: 2em 0;
    padding: 1.5em;
    background: #f0f8ff;
    border: 1px solid #0073aa;
    border-radius: 8px;
}

.woocommerce-order-tracking-thankyou h2 {
    margin-top: 0;
    color: #0073aa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .logistics-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .tracking-info-grid,
    .logistics-tracking-info .tracking-basic-info {
        grid-template-columns: 1fr;
    }
    
    .logistics-tracking-item {
        padding: 1em;
    }
    
    .timeline-item {
        flex-direction: column;
    }
    
    .timeline-item::before {
        left: 0;
        top: 0.5em;
    }
    
    .timeline-time {
        flex: none;
        margin-bottom: 0.5em;
        padding-right: 0;
    }
    
    .timeline-content {
        padding-left: 0;
    }

    .tracking-actions .button {
        display: block;
        width: 100%;
        margin-right: 0;
        margin-bottom: 0.5em;
    }

    .tracking-actions .button.button-small {
        font-size: 0.9em;
        padding: 0.5em 1em;
    }
}

@media (max-width: 480px) {
    .woocommerce-logistics-tracking,
    .logistics-tracking-item {
        padding: 1em;
        margin: 1em 0;
    }

    .tracking-timeline {
        max-height: 250px;
    }

    .field-value.tracking-number {
        word-break: break-all;
    }
}
