/**move to css variables in time */
:root {
  --rsp-break-xs: 400px;
  --rsp-break-s: 576px;
  --rsp-break-m: 768px;
  --rsp-break-l: 992px;
  --rsp-break-xl: 1200px;
  --rsp-break-xxl: 1400px;
  --rsp-spacing-xs: 10px;
  --rsp-spacing-s: 15px;
  --rsp-spacing-m: 20px;
  --rsp-spacing-l: 30px;
  --rsp-spacing-xl: 40px;
  --rsp-grid-margin: var(--rsp-spacing-xs);
  --rsp-grid-gap: var(--rsp-spacing-m);
  --rsp-border-radius: 12px;
  --rsp-border: 1px solid var(--rsp-border-color);
  --rsp-box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
  --rsp-border-color: #dfdfdf;
  --rsp-black: #333;
  --rsp-white: #fff;
  --rsp-yellow: #f4bf3e;
  --rsp-blue: #009fff;
  --rsp-dark-blue: #5A6FA5;
  --rsp-green: #2e8a37;
  --rsp-red: #D7263D;
  --rsp-pink: #E35899;
  --rsp-orange: #f39c12;
  --rsp-yellow-faded: rgba(244, 191, 62, 0.1);
  --rsp-blue-faded: rgba(0,159,255, 0.1);
  --rsp-dark-blue-faded: rgba(0,159,255, 0.1);
  --rsp-green-faded: rgba(46, 138, 55, 0.1);
  --rsp-red-faded: rgba(215, 38, 61, 0.1);
  --rsp-pink-faded: rgba(227, 88, 153, 0.1);
  --rsp-orange-faded: rgba(243, 156, 18, 0.1);
  --rsp-background-block-color: var(--rsp-white);
  --rsp-background-color: #f0f0f1;
  --rsp-input-background-color: #F1F1F1;
  --rsp-input-text-color: var(--rsp-text-color);
  --rsp-input-border-color: #8c8f94;
  --rsp-text-color: rgba(26, 26, 26, 0.9);
  --rsp-text-color-invert: rgba(255, 255, 255, 0.9);
  --rsp-text-color-white: rgba(255, 255, 255, 0.9);
  --rsp-text-color-light: rgba(69, 69, 82, 0.9);
  --rsp-grey-200: #fafafa;
  --rsp-grey-300: #ededed;
  --rsp-grey-400: #c6c6c6;
  --rsp-grey-500: #737373;
  --rsp-grey-600: #696969;
  --rsp-color-success: var(--rsp-green);
  --rsp-color-error: var(--rsp-red);
  --rsp-color-warning: var(--rsp-orange);
  --rsp-color-open: var(--rsp-yellow);
  --rsp-fs-100: 0.6125rem;
  --rsp-fs-200: 0.625rem;
  --rsp-fs-300: 0.8125rem;
  --rsp-fs-400: 0.875rem;
  --rsp-fs-500: 1rem;
  --rsp-fs-600: 1.125rem;
  --rsp-fs-700: 1.25rem;
  --rsp-fs-800: 1.5rem;
  --rsp-fs-900: 2.5rem;
  --rsp-brand-primary: var(--rsp-green);
  --rsp-brand-secondary: var(--rsp-yellow);
  --rsp-brand-primary-faded: var(--rsp-green-faded);
}
#rsp-step-template,
#rsp-plugin-suggestion-template {
  display: none;
}
.rsp-recommended {
  padding-bottom: 10px;
}
.rsp-plugin-suggestion {
  display: flex;
  gap: 10px;
  padding: 20px 10px;
  background-color: #f0f6fc;
}
.rsp-plugin-suggestion .rsp-title {
  font-weight: bold;
}
.rsp-plugin-suggestion .rsp-description-short {
  font-weight: bold;
  font-size: 10px;
}
.rsp-plugin-suggestion .rsp-icon img {
  width: 50px;
  height: inherit;
}
.rsp-plugin-suggestion .rsp-description {
  color: #888;
  font-size: 11px;
  line-height: 13px;
  width: 300px;
}
.rsp-plugin-suggestion .rsp-install-button {
  align-items: center;
  justify-content: center;
  display: flex;
}
.rsp-plugin-suggestion .rsp-install-button .button-secondary {
  font-size: 18px;
}
.rsp-plugin-suggestion .star-rating .star {
  width: 16px;
  height: 16px;
  font-size: 16px;
}
.rsp-modal-transparent-background {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal {
  max-height: calc(100vh - 20px);
  position: fixed;
  left: 50%;
  top: 50%;
  -ms-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  width: fit-content;
  height: fit-content;
  min-width: 700px;
  padding: 25px;
  background-color: white;
  border-radius: 10px;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal h3 {
  font-size: 23px;
  margin: 0 0 20px 0;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-progress-bar-container {
  margin: 0 0 15px 0;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-progress-bar-container .rsp-progress {
  overflow: hidden;
  height: 10px;
  border-radius: 5px;
  width: 100%;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-progress-bar-container .rsp-progress .rsp-bar {
  height: 100%;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-install-steps {
  display: flex;
  flex-direction: column;
  margin: 0 0 15px 0;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-install-steps .rsp-install-step {
  display: grid;
  grid-template-columns: 50px 1fr;
  grid-template-rows: 25px;
  align-items: center;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer {
  display: flex;
  gap: 20px;
  align-items: center;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-error-message {
  color: #D7263D;
  font-weight: 14px;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-error-message a {
  color: #D7263D;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-btn {
  width: fit-content;
  margin: 0;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-btn.rsp-yellow {
  background-color: #f8be2e;
  color: #333;
  font-size: 17px;
  border: 1px solid #f8be2e;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-btn.rsp-yellow:hover {
  background-color: #f8be2e;
  border: 1px solid #f8be2e;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-btn.rsp-red {
  background-color: #D7263D;
  color: white;
  border: 1px solid #D7263D;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-footer .rsp-btn.rsp-red:hover {
  background-color: #D7263D;
  border: 1px solid #D7263D;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-bullet {
  height: 13px;
  width: 13px;
  border-radius: 50%;
  margin-right: 10px;
  text-decoration: none;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-grey {
  background-color: #eeeeee;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-green {
  background-color: #61ce70;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-red {
  background-color: #D7263D;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-yellow {
  background-color: #f8be2e;
}
.rsp-modal-transparent-background .rsp-install-plugin-modal .rsp-hidden {
  display: none;
}
/*# sourceMappingURL=upgrade-to-pro.css.map */