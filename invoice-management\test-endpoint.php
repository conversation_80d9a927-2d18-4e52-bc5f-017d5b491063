<?php
/**
 * 测试发票管理端点
 * 访问: /wp-content/plugins/invoice-management/test-endpoint.php
 */

// 加载WordPress
require_once('../../../wp-config.php');

// 检查用户权限
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

echo '<h1>🧪 测试发票管理端点</h1>';

// 1. 基本信息
echo '<h2>1. 基本信息</h2>';
echo '<p><strong>WordPress版本:</strong> ' . get_bloginfo('version') . '</p>';
echo '<p><strong>WooCommerce版本:</strong> ' . (defined('WC_VERSION') ? WC_VERSION : '未安装') . '</p>';
echo '<p><strong>当前用户:</strong> ' . wp_get_current_user()->display_name . ' (ID: ' . get_current_user_id() . ')</p>';

// 2. 检查WooCommerce我的账户页面
echo '<h2>2. WooCommerce我的账户页面</h2>';
$myaccount_page_id = wc_get_page_id('myaccount');
if ($myaccount_page_id > 0) {
    $myaccount_url = get_permalink($myaccount_page_id);
    echo '<p style="color: green;">✅ 我的账户页面ID: ' . $myaccount_page_id . '</p>';
    echo '<p><strong>URL:</strong> <a href="' . esc_url($myaccount_url) . '" target="_blank">' . esc_html($myaccount_url) . '</a></p>';
} else {
    echo '<p style="color: red;">❌ 我的账户页面未设置</p>';
}

// 3. 检查端点配置
echo '<h2>3. 端点配置</h2>';

// WooCommerce默认端点
$wc_endpoints = array(
    'orders' => get_option('woocommerce_myaccount_orders_endpoint', 'orders'),
    'view-order' => get_option('woocommerce_myaccount_view_order_endpoint', 'view-order'),
    'downloads' => get_option('woocommerce_myaccount_downloads_endpoint', 'downloads'),
    'edit-account' => get_option('woocommerce_myaccount_edit_account_endpoint', 'edit-account'),
    'edit-address' => get_option('woocommerce_myaccount_edit_address_endpoint', 'edit-address'),
    'payment-methods' => get_option('woocommerce_myaccount_payment_methods_endpoint', 'payment-methods'),
    'lost-password' => get_option('woocommerce_myaccount_lost_password_endpoint', 'lost-password'),
    'customer-logout' => get_option('woocommerce_logout_endpoint', 'customer-logout'),
);

echo '<h3>WooCommerce默认端点:</h3>';
echo '<ul>';
foreach ($wc_endpoints as $key => $endpoint) {
    $url = wc_get_account_endpoint_url($endpoint);
    echo '<li><strong>' . $key . '</strong>: ' . $endpoint . ' - <a href="' . esc_url($url) . '" target="_blank">测试</a></li>';
}
echo '</ul>';

// 4. 检查自定义端点
echo '<h2>4. 自定义端点检查</h2>';

// 检查invoices端点
$invoices_endpoint = 'invoices';
$invoices_url = wc_get_account_endpoint_url($invoices_endpoint);

echo '<p><strong>发票端点:</strong> ' . $invoices_endpoint . '</p>';
echo '<p><strong>发票URL:</strong> <a href="' . esc_url($invoices_url) . '" target="_blank">' . esc_html($invoices_url) . '</a></p>';

// 5. 检查重写规则
echo '<h2>5. 重写规则检查</h2>';
$rules = get_option('rewrite_rules');

$invoice_rules = array();
foreach ($rules as $pattern => $replacement) {
    if (strpos($pattern, 'invoices') !== false || strpos($pattern, 'my-account') !== false) {
        $invoice_rules[$pattern] = $replacement;
    }
}

if (!empty($invoice_rules)) {
    echo '<h3>相关重写规则:</h3>';
    echo '<ul>';
    foreach ($invoice_rules as $pattern => $replacement) {
        echo '<li><strong>' . esc_html($pattern) . '</strong> => ' . esc_html($replacement) . '</li>';
    }
    echo '</ul>';
} else {
    echo '<p style="color: red;">❌ 没有找到相关的重写规则</p>';
}

// 6. 测试URL解析
echo '<h2>6. URL解析测试</h2>';

// 解析invoices URL
$parsed_url = parse_url($invoices_url);
echo '<p><strong>解析的URL路径:</strong> ' . esc_html($parsed_url['path']) . '</p>';

// 检查是否匹配重写规则
$matched = false;
foreach ($rules as $pattern => $replacement) {
    $regex = str_replace('?$', '', $pattern);
    $regex = '^' . $regex . '$';
    
    if (preg_match('#' . $regex . '#', trim($parsed_url['path'], '/'))) {
        echo '<p style="color: green;">✅ URL匹配规则: ' . esc_html($pattern) . '</p>';
        $matched = true;
        break;
    }
}

if (!$matched) {
    echo '<p style="color: red;">❌ URL不匹配任何重写规则</p>';
}

// 7. 检查查询变量
echo '<h2>7. 查询变量检查</h2>';

global $wp;
$query_vars = $wp->public_query_vars;

if (in_array('invoices', $query_vars)) {
    echo '<p style="color: green;">✅ invoices查询变量已注册</p>';
} else {
    echo '<p style="color: red;">❌ invoices查询变量未注册</p>';
}

// WooCommerce查询变量
$wc_query_vars = WC()->query->get_query_vars();
if (isset($wc_query_vars['invoices'])) {
    echo '<p style="color: green;">✅ invoices在WooCommerce查询变量中已注册</p>';
} else {
    echo '<p style="color: red;">❌ invoices在WooCommerce查询变量中未注册</p>';
}

// 8. 模拟请求测试
echo '<h2>8. 模拟请求测试</h2>';

// 创建一个模拟的WP_Query来测试端点
$test_query = new WP_Query();
$test_query->set('pagename', 'my-account');
$test_query->set('invoices', '');

// 检查是否识别为端点
if (isset($test_query->query_vars['invoices'])) {
    echo '<p style="color: green;">✅ 模拟查询识别了invoices端点</p>';
} else {
    echo '<p style="color: red;">❌ 模拟查询未识别invoices端点</p>';
}

// 9. 检查插件钩子
echo '<h2>9. 插件钩子检查</h2>';

$hooks_to_check = array(
    'woocommerce_account_menu_items',
    'woocommerce_get_query_vars',
    'woocommerce_account_invoices_endpoint',
    'init'
);

foreach ($hooks_to_check as $hook) {
    $callbacks = isset($GLOBALS['wp_filter'][$hook]) ? count($GLOBALS['wp_filter'][$hook]->callbacks) : 0;
    if ($callbacks > 0) {
        echo '<p style="color: green;">✅ ' . $hook . ' 有 ' . $callbacks . ' 个回调函数</p>';
    } else {
        echo '<p style="color: red;">❌ ' . $hook . ' 没有回调函数</p>';
    }
}

// 10. 直接测试端点内容
echo '<h2>10. 端点内容测试</h2>';

if (class_exists('Invoice_MyAccount')) {
    echo '<p style="color: green;">✅ Invoice_MyAccount类存在</p>';
    
    // 创建实例并测试方法
    $myaccount = new Invoice_MyAccount();
    
    if (method_exists($myaccount, 'invoices_content')) {
        echo '<p style="color: green;">✅ invoices_content方法存在</p>';
        
        // 测试菜单项
        $menu_items = array('orders' => '订单');
        $new_menu_items = $myaccount->add_menu_item($menu_items);
        
        if (isset($new_menu_items['invoices'])) {
            echo '<p style="color: green;">✅ 菜单项添加成功: ' . esc_html($new_menu_items['invoices']) . '</p>';
        } else {
            echo '<p style="color: red;">❌ 菜单项添加失败</p>';
        }
        
    } else {
        echo '<p style="color: red;">❌ invoices_content方法不存在</p>';
    }
} else {
    echo '<p style="color: red;">❌ Invoice_MyAccount类不存在</p>';
}

// 11. 建议的修复步骤
echo '<h2>11. 建议的修复步骤</h2>';

if (!$matched || !isset($wc_query_vars['invoices'])) {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0;">';
    echo '<h3>🔧 需要执行的修复步骤:</h3>';
    echo '<ol>';
    echo '<li><a href="fix-endpoint-404.php" target="_blank">运行端点修复脚本</a></li>';
    echo '<li>停用并重新激活发票管理插件</li>';
    echo '<li>在WordPress后台 设置 > 固定链接 中点击"保存更改"</li>';
    echo '<li>清除所有缓存</li>';
    echo '</ol>';
    echo '</div>';
} else {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0;">';
    echo '<h3>✅ 端点配置看起来正常</h3>';
    echo '<p>如果仍然出现404错误，请检查:</p>';
    echo '<ul>';
    echo '<li>服务器的.htaccess文件是否可写</li>';
    echo '<li>WordPress的固定链接设置</li>';
    echo '<li>是否有其他插件冲突</li>';
    echo '</ul>';
    echo '</div>';
}

echo '<h3>测试链接:</h3>';
echo '<ul>';
echo '<li><a href="' . esc_url($invoices_url) . '" target="_blank">直接访问发票页面</a></li>';
echo '<li><a href="' . esc_url($myaccount_url) . '" target="_blank">访问我的账户页面</a></li>';
echo '<li><a href="test-invoice.php">运行完整插件测试</a></li>';
echo '</ul>';
?>
