(globalThis.webpackChunkreally_simple_ssl_modal=globalThis.webpackChunkreally_simple_ssl_modal||[]).push([[926],{732:(e,t)=>{var o;!function(){"use strict";var n={}.hasOwnProperty;function r(){for(var e="",t=0;t<arguments.length;t++){var o=arguments[t];o&&(e=i(e,l(o)))}return e}function l(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return r.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var o in e)n.call(e,o)&&e[o]&&(t=i(t,o));return t}function i(e,t){return t?e?e+" "+t:e+t:e}e.exports?(r.default=r,e.exports=r):void 0===(o=function(){return r}.apply(t,[]))||(e.exports=o)}()},926:(e,t,o)=>{"use strict";o.d(t,{m_:()=>Oe});var n=o(609);const r=Math.min,l=Math.max,i=Math.round,s=Math.floor,c=e=>({x:e,y:e}),a={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function d(e,t,o){return l(e,r(t,o))}function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function y(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function h(e){return["top","bottom"].includes(p(e))?"y":"x"}function g(e){return y(h(e))}function w(e){return e.replace(/start|end/g,(e=>u[e]))}function b(e){return e.replace(/left|right|bottom|top/g,(e=>a[e]))}function x(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function _(e){const{x:t,y:o,width:n,height:r}=e;return{width:n,height:r,top:o,left:t,right:t+n,bottom:o+r,x:t,y:o}}function E(e,t,o){let{reference:n,floating:r}=e;const l=h(t),i=g(t),s=v(i),c=p(t),a="y"===l,u=n.x+n.width/2-r.width/2,d=n.y+n.height/2-r.height/2,f=n[s]/2-r[s]/2;let y;switch(c){case"top":y={x:u,y:n.y-r.height};break;case"bottom":y={x:u,y:n.y+n.height};break;case"right":y={x:n.x+n.width,y:d};break;case"left":y={x:n.x-r.width,y:d};break;default:y={x:n.x,y:n.y}}switch(m(t)){case"start":y[i]-=f*(o&&a?-1:1);break;case"end":y[i]+=f*(o&&a?-1:1)}return y}async function S(e,t){var o;void 0===t&&(t={});const{x:n,y:r,platform:l,rects:i,elements:s,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),y=x(m),v=s[p?"floating"===d?"reference":"floating":d],h=_(await l.getClippingRect({element:null==(o=await(null==l.isElement?void 0:l.isElement(v)))||o?v:v.contextElement||await(null==l.getDocumentElement?void 0:l.getDocumentElement(s.floating)),boundary:a,rootBoundary:u,strategy:c})),g="floating"===d?{x:n,y:r,width:i.floating.width,height:i.floating.height}:i.reference,w=await(null==l.getOffsetParent?void 0:l.getOffsetParent(s.floating)),b=await(null==l.isElement?void 0:l.isElement(w))&&await(null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},E=_(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:w,strategy:c}):g);return{top:(h.top-E.top+y.top)/b.y,bottom:(E.bottom-h.bottom+y.bottom)/b.y,left:(h.left-E.left+y.left)/b.x,right:(E.right-h.right+y.right)/b.x}}function A(){return"undefined"!=typeof window}function R(e){return k(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(k(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function k(e){return!!A()&&(e instanceof Node||e instanceof T(e).Node)}function L(e){return!!A()&&(e instanceof Element||e instanceof T(e).Element)}function C(e){return!!A()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function D(e){return!(!A()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function N(e){const{overflow:t,overflowX:o,overflowY:n,display:r}=H(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+o)&&!["inline","contents"].includes(r)}function $(e){return["table","td","th"].includes(R(e))}function B(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(e){return!1}}))}function j(e){const t=I(),o=L(e)?H(e):e;return"none"!==o.transform||"none"!==o.perspective||!!o.containerType&&"normal"!==o.containerType||!t&&!!o.backdropFilter&&"none"!==o.backdropFilter||!t&&!!o.filter&&"none"!==o.filter||["transform","perspective","filter"].some((e=>(o.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(o.contain||"").includes(e)))}function I(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function P(e){return["html","body","#document"].includes(R(e))}function H(e){return T(e).getComputedStyle(e)}function W(e){return L(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===R(e))return e;const t=e.assignedSlot||e.parentNode||D(e)&&e.host||O(e);return D(t)?t.host:t}function F(e){const t=z(e);return P(t)?e.ownerDocument?e.ownerDocument.body:e.body:C(t)&&N(t)?t:F(t)}function M(e,t,o){var n;void 0===t&&(t=[]),void 0===o&&(o=!0);const r=F(e),l=r===(null==(n=e.ownerDocument)?void 0:n.body),i=T(r);if(l){const e=V(i);return t.concat(i,i.visualViewport||[],N(r)?r:[],e&&o?M(e):[])}return t.concat(r,M(r,[],o))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){const t=H(e);let o=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const r=C(e),l=r?e.offsetWidth:o,s=r?e.offsetHeight:n,c=i(o)!==l||i(n)!==s;return c&&(o=l,n=s),{width:o,height:n,$:c}}function K(e){return L(e)?e:e.contextElement}function X(e){const t=K(e);if(!C(t))return c(1);const o=t.getBoundingClientRect(),{width:n,height:r,$:l}=q(t);let s=(l?i(o.width):o.width)/n,a=(l?i(o.height):o.height)/r;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}const Y=c(0);function Z(e){const t=T(e);return I()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function G(e,t,o,n){void 0===t&&(t=!1),void 0===o&&(o=!1);const r=e.getBoundingClientRect(),l=K(e);let i=c(1);t&&(n?L(n)&&(i=X(n)):i=X(e));const s=function(e,t,o){return void 0===t&&(t=!1),!(!o||t&&o!==T(e))&&t}(l,o,n)?Z(l):c(0);let a=(r.left+s.x)/i.x,u=(r.top+s.y)/i.y,d=r.width/i.x,f=r.height/i.y;if(l){const e=T(l),t=n&&L(n)?T(n):n;let o=e,r=V(o);for(;r&&n&&t!==o;){const e=X(r),t=r.getBoundingClientRect(),n=H(r),l=t.left+(r.clientLeft+parseFloat(n.paddingLeft))*e.x,i=t.top+(r.clientTop+parseFloat(n.paddingTop))*e.y;a*=e.x,u*=e.y,d*=e.x,f*=e.y,a+=l,u+=i,o=T(r),r=V(o)}}return _({width:d,height:f,x:a,y:u})}function U(e,t){const o=W(e).scrollLeft;return t?t.left+o:G(O(e)).left+o}function J(e,t,o){let n;if("viewport"===t)n=function(e,t){const o=T(e),n=O(e),r=o.visualViewport;let l=n.clientWidth,i=n.clientHeight,s=0,c=0;if(r){l=r.width,i=r.height;const e=I();(!e||e&&"fixed"===t)&&(s=r.offsetLeft,c=r.offsetTop)}return{width:l,height:i,x:s,y:c}}(e,o);else if("document"===t)n=function(e){const t=O(e),o=W(e),n=e.ownerDocument.body,r=l(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=l(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let s=-o.scrollLeft+U(e);const c=-o.scrollTop;return"rtl"===H(n).direction&&(s+=l(t.clientWidth,n.clientWidth)-r),{width:r,height:i,x:s,y:c}}(O(e));else if(L(t))n=function(e,t){const o=G(e,!0,"fixed"===t),n=o.top+e.clientTop,r=o.left+e.clientLeft,l=C(e)?X(e):c(1);return{width:e.clientWidth*l.x,height:e.clientHeight*l.y,x:r*l.x,y:n*l.y}}(t,o);else{const o=Z(e);n={...t,x:t.x-o.x,y:t.y-o.y}}return _(n)}function Q(e,t){const o=z(e);return!(o===t||!L(o)||P(o))&&("fixed"===H(o).position||Q(o,t))}function ee(e,t,o){const n=C(t),r=O(t),l="fixed"===o,i=G(e,!0,l,t);let s={scrollLeft:0,scrollTop:0};const a=c(0);if(n||!n&&!l)if(("body"!==R(t)||N(r))&&(s=W(t)),n){const e=G(t,!0,l,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else r&&(a.x=U(r));let u=0,d=0;if(r&&!n&&!l){const e=r.getBoundingClientRect();d=e.top+s.scrollTop,u=e.left+s.scrollLeft-U(r,e)}return{x:i.left+s.scrollLeft-a.x-u,y:i.top+s.scrollTop-a.y-d,width:i.width,height:i.height}}function te(e){return"static"===H(e).position}function oe(e,t){if(!C(e)||"fixed"===H(e).position)return null;if(t)return t(e);let o=e.offsetParent;return O(e)===o&&(o=o.ownerDocument.body),o}function ne(e,t){const o=T(e);if(B(e))return o;if(!C(e)){let t=z(e);for(;t&&!P(t);){if(L(t)&&!te(t))return t;t=z(t)}return o}let n=oe(e,t);for(;n&&$(n)&&te(n);)n=oe(n,t);return n&&P(n)&&te(n)&&!j(n)?o:n||function(e){let t=z(e);for(;C(t)&&!P(t);){if(j(t))return t;if(B(t))return null;t=z(t)}return null}(e)||o}const re={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:o,offsetParent:n,strategy:r}=e;const l="fixed"===r,i=O(n),s=!!t&&B(t.floating);if(n===i||s&&l)return o;let a={scrollLeft:0,scrollTop:0},u=c(1);const d=c(0),f=C(n);if((f||!f&&!l)&&(("body"!==R(n)||N(i))&&(a=W(n)),C(n))){const e=G(n);u=X(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}return{width:o.width*u.x,height:o.height*u.y,x:o.x*u.x-a.scrollLeft*u.x+d.x,y:o.y*u.y-a.scrollTop*u.y+d.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:o,rootBoundary:n,strategy:i}=e;const s=[..."clippingAncestors"===o?B(t)?[]:function(e,t){const o=t.get(e);if(o)return o;let n=M(e,[],!1).filter((e=>L(e)&&"body"!==R(e))),r=null;const l="fixed"===H(e).position;let i=l?z(e):e;for(;L(i)&&!P(i);){const t=H(i),o=j(i);o||"fixed"!==t.position||(r=null),(l?!o&&!r:!o&&"static"===t.position&&r&&["absolute","fixed"].includes(r.position)||N(i)&&!o&&Q(e,i))?n=n.filter((e=>e!==i)):r=t,i=z(i)}return t.set(e,n),n}(t,this._c):[].concat(o),n],c=s[0],a=s.reduce(((e,o)=>{const n=J(t,o,i);return e.top=l(n.top,e.top),e.right=r(n.right,e.right),e.bottom=r(n.bottom,e.bottom),e.left=l(n.left,e.left),e}),J(t,c,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ne,getElementRects:async function(e){const t=this.getOffsetParent||ne,o=this.getDimensions,n=await o(e.floating);return{reference:ee(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:o}=q(e);return{width:t,height:o}},getScale:X,isElement:L,isRTL:function(e){return"rtl"===H(e).direction}};const le=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var o,n;const{x:r,y:l,placement:i,middlewareData:s}=t,c=await async function(e,t){const{placement:o,platform:n,elements:r}=e,l=await(null==n.isRTL?void 0:n.isRTL(r.floating)),i=p(o),s=m(o),c="y"===h(o),a=["left","top"].includes(i)?-1:1,u=l&&c?-1:1,d=f(t,e);let{mainAxis:y,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof g&&(v="end"===s?-1*g:g),c?{x:v*u,y:y*a}:{x:y*a,y:v*u}}(t,e);return i===(null==(o=s.offset)?void 0:o.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:r+c.x,y:l+c.y,data:{...c,placement:i}}}}},ie=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:o,y:n,placement:r}=t,{mainAxis:l=!0,crossAxis:i=!1,limiter:s={fn:e=>{let{x:t,y:o}=e;return{x:t,y:o}}},...c}=f(e,t),a={x:o,y:n},u=await S(t,c),m=h(p(r)),v=y(m);let g=a[v],w=a[m];if(l){const e="y"===v?"bottom":"right";g=d(g+u["y"===v?"top":"left"],g,g-u[e])}if(i){const e="y"===m?"bottom":"right";w=d(w+u["y"===m?"top":"left"],w,w-u[e])}const b=s.fn({...t,[v]:g,[m]:w});return{...b,data:{x:b.x-o,y:b.y-n,enabled:{[v]:l,[m]:i}}}}}},se=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var o,n;const{placement:r,middlewareData:l,rects:i,initialPlacement:s,platform:c,elements:a}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:y,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:E=!0,...A}=f(e,t);if(null!=(o=l.arrow)&&o.alignmentOffset)return{};const R=p(r),T=h(s),O=p(s)===s,k=await(null==c.isRTL?void 0:c.isRTL(a.floating)),L=y||(O||!E?[b(s)]:function(e){const t=b(e);return[w(e),t,w(t)]}(s)),C="none"!==_;!y&&C&&L.push(...function(e,t,o,n){const r=m(e);let l=function(e,t,o){const n=["left","right"],r=["right","left"],l=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return o?t?r:n:t?n:r;case"left":case"right":return t?l:i;default:return[]}}(p(e),"start"===o,n);return r&&(l=l.map((e=>e+"-"+r)),t&&(l=l.concat(l.map(w)))),l}(s,E,_,k));const D=[s,...L],N=await S(t,A),$=[];let B=(null==(n=l.flip)?void 0:n.overflows)||[];if(u&&$.push(N[R]),d){const e=function(e,t,o){void 0===o&&(o=!1);const n=m(e),r=g(e),l=v(r);let i="x"===r?n===(o?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=b(i)),[i,b(i)]}(r,i,k);$.push(N[e[0]],N[e[1]])}if(B=[...B,{placement:r,overflows:$}],!$.every((e=>e<=0))){var j,I;const e=((null==(j=l.flip)?void 0:j.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:B},reset:{placement:t}};let o=null==(I=B.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:I.placement;if(!o)switch(x){case"bestFit":{var P;const e=null==(P=B.filter((e=>{if(C){const t=h(e.placement);return t===T||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:P[0];e&&(o=e);break}case"initialPlacement":o=s}if(r!==o)return{reset:{placement:o}}}return{}}}},ce=(e,t,o)=>{const n=new Map,r={platform:re,...o},l={...r.platform,_c:n};return(async(e,t,o)=>{const{placement:n="bottom",strategy:r="absolute",middleware:l=[],platform:i}=o,s=l.filter(Boolean),c=await(null==i.isRTL?void 0:i.isRTL(t));let a=await i.getElementRects({reference:e,floating:t,strategy:r}),{x:u,y:d}=E(a,n,c),f=n,p={},m=0;for(let o=0;o<s.length;o++){const{name:l,fn:y}=s[o],{x:v,y:h,data:g,reset:w}=await y({x:u,y:d,initialPlacement:n,placement:f,strategy:r,middlewareData:p,rects:a,platform:i,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=h?h:d,p={...p,[l]:{...p[l],...g}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(a=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:r}):w.rects),({x:u,y:d}=E(a,f,c))),o=-1)}return{x:u,y:d,placement:f,strategy:r,middlewareData:p}})(e,t,{...r,platform:l})};var ae=o(732);const ue={core:!1,base:!1};function de({css:e,id:t="react-tooltip-base-styles",type:o="base",ref:n}){var r,l;if(!e||"undefined"==typeof document||ue[o])return;if("core"===o&&"undefined"!=typeof process&&(null===(r=null===process||void 0===process?void 0:process.env)||void 0===r?void 0:r.REACT_TOOLTIP_DISABLE_CORE_STYLES))return;if("base"!==o&&"undefined"!=typeof process&&(null===(l=null===process||void 0===process?void 0:process.env)||void 0===l?void 0:l.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===o&&(t="react-tooltip-core-styles"),n||(n={});const{insertAt:i}=n;if(document.getElementById(t))return;const s=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.id=t,c.type="text/css","top"===i&&s.firstChild?s.insertBefore(c,s.firstChild):s.appendChild(c),c.styleSheet?c.styleSheet.cssText=e:c.appendChild(document.createTextNode(e)),ue[o]=!0}const fe=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:o=null,place:n="top",offset:l=10,strategy:i="absolute",middlewares:s=[le(Number(l)),se({fallbackAxisSideDirection:"start"}),ie({padding:5})],border:c})=>{if(!e)return{tooltipStyles:{},tooltipArrowStyles:{},place:n};if(null===t)return{tooltipStyles:{},tooltipArrowStyles:{},place:n};const a=s;return o?(a.push({name:"arrow",options:u={element:o,padding:5},async fn(e){const{x:t,y:o,placement:n,rects:l,platform:i,elements:s,middlewareData:c}=e,{element:a,padding:p=0}=f(u,e)||{};if(null==a)return{};const y=x(p),h={x:t,y:o},w=g(n),b=v(w),_=await i.getDimensions(a),E="y"===w,S=E?"top":"left",A=E?"bottom":"right",R=E?"clientHeight":"clientWidth",T=l.reference[b]+l.reference[w]-h[w]-l.floating[b],O=h[w]-l.reference[w],k=await(null==i.getOffsetParent?void 0:i.getOffsetParent(a));let L=k?k[R]:0;L&&await(null==i.isElement?void 0:i.isElement(k))||(L=s.floating[R]||l.floating[b]);const C=T/2-O/2,D=L/2-_[b]/2-1,N=r(y[S],D),$=r(y[A],D),B=N,j=L-_[b]-$,I=L/2-_[b]/2+C,P=d(B,I,j),H=!c.arrow&&null!=m(n)&&I!==P&&l.reference[b]/2-(I<B?N:$)-_[b]/2<0,W=H?I<B?I-B:I-j:0;return{[w]:h[w]+W,data:{[w]:P,centerOffset:I-P-W,...H&&{alignmentOffset:W}},reset:H}}}),ce(e,t,{placement:n,strategy:i,middleware:a}).then((({x:e,y:t,placement:o,middlewareData:n})=>{var r,l;const i={left:`${e}px`,top:`${t}px`,border:c},{x:s,y:a}=null!==(r=n.arrow)&&void 0!==r?r:{x:0,y:0},u=null!==(l={top:"bottom",right:"left",bottom:"top",left:"right"}[o.split("-")[0]])&&void 0!==l?l:"bottom",d=c&&{borderBottom:c,borderRight:c};let f=0;if(c){const e=`${c}`.match(/(\d+)px/);f=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:i,tooltipArrowStyles:{left:null!=s?`${s}px`:"",top:null!=a?`${a}px`:"",right:"",bottom:"",...d,[u]:`-${4+f}px`},place:o}}))):ce(e,t,{placement:"bottom",strategy:i,middleware:a}).then((({x:e,y:t,placement:o})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:o})));var u},pe=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),me=(e,t,o)=>{let n=null;const r=function(...r){const l=()=>{n=null,o||e.apply(this,r)};o&&!n&&(e.apply(this,r),n=setTimeout(l,t)),o||(n&&clearTimeout(n),n=setTimeout(l,t))};return r.cancel=()=>{n&&(clearTimeout(n),n=null)},r},ye=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,ve=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every(((e,o)=>ve(e,t[o])));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!ye(e)||!ye(t))return e===t;const o=Object.keys(e),n=Object.keys(t);return o.length===n.length&&o.every((o=>ve(e[o],t[o])))},he=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;const t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some((e=>{const o=t.getPropertyValue(e);return"auto"===o||"scroll"===o}))},ge=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(he(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},we="undefined"!=typeof window?n.useLayoutEffect:n.useEffect,be=e=>{e.current&&(clearTimeout(e.current),e.current=null)},xe={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},_e=(0,n.createContext)({getTooltipData:()=>xe});function Ee(e="DEFAULT_TOOLTIP_ID"){return(0,n.useContext)(_e).getTooltipData(e)}var Se={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},Ae={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const Re=({forwardRef:e,id:t,className:o,classNameArrow:i,variant:c="dark",anchorId:a,anchorSelect:u,place:d="top",offset:f=10,events:p=["hover"],openOnClick:m=!1,positionStrategy:y="absolute",middlewares:v,wrapper:h,delayShow:g=0,delayHide:w=0,float:b=!1,hidden:x=!1,noArrow:_=!1,clickable:E=!1,closeOnEsc:S=!1,closeOnScroll:A=!1,closeOnResize:R=!1,openEvents:T,closeEvents:k,globalCloseEvents:L,imperativeModeOnly:C,style:D,position:N,afterShow:$,afterHide:B,disableTooltip:j,content:I,contentWrapperRef:P,isOpen:H,defaultIsOpen:W=!1,setIsOpen:z,activeAnchor:F,setActiveAnchor:V,border:q,opacity:X,arrowColor:Y,role:Z="tooltip"})=>{var U;const J=(0,n.useRef)(null),Q=(0,n.useRef)(null),ee=(0,n.useRef)(null),te=(0,n.useRef)(null),oe=(0,n.useRef)(null),[ne,re]=(0,n.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:d}),[le,ie]=(0,n.useState)(!1),[se,ce]=(0,n.useState)(!1),[ue,de]=(0,n.useState)(null),pe=(0,n.useRef)(!1),ye=(0,n.useRef)(null),{anchorRefs:he,setActiveAnchor:xe}=Ee(t),_e=(0,n.useRef)(!1),[Re,Te]=(0,n.useState)([]),Oe=(0,n.useRef)(!1),ke=m||p.includes("click"),Le=ke||(null==T?void 0:T.click)||(null==T?void 0:T.dblclick)||(null==T?void 0:T.mousedown),Ce=T?{...T}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!T&&ke&&Object.assign(Ce,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const De=k?{...k}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!k&&ke&&Object.assign(De,{mouseleave:!1,blur:!1,mouseout:!1});const Ne=L?{...L}:{escape:S||!1,scroll:A||!1,resize:R||!1,clickOutsideAnchor:Le||!1};C&&(Object.assign(Ce,{mouseenter:!1,focus:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(De,{mouseleave:!1,blur:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(Ne,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),we((()=>(Oe.current=!0,()=>{Oe.current=!1})),[]);const $e=e=>{Oe.current&&(e&&ce(!0),setTimeout((()=>{Oe.current&&(null==z||z(e),void 0===H&&ie(e))}),10))};(0,n.useEffect)((()=>{if(void 0===H)return()=>null;H&&ce(!0);const e=setTimeout((()=>{ie(H)}),10);return()=>{clearTimeout(e)}}),[H]),(0,n.useEffect)((()=>{if(le!==pe.current)if(be(oe),pe.current=le,le)null==$||$();else{const e=(e=>{const t=getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay").match(/^([\d.]+)(ms|s)$/);if(!t)return 0;const[,o,n]=t;return Number(o)*("ms"===n?1:1e3)})();oe.current=setTimeout((()=>{ce(!1),de(null),null==B||B()}),e+25)}}),[le]);const Be=e=>{re((t=>ve(t,e)?t:e))},je=(e=g)=>{be(ee),se?$e(!0):ee.current=setTimeout((()=>{$e(!0)}),e)},Ie=(e=w)=>{be(te),te.current=setTimeout((()=>{_e.current||$e(!1)}),e)},Pe=e=>{var t;if(!e)return;const o=null!==(t=e.currentTarget)&&void 0!==t?t:e.target;if(!(null==o?void 0:o.isConnected))return V(null),void xe({current:null});g?je():$e(!0),V(o),xe({current:o}),be(te)},He=()=>{E?Ie(w||100):w?Ie():$e(!1),be(ee)},We=({x:e,y:t})=>{var o;const n={getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})};fe({place:null!==(o=null==ue?void 0:ue.place)&&void 0!==o?o:d,offset:f,elementReference:n,tooltipReference:J.current,tooltipArrowReference:Q.current,strategy:y,middlewares:v,border:q}).then((e=>{Be(e)}))},ze=e=>{if(!e)return;const t=e,o={x:t.clientX,y:t.clientY};We(o),ye.current=o},Fe=e=>{var t;if(!le)return;const o=e.target;o.isConnected&&((null===(t=J.current)||void 0===t?void 0:t.contains(o))||[document.querySelector(`[id='${a}']`),...Re].some((e=>null==e?void 0:e.contains(o)))||($e(!1),be(ee)))},Me=me(Pe,50,!0),Ve=me(He,50,!0),qe=e=>{Ve.cancel(),Me(e)},Ke=()=>{Me.cancel(),Ve()},Xe=(0,n.useCallback)((()=>{var e,t;const o=null!==(e=null==ue?void 0:ue.position)&&void 0!==e?e:N;o?We(o):b?ye.current&&We(ye.current):(null==F?void 0:F.isConnected)&&fe({place:null!==(t=null==ue?void 0:ue.place)&&void 0!==t?t:d,offset:f,elementReference:F,tooltipReference:J.current,tooltipArrowReference:Q.current,strategy:y,middlewares:v,border:q}).then((e=>{Oe.current&&Be(e)}))}),[le,F,I,D,d,null==ue?void 0:ue.place,f,y,N,null==ue?void 0:ue.position,b]);(0,n.useEffect)((()=>{var e,t;const o=new Set(he);Re.forEach((e=>{(null==j?void 0:j(e))||o.add({current:e})}));const n=document.querySelector(`[id='${a}']`);n&&!(null==j?void 0:j(n))&&o.add({current:n});const i=()=>{$e(!1)},c=ge(F),u=ge(J.current);Ne.scroll&&(window.addEventListener("scroll",i),null==c||c.addEventListener("scroll",i),null==u||u.addEventListener("scroll",i));let d=null;Ne.resize?window.addEventListener("resize",i):F&&J.current&&(d=function(e,t,o,n){void 0===n&&(n={});const{ancestorScroll:i=!0,ancestorResize:c=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,f=K(e),p=i||c?[...f?M(f):[],...M(t)]:[];p.forEach((e=>{i&&e.addEventListener("scroll",o,{passive:!0}),c&&e.addEventListener("resize",o)}));const m=f&&u?function(e,t){let o,n=null;const i=O(e);function c(){var e;clearTimeout(o),null==(e=n)||e.disconnect(),n=null}return function a(u,d){void 0===u&&(u=!1),void 0===d&&(d=1),c();const{left:f,top:p,width:m,height:y}=e.getBoundingClientRect();if(u||t(),!m||!y)return;const v={rootMargin:-s(p)+"px "+-s(i.clientWidth-(f+m))+"px "+-s(i.clientHeight-(p+y))+"px "+-s(f)+"px",threshold:l(0,r(1,d))||1};let h=!0;function g(e){const t=e[0].intersectionRatio;if(t!==d){if(!h)return a();t?a(!1,t):o=setTimeout((()=>{a(!1,1e-7)}),1e3)}h=!1}try{n=new IntersectionObserver(g,{...v,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(g,v)}n.observe(e)}(!0),c}(f,o):null;let y,v=-1,h=null;a&&(h=new ResizeObserver((e=>{let[n]=e;n&&n.target===f&&h&&(h.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame((()=>{var e;null==(e=h)||e.observe(t)}))),o()})),f&&!d&&h.observe(f),h.observe(t));let g=d?G(e):null;return d&&function t(){const n=G(e);!g||n.x===g.x&&n.y===g.y&&n.width===g.width&&n.height===g.height||o(),g=n,y=requestAnimationFrame(t)}(),o(),()=>{var e;p.forEach((e=>{i&&e.removeEventListener("scroll",o),c&&e.removeEventListener("resize",o)})),null==m||m(),null==(e=h)||e.disconnect(),h=null,d&&cancelAnimationFrame(y)}}(F,J.current,Xe,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const f=e=>{"Escape"===e.key&&$e(!1)};Ne.escape&&window.addEventListener("keydown",f),Ne.clickOutsideAnchor&&window.addEventListener("click",Fe);const p=[],m=e=>{le&&(null==e?void 0:e.target)===F||Pe(e)},y=e=>{le&&(null==e?void 0:e.target)===F&&He()},v=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],h=["click","dblclick","mousedown","mouseup"];Object.entries(Ce).forEach((([e,t])=>{t&&(v.includes(e)?p.push({event:e,listener:qe}):h.includes(e)&&p.push({event:e,listener:m}))})),Object.entries(De).forEach((([e,t])=>{t&&(v.includes(e)?p.push({event:e,listener:Ke}):h.includes(e)&&p.push({event:e,listener:y}))})),b&&p.push({event:"pointermove",listener:ze});const g=()=>{_e.current=!0},w=()=>{_e.current=!1,He()};return E&&!Le&&(null===(e=J.current)||void 0===e||e.addEventListener("mouseenter",g),null===(t=J.current)||void 0===t||t.addEventListener("mouseleave",w)),p.forEach((({event:e,listener:t})=>{o.forEach((o=>{var n;null===(n=o.current)||void 0===n||n.addEventListener(e,t)}))})),()=>{var e,t;Ne.scroll&&(window.removeEventListener("scroll",i),null==c||c.removeEventListener("scroll",i),null==u||u.removeEventListener("scroll",i)),Ne.resize?window.removeEventListener("resize",i):null==d||d(),Ne.clickOutsideAnchor&&window.removeEventListener("click",Fe),Ne.escape&&window.removeEventListener("keydown",f),E&&!Le&&(null===(e=J.current)||void 0===e||e.removeEventListener("mouseenter",g),null===(t=J.current)||void 0===t||t.removeEventListener("mouseleave",w)),p.forEach((({event:e,listener:t})=>{o.forEach((o=>{var n;null===(n=o.current)||void 0===n||n.removeEventListener(e,t)}))}))}}),[F,Xe,se,he,Re,T,k,L,ke,g,w]),(0,n.useEffect)((()=>{var e,o;let n=null!==(o=null!==(e=null==ue?void 0:ue.anchorSelect)&&void 0!==e?e:u)&&void 0!==o?o:"";!n&&t&&(n=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);const r=new MutationObserver((e=>{const o=[],r=[];e.forEach((e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?o.push(e.target):e.oldValue===t&&r.push(e.target)),"childList"===e.type){if(F){const t=[...e.removedNodes].filter((e=>1===e.nodeType));if(n)try{r.push(...t.filter((e=>e.matches(n)))),r.push(...t.flatMap((e=>[...e.querySelectorAll(n)])))}catch(e){}t.some((e=>{var t;return!!(null===(t=null==e?void 0:e.contains)||void 0===t?void 0:t.call(e,F))&&(ce(!1),$e(!1),V(null),be(ee),be(te),!0)}))}if(n)try{const t=[...e.addedNodes].filter((e=>1===e.nodeType));o.push(...t.filter((e=>e.matches(n)))),o.push(...t.flatMap((e=>[...e.querySelectorAll(n)])))}catch(e){}}})),(o.length||r.length)&&Te((e=>[...e.filter((e=>!r.includes(e))),...o]))}));return r.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{r.disconnect()}}),[t,u,null==ue?void 0:ue.anchorSelect,F]),(0,n.useEffect)((()=>{Xe()}),[Xe]),(0,n.useEffect)((()=>{if(!(null==P?void 0:P.current))return()=>null;const e=new ResizeObserver((()=>{setTimeout((()=>Xe()))}));return e.observe(P.current),()=>{e.disconnect()}}),[I,null==P?void 0:P.current]),(0,n.useEffect)((()=>{var e;const t=document.querySelector(`[id='${a}']`),o=[...Re,t];F&&o.includes(F)||V(null!==(e=Re[0])&&void 0!==e?e:t)}),[a,Re,F]),(0,n.useEffect)((()=>(W&&$e(!0),()=>{be(ee),be(te)})),[]),(0,n.useEffect)((()=>{var e;let o=null!==(e=null==ue?void 0:ue.anchorSelect)&&void 0!==e?e:u;if(!o&&t&&(o=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),o)try{const e=Array.from(document.querySelectorAll(o));Te(e)}catch(e){Te([])}}),[t,u,null==ue?void 0:ue.anchorSelect]),(0,n.useEffect)((()=>{ee.current&&(be(ee),je(g))}),[g]);const Ye=null!==(U=null==ue?void 0:ue.content)&&void 0!==U?U:I,Ze=le&&Object.keys(ne.tooltipStyles).length>0;return(0,n.useImperativeHandle)(e,(()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}de(null!=e?e:null),(null==e?void 0:e.delay)?je(e.delay):$e(!0)},close:e=>{(null==e?void 0:e.delay)?Ie(e.delay):$e(!1)},activeAnchor:F,place:ne.place,isOpen:Boolean(se&&!x&&Ye&&Ze)}))),se&&!x&&Ye?n.createElement(h,{id:t,role:Z,className:ae("react-tooltip",Se.tooltip,Ae.tooltip,Ae[c],o,`react-tooltip__place-${ne.place}`,Se[Ze?"show":"closing"],Ze?"react-tooltip__show":"react-tooltip__closing","fixed"===y&&Se.fixed,E&&Se.clickable),onTransitionEnd:e=>{be(oe),le||"opacity"!==e.propertyName||(ce(!1),de(null),null==B||B())},style:{...D,...ne.tooltipStyles,opacity:void 0!==X&&Ze?X:void 0},ref:J},Ye,n.createElement(h,{className:ae("react-tooltip-arrow",Se.arrow,Ae.arrow,i,_&&Se.noArrow),style:{...ne.tooltipArrowStyles,background:Y?`linear-gradient(to right bottom, transparent 50%, ${Y} 50%)`:void 0},ref:Q})):null},Te=({content:e})=>n.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),Oe=n.forwardRef((({id:e,anchorId:t,anchorSelect:o,content:r,html:l,render:i,className:s,classNameArrow:c,variant:a="dark",place:u="top",offset:d=10,wrapper:f="div",children:p=null,events:m=["hover"],openOnClick:y=!1,positionStrategy:v="absolute",middlewares:h,delayShow:g=0,delayHide:w=0,float:b=!1,hidden:x=!1,noArrow:_=!1,clickable:E=!1,closeOnEsc:S=!1,closeOnScroll:A=!1,closeOnResize:R=!1,openEvents:T,closeEvents:O,globalCloseEvents:k,imperativeModeOnly:L=!1,style:C,position:D,isOpen:N,defaultIsOpen:$=!1,disableStyleInjection:B=!1,border:j,opacity:I,arrowColor:P,setIsOpen:H,afterShow:W,afterHide:z,disableTooltip:F,role:M="tooltip"},V)=>{const[q,K]=(0,n.useState)(r),[X,Y]=(0,n.useState)(l),[Z,G]=(0,n.useState)(u),[U,J]=(0,n.useState)(a),[Q,ee]=(0,n.useState)(d),[te,oe]=(0,n.useState)(g),[ne,re]=(0,n.useState)(w),[le,ie]=(0,n.useState)(b),[se,ce]=(0,n.useState)(x),[ue,de]=(0,n.useState)(f),[fe,me]=(0,n.useState)(m),[ye,ve]=(0,n.useState)(v),[he,ge]=(0,n.useState)(null),[we,be]=(0,n.useState)(null),xe=(0,n.useRef)(B),{anchorRefs:_e,activeAnchor:Se}=Ee(e),Ae=e=>null==e?void 0:e.getAttributeNames().reduce(((t,o)=>{var n;return o.startsWith("data-tooltip-")&&(t[o.replace(/^data-tooltip-/,"")]=null!==(n=null==e?void 0:e.getAttribute(o))&&void 0!==n?n:null),t}),{}),Oe=e=>{const t={place:e=>{var t;G(null!==(t=e)&&void 0!==t?t:u)},content:e=>{K(null!=e?e:r)},html:e=>{Y(null!=e?e:l)},variant:e=>{var t;J(null!==(t=e)&&void 0!==t?t:a)},offset:e=>{ee(null===e?d:Number(e))},wrapper:e=>{var t;de(null!==(t=e)&&void 0!==t?t:f)},events:e=>{const t=null==e?void 0:e.split(" ");me(null!=t?t:m)},"position-strategy":e=>{var t;ve(null!==(t=e)&&void 0!==t?t:v)},"delay-show":e=>{oe(null===e?g:Number(e))},"delay-hide":e=>{re(null===e?w:Number(e))},float:e=>{ie(null===e?b:"true"===e)},hidden:e=>{ce(null===e?x:"true"===e)},"class-name":e=>{ge(e)}};Object.values(t).forEach((e=>e(null))),Object.entries(e).forEach((([e,o])=>{var n;null===(n=t[e])||void 0===n||n.call(t,o)}))};(0,n.useEffect)((()=>{K(r)}),[r]),(0,n.useEffect)((()=>{Y(l)}),[l]),(0,n.useEffect)((()=>{G(u)}),[u]),(0,n.useEffect)((()=>{J(a)}),[a]),(0,n.useEffect)((()=>{ee(d)}),[d]),(0,n.useEffect)((()=>{oe(g)}),[g]),(0,n.useEffect)((()=>{re(w)}),[w]),(0,n.useEffect)((()=>{ie(b)}),[b]),(0,n.useEffect)((()=>{ce(x)}),[x]),(0,n.useEffect)((()=>{ve(v)}),[v]),(0,n.useEffect)((()=>{xe.current!==B&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")}),[B]),(0,n.useEffect)((()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===B,disableBase:B}}))}),[]),(0,n.useEffect)((()=>{var n;const r=new Set(_e);let l=o;if(!l&&e&&(l=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),l)try{document.querySelectorAll(l).forEach((e=>{r.add({current:e})}))}catch(n){console.warn(`[react-tooltip] "${l}" is not a valid CSS selector`)}const i=document.querySelector(`[id='${t}']`);if(i&&r.add({current:i}),!r.size)return()=>null;const s=null!==(n=null!=we?we:i)&&void 0!==n?n:Se.current,c=new MutationObserver((e=>{e.forEach((e=>{var t;if(!s||"attributes"!==e.type||!(null===(t=e.attributeName)||void 0===t?void 0:t.startsWith("data-tooltip-")))return;const o=Ae(s);Oe(o)}))})),a={attributes:!0,childList:!1,subtree:!1};if(s){const e=Ae(s);Oe(e),c.observe(s,a)}return()=>{c.disconnect()}}),[_e,Se,we,t,o]),(0,n.useEffect)((()=>{(null==C?void 0:C.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),j&&!pe("border",`${j}`)&&console.warn(`[react-tooltip] "${j}" is not a valid \`border\`.`),(null==C?void 0:C.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),I&&!pe("opacity",`${I}`)&&console.warn(`[react-tooltip] "${I}" is not a valid \`opacity\`.`)}),[]);let ke=p;const Le=(0,n.useRef)(null);if(i){const e=i({content:(null==we?void 0:we.getAttribute("data-tooltip-content"))||q||null,activeAnchor:we});ke=e?n.createElement("div",{ref:Le,className:"react-tooltip-content-wrapper"},e):null}else q&&(ke=q);X&&(ke=n.createElement(Te,{content:X}));const Ce={forwardRef:V,id:e,anchorId:t,anchorSelect:o,className:ae(s,he),classNameArrow:c,content:ke,contentWrapperRef:Le,place:Z,variant:U,offset:Q,wrapper:ue,events:fe,openOnClick:y,positionStrategy:ye,middlewares:h,delayShow:te,delayHide:ne,float:le,hidden:se,noArrow:_,clickable:E,closeOnEsc:S,closeOnScroll:A,closeOnResize:R,openEvents:T,closeEvents:O,globalCloseEvents:k,imperativeModeOnly:L,style:C,position:D,isOpen:N,defaultIsOpen:$,border:j,opacity:I,arrowColor:P,setIsOpen:H,afterShow:W,afterHide:z,disableTooltip:F,activeAnchor:we,setActiveAnchor:e=>be(e),role:M};return n.createElement(Re,{...Ce})}));"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",(e=>{e.detail.disableCore||de({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||de({css:"\n.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}",type:"base"})}))}}]);