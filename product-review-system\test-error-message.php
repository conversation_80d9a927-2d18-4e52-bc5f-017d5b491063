<?php
/**
 * 测试错误消息显示
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once($wp_load_path);
    } else {
        die('请在WordPress环境中运行此测试文件');
    }
}

// 检查用户权限
if (!current_user_can('manage_options')) {
    die('您没有权限运行此测试');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>错误消息测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        button { padding: 10px 20px; margin: 5px; }
        .preview { border: 1px solid #ccc; padding: 20px; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>产品删除错误消息测试</h1>
    
    <?php
    // 处理测试操作
    if (isset($_POST['action']) && $_POST['action'] === 'preview_error') {
        echo '<div class="test-section">';
        echo '<h2>错误页面预览</h2>';
        echo '<div class="preview">';
        
        // 模拟错误页面HTML
        $title = __('操作被阻止', 'product-review-system');
        $product_name = 'Retro Glass Jug (330 ml)';
        $operation_label = '修改产品';
        $message = sprintf(
            __('产品"%s"当前处于待审核状态（%s），请等待审核完成后再次操作。', 'product-review-system'),
            $product_name,
            $operation_label
        );
        
        $review_url = admin_url('admin.php?page=product-review-system&action=view&id=48');
        $back_url = admin_url('edit.php?post_type=product');
        
        $error_html = '<div style="max-width: 600px; margin: 50px auto; padding: 30px; border: 1px solid #ddd; border-radius: 8px; background: #fff;">';
        $error_html .= '<div style="text-align: center; margin-bottom: 30px;">';
        $error_html .= '<div style="width: 80px; height: 80px; margin: 0 auto 20px; background: #ff6b6b; border-radius: 50%; display: flex; align-items: center; justify-content: center;">';
        $error_html .= '<span style="color: white; font-size: 36px;">⚠</span>';
        $error_html .= '</div>';
        $error_html .= '<h2 style="color: #333; margin: 0 0 10px 0;">' . esc_html($title) . '</h2>';
        $error_html .= '<p style="color: #666; font-size: 16px; line-height: 1.5; margin: 0;">' . esc_html($message) . '</p>';
        $error_html .= '</div>';
        
        $error_html .= '<div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 30px;">';
        $error_html .= '<h3 style="margin: 0 0 15px 0; color: #333;">您可以：</h3>';
        $error_html .= '<ul style="margin: 0; padding-left: 20px; color: #666;">';
        $error_html .= '<li style="margin-bottom: 8px;"><a href="' . esc_url($review_url) . '" style="color: #0073aa; text-decoration: none;">查看审核详情</a></li>';
        $error_html .= '<li style="margin-bottom: 8px;">等待审核员处理当前审核</li>';
        $error_html .= '<li style="margin-bottom: 8px;">联系管理员加快审核进度</li>';
        $error_html .= '</ul>';
        $error_html .= '</div>';
        
        $error_html .= '<div style="text-align: center;">';
        $error_html .= '<a href="' . esc_url($back_url) . '" style="display: inline-block; padding: 12px 24px; background: #0073aa; color: white; text-decoration: none; border-radius: 4px; margin-right: 10px;">返回产品列表</a>';
        $error_html .= '<a href="' . esc_url($review_url) . '" style="display: inline-block; padding: 12px 24px; background: #46b450; color: white; text-decoration: none; border-radius: 4px;">查看审核详情</a>';
        $error_html .= '</div>';
        $error_html .= '</div>';
        
        echo $error_html;
        echo '</div>';
        echo '</div>';
    }
    ?>
    
    <div class="test-section">
        <h2>错误消息改进说明</h2>
        <p>当产品处于待审核状态时尝试删除，现在会显示：</p>
        <ul>
            <li><strong>友好的标题</strong>：操作被阻止</li>
            <li><strong>清晰的说明</strong>：产品当前处于待审核状态，请等待审核完成后再次操作</li>
            <li><strong>操作建议</strong>：查看审核详情、等待审核、联系管理员</li>
            <li><strong>快捷链接</strong>：返回产品列表、查看审核详情</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>测试操作</h2>
        <form method="post">
            <button type="submit" name="action" value="preview_error">预览错误页面</button>
        </form>
    </div>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>找到一个已有待审核记录的产品</li>
            <li>尝试删除该产品</li>
            <li>应该看到新的友好错误页面而不是"权限验证失败"</li>
            <li>验证错误页面中的链接是否正常工作</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>当前待审核产品</h2>
        <?php
        // 查找有待审核记录的产品
        global $wpdb;
        $table_name = $wpdb->prefix . 'product_reviews';
        $pending_reviews = $wpdb->get_results("
            SELECT pr.*, p.post_title 
            FROM $table_name pr 
            LEFT JOIN {$wpdb->posts} p ON pr.product_id = p.ID 
            WHERE pr.status IN ('pending_review', 'pending_admin') 
            ORDER BY pr.created_at DESC 
            LIMIT 5
        ");
        
        if ($pending_reviews) {
            echo '<p class="info">以下产品当前有待审核记录，可以用来测试错误消息：</p>';
            echo '<ul>';
            foreach ($pending_reviews as $review) {
                $operation_type = isset($review->operation_type) ? $review->operation_type : 'modify';
                $operation_types = PRS_Database::get_operation_type_options();
                $operation_label = isset($operation_types[$operation_type]) ? $operation_types[$operation_type] : $operation_type;
                
                echo '<li>';
                echo '<strong>' . esc_html($review->post_title) . '</strong> ';
                echo '(ID: ' . $review->product_id . ') - ';
                echo '<span style="color: orange;">' . esc_html($operation_label) . '</span> - ';
                echo '<a href="' . admin_url('post.php?post=' . $review->product_id . '&action=edit') . '" target="_blank">编辑产品</a> | ';
                echo '<a href="' . admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id) . '" target="_blank">查看审核</a>';
                echo '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p class="warning">当前没有待审核的产品。您可以先修改一个产品来创建审核记录，然后测试删除功能。</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>快速链接</h2>
        <p><a href="<?php echo admin_url('edit.php?post_type=product'); ?>" target="_blank">产品列表页面</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=product-review-system'); ?>" target="_blank">产品审核系统</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=prs-pending-reviews'); ?>" target="_blank">待审核列表</a></p>
    </div>
</body>
</html>
