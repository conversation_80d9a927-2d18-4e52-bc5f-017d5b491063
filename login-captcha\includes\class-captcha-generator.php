<?php
/**
 * 验证码生成器类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class Login_Captcha_Generator {

    /**
     * 验证码字符集
     */
    private $numbers = '0123456789';
    private $letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    private $mixed = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

    /**
     * 生成验证码字符串
     */
    public function generate_code($length = 4, $type = 'mixed') {
        $characters = '';
        
        switch ($type) {
            case 'numbers':
                $characters = $this->numbers;
                break;
            case 'letters':
                $characters = $this->letters;
                break;
            case 'mixed':
            default:
                $characters = $this->mixed;
                break;
        }

        $code = '';
        $max = strlen($characters) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[wp_rand(0, $max)];
        }

        return $code;
    }

    /**
     * 生成验证码图片
     */
    public function generate_image($captcha_id) {
        // 获取设置
        $type = get_option('login_captcha_type', 'mixed');
        $length = get_option('login_captcha_length', 4);
        $width = get_option('login_captcha_width', 120);
        $height = get_option('login_captcha_height', 40);

        // 生成验证码
        $code = $this->generate_code($length, $type);
        
        // 将验证码存储到临时文件或数据库
        $this->store_captcha($captcha_id, $code);

        // 创建图片
        $image = imagecreate($width, $height);
        
        // 设置颜色
        $bg_color = imagecolorallocate($image, 255, 255, 255); // 白色背景
        $text_color = imagecolorallocate($image, 0, 0, 0); // 黑色文字
        $line_color = imagecolorallocate($image, 200, 200, 200); // 灰色干扰线
        $noise_color = imagecolorallocate($image, 150, 150, 150); // 噪点颜色

        // 填充背景
        imagefill($image, 0, 0, $bg_color);

        // 添加干扰线
        for ($i = 0; $i < 5; $i++) {
            imageline($image, 
                wp_rand(0, $width), wp_rand(0, $height),
                wp_rand(0, $width), wp_rand(0, $height),
                $line_color
            );
        }

        // 添加噪点
        for ($i = 0; $i < 50; $i++) {
            imagesetpixel($image, wp_rand(0, $width), wp_rand(0, $height), $noise_color);
        }

        // 添加验证码文字 - 增大字体
        $base_font_size = min($width / $length * 0.8, $height * 0.8); // 动态计算字体大小
        $char_width = $width / $length;

        for ($i = 0; $i < $length; $i++) {
            $char = $code[$i];

            // 计算字符位置，确保居中
            $x = $char_width * $i + ($char_width - $base_font_size) / 2 + wp_rand(-2, 2);
            $y = $height * 0.75 + wp_rand(-3, 3);

            // 随机倾斜角度
            $angle = wp_rand(-8, 8); // 减少倾斜角度，让字母更清晰

            // 使用内置字体
            if (function_exists('imagettftext') && $this->get_font_path()) {
                // TTF字体，使用较大的字体大小
                imagettftext($image, $base_font_size, $angle, $x, $y, $text_color, $this->get_font_path(), $char);
            } else {
                // 使用内置字体作为备选 - 创建放大效果
                $this->draw_large_builtin_char($image, $char, $char_width * $i, $char_width, $height, $text_color);
            }
        }

        // 输出图片
        header('Content-Type: image/png');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        imagepng($image);
        imagedestroy($image);
    }

    /**
     * 获取字体文件路径
     */
    private function get_font_path() {
        $font_path = LOGIN_CAPTCHA_PLUGIN_DIR . 'assets/fonts/arial.ttf';
        if (file_exists($font_path)) {
            return $font_path;
        }
        return false;
    }

    /**
     * 存储验证码
     */
    private function store_captcha($captcha_id, $code) {
        // 验证captcha_id格式（UUID格式）
        if (!$this->is_valid_uuid($captcha_id)) {
            return false;
        }

        // 使用WordPress transient API存储验证码，5分钟过期
        // 添加前缀防止与其他插件冲突
        $transient_key = 'login_captcha_' . hash('sha256', $captcha_id);
        return set_transient($transient_key, strtoupper($code), 300);
    }

    /**
     * 验证验证码
     */
    public function verify_captcha($captcha_id, $user_input) {
        // 验证输入参数
        if (empty($captcha_id) || empty($user_input)) {
            return false;
        }

        // 验证captcha_id格式（UUID格式）
        if (!$this->is_valid_uuid($captcha_id)) {
            return false;
        }

        // 验证用户输入长度和格式
        if (strlen($user_input) > 10 || !preg_match('/^[a-zA-Z0-9]+$/', $user_input)) {
            return false;
        }

        // 获取存储的验证码
        $transient_key = 'login_captcha_' . hash('sha256', $captcha_id);
        $stored_code = get_transient($transient_key);

        if (!$stored_code) {
            return false; // 验证码已过期或不存在
        }

        // 删除已使用的验证码（防止重复使用）
        delete_transient($transient_key);

        // 比较验证码（不区分大小写）
        $user_input_clean = strtoupper(trim($user_input));
        $stored_code_clean = strtoupper(trim($stored_code));

        // 使用hash_equals防止时序攻击
        return hash_equals($user_input_clean, $stored_code_clean);
    }

    /**
     * 清理过期的验证码
     */
    public function cleanup_expired_captchas() {
        global $wpdb;
        
        // 清理过期的transient
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} 
                WHERE option_name LIKE %s 
                AND option_value < %d",
                '_transient_timeout_login_captcha_%',
                time()
            )
        );
    }

    /**
     * 生成简单的数学验证码
     */
    public function generate_math_captcha($captcha_id) {
        $num1 = wp_rand(1, 10);
        $num2 = wp_rand(1, 10);
        $operators = array('+', '-');
        $operator = $operators[wp_rand(0, 1)];
        
        switch ($operator) {
            case '+':
                $result = $num1 + $num2;
                break;
            case '-':
                // 确保结果为正数
                if ($num1 < $num2) {
                    $temp = $num1;
                    $num1 = $num2;
                    $num2 = $temp;
                }
                $result = $num1 - $num2;
                break;
        }

        $question = $num1 . ' ' . $operator . ' ' . $num2 . ' = ?';
        
        // 存储答案
        set_transient('login_captcha_' . $captcha_id, $result, 300);
        
        return $question;
    }

    /**
     * 绘制真正放大的内置字体字符
     */
    private function draw_large_builtin_char($image, $char, $start_x, $char_width, $height, $color) {
        $built_in_font = 5; // 使用最大的内置字体

        // 获取字符的像素尺寸
        $char_pixel_width = imagefontwidth($built_in_font);
        $char_pixel_height = imagefontheight($built_in_font);

        // 计算更大的放大倍数
        $scale_x = min(4, ($char_width * 0.9) / $char_pixel_width);
        $scale_y = min(4, ($height * 0.9) / $char_pixel_height);
        $scale = min($scale_x, $scale_y);

        // 确保最小放大倍数
        $scale = max($scale, 2);

        // 创建临时图像来绘制单个字符
        $temp_width = $char_pixel_width;
        $temp_height = $char_pixel_height;
        $temp_image = imagecreate($temp_width, $temp_height);

        // 设置临时图像的颜色
        $temp_bg = imagecolorallocate($temp_image, 255, 255, 255); // 白色背景
        $temp_color = imagecolorallocate($temp_image, 0, 0, 0); // 黑色文字

        // 在临时图像上绘制字符
        imagestring($temp_image, $built_in_font, 0, 0, $char, $temp_color);

        // 计算放大后的尺寸和位置
        $scaled_width = $temp_width * $scale;
        $scaled_height = $temp_height * $scale;
        $dest_x = $start_x + ($char_width - $scaled_width) / 2;
        $dest_y = ($height - $scaled_height) / 2;

        // 添加随机偏移
        $dest_x += wp_rand(-1, 1);
        $dest_y += wp_rand(-1, 1);

        // 使用imagecopyresized进行真正的放大
        imagecopyresized(
            $image, $temp_image,
            $dest_x, $dest_y, 0, 0,
            $scaled_width, $scaled_height,
            $temp_width, $temp_height
        );

        // 清理临时图像
        imagedestroy($temp_image);

        // 如果放大效果不够明显，再用像素级放大
        if ($scale < 3) {
            $this->draw_pixel_scaled_char($image, $char, $start_x, $char_width, $height, $color, 3);
        }
    }

    /**
     * 像素级放大字符
     */
    private function draw_pixel_scaled_char($image, $char, $start_x, $char_width, $height, $color, $pixel_scale) {
        $built_in_font = 5;
        $char_pixel_width = imagefontwidth($built_in_font);
        $char_pixel_height = imagefontheight($built_in_font);

        // 计算起始位置
        $scaled_width = $char_pixel_width * $pixel_scale;
        $scaled_height = $char_pixel_height * $pixel_scale;
        $start_draw_x = $start_x + ($char_width - $scaled_width) / 2;
        $start_draw_y = ($height - $scaled_height) / 2;

        // 创建字符的像素映射
        $temp_image = imagecreate($char_pixel_width, $char_pixel_height);
        $temp_bg = imagecolorallocate($temp_image, 255, 255, 255);
        $temp_color = imagecolorallocate($temp_image, 0, 0, 0);
        imagestring($temp_image, $built_in_font, 0, 0, $char, $temp_color);

        // 逐像素放大绘制
        for ($y = 0; $y < $char_pixel_height; $y++) {
            for ($x = 0; $x < $char_pixel_width; $x++) {
                $pixel_color = imagecolorat($temp_image, $x, $y);

                // 如果是文字像素（非背景色）
                if ($pixel_color != $temp_bg) {
                    // 绘制放大的像素块
                    for ($py = 0; $py < $pixel_scale; $py++) {
                        for ($px = 0; $px < $pixel_scale; $px++) {
                            $draw_x = $start_draw_x + $x * $pixel_scale + $px;
                            $draw_y = $start_draw_y + $y * $pixel_scale + $py;

                            // 确保在图像边界内
                            if ($draw_x >= 0 && $draw_y >= 0 && $draw_x < imagesx($image) && $draw_y < imagesy($image)) {
                                imagesetpixel($image, $draw_x, $draw_y, $color);
                            }
                        }
                    }
                }
            }
        }

        imagedestroy($temp_image);
    }

    /**
     * 绘制放大的内置字体文本（用于数学验证码）
     */
    private function draw_large_builtin_text($image, $text, $width, $height, $color) {
        $font_size = 5; // 使用最大的内置字体
        $text_length = strlen($text);

        // 计算原始文本尺寸
        $char_width = imagefontwidth($font_size);
        $char_height = imagefontheight($font_size);
        $text_width = $char_width * $text_length;
        $text_height = $char_height;

        // 计算放大倍数
        $scale_x = min(3, ($width * 0.8) / $text_width);
        $scale_y = min(3, ($height * 0.8) / $text_height);
        $scale = min($scale_x, $scale_y);
        $scale = max($scale, 2); // 确保至少放大2倍

        // 创建临时图像绘制原始文本
        $temp_image = imagecreate($text_width, $text_height);
        $temp_bg = imagecolorallocate($temp_image, 255, 255, 255);
        $temp_color = imagecolorallocate($temp_image, 0, 0, 0);

        // 在临时图像上绘制文本
        imagestring($temp_image, $font_size, 0, 0, $text, $temp_color);

        // 计算放大后的位置
        $scaled_width = $text_width * $scale;
        $scaled_height = $text_height * $scale;
        $dest_x = ($width - $scaled_width) / 2;
        $dest_y = ($height - $scaled_height) / 2;

        // 使用imagecopyresized进行真正的放大
        imagecopyresized(
            $image, $temp_image,
            $dest_x, $dest_y, 0, 0,
            $scaled_width, $scaled_height,
            $text_width, $text_height
        );

        imagedestroy($temp_image);
    }

    /**
     * 生成数学验证码图片
     */
    public function generate_math_image($captcha_id) {
        $width = get_option('login_captcha_width', 120);
        $height = get_option('login_captcha_height', 40);

        // 生成数学题
        $question = $this->generate_math_captcha($captcha_id);

        // 创建图片
        $image = imagecreate($width, $height);
        
        // 设置颜色
        $bg_color = imagecolorallocate($image, 255, 255, 255);
        $text_color = imagecolorallocate($image, 0, 0, 0);
        $line_color = imagecolorallocate($image, 200, 200, 200);

        // 填充背景
        imagefill($image, 0, 0, $bg_color);

        // 添加干扰线
        for ($i = 0; $i < 3; $i++) {
            imageline($image, 
                wp_rand(0, $width), wp_rand(0, $height),
                wp_rand(0, $width), wp_rand(0, $height),
                $line_color
            );
        }

        // 添加文字 - 使用真正放大的字体
        $this->draw_large_builtin_text($image, $question, $width, $height, $text_color);

        // 输出图片
        header('Content-Type: image/png');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        imagepng($image);
        imagedestroy($image);
    }

    /**
     * 验证UUID格式
     */
    private function is_valid_uuid($uuid) {
        // 基本格式检查：长度和字符
        if (strlen($uuid) !== 36) {
            return false;
        }

        // 检查连字符位置
        if ($uuid[8] !== '-' || $uuid[13] !== '-' || $uuid[18] !== '-' || $uuid[23] !== '-') {
            return false;
        }

        // 移除连字符后检查是否都是十六进制字符
        $hex_part = str_replace('-', '', $uuid);
        if (strlen($hex_part) !== 32 || !ctype_xdigit($hex_part)) {
            return false;
        }

        return true;
    }
}
