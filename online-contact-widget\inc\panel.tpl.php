<?php

/**
 * 展开面板
 */
$is_mobile = wp_is_mobile();
$panel_class = $is_mobile ? 'ocw-panel-m' : 'ocw-buoy-panel';
$panel_class .= $is_fold == '2' && !$is_mobile ? ' active-panel' : '';
$item_icons_html = '';
?>
<div class="<?php echo esc_attr($panel_class); ?>" id="OCW_Wp">
  <div class="ocw-panel-head">
    <div class="ocw-pic-head">
      <img src="<?php echo esc_url($avatar_url); ?>" alt="">
      <i class="ocw-b"></i>
    </div>
    <div class="ocw-head-info">
      <div class="ocw-name"><?php echo esc_html($contact_name); ?></div>
      <div class="ocw-text"><?php echo ($contact_msg); ?></div>
    </div>
  </div>

  <div class="ocw-panel-main">
    <div class="ocw-now-time">
      <span class="ocw-time"><?php echo esc_html(current_time('mysql')); ?></span>
    </div>

    <div class="buoy-default">
      <div class="ocw-msg-item">
        <img class="ocw-pic" src="<?php echo esc_url($avatar_url); ?>" alt="">
        <div class="ocw-msg-con">
          <?php echo ($open_msg) ?>
        </div>
      </div>
    </div>

    <div class="ocw-type-item ocw-msg-set" id="OCW_afterSetMsgBox">
      <div class="ocw-msg-item by-user">
        <img class="ocw-pic" src="<?php echo esc_url($user_avatar); ?>" alt="">
        <div class="ocw-msg-con">
          <div class="ocw-user-msg" id="OCW_replyCont"></div>
        </div>
      </div>

      <div class="ocw-msg-item">
        <img class="ocw-pic" src="<?php echo esc_url($avatar_url); ?>" alt="">
        <div class="ocw-msg-con">
          <?php echo $msg_opt['auto_reply_msg']; ?>
        </div>
      </div>
    </div>

    <?php foreach ($tool_items as $tool_item) {
      $item_id = $tool_item['id'];
      $item_opt = $tool_item['opt'];
      $item_data = $item_opt['data'] ?? false;
      $item_cnf = $tool_item['cnf'];
      $label = $tool_item['opt'] ? $tool_item['opt']['name'] : $tool_item['name'];
      $icon_name = $item_cnf['icon'];

      // backtop
      if ($item_id == 'backtop') {
        continue;
      }

      if ($item_id == 'order' && !$vk_active) {
        continue;
      }

      // 我的订单
      if ($item_id == 'order') {
        $link = home_url('?wbp=member&slug=vk');

        // 仅显示图即可
        $item_icons_html .= '<a class="ocw-btn-tool wbp-act-mbc ' . $item_id . '" data-target="vk-order" rel="nofollow" href="' . $link . '" title="' . $label . '">
          <svg class="ocw-wb-icon ocw-' . $item_id . '">
            <use xlink:href="#ocw-' . $item_id . '"></use>
          </svg>
        </a>';

        continue;
      }

      //qq、微信 配置一个时，直接打开 (仅自定类型)
      $item_cst_cnf = $custom_item_cnf[$item_id] ?? [];
      $url_format = $item_cst_cnf['mobile'] ?? '';
      if (!empty($item_data) && $is_mobile && !$item_cnf['base'] && count($item_data) == 1 && !empty($url_format)) {
        $item_detail = $item_data[0];
        $url = sprintf($url_format, $item_detail['url'], $current_url);

        // 图标组
        $item_icons_html .= '<a class="ocw-btn-tool ' . $item_id . '" rel="nofollow" href="' . $url . '" target="_blank"  title="' . $label . '">
            <svg class="ocw-wb-icon ocw-' . $icon_name . '">
              <use xlink:href="#ocw-' . $icon_name . '"></use>
            </svg>
          </a>';

        continue;
      } else {
        // 图标组
        $item_icons_html .= '<a class="ocw-btn-tool ' . $item_id . '" rel="nofollow" data-target="' .  $item_id . '" title="' . $label . '">
            <svg class="ocw-wb-icon ocw-' . $icon_name . '">
              <use xlink:href="#ocw-' . $icon_name . '"></use>
            </svg>
          </a>';
      }

      // 留言
      if ($item_id == 'msg') {
        continue;
      }


      if (empty($item_data)) {
        continue;
      }
      $tips = '';
    ?>
      <div class="ocw-type-item buoy-<?php echo esc_attr($item_id); ?>">
        <div class="ocw-msg-item">
          <img class="ocw-pic" src="<?php echo esc_url($avatar_url); ?>" alt="">
          <div class="buoy-list list-<?php echo esc_attr($item_id); ?>">

            <?php foreach ($item_data as $item_detail) :

              $url = $item_detail['url'] ?? '';
              $link = '';
              $item_img = $item_detail['img'] ?? '';

              switch ($item_id) {
                case 'email':
                  $link = 'mailto:' . $url . ' ';
                  break;

                case 'tel':
                  $link = 'tel:' . $url . ' ';
                  break;

                case 'wx':
                  $link = 'javascript:void(0);';
                  $url = $item_detail['url'] ?? '';
                  if ($url) {
                    //  'weixin://dl/chat?' .
                    $link = $url;
                  }
                  if ($is_mobile) {
                    $item_img = '';
                    // if (preg_match('#android#i', $_SERVER['HTTP_USER_AGENT'])) {
                    //   $link = 'javascript:void(0);';
                    // }
                  }
                  $url = $item_detail['nickname'] ?? $url;

                  if (!$tips && !$item_img) {
                    $tips = '<div class="ocw-list-item ocw-msg-con">' . __('注：点击复制微信号并打开微信APP，添加好友后进行聊天。', WB_OCW_DM) . '</div>';
                  }
                  break;
              }

              if ($item_cnf = $custom_item_cnf[$item_id] ?? false) {
                $link_format = $is_mobile ? $item_cnf['mobile'] : $item_cnf['desktop'];
                $link = sprintf($link_format, $url);
              }
            ?>
              <a class="ocw-list-item<?php echo !$is_mobile && $item_img ? ' with-img' : ''; ?>" href="<?php echo $link; ?>" target="_blank">
                <?php
                if (!$is_mobile && $item_img) {
                ?>
                  <img class="qr-img" src="<?php echo $item_img; ?>" alt="">
                  <div class="ocw-label"><?php echo $item_detail['label']; ?></div>
                <?php } else {
                  echo isset($item_detail['icon']) && $item_detail['icon'] ? '<img class="ocw-wb-icon ocw-wb-custom" src="' . $item_detail['icon'] . '" alt="">' : '<svg class="ocw-wb-icon ocw-' . esc_attr($item_id) . '"><use xlink:href="#ocw-' . esc_attr($item_id) . '"></use></svg>';
                ?>
                  <div class="ocw-label"><?php echo '[' . $item_detail['label'] . ']'; ?></div>
                  <div class="ocw-link">
                    <?php echo $url; ?>
                  </div>
                <?php }
                ?>
              </a>
            <?php endforeach; ?>
            <?php echo $tips; ?>
          </div>

        </div>

      </div>

    <?php } // $tool_items 
    ?>

    <div class="ocw-type-item buoy-msg">
      <div class="ocw-contact-form">
        <?php include_once ONLINE_CONTACT_WIDGET_PATH . '/inc/contact_form.php'; ?>
      </div>
    </div>

    <div class="ocw-contact-tool">
      <h4 class="ocw-title"><?php echo esc_html($opt['mode_tips']); ?></h4>
      <div class="ocw-tool-list<?php echo $custom_icon_class; ?>" id="OCW_btnItems">
        <?php echo $item_icons_html; ?>
      </div>
    </div>
  </div>

  <span class="ocw-btn-close">
    <svg class="ocw-wb-icon ocw-close">
      <use xlink:href="#ocw-close"></use>
    </svg>
  </span>
</div>