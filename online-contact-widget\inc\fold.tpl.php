<div class="wb-ocw plugin-pc<?php echo $class_name . ' ' . $position; ?>" id="OCW_Wp">
  <?php foreach ($tool_items as $tool_item) :
    $key = $tool_item['id'];

    if ($key === 'order') {
      if (!$vk_active) {
        continue;
      }

      $link = home_url('?wbp=member&slug=vk');
  ?>
      <div class="ocw-el-item order">
        <a class="ocw-btn-item order wbp-act-mbc" data-target="vk-order" title="<?php _ex('我的订单', '功能入口', WB_OCW_DM); ?>" href="<?php echo esc_url($link); ?>">
          <svg class="ocw-wb-icon ocw-order">
            <use xlink:href="#ocw-order"></use>
          </svg>
        </a>
      </div>
    <?php
      continue;
    } // order

    $_opt = $tool_item['opt'];
    $_cnf = $tool_item['cnf'];
    $name = isset($_opt['name']) ? $_opt['name'] : $_cnf['name'];
    $item_class = $key === 'msg' ? $key . ' ocw-msg-btn' : $key;
    $item_icon = '<svg class="ocw-wb-icon ocw-' . esc_attr($_cnf['icon']) . '"><use xlink:href="#ocw-' . esc_attr($_cnf['icon']) . '"></use></svg>';
    ?>
    <div class="ocw-el-item <?php echo esc_attr($key); ?>">
      <span class="ocw-btn-item<?php echo esc_attr($key == 'msg' ? ' ocw-msg-btn' : ''); ?>" title="<?php echo esc_attr($name); ?>">
        <?php echo $item_icon; ?>
      </span>

      <?php
      /**
       * 泡泡多item
       */
      $is_multiple = $_cnf['multiple'] ?? false;
      if ($is_multiple) : ?>
        <div class="ocw-el-more">
          <div class="ocw-more-inner">
            <?php foreach ($_opt['data'] as $item_detail) :
              $img = $item_detail['img'] ?? '';
              $val = $item_detail['url'] ?? '';
              $label = $item_detail['label'] ?? '';
              $link = '';
              $tips = '';
              $icon_html = isset($item_detail['icon']) && $item_detail['icon'] ? '<img class="ocw-wb-icon ocw-wb-custom" src="' . $item_detail['icon'] . '" alt="">' : '<svg class="ocw-wb-icon ocw-' . esc_attr($key) . '"><use xlink:href="#ocw-' . esc_attr($key) . '"></use></svg>';

              if (wp_is_mobile()) {
                $img = '';
              }
            ?>

              <div class="ocw-more-item">
                <?php if ($img) { ?>
                  <div class="wx-inner">
                    <img class="qr-img" src="<?php echo $img; ?>" alt="<?php echo $label; ?>">
                    <div class="wx-text"><?php echo $label; ?></div>
                  </div>

                <?php
                  echo '</div>';
                  continue;
                }

                if ($val) {
                  switch ($key) {
                    case 'email':
                      $link = 'mailto:' . $val . ' ';
                      break;

                    case 'tel':
                      $link = 'tel:' . $val . ' ';
                      break;

                    case 'wx':
                      $link = $val;
                      $tips = ' title="' . __('点击复制微信号', WB_OCW_DM) . '"';
                      $val = $item_detail['nickname'] ?? $val;
                      break;
                  }

                  if ($item_cnf = $custom_item_cnf[$key] ?? false) {
                    $link_format = wp_is_mobile() && $item_cnf['mobile'] ? $item_cnf['mobile'] : $item_cnf['desktop'];
                    $link = sprintf($link_format, $val);
                  }
                ?>

                  <?php echo $icon_html; ?>
                  <div class="ocw-p ocw-label"><?php echo $label; ?></div>
                  <div class="ocw-p">
                    <a class="ocw-link" target="_blank" <?php echo $tips; ?> href="<?php echo $link; ?>" rel="nofollow">
                      <?php echo $val; ?>
                    </a>
                  </div>

                <?php } ?>
              </div>
            <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>

      <?php
      /**
       * 在线留言面板
       */
      if ($key === 'msg') : ?>
        <div class="ocw-form-panel ocw-el-more">
          <div class="ocw-more-inner">
            <div class="ocw-form-header"><?php echo $contact_msg; ?></div>

            <div class="ocw-contact-form">
              <?php include_once ONLINE_CONTACT_WIDGET_PATH . '/inc/contact_form.php'; ?>
            </div>

            <span class="ocw-btn-close">
              <svg class="ocw-wb-icon ocw-close">
                <use xlink:href="#ocw-close"></use>
              </svg>
            </span>
          </div>
        </div>
      <?php endif; ?>
    </div>

  <?php endforeach;
  ?>
</div>