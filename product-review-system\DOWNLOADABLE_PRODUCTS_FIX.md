# 虚拟可下载产品修复说明

## 问题概述

产品审核插件在处理虚拟可下载产品时存在以下问题：

1. **数据收集问题**：WooCommerce产品编辑页面的下载文件数据无法正确被审核系统收集
2. **审核界面问题**：审核员无法查看产品的下载文件信息
3. **价格显示问题**：价格为0时在变更摘要中显示为"无"而不是"0"
4. **发布问题**：审核通过后下载文件元数据设置不正确

## 修复内容

### 1. 数据收集修复 (PRS_Product_Handler::get_modified_product_data)

**问题**：WooCommerce使用三个分离的数组传递下载文件数据，但审核系统期望单一的`_downloadable_files`格式。

**修复**：
- 添加了对WooCommerce标准格式的支持：`_wc_file_names[]`、`_wc_file_urls[]`、`_wc_file_hashes[]`
- 自动转换为审核系统期望的格式
- 保持向后兼容性

```php
// 新增代码片段
if (isset($post_data['_wc_file_names']) || isset($post_data['_wc_file_urls']) || isset($post_data['_wc_file_hashes'])) {
    $file_names = isset($post_data['_wc_file_names']) ? $post_data['_wc_file_names'] : array();
    $file_urls = isset($post_data['_wc_file_urls']) ? $post_data['_wc_file_urls'] : array();
    $file_hashes = isset($post_data['_wc_file_hashes']) ? $post_data['_wc_file_hashes'] : array();
    
    // 转换为标准格式
    for ($i = 0; $i < min(count($file_names), count($file_urls), count($file_hashes)); $i++) {
        if (!empty($file_urls[$i]) && !empty($file_names[$i]) && !empty($file_hashes[$i])) {
            $data['downloadable_files'][$file_hashes[$i]] = array(
                'name' => sanitize_text_field($file_names[$i]),
                'file' => esc_url_raw($file_urls[$i])
            );
        }
    }
}
```

### 2. 现有产品数据获取修复 (PRS_Product_Handler::get_product_data)

**问题**：获取现有产品数据时没有包含下载文件信息。

**修复**：
- 添加了下载文件数据的获取逻辑
- 支持从WooCommerce产品对象和元数据两种方式获取
- 添加了格式转换方法

### 3. 价格显示修复 (PRS_Product_Handler::generate_change_summary)

**问题**：价格为0时被`empty()`函数认为是空值，显示为"无"。

**修复**：
- 添加了专门的价格格式化方法`format_price_display()`
- 特殊处理数字0和字符串"0"的情况
- 修复了价格更新逻辑中的类似问题

```php
private function format_price_display($price) {
    // 如果价格是数字0或字符串"0"，显示为"0"
    if (is_numeric($price) && floatval($price) == 0) {
        return '0';
    }
    
    // 如果价格为空或null，显示为"无"
    if (empty($price) && $price !== '0' && $price !== 0) {
        return __('无', 'product-review-system');
    }
    
    return (string) $price;
}
```

### 4. 审核界面显示修复 (PRS_Admin::display_change_comparison)

**问题**：审核界面无法显示下载文件信息。

**修复**：
- 在字段标签中添加了`downloadable_files`字段
- 添加了专门的下载文件格式化方法`format_downloadable_files_display()`
- 改进了数组比较逻辑以正确处理下载文件数据

### 5. 审核通过后的应用修复 (PRS_Product_Handler::apply_approved_changes)

**问题**：审核通过后下载文件元数据设置不完整。

**修复**：
- 添加了完整的下载文件应用逻辑
- 根据产品是否可下载来决定设置或清除下载数据
- 添加了`ensure_download_metadata()`方法确保相关元数据正确设置

## 测试验证

### 自动化测试

创建了专门的测试类`PRS_Downloadable_Products_Test`，包含以下测试用例：

1. **WooCommerce文件格式转换测试**：验证三个数组格式正确转换为单一格式
2. **价格为0显示测试**：验证价格0正确显示
3. **下载文件比较测试**：验证变更摘要中下载文件正确显示
4. **变更应用测试**：验证审核通过后数据正确更新
5. **变更摘要生成测试**：验证完整的变更摘要功能

### 手动测试步骤

1. **创建虚拟可下载产品**：
   - 在WooCommerce产品编辑页面创建新的虚拟可下载产品
   - 设置价格为0
   - 添加下载文件
   - 提交审核

2. **审核流程测试**：
   - 审核员登录查看审核详情
   - 验证能看到下载文件信息
   - 验证价格为0时显示正确
   - 通过审核

3. **结果验证**：
   - 检查产品是否正确发布
   - 验证下载文件是否可用
   - 确认价格显示正确

## 使用说明

### 启用测试功能

在开发环境中，测试页面会自动显示。在生产环境中，可以通过以下方式启用：

```php
// 在wp-config.php中添加
define('WP_DEBUG', true);

// 或者在数据库中设置选项
update_option('prs_enable_tests', true);
```

### 运行测试

1. 进入WordPress后台
2. 导航到"产品审核" > "系统测试"
3. 点击"运行测试"按钮
4. 查看测试结果

## 注意事项

1. **兼容性**：修复保持了向后兼容性，不会影响现有功能
2. **性能**：添加的代码经过优化，不会显著影响性能
3. **安全性**：所有用户输入都经过了适当的清理和验证
4. **日志记录**：关键操作都有详细的错误日志记录

## 相关文件

- `includes/class-prs-product-handler.php` - 主要修复文件
- `includes/class-prs-admin.php` - 审核界面修复
- `tests/test-downloadable-products.php` - 测试文件
- `DOWNLOADABLE_PRODUCTS_FIX.md` - 本说明文档

## 版本信息

- 修复版本：基于当前产品审核系统
- 修复日期：2025-06-17
- 修复范围：虚拟可下载产品完整支持
