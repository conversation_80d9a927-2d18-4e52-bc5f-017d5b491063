import{A as e,ba as a,bb as t,c as l,bc as s,e as o,_ as n,d as r,i,ah as u,j as d,a as c,W as v,aj as b,b as p,q as f,m,a8 as h,s as y,u as g,F as $,D as C,bd as P,be as w,h as x,o as k,ai as T,O as N,az as B,bf as E,bg as S,E as R,aZ as A,aa as F,a$ as _,r as j,b4 as K,b1 as L,aQ as q,p as z,al as V,bh as W,U as M,bi as O,N as Z,n as D,Z as H,y as Q,ar as U}from"./wbs-Dtem2-xP.js";import{c as X}from"./strings-BZz7wQvk.js";const Y=(l,s)=>{const o={},n=e([]);return{children:n,addChild:e=>{o[e.uid]=e,n.value=((e,l,s)=>a(e.subTree).filter((e=>{var a;return t(e)&&(null==(a=e.type)?void 0:a.name)===l&&!!e.component})).map((e=>e.component.uid)).map((e=>s[e])).filter((e=>!!e)))(l,s,o)},removeChild:e=>{delete o[e],n.value=n.value.filter((a=>a.uid!==e))}}},G=Symbol("tabsRootContextKey"),I=l({tabs:{type:o(Array),default:()=>s([])}}),J="ElTabBar",ee=r({name:J});var ae=n(r({...ee,props:I,setup(e,{expose:a}){const t=e,l=$(),s=i(G);s||u(J,"<el-tabs><el-tab-bar /></el-tabs>");const o=d("tabs"),n=c(),r=c(),P=()=>r.value=(()=>{let e=0,a=0;const o=["top","bottom"].includes(s.props.tabPosition)?"width":"height",n="width"===o?"x":"y",r="x"===n?"left":"top";return t.tabs.every((t=>{var s,n;const i=null==(n=null==(s=l.parent)?void 0:s.refs)?void 0:n[`tab-${t.uid}`];if(!i)return!1;if(!t.active)return!0;e=i[`offset${X(r)}`],a=i[`client${X(o)}`];const u=window.getComputedStyle(i);return"width"===o&&(a-=Number.parseFloat(u.paddingLeft)+Number.parseFloat(u.paddingRight),e+=Number.parseFloat(u.paddingLeft)),!1})),{[o]:`${a}px`,transform:`translate${X(n)}(${e}px)`}})(),w=[];v((()=>t.tabs),(async()=>{await C(),P(),(()=>{var e;w.forEach((e=>e.stop())),w.length=0;const a=null==(e=l.parent)?void 0:e.refs;if(a)for(const t in a)if(t.startsWith("tab-")){const e=a[t];e&&w.push(b(e,P))}})()}),{immediate:!0});const x=b(n,(()=>P()));return p((()=>{w.forEach((e=>e.stop())),w.length=0,x.stop()})),a({ref:n,update:P}),(e,a)=>(m(),f("div",{ref_key:"barRef",ref:n,class:y([g(o).e("active-bar"),g(o).is(g(s).props.tabPosition)]),style:h(r.value)},null,6))}}),[["__file","tab-bar.vue"]]);const te=l({panes:{type:o(Array),default:()=>s([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),le="ElTabNav",se=r({name:le,props:te,emits:{tabClick:(e,a,t)=>t instanceof Event,tabRemove:(e,a)=>a instanceof Event},setup(e,{expose:a,emit:t}){const l=i(G);l||u(le,"<el-tabs><tab-nav /></el-tabs>");const s=d("tabs"),o=P(),n=w(),r=c(),p=c(),f=c(),m=c(),h=c(!1),y=c(0),g=c(!1),$=c(!0),F=x((()=>["top","bottom"].includes(l.props.tabPosition)?"width":"height")),_=x((()=>({transform:`translate${"width"===F.value?"X":"Y"}(-${y.value}px)`}))),j=()=>{if(!r.value)return;const e=r.value[`offset${X(F.value)}`],a=y.value;if(!a)return;const t=a>e?a-e:0;y.value=t},K=()=>{if(!r.value||!p.value)return;const e=p.value[`offset${X(F.value)}`],a=r.value[`offset${X(F.value)}`],t=y.value;if(e-t<=a)return;const l=e-t>2*a?t+a:e-a;y.value=l},L=async()=>{const e=p.value;if(!(h.value&&f.value&&r.value&&e))return;await C();const a=f.value.querySelector(".is-active");if(!a)return;const t=r.value,s=["top","bottom"].includes(l.props.tabPosition),o=a.getBoundingClientRect(),n=t.getBoundingClientRect(),i=s?e.offsetWidth-n.width:e.offsetHeight-n.height,u=y.value;let d=u;s?(o.left<n.left&&(d=u-(n.left-o.left)),o.right>n.right&&(d=u+o.right-n.right)):(o.top<n.top&&(d=u-(n.top-o.top)),o.bottom>n.bottom&&(d=u+(o.bottom-n.bottom))),d=Math.max(d,0),y.value=Math.min(d,i)},q=()=>{var a;if(!p.value||!r.value)return;e.stretch&&(null==(a=m.value)||a.update());const t=p.value[`offset${X(F.value)}`],l=r.value[`offset${X(F.value)}`],s=y.value;l<t?(h.value=h.value||{},h.value.prev=s,h.value.next=s+l<t,t-s<l&&(y.value=t-l)):(h.value=!1,s>0&&(y.value=0))},z=e=>{let a=0;switch(e.code){case R.left:case R.up:a=-1;break;case R.right:case R.down:a=1;break;default:return}const t=Array.from(e.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)"));let l=t.indexOf(e.target)+a;l<0?l=t.length-1:l>=t.length&&(l=0),t[l].focus({preventScroll:!0}),t[l].click(),V()},V=()=>{$.value&&(g.value=!0)},W=()=>g.value=!1;return v(o,(e=>{"hidden"===e?$.value=!1:"visible"===e&&setTimeout((()=>$.value=!0),50)})),v(n,(e=>{e?setTimeout((()=>$.value=!0),50):$.value=!1})),b(f,q),k((()=>setTimeout((()=>L()),0))),T((()=>q())),a({scrollToActiveTab:L,removeFocus:W}),()=>{const a=h.value?[N("span",{class:[s.e("nav-prev"),s.is("disabled",!h.value.prev)],onClick:j},[N(B,null,{default:()=>[N(E,null,null)]})]),N("span",{class:[s.e("nav-next"),s.is("disabled",!h.value.next)],onClick:K},[N(B,null,{default:()=>[N(S,null,null)]})])]:null,o=e.panes.map(((a,o)=>{var n,r,i,u;const d=a.uid,c=a.props.disabled,v=null!=(r=null!=(n=a.props.name)?n:a.index)?r:`${o}`,b=!c&&(a.isClosable||e.editable);a.index=`${o}`;const p=b?N(B,{class:"is-icon-close",onClick:e=>t("tabRemove",a,e)},{default:()=>[N(A,null,null)]}):null,f=(null==(u=(i=a.slots).label)?void 0:u.call(i))||a.props.label,m=!c&&a.active?0:-1;return N("div",{ref:`tab-${d}`,class:[s.e("item"),s.is(l.props.tabPosition),s.is("active",a.active),s.is("disabled",c),s.is("closable",b),s.is("focus",g.value)],id:`tab-${v}`,key:`tab-${d}`,"aria-controls":`pane-${v}`,role:"tab","aria-selected":a.active,tabindex:m,onFocus:()=>V(),onBlur:()=>W(),onClick:e=>{W(),t("tabClick",a,v,e)},onKeydown:e=>{!b||e.code!==R.delete&&e.code!==R.backspace||t("tabRemove",a,e)}},[f,p])}));return N("div",{ref:f,class:[s.e("nav-wrap"),s.is("scrollable",!!h.value),s.is(l.props.tabPosition)]},[a,N("div",{class:s.e("nav-scroll"),ref:r},[N("div",{class:[s.e("nav"),s.is(l.props.tabPosition),s.is("stretch",e.stretch&&["top","bottom"].includes(l.props.tabPosition))],ref:p,style:_.value,role:"tablist",onKeydown:z},[e.type?null:N(ae,{ref:m,tabs:[...e.panes]},null),o])])])}}}),oe=l({type:{type:String,values:["card","border-card",""],default:""},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:o(Function),default:()=>!0},stretch:Boolean}),ne=e=>F(e)||_(e);var re=r({name:"ElTabs",props:oe,emits:{[q]:e=>ne(e),tabClick:(e,a)=>a instanceof Event,tabChange:e=>ne(e),edit:(e,a)=>["remove","add"].includes(a),tabRemove:e=>ne(e),tabAdd:()=>!0},setup(e,{emit:a,slots:t,expose:l}){var s;const o=d("tabs"),n=x((()=>["left","right"].includes(e.tabPosition))),{children:r,addChild:i,removeChild:u}=Y($(),"ElTabPane"),b=c(),p=c(null!=(s=e.modelValue)?s:"0"),f=async(t,l=!1)=>{var s,o,n;if(p.value!==t&&!L(t))try{!1!==await(null==(s=e.beforeLeave)?void 0:s.call(e,t,p.value))&&(p.value=t,l&&(a(q,t),a("tabChange",t)),null==(n=null==(o=b.value)?void 0:o.removeFocus)||n.call(o))}catch(r){}},m=(e,t,l)=>{e.props.disabled||(f(t,!0),a("tabClick",e,l))},h=(e,t)=>{e.props.disabled||L(e.props.name)||(t.stopPropagation(),a("edit",e.props.name,"remove"),a("tabRemove",e.props.name))},y=()=>{a("edit",void 0,"add"),a("tabAdd")};v((()=>e.modelValue),(e=>f(e))),v(p,(async()=>{var e;await C(),null==(e=b.value)||e.scrollToActiveTab()})),z(G,{props:e,currentName:p,registerPane:e=>{r.value.push(e)},sortPane:i,unregisterPane:u}),l({currentName:p});const g=({render:e})=>e();return()=>{const a=t["add-icon"],l=e.editable||e.addable?N("div",{class:[o.e("new-tab"),n.value&&o.e("new-tab-vertical")],tabindex:"0",onClick:y,onKeydown:e=>{[R.enter,R.numpadEnter].includes(e.code)&&y()}},[a?j(t,"add-icon"):N(B,{class:o.is("icon-plus")},{default:()=>[N(K,null,null)]})]):null,s=N("div",{class:[o.e("header"),n.value&&o.e("header-vertical"),o.is(e.tabPosition)]},[N(g,{render:()=>{const a=r.value.some((e=>e.slots.label));return N(se,{ref:b,currentName:p.value,editable:e.editable,type:e.type,panes:r.value,stretch:e.stretch,onTabClick:m,onTabRemove:h},{$stable:!a})}},null),l]),i=N("div",{class:o.e("content")},[j(t,"default")]);return N("div",{class:[o.b(),o.m(e.tabPosition),{[o.m("card")]:"card"===e.type,[o.m("border-card")]:"border-card"===e.type}]},[i,s])}}});const ie=l({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),ue="ElTabPane",de=r({name:ue});var ce=n(r({...de,props:ie,setup(e){const a=e,t=$(),l=V(),s=i(G);s||u(ue,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const o=d("tab-pane"),n=c(),r=x((()=>a.closable||s.props.closable)),b=W((()=>{var e;return s.currentName.value===(null!=(e=a.name)?e:n.value)})),p=c(b.value),h=x((()=>{var e;return null!=(e=a.name)?e:n.value})),C=W((()=>!a.lazy||p.value||b.value));v(b,(e=>{e&&(p.value=!0)}));const P=M({uid:t.uid,slots:l,props:a,paneName:h,active:b,index:n,isClosable:r});return s.registerPane(P),k((()=>{s.sortPane(P)})),O((()=>{s.unregisterPane(P.uid)})),(e,a)=>g(C)?Z((m(),f("div",{key:0,id:`pane-${g(h)}`,class:y(g(o).b()),role:"tabpanel","aria-hidden":!g(b),"aria-labelledby":`tab-${g(h)}`},[j(e.$slots,"default")],10,["id","aria-hidden","aria-labelledby"])),[[H,g(b)]]):D("v-if",!0)}}),[["__file","tab-pane.vue"]]);const ve=Q(re,{TabPane:ce}),be=U(ce);export{be as E,ve as a};
