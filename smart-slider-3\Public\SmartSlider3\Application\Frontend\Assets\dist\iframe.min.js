window.n2SSIframeLoader||function(){var i=[];function t(i,t){this.i=t,this.frame=i,this.window=i.ownerDocument.defaultView,this.document=this.window.document,this.bt="rtl"===this.document.documentElement.getAttribute("dir"),this._width=0,this.verticalOffsetTop=[],this.verticalOffsetBottom=[]}window.addEventListener("message",(function(e){for(var t=0;t<i.length;t++)i[t]&&i[t].match(e.source)&&i[t].message(e.data)})),t.prototype.match=function(i){return i===(this.frame.contentWindow||this.frame.contentDocument)&&(this.frameContent=this.frame.contentWindow||this.frame.contentDocument,!0)},t.prototype.message=function(i){switch(i.key){case"setLocation":"function"==typeof this.window.zajax_goto?this.window.zajax_goto(i.location):this.window.location=i.location;break;case"ready":this.frameContent.postMessage({key:"ackReady",windowInnerHeight:window.innerHeight},"*");break;case"option":switch(i.name){case"forceFullWidth":this.document.body.style.overflowX="hidden",this.resizeForceFullWidth(),this.resizeForceFullWidthCallback=this.resizeForceFullWidth.bind(this),window.addEventListener("resize",this.resizeForceFullWidthCallback),this.fullWidthTo=this.document.querySelector(".edit-post-visual-editor,.fl-responsive-preview .fl-builder-content"),this.watchWidth();break;case"fullPage":this.resizeFullPage(),this.resizeFullPageCallback=this.resizeFullPage.bind(this),window.addEventListener("resize",this.resizeFullPageCallback);break;case"focusOffsetTop":this.verticalOffsetTop=this.document.querySelectorAll(i.value);break;case"focusOffsetBottom":this.verticalOffsetBottom=this.document.querySelectorAll(i.value);break;case"margin":this.frame.parentNode.style.margin=i.value;break;case"height":this.frame.style.height=i.value+"px",requestAnimationFrame(function(){this.opacity=1}.bind(this.frame.style))}}},t.prototype.exists=function(){return!!this.frame.isConnected||(i[this.i]=!1,this.observer&&(this.observer.unobserve(this.fullWidthTo),delete this.observer),this.resizeForceFullWidthCallback&&window.removeEventListener("resize",this.resizeForceFullWidthCallback),this.resizeFullPageCallback&&window.removeEventListener("resize",this.resizeFullPageCallback),!1)},t.prototype.watchWidth=function(){if(this.fullWidthTo){var i=0;this.observer=new ResizeObserver(function(t){var s=t[0];i!==s.contentRect.width&&(i=s.contentRect.width,this.resizeForceFullWidth())}.bind(this)),this.observer.observe(this.fullWidthTo)}},t.prototype.resizeForceFullWidth=function(){if(this.exists()){var i=0,t=0;if(this.fullWidthTo){var s=this.fullWidthTo.getBoundingClientRect();t=s.width,i=this.bt?-t+s.right:s.left}var h,n=t>0?t:this.document.body.clientWidth,o=window.getComputedStyle(this.frame.parentNode);(this._width-n<=0||this._width-n>1)&&(h=this.bt?n-this.frame.parentNode.getBoundingClientRect().right-parseInt(o.getPropertyValue("padding-right"))-parseInt(o.getPropertyValue("border-right-width"))+i:-this.frame.parentNode.getBoundingClientRect().left-parseInt(o.getPropertyValue("padding-left"))-parseInt(o.getPropertyValue("border-left-width"))+i,this._offset!==h&&(this.frame.style.transform="translateX("+h+"px)",this._offset=h),this._width!==n&&(this.frame.style.width=n+"px",this._width=n))}},t.prototype.resizeFullPage=function(e){if(this.exists()){var i,t=window.innerHeight,s=0,h=0;for(window.parent!==window&&(t=Math.min(t,window.screen.height)),i=0;i<this.verticalOffsetTop.length;i++)s-=this.verticalOffsetTop[i].offsetHeight;for(i=0;i<this.verticalOffsetBottom.length;i++)h-=this.verticalOffsetBottom[i].offsetHeight;this.frameContent.postMessage({key:"fullpage",height:t,offsetTop:s,offsetBottom:h},"*")}},t.prototype.reset=function(){this.resizeForceFullWidthCallback&&(window.removeEventListener("resize",this.resizeForceFullWidthCallback),delete this.resizeForceFullWidthCallback),this.resizeFullPageCallback&&(window.removeEventListener("resize",this.resizeFullPageCallback),delete this.resizeFullPageCallback),this.observer&&(this.observer.disconnect(),delete this.observer),this.frame.parentNode.style.margin="0px",this.frame.style.height="auto",this.opacity=1,this.frame.style.transform="none",this.frame.style.width="100%"},window.n2SSIframeLoader=function(s){var h=new t(s,i.length);return i.push(h),h}}();