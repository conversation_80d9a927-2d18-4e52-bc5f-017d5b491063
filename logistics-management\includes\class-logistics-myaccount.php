<?php
/**
 * 我的账户页面集成类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class Logistics_MyAccount {

    /**
     * 构造函数
     */
    public function __construct() {
        // 在我的账户页面添加物流追踪端点
        add_action('init', array($this, 'add_endpoints'));

        // 添加查询变量
        add_filter('woocommerce_get_query_vars', array($this, 'add_query_vars'));

        // 在我的账户菜单中添加物流追踪链接
        add_filter('woocommerce_account_menu_items', array($this, 'add_menu_items'));

        // 处理物流追踪端点内容
        add_action('woocommerce_account_logistics-tracking_endpoint', array($this, 'logistics_tracking_content'));

        // 在订单操作中添加查看物流按钮
        add_filter('woocommerce_my_account_my_orders_actions', array($this, 'add_tracking_action'), 10, 2);

        // 在订单列表中显示物流状态
        add_action('woocommerce_my_account_my_orders_column_order-status', array($this, 'add_tracking_status_to_orders'));

        // 确保重写规则被刷新
        add_action('wp_loaded', array($this, 'maybe_flush_rewrite_rules'));
    }

    /**
     * 添加端点
     */
    public function add_endpoints() {
        add_rewrite_endpoint('logistics-tracking', EP_ROOT | EP_PAGES);
    }

    /**
     * 添加查询变量
     */
    public function add_query_vars($vars) {
        $vars['logistics-tracking'] = 'logistics-tracking';
        return $vars;
    }

    /**
     * 检查并刷新重写规则
     */
    public function maybe_flush_rewrite_rules() {
        // 检查是否需要刷新重写规则
        $rules_flushed = get_option('logistics_rewrite_rules_flushed', false);

        if (!$rules_flushed) {
            flush_rewrite_rules();
            update_option('logistics_rewrite_rules_flushed', true);
        }
    }

    /**
     * 在我的账户菜单中添加物流追踪
     */
    public function add_menu_items($items) {
        // 在订单后面插入物流追踪
        $new_items = array();

        foreach ($items as $key => $item) {
            $new_items[$key] = $item;

            if ($key === 'orders') {
                $new_items['logistics-tracking'] = __('物流追踪', 'logistics-management');
            }
        }

        return $new_items;
    }

    /**
     * 物流追踪页面内容
     */
    public function logistics_tracking_content() {
        $current_user = wp_get_current_user();
        $customer_orders = wc_get_orders(array(
            'customer' => $current_user->ID,
            'status' => array('wc-processing', 'wc-shipped', 'wc-completed'),
            'limit' => -1,
        ));

        ?>
        <div class="woocommerce-logistics-tracking-page">
            <h2><?php _e('我的物流追踪', 'logistics-management'); ?></h2>

            <?php if (empty($customer_orders)): ?>
                <div class="woocommerce-message woocommerce-message--info woocommerce-Message woocommerce-Message--info woocommerce-info">
                    <p><?php _e('您还没有任何订单。', 'logistics-management'); ?></p>
                    <a class="button" href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>">
                        <?php _e('开始购物', 'logistics-management'); ?>
                    </a>
                </div>
            <?php else: ?>
                <div class="logistics-tracking-list">
                    <?php foreach ($customer_orders as $order): ?>
                        <?php
                        $tracking = Logistics_Database::get_tracking_by_order($order->get_id());
                        ?>
                        <div class="logistics-tracking-item">
                            <div class="tracking-order-info">
                                <h3>
                                    <a href="<?php echo esc_url($order->get_view_order_url()); ?>">
                                        <?php printf(__('订单 #%s', 'logistics-management'), $order->get_order_number()); ?>
                                    </a>
                                </h3>
                                <p class="order-date">
                                    <?php printf(__('下单时间: %s', 'logistics-management'), $order->get_date_created()->date('Y-m-d H:i')); ?>
                                </p>
                                <p class="order-status">
                                    <?php printf(__('订单状态: %s', 'logistics-management'), wc_get_order_status_name($order->get_status())); ?>
                                </p>
                            </div>

                            <div class="tracking-logistics-info">
                                <?php
                                // 检查订单金额是否为0
                                $order_total = $order->get_total();
                                $is_zero_amount = abs((float)$order_total) < 0.01;
                                ?>

                                <?php if ($tracking): ?>
                                    <?php
                                    $status_options = Logistics_Database::get_status_options();
                                    $status_label = isset($status_options[$tracking->status]) ? $status_options[$tracking->status] : $tracking->status;
                                    ?>

                                    <?php if ($is_zero_amount): ?>
                                        <!-- 金额为0的订单简化显示 -->
                                        <div class="zero-amount-order-info">
                                            <div class="tracking-field">
                                                <span class="field-label"><?php _e('订单状态:', 'logistics-management'); ?></span>
                                                <span class="field-value status-<?php echo esc_attr($tracking->status); ?>">
                                                    <?php echo esc_html($status_label); ?>
                                                </span>
                                            </div>
                                            <div class="zero-amount-note">
                                                <p><?php _e('此订单金额为0，无需物流配送', 'logistics-management'); ?></p>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- 正常订单完整显示 -->
                                        <div class="tracking-info-grid">
                                            <div class="tracking-field">
                                                <span class="field-label"><?php _e('快递公司:', 'logistics-management'); ?></span>
                                                <span class="field-value"><?php echo esc_html($tracking->courier_company); ?></span>
                                            </div>
                                            <div class="tracking-field">
                                                <span class="field-label"><?php _e('快递单号:', 'logistics-management'); ?></span>
                                                <span class="field-value tracking-number"><?php echo esc_html($tracking->tracking_number); ?></span>
                                            </div>
                                            <div class="tracking-field">
                                                <span class="field-label"><?php _e('物流状态:', 'logistics-management'); ?></span>
                                                <span class="field-value status-<?php echo esc_attr($tracking->status); ?>">
                                                    <?php echo esc_html($status_label); ?>
                                                </span>
                                            </div>
                                            <div class="tracking-field">
                                                <span class="field-label"><?php _e('最后更新:', 'logistics-management'); ?></span>
                                                <span class="field-value"><?php echo esc_html(mysql2date('Y-m-d H:i', $tracking->last_update)); ?></span>
                                            </div>
                                        </div>

                                        <div class="tracking-actions">
                                            <button type="button" class="button logistics-track-btn" data-tracking-id="<?php echo esc_attr($tracking->id); ?>">
                                                <?php _e('查看物流详情', 'logistics-management'); ?>
                                            </button>
                                            <a href="<?php echo esc_url($order->get_view_order_url()); ?>" class="button">
                                                <?php _e('查看订单', 'logistics-management'); ?>
                                            </a>
                                        </div>

                                        <div class="tracking-details" id="tracking-details-<?php echo esc_attr($tracking->id); ?>" style="display: none;">
                                            <h4><?php _e('物流详情', 'logistics-management'); ?></h4>
                                            <div class="demo-notice" id="demo-notice-<?php echo esc_attr($tracking->id); ?>" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-bottom: 15px; border-radius: 4px; font-size: 0.9em; display: none;">
                                                <strong>📋 演示说明：</strong> 当前显示的是模拟物流数据，用于演示功能。要显示真实物流信息，请联系管理员配置物流API接口。
                                            </div>
                                            <div class="tracking-timeline">
                                                <div class="loading-message">
                                                    <?php _e('正在加载物流信息...', 'logistics-management'); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <?php if ($is_zero_amount): ?>
                                        <!-- 金额为0但没有物流信息的订单 -->
                                        <div class="zero-amount-order-info">
                                            <div class="tracking-field">
                                                <span class="field-label"><?php _e('订单状态:', 'logistics-management'); ?></span>
                                                <span class="field-value status-completed">
                                                    <?php _e('已签收', 'logistics-management'); ?>
                                                </span>
                                            </div>
                                            <div class="zero-amount-note">
                                                <p><?php _e('此订单金额为0，无需物流配送', 'logistics-management'); ?></p>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <!-- 正常订单但没有物流信息 -->
                                        <div class="no-tracking-info">
                                            <p><?php _e('该订单暂无物流信息', 'logistics-management'); ?></p>
                                            <?php if ($order->get_status() === 'processing'): ?>
                                                <p class="note"><?php _e('订单正在处理中，物流信息将在发货后更新', 'logistics-management'); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <style>
        .logistics-tracking-list {
            display: flex;
            flex-direction: column;
            gap: 2em;
        }

        .logistics-tracking-item {
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding: 1.5em;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .tracking-order-info {
            margin-bottom: 1.5em;
            padding-bottom: 1em;
            border-bottom: 1px solid #f0f0f0;
        }

        .tracking-order-info h3 {
            margin: 0 0 0.5em 0;
            font-size: 1.2em;
        }

        .tracking-order-info h3 a {
            color: #0073aa;
            text-decoration: none;
        }

        .tracking-order-info h3 a:hover {
            text-decoration: underline;
        }

        .tracking-order-info p {
            margin: 0.25em 0;
            color: #666;
            font-size: 0.9em;
        }

        .tracking-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1em;
            margin-bottom: 1em;
        }

        .tracking-field {
            display: flex;
            flex-direction: column;
            gap: 0.25em;
        }

        .field-label {
            font-weight: bold;
            color: #666;
            font-size: 0.85em;
            text-transform: uppercase;
        }

        .field-value {
            color: #333;
            font-size: 1em;
        }

        .field-value.tracking-number {
            font-family: monospace;
            font-weight: bold;
            color: #0073aa;
        }

        .field-value.status-pending { color: #f56e28; font-weight: bold; }
        .field-value.status-shipped { color: #ffb900; font-weight: bold; }
        .field-value.status-in_transit { color: #0073aa; font-weight: bold; }
        .field-value.status-delivered { color: #00a32a; font-weight: bold; }
        .field-value.status-exception { color: #d63638; font-weight: bold; }
        .field-value.status-returned { color: #8f98a1; font-weight: bold; }

        .tracking-actions {
            margin-bottom: 1em;
        }

        .tracking-actions .button {
            margin-right: 0.5em;
            margin-bottom: 0.5em;
        }

        .tracking-details {
            border-top: 1px solid #f0f0f0;
            padding-top: 1em;
        }

        .tracking-timeline {
            max-height: 300px;
            overflow-y: auto;
        }

        .no-tracking-info {
            text-align: center;
            padding: 2em;
            color: #666;
        }

        .no-tracking-info .note {
            font-size: 0.9em;
            font-style: italic;
        }

        /* 金额为0订单的样式 */
        .zero-amount-order-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 1.5em;
            text-align: center;
        }

        .zero-amount-order-info .tracking-field {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5em;
            margin-bottom: 1em;
        }

        .zero-amount-order-info .field-label {
            font-weight: bold;
            color: #495057;
            font-size: 1em;
            text-transform: none;
        }

        .zero-amount-order-info .field-value {
            color: #00a32a;
            font-weight: bold;
            font-size: 1.1em;
        }

        .zero-amount-note {
            margin-top: 1em;
        }

        .zero-amount-note p {
            color: #6c757d;
            font-size: 0.9em;
            margin: 0;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .tracking-info-grid {
                grid-template-columns: 1fr;
            }

            .logistics-tracking-item {
                padding: 1em;
            }

            .zero-amount-order-info {
                padding: 1em;
            }

            .zero-amount-order-info .tracking-field {
                flex-direction: column;
                gap: 0.25em;
            }
        }
        </style>
        <?php
    }

    /**
     * 在订单操作中添加查看物流按钮
     */
    public function add_tracking_action($actions, $order) {
        $tracking = Logistics_Database::get_tracking_by_order($order->get_id());

        if ($tracking) {
            $actions['view-tracking'] = array(
                'url'  => add_query_arg('tracking_id', $tracking->id, wc_get_account_endpoint_url('logistics-tracking')),
                'name' => __('查看物流', 'logistics-management'),
            );
        }

        return $actions;
    }

    /**
     * 在订单列表中显示物流状态
     */
    public function add_tracking_status_to_orders($order) {
        // 检查订单金额是否为0
        $order_total = $order->get_total();
        $is_zero_amount = abs((float)$order_total) < 0.01;

        if ($is_zero_amount) {
            // 金额为0的订单显示简化状态
            echo '<br><small class="logistics-status status-delivered">';
            echo __('状态: 已签收', 'logistics-management');
            echo '</small>';
            return;
        }

        $tracking = Logistics_Database::get_tracking_by_order($order->get_id());

        if ($tracking) {
            $status_options = Logistics_Database::get_status_options();
            $status_label = isset($status_options[$tracking->status]) ? $status_options[$tracking->status] : $tracking->status;

            echo '<br><small class="logistics-status status-' . esc_attr($tracking->status) . '">';
            echo __('物流: ', 'logistics-management') . esc_html($status_label);
            echo '</small>';
        } else {
            // 如果没有物流信息，但订单已完成，显示"待发货"
            $order_status = $order->get_status();
            if (in_array($order_status, array('completed', 'processing'))) {
                echo '<br><small class="logistics-status status-pending">';
                echo __('物流: 待发货', 'logistics-management');
                echo '</small>';
            }
        }
    }
}
