<?php

/**
 * 多语言处理数据合集
 */
$lang_data = [
  '数据加载失败' => _x('数据加载失败', 'vue部分', WB_OCW_DM),
  '设置保存成功' => _x('设置保存成功', 'vue部分', WB_OCW_DM),
  '保存失败' => _x('保存失败', 'vue部分', WB_OCW_DM),
  '保存并离开' => _x('保存并离开', 'vue部分', WB_OCW_DM),
  '放弃修改' => _x('放弃修改', 'vue部分', WB_OCW_DM),
  '您修改的设置尚未保存，确定离开此页面吗？' => _x('您修改的设置尚未保存，确定离开此页面吗？', 'vue部分', WB_OCW_DM),
  '预览' => _x('预览', 'vue部分', WB_OCW_DM),
  '展示位置' => _x('展示位置', 'vue部分', WB_OCW_DM),
  '主色调' => _x('主色调', 'vue部分', WB_OCW_DM),
  '* 建议跟网站主色调一致。' => _x('* 建议跟网站主色调一致。', 'vue部分', WB_OCW_DM),
  '暗黑模式' => _x('暗黑模式', 'vue部分', WB_OCW_DM),
  '已设置为暗黑风格，' => _x('已设置为暗黑风格，', 'vue部分', WB_OCW_DM),
  '未启用，' => _x('未启用，', 'vue部分', WB_OCW_DM),
  '该选项适用于某些固定为暗黑风格的站点。' => _x('该选项适用于某些固定为暗黑风格的站点。', 'vue部分', WB_OCW_DM),
  '展示模式' => _x('展示模式', 'vue部分', WB_OCW_DM),
  '展开' => _x('展开', 'vue部分', WB_OCW_DM),
  '浮标' => _x('浮标', 'vue部分', WB_OCW_DM),
  '浮标+窗口' => _x('浮标+窗口', 'vue部分', WB_OCW_DM),
  '展开设置' => _x('展开设置', 'vue部分', WB_OCW_DM),
  '组件形状' => _x('组件形状', 'vue部分', WB_OCW_DM),
  '默认' => _x('默认', 'vue部分', WB_OCW_DM),
  '圆角' => _x('圆角', 'vue部分', WB_OCW_DM),
  '组件尺寸' => _x('组件尺寸', 'vue部分', WB_OCW_DM),
  '大尺寸' => _x('大尺寸', 'vue部分', WB_OCW_DM),
  '浮标名称' => _x('浮标名称', 'vue部分', WB_OCW_DM),
  '显示浮标名称' => _x('显示浮标名称', 'vue部分', WB_OCW_DM),
  '隐藏浮标名称' => _x('隐藏浮标名称', 'vue部分', WB_OCW_DM),
  '浮标动效' => _x('浮标动效', 'vue部分', WB_OCW_DM),
  '显示动效' => _x('显示动效', 'vue部分', WB_OCW_DM),
  '隐藏动效' => _x('隐藏动效', 'vue部分', WB_OCW_DM),
  '出现时间间隔：' => _x('出现时间间隔：', 'vue部分', WB_OCW_DM),
  '秒' => _x('秒', 'vue部分', WB_OCW_DM),
  '界面元素' => _x('界面元素', 'vue部分', WB_OCW_DM),
  '头像' => _x('头像', 'vue部分', WB_OCW_DM),
  '联系人昵称' => _x('联系人昵称', 'vue部分', WB_OCW_DM),
  '欢迎语' => _x('欢迎语', 'vue部分', WB_OCW_DM),
  '响应文字' => _x('响应文字', 'vue部分', WB_OCW_DM),
  '选择提示' => _x('选择提示', 'vue部分', WB_OCW_DM),
  '会员中心' => _x('会员中心', 'vue部分', WB_OCW_DM),
  '展示时高度为60像素，建议准备高度至少为120像素（包括上下留白）的图片。' => _x('展示时高度为60像素，建议准备高度至少为120像素（包括上下留白）的图片。', 'vue部分', WB_OCW_DM),
  '会员中心路径:' => _x('会员中心路径:', 'vue部分', WB_OCW_DM),
  '兼容小部件' => _x('兼容小部件', 'vue部分', WB_OCW_DM),
  '* 填写主题小部件模块的css类名，在插件激活的页面隐藏该模块。' => _x('* 填写主题小部件模块的css类名，在插件激活的页面隐藏该模块。', 'vue部分', WB_OCW_DM),
  '如何找到小部件css类名?' => _x('如何找到小部件css类名?', 'vue部分', WB_OCW_DM),
  '兼容暗黑模式' => _x('兼容暗黑模式', 'vue部分', WB_OCW_DM),
  '* 填写主题暗黑模式激活时的css类名，以响应模式间的切换。' => _x('* 填写主题暗黑模式激活时的css类名，以响应模式间的切换。', 'vue部分', WB_OCW_DM),
  '如何找到暗黑模式css类名?' => _x('如何找到暗黑模式css类名?', 'vue部分', WB_OCW_DM),
  '左中' => _x('左中', 'vue部分', WB_OCW_DM),
  '左下' => _x('左下', 'vue部分', WB_OCW_DM),
  '右中' => _x('右中', 'vue部分', WB_OCW_DM),
  '右下' => _x('右下', 'vue部分', WB_OCW_DM),
  '自定义' => _x('自定义', 'vue部分', WB_OCW_DM),
  '请至少选择一种联系方式' => _x('请至少选择一种联系方式', 'vue部分', WB_OCW_DM),
  '请输入自定义类型的别名' => _x('请输入自定义类型的别名', 'vue部分', WB_OCW_DM),
  '确认删除?' => _x('确认删除?', 'vue部分', WB_OCW_DM),
  '类型' => _x('类型', 'vue部分', WB_OCW_DM),
  '请选择' => _x('请选择', 'vue部分', WB_OCW_DM),
  '别名' => _x('别名', 'vue部分', WB_OCW_DM),
  '* 以简短全小写字母定义, 如telegram, viber等。' => _x('* 以简短全小写字母定义, 如telegram, viber等。', 'vue部分', WB_OCW_DM),
  '显示名称' => _x('显示名称', 'vue部分', WB_OCW_DM),
  '咨询类型' => _x('咨询类型', 'vue部分', WB_OCW_DM),
  '按回车确认' => _x('按回车确认', 'vue部分', WB_OCW_DM),
  '联系方式' => _x('联系方式', 'vue部分', WB_OCW_DM),
  '* 请至少选择一种联系方式' => _x('* 请至少选择一种联系方式', 'vue部分', WB_OCW_DM),
  '提交成功提示语' => _x('提交成功提示语', 'vue部分', WB_OCW_DM),
  '输入自动回复的内容。' => _x('输入自动回复的内容。', 'vue部分', WB_OCW_DM),
  '验证码方式' => _x('验证码方式', 'vue部分', WB_OCW_DM),
  '无' => _x('无', 'vue部分', WB_OCW_DM),
  '一般验证码' => _x('一般验证码', 'vue部分', WB_OCW_DM),
  '谷歌reCAPTCHA v3' => _x('谷歌reCAPTCHA v3', 'vue部分', WB_OCW_DM),
  '网站秘钥' => _x('网站秘钥', 'vue部分', WB_OCW_DM),
  '通讯秘钥' => _x('通讯秘钥', 'vue部分', WB_OCW_DM),
  '分数阈值' => _x('分数阈值', 'vue部分', WB_OCW_DM),
  '注：要启用谷歌reCAPTCHA验证注册，请参照' => _x('注：要启用谷歌reCAPTCHA验证注册，请参照', 'vue部分', WB_OCW_DM),
  '谷歌reCAPTCHA v3申请及配置教程' => _x('谷歌reCAPTCHA v3申请及配置教程', 'vue部分', WB_OCW_DM),
  '留言方式' => _x('留言方式', 'vue部分', WB_OCW_DM),
  '已开启需要登录留言' => _x('已开启需要登录留言', 'vue部分', WB_OCW_DM),
  '无登录访客可留言' => _x('无登录访客可留言', 'vue部分', WB_OCW_DM),
  '登录链接' => _x('登录链接', 'vue部分', WB_OCW_DM),
  '* 若无改写登录链接，留空使用默认即可。' => _x('* 若无改写登录链接，留空使用默认即可。', 'vue部分', WB_OCW_DM),
  '留言通知' => _x('留言通知', 'vue部分', WB_OCW_DM),
  '邮件通知' => _x('邮件通知', 'vue部分', WB_OCW_DM),
  '短信通知' => _x('短信通知', 'vue部分', WB_OCW_DM),
  '添加' => _x('添加', 'vue部分', WB_OCW_DM),
  '取消' => _x('取消', 'vue部分', WB_OCW_DM),
  '保存' => _x('保存', 'vue部分', WB_OCW_DM),
  '建议至多设定%s个选项。' => _x('建议至多设定%s个选项。', 'vue部分', WB_OCW_DM),
  '默认为: %s' => _x('默认为: %s', 'vue部分', WB_OCW_DM),
  '设置成功' => _x('设置成功', 'vue部分', WB_OCW_DM),
  '请先配置选项再启用。' => _x('请先配置选项再启用。', 'vue部分', WB_OCW_DM),
  '启用中' => _x('启用中', 'vue部分', WB_OCW_DM),
  '未启用' => _x('未启用', 'vue部分', WB_OCW_DM),
  '删除' => _x('删除', 'vue部分', WB_OCW_DM),
  '编辑' => _x('编辑', 'vue部分', WB_OCW_DM),
  '停用' => _x('停用', 'vue部分', WB_OCW_DM),
  '启用' => _x('启用', 'vue部分', WB_OCW_DM),
  '组件' => _x('组件', 'vue部分', WB_OCW_DM),
  '选项' => _x('选项', 'vue部分', WB_OCW_DM),
  '状态' => _x('状态', 'vue部分', WB_OCW_DM),
  '操作' => _x('操作', 'vue部分', WB_OCW_DM),
  '* 可拖拽对组件排序' => _x('* 可拖拽对组件排序', 'vue部分', WB_OCW_DM),
  '短信发送成功' => _x('短信发送成功', 'vue部分', WB_OCW_DM),
  '短信发送失败' => _x('短信发送失败', 'vue部分', WB_OCW_DM),
  '请确认手机号码格式' => _x('请确认手机号码格式', 'vue部分', WB_OCW_DM),
  '请输入测试收件箱。' => _x('请输入测试收件箱。', 'vue部分', WB_OCW_DM),
  '邮件发送失败' => _x('邮件发送失败', 'vue部分', WB_OCW_DM),
  '邮局设置' => _x('邮局设置', 'vue部分', WB_OCW_DM),
  '通知邮箱' => _x('通知邮箱', 'vue部分', WB_OCW_DM),
  '* 多个邮箱用英文逗号(,)分隔' => _x('* 多个邮箱用英文逗号(,)分隔', 'vue部分', WB_OCW_DM),
  '邮件服务' => _x('邮件服务', 'vue部分', WB_OCW_DM),
  '检测到当前正在使用闪电博主题，将使用主题邮件服务设置。' => _x('检测到当前正在使用闪电博主题，将使用主题邮件服务设置。', 'vue部分', WB_OCW_DM),
  '发件邮箱' => _x('发件邮箱', 'vue部分', WB_OCW_DM),
  '* 如果启用SMTP邮局，发件邮箱必须与SMTP邮局邮箱保持一致。' => _x('* 如果启用SMTP邮局，发件邮箱必须与SMTP邮局邮箱保持一致。', 'vue部分', WB_OCW_DM),
  '发件人名称' => _x('发件人名称', 'vue部分', WB_OCW_DM),
  '邮件程序' => _x('邮件程序', 'vue部分', WB_OCW_DM),
  '* 由于大部分国内服务器禁用了邮局端口，无法通过WP默认邮局发信。建议使用QQ、163或者其他SMTP邮局执行WP发信任务。' => _x('* 由于大部分国内服务器禁用了邮局端口，无法通过WP默认邮局发信。建议使用QQ、163或者其他SMTP邮局执行WP发信任务。', 'vue部分', WB_OCW_DM),
  '查看SMTP邮局设置教程' => _x('查看SMTP邮局设置教程', 'vue部分', WB_OCW_DM),
  'SMTP配置' => _x('SMTP配置', 'vue部分', WB_OCW_DM),
  'SMTP服务器' => _x('SMTP服务器', 'vue部分', WB_OCW_DM),
  '加密方式' => _x('加密方式', 'vue部分', WB_OCW_DM),
  '端口' => _x('端口', 'vue部分', WB_OCW_DM),
  'SMTP用户名' => _x('SMTP用户名', 'vue部分', WB_OCW_DM),
  'SMTP密码/授权码' => _x('SMTP密码/授权码', 'vue部分', WB_OCW_DM),
  '发送测试邮件' => _x('发送测试邮件', 'vue部分', WB_OCW_DM),
  '<EMAIL>' => _x('<EMAIL>', 'vue部分', WB_OCW_DM),
  '发送' => _x('发送', 'vue部分', WB_OCW_DM),
  '* 测试邮件收件人必须与发件人邮箱地址保持一致，否则测试邮件将会发送失败。' => _x('* 测试邮件收件人必须与发件人邮箱地址保持一致，否则测试邮件将会发送失败。', 'vue部分', WB_OCW_DM),
  '短信设置' => _x('短信设置', 'vue部分', WB_OCW_DM),
  '通知手机号' => _x('通知手机号', 'vue部分', WB_OCW_DM),
  '* 多个手机用英文逗号(,)分隔' => _x('* 多个手机用英文逗号(,)分隔', 'vue部分', WB_OCW_DM),
  '服务商' => _x('服务商', 'vue部分', WB_OCW_DM),
  '又拍云' => _x('又拍云', 'vue部分', WB_OCW_DM),
  '阿里云' => _x('阿里云', 'vue部分', WB_OCW_DM),
  '华为云' => _x('华为云', 'vue部分', WB_OCW_DM),
  '参数' => _x('参数', 'vue部分', WB_OCW_DM),
  '账号ID' => _x('账号ID', 'vue部分', WB_OCW_DM),
  '进入又拍云短信服务控制台>短信发送>API发送，点击流程步骤2-获取账号token获取' => _x('进入又拍云短信服务控制台>短信发送>API发送，点击流程步骤2-获取账号token获取', 'vue部分', WB_OCW_DM),
  'Token' => _x('Token', 'vue部分', WB_OCW_DM),
  '账号token或者模板token（推荐后者）' => _x('账号token或者模板token（推荐后者）', 'vue部分', WB_OCW_DM),
  '模板编号' => _x('模板编号', 'vue部分', WB_OCW_DM),
  '进入又拍云短信服务控制台>配置中心>模板配置获取' => _x('进入又拍云短信服务控制台>配置中心>模板配置获取', 'vue部分', WB_OCW_DM),
  '短信类型' => _x('短信类型', 'vue部分', WB_OCW_DM),
  '行业短信-通用' => _x('行业短信-通用', 'vue部分', WB_OCW_DM),
  '模板名称' => _x('模板名称', 'vue部分', WB_OCW_DM),
  '自定义命名' => _x('自定义命名', 'vue部分', WB_OCW_DM),
  '短信内容' => _x('短信内容', 'vue部分', WB_OCW_DM),
  '自定义。模板文本变量{$var1}。例如：收到{$var1}的留言。' => _x('自定义。模板文本变量{$var1}。例如：收到{$var1}的留言。', 'vue部分', WB_OCW_DM),
  '签名' => _x('签名', 'vue部分', WB_OCW_DM),
  '测试短信' => _x('测试短信', 'vue部分', WB_OCW_DM),
  '输入手机号' => _x('输入手机号', 'vue部分', WB_OCW_DM),
  '测试短信返回结果：' => _x('测试短信返回结果：', 'vue部分', WB_OCW_DM),
  'Access Key Id' => _x('Access Key Id', 'vue部分', WB_OCW_DM),
  '登录阿里云工作台>点击主账号头像>AccessKey管理，创建生成。' => _x('登录阿里云工作台>点击主账号头像>AccessKey管理，创建生成。', 'vue部分', WB_OCW_DM),
  'Access Key Secret' => _x('Access Key Secret', 'vue部分', WB_OCW_DM),
  '短信签名' => _x('短信签名', 'vue部分', WB_OCW_DM),
  '登录阿里云短信服务工作台>国内消息>模板管理>查看短信模板详情>关联签名。' => _x('登录阿里云短信服务工作台>国内消息>模板管理>查看短信模板详情>关联签名。', 'vue部分', WB_OCW_DM),
  '模板CODE' => _x('模板CODE', 'vue部分', WB_OCW_DM),
  '登录阿里云短信服务工作台>国内消息>模板管理，填入对应模板的CODE。' => _x('登录阿里云短信服务工作台>国内消息>模板管理，填入对应模板的CODE。', 'vue部分', WB_OCW_DM),
  '模板类型' => _x('模板类型', 'vue部分', WB_OCW_DM),
  '通知短信' => _x('通知短信', 'vue部分', WB_OCW_DM),
  '模版内容' => _x('模版内容', 'vue部分', WB_OCW_DM),
  '自定义，建议使用阿里云提供的常用模版库修改。变量${name}。如：收到${name}的留言。' => _x('自定义，建议使用阿里云提供的常用模版库修改。变量${name}。如：收到${name}的留言。', 'vue部分', WB_OCW_DM),
  '申请说明' => _x('申请说明', 'vue部分', WB_OCW_DM),
  '使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。' => _x('使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。', 'vue部分', WB_OCW_DM),
  '接入地址' => _x('接入地址', 'vue部分', WB_OCW_DM),
  '登录华为云消息&短信控制台>国内短信>应用管理，填入对应的APP接入地址。' => _x('登录华为云消息&短信控制台>国内短信>应用管理，填入对应的APP接入地址。', 'vue部分', WB_OCW_DM),
  'Application Key' => _x('Application Key', 'vue部分', WB_OCW_DM),
  '登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Key。' => _x('登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Key。', 'vue部分', WB_OCW_DM),
  'Application Secret' => _x('Application Secret', 'vue部分', WB_OCW_DM),
  '登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Secret。' => _x('登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Secret。', 'vue部分', WB_OCW_DM),
  '登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名名称。' => _x('登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名名称。', 'vue部分', WB_OCW_DM),
  '签名通道号' => _x('签名通道号', 'vue部分', WB_OCW_DM),
  '登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名通道号。' => _x('登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名通道号。', 'vue部分', WB_OCW_DM),
  '模板ID' => _x('模板ID', 'vue部分', WB_OCW_DM),
  '登录华为云消息&短信控制台>国内短信>模板管理，填入对应的模板ID。' => _x('登录华为云消息&短信控制台>国内短信>模板管理，填入对应的模板ID。', 'vue部分', WB_OCW_DM),
  '自定义。使用变量${1}。如：收到${1}的留言。' => _x('自定义。使用变量${1}。如：收到${1}的留言。', 'vue部分', WB_OCW_DM),
  '使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。。' => _x('使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。。', 'vue部分', WB_OCW_DM),
  '当前为Free版，高级功能需' => _x('当前为Free版，高级功能需', 'vue部分', WB_OCW_DM),
  '升级为PRO版' => _x('升级为PRO版', 'vue部分', WB_OCW_DM),
  '显示设备' => _x('显示设备', 'vue部分', WB_OCW_DM),
  '桌面端' => _x('桌面端', 'vue部分', WB_OCW_DM),
  '移动端' => _x('移动端', 'vue部分', WB_OCW_DM),
  '显示页面' => _x('显示页面', 'vue部分', WB_OCW_DM),
  '全部页面' => _x('全部页面', 'vue部分', WB_OCW_DM),
  '指定页面' => _x('指定页面', 'vue部分', WB_OCW_DM),
  '例外页面' => _x('例外页面', 'vue部分', WB_OCW_DM),
  '缓存功能' => _x('缓存功能', 'vue部分', WB_OCW_DM),
  '已开启，' => _x('已开启，', 'vue部分', WB_OCW_DM),
  '该功能会将模块静态化处理（适用于性能优化）。' => _x('该功能会将模块静态化处理（适用于性能优化）。', 'vue部分', WB_OCW_DM),
  '细节定制' => _x('细节定制', 'vue部分', WB_OCW_DM),
  '浮标模式' => _x('浮标模式', 'vue部分', WB_OCW_DM),
  '浮标图标尺寸' => _x('浮标图标尺寸', 'vue部分', WB_OCW_DM),
  '浮标文字' => _x('浮标文字', 'vue部分', WB_OCW_DM),
  '浮标图标' => _x('浮标图标', 'vue部分', WB_OCW_DM),
  '自定义浮标' => _x('自定义浮标', 'vue部分', WB_OCW_DM),
  '基础字号' => _x('基础字号', 'vue部分', WB_OCW_DM),
  '窗口宽度' => _x('窗口宽度', 'vue部分', WB_OCW_DM),
  '窗口头部' => _x('窗口头部', 'vue部分', WB_OCW_DM),
  '背景颜色' => _x('背景颜色', 'vue部分', WB_OCW_DM),
  '文字颜色' => _x('文字颜色', 'vue部分', WB_OCW_DM),
  '展开模式' => _x('展开模式', 'vue部分', WB_OCW_DM),
  '尺寸' => _x('尺寸', 'vue部分', WB_OCW_DM),
  '留言窗口宽度' => _x('留言窗口宽度', 'vue部分', WB_OCW_DM),
  '位置调整' => _x('位置调整', 'vue部分', WB_OCW_DM),
  '横向偏移值' => _x('横向偏移值', 'vue部分', WB_OCW_DM),
  '纵向偏移值' => _x('纵向偏移值', 'vue部分', WB_OCW_DM),
  '自定义CSS样式' => _x('自定义CSS样式', 'vue部分', WB_OCW_DM),
  '升级为Pro版' => _x('升级为Pro版', 'vue部分', WB_OCW_DM),
  '操作成功' => _x('操作成功', 'vue部分', WB_OCW_DM),
  '确认标记为已处理？' => _x('确认标记为已处理？', 'vue部分', WB_OCW_DM),
  '确认关闭？' => _x('确认关闭？', 'vue部分', WB_OCW_DM),
  '删除后将无法恢复，确认删除？' => _x('删除后将无法恢复，确认删除？', 'vue部分', WB_OCW_DM),
  '请输入内容再提交' => _x('请输入内容再提交', 'vue部分', WB_OCW_DM),
  '已关闭' => _x('已关闭', 'vue部分', WB_OCW_DM),
  '待处理' => _x('待处理', 'vue部分', WB_OCW_DM),
  '已处理' => _x('已处理', 'vue部分', WB_OCW_DM),
  '未填写' => _x('未填写', 'vue部分', WB_OCW_DM),
  '联系人' => _x('联系人', 'vue部分', WB_OCW_DM),
  '发起时间' => _x('发起时间', 'vue部分', WB_OCW_DM),
  '更新时间' => _x('更新时间', 'vue部分', WB_OCW_DM),
  '关闭工单' => _x('关闭工单', 'vue部分', WB_OCW_DM),
  '返回列表' => _x('返回列表', 'vue部分', WB_OCW_DM),
  '备注:' => _x('备注:', 'vue部分', WB_OCW_DM),
  '留言内容:' => _x('留言内容:', 'vue部分', WB_OCW_DM),
  '请输入备注' => _x('请输入备注', 'vue部分', WB_OCW_DM),
  '添加备注' => _x('添加备注', 'vue部分', WB_OCW_DM),
  '关闭' => _x('关闭', 'vue部分', WB_OCW_DM),
  '标记为已处理' => _x('标记为已处理', 'vue部分', WB_OCW_DM),
  '全部' => _x('全部', 'vue部分', WB_OCW_DM),
  '工单类型' => _x('工单类型', 'vue部分', WB_OCW_DM),
  '输入关键字' => _x('输入关键字', 'vue部分', WB_OCW_DM),
  '筛选' => _x('筛选', 'vue部分', WB_OCW_DM),
  '时间' => _x('时间', 'vue部分', WB_OCW_DM),
  '详情' => _x('详情', 'vue部分', WB_OCW_DM),
  '多合一在线客服插件' => _x('多合一在线客服插件', 'vue部分', WB_OCW_DM),
  '插件设置' => _x('插件设置', 'vue部分', WB_OCW_DM),
  '基本设置' => _x('基本设置', 'vue部分', WB_OCW_DM),
  '组件设置' => _x('组件设置', 'vue部分', WB_OCW_DM),
  '高级设置' => _x('高级设置', 'vue部分', WB_OCW_DM),
  '组件编辑' => _x('组件编辑', 'vue部分', WB_OCW_DM),
  '工单管理' => _x('工单管理', 'vue部分', WB_OCW_DM),
  '工单详情' => _x('工单详情', 'vue部分', WB_OCW_DM),
  '主题推荐' => _x('主题推荐', 'vue部分', WB_OCW_DM),
  '插件推荐' => _x('插件推荐', 'vue部分', WB_OCW_DM),
  'WP教程' => _x('WP教程', 'vue部分', WB_OCW_DM),
  '全屏' => _x('全屏', 'vue部分', WB_OCW_DM),
  '闪电博' => _x('闪电博', 'vue部分', WB_OCW_DM),
  '免费插件' => _x('免费插件', 'vue部分', WB_OCW_DM),
  '说明文档' => _x('说明文档', 'vue部分', WB_OCW_DM),
  '服务协议' => _x('服务协议', 'vue部分', WB_OCW_DM),
  '隐私条例' => _x('隐私条例', 'vue部分', WB_OCW_DM),
  '版本：%s' => _x('版本：%s', 'vue部分', WB_OCW_DM),
  '保存设置' => _x('保存设置', 'vue部分', WB_OCW_DM),
  '首页' => _x('首页', 'vue部分', WB_OCW_DM),
  'PRO版' => _x('PRO版', 'vue部分', WB_OCW_DM),
  'Free版' => _x('Free版', 'vue部分', WB_OCW_DM),
  '插件主页' => _x('插件主页', 'vue部分', WB_OCW_DM),
  '激活插件' => _x('激活插件', 'vue部分', WB_OCW_DM),
  '或' => _x('或', 'vue部分', WB_OCW_DM),
  '现在更新' => _x('现在更新', 'vue部分', WB_OCW_DM),
  '当前%s有新版本可用.' => _x('当前%s有新版本可用.', 'vue部分', WB_OCW_DM),
  '查看版本%s详情' => _x('查看版本%s详情', 'vue部分', WB_OCW_DM),
  '温馨提示' => _x('温馨提示', 'vue部分', WB_OCW_DM),
  '输入关键字筛选' => _x('输入关键字筛选', 'vue部分', WB_OCW_DM),
  '完成' => _x('完成', 'vue部分', WB_OCW_DM),
  '没有匹配 %s 的记录，请换个关键词试试...' => _x('没有匹配 %s 的记录，请换个关键词试试...', 'vue部分', WB_OCW_DM),
  '请输入标签' => _x('请输入标签', 'vue部分', WB_OCW_DM),
  '已存在' => _x('已存在', 'vue部分', WB_OCW_DM),
  '已移除' => _x('已移除', 'vue部分', WB_OCW_DM),
  '别名:' => _x('别名:', 'vue部分', WB_OCW_DM),
  '选择文件' => _x('选择文件', 'vue部分', WB_OCW_DM),
  '确认' => _x('确认', 'vue部分', WB_OCW_DM),
  '选择' => _x('选择', 'vue部分', WB_OCW_DM),
  '插入URL' => _x('插入URL', 'vue部分', WB_OCW_DM),
  '设置链接' => _x('设置链接', 'vue部分', WB_OCW_DM),
  '激活KEY' => _x('激活KEY', 'vue部分', WB_OCW_DM),
  '请输入激活KEY' => _x('请输入激活KEY', 'vue部分', WB_OCW_DM),
  '获取KEY' => _x('获取KEY', 'vue部分', WB_OCW_DM),
  '绑定信息确认' => _x('绑定信息确认', 'vue部分', WB_OCW_DM),
  '提交验证' => _x('提交验证', 'vue部分', WB_OCW_DM),
  '请输入激活码' => _x('请输入激活码', 'vue部分', WB_OCW_DM),
  '验证中...' => _x('验证中...', 'vue部分', WB_OCW_DM),
  '验证成功' => _x('验证成功', 'vue部分', WB_OCW_DM),
  '绑定域名后将不可更改，确认绑定到域名：%s' => _x('绑定域名后将不可更改，确认绑定到域名：%s', 'vue部分', WB_OCW_DM),
];
