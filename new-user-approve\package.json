{"name": "new-user-approve", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "wp-scripts build", "start": "wp-scripts start"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/base": "^5.0.0-beta.40", "@mui/icons-material": "^5.15.21", "@mui/joy": "^5.0.0-beta.47", "@mui/material": "^5.15.21", "@mui/system": "^5.15.20", "@wordpress/block-editor": "^13.4.0", "@wordpress/block-library": "^9.4.0", "@wordpress/blocks": "^13.4.0", "@wordpress/components": "^28.4.0", "@wordpress/data": "^10.4.0", "@wordpress/editor": "^14.4.0", "@wordpress/element": "^6.4.0", "axios": "^1.7.2", "he": "^1.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.24.1", "react-select": "^5.8.0", "react-toastify": "^11.0.5", "webpack-merge": "^6.0.1"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@svgr/webpack": "^8.1.0", "@wordpress/scripts": "^27.8.0", "babel-loader": "^9.1.3", "file-loader": "^6.2.0", "webpack": "^5.92.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}}