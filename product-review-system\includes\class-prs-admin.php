<?php
/**
 * 管理员界面类
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class PRS_Admin {

    /**
     * 构造函数
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'handle_admin_actions'));
        add_filter('set-screen-option', array($this, 'set_screen_option'), 10, 3);


    }

    /**
     * 添加管理员菜单
     */
    public function add_admin_menu() {
        // 检查权限
        $capability = PRS_Permissions::can_user_view_reviews() ? 'view_product_reviews' : 'manage_options';

        $hook = add_menu_page(
            __('产品审核系统', 'product-review-system'),
            __('产品审核', 'product-review-system'),
            $capability,
            'product-review-system',
            array($this, 'admin_page'),
            'dashicons-products',
            57
        );

        // 待审核列表
        add_submenu_page(
            'product-review-system',
            __('待审核列表', 'product-review-system'),
            __('待审核列表', 'product-review-system'),
            $capability,
            'prs-pending-reviews',
            array($this, 'pending_reviews_page')
        );

        // 审核历史
        add_submenu_page(
            'product-review-system',
            __('审核历史', 'product-review-system'),
            __('审核历史', 'product-review-system'),
            $capability,
            'prs-review-history',
            array($this, 'review_history_page')
        );

        // 设置页面（仅管理员可见）
        if (current_user_can('manage_product_review_settings')) {
            add_submenu_page(
                'product-review-system',
                __('审核设置', 'product-review-system'),
                __('审核设置', 'product-review-system'),
                'manage_product_review_settings',
                'prs-settings',
                array($this, 'settings_page')
            );
        }

        add_action("load-$hook", array($this, 'screen_option'));
    }

    /**
     * 设置屏幕选项
     */
    public function screen_option() {
        $option = 'per_page';
        $args = array(
            'label' => __('每页显示项目数', 'product-review-system'),
            'default' => 20,
            'option' => 'prs_reviews_per_page'
        );

        add_screen_option($option, $args);
    }

    /**
     * 设置屏幕选项值
     */
    public function set_screen_option($status, $option, $value) {
        return $value;
    }

    /**
     * 处理管理员操作
     */
    public function handle_admin_actions() {
        if (!isset($_GET['page']) || strpos($_GET['page'], 'product-review-system') === false) {
            return;
        }

        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        switch ($action) {
            case 'delete':
                $this->handle_delete_review($id);
                break;
        }
    }

    /**
     * 处理删除审核记录
     */
    private function handle_delete_review($id) {
        if (!wp_verify_nonce($_GET['_wpnonce'], 'delete_review_' . $id)) {
            wp_die(__('安全验证失败', 'product-review-system'));
        }

        if (!current_user_can('delete_product_reviews')) {
            wp_die(__('您没有权限删除审核记录。', 'product-review-system'));
        }

        $result = PRS_Database::delete_review($id);

        if ($result) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success"><p>' . __('审核记录删除成功！', 'product-review-system') . '</p></div>';
            });
        } else {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('删除失败，请重试！', 'product-review-system') . '</p></div>';
            });
        }

        wp_redirect(admin_url('admin.php?page=product-review-system'));
        exit;
    }

    /**
     * 主管理页面
     */
    public function admin_page() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'dashboard';
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        switch ($action) {
            case 'view':
                $this->view_review_page($id);
                break;
            case 'edit':
                $this->edit_review_page($id);
                break;
            default:
                $this->dashboard_page();
                break;
        }
    }

    /**
     * 仪表板页面
     */
    private function dashboard_page() {
        $stats = PRS_Database::get_review_stats();
        $user_role = PRS_Permissions::get_user_review_role();
        
        // 获取当前用户相关的待审核列表
        $pending_reviews = PRS_Database::get_pending_reviews($user_role, 10);
        
        ?>
        <div class="wrap">
            <h1><?php _e('产品审核系统', 'product-review-system'); ?></h1>

            <?php if (isset($_GET['submitted']) && $_GET['submitted'] == '1'): ?>
                <div class="notice notice-success">
                    <p><?php _e('产品修改已提交审核，请等待审核结果。', 'product-review-system'); ?></p>
                </div>
            <?php endif; ?>

            <!-- 统计概览 -->
            <div class="prs-stats-grid">
                <div class="prs-stat-card">
                    <h3><?php _e('总审核数', 'product-review-system'); ?></h3>
                    <div class="prs-stat-number"><?php echo esc_html($stats['total']); ?></div>
                </div>
                <div class="prs-stat-card prs-stat-pending">
                    <h3><?php _e('等待审核员审核', 'product-review-system'); ?></h3>
                    <div class="prs-stat-number"><?php echo esc_html($stats['pending_review']); ?></div>
                </div>
                <div class="prs-stat-card prs-stat-admin">
                    <h3><?php _e('等待管理员审核', 'product-review-system'); ?></h3>
                    <div class="prs-stat-number"><?php echo esc_html($stats['pending_admin']); ?></div>
                </div>
                <div class="prs-stat-card prs-stat-approved">
                    <h3><?php _e('已通过', 'product-review-system'); ?></h3>
                    <div class="prs-stat-number"><?php echo esc_html($stats['approved']); ?></div>
                </div>
                <div class="prs-stat-card prs-stat-rejected">
                    <h3><?php _e('已拒绝', 'product-review-system'); ?></h3>
                    <div class="prs-stat-number"><?php echo esc_html($stats['rejected']); ?></div>
                </div>
            </div>

            <!-- 待处理审核 -->
            <?php if (!empty($pending_reviews)): ?>
            <div class="prs-pending-section">
                <h2><?php _e('待处理审核', 'product-review-system'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('ID', 'product-review-system'); ?></th>
                            <th><?php _e('产品', 'product-review-system'); ?></th>
                            <th><?php _e('提交者', 'product-review-system'); ?></th>
                            <th><?php _e('状态', 'product-review-system'); ?></th>
                            <th><?php _e('提交时间', 'product-review-system'); ?></th>
                            <th><?php _e('操作', 'product-review-system'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pending_reviews as $review): ?>
                            <?php
                            $product = wc_get_product($review->product_id);
                            $submitter = isset($review->submitter_id) && $review->submitter_id ? get_user_by('id', $review->submitter_id) : null;
                            $status_options = PRS_Database::get_status_options();
                            ?>
                            <tr>
                                <td><?php echo esc_html($review->id); ?></td>
                                <td>
                                    <?php if ($product): ?>
                                        <a href="<?php echo esc_url(admin_url('post.php?post=' . $review->product_id . '&action=edit')); ?>">
                                            <?php echo esc_html($product->get_name()); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php _e('产品不存在', 'product-review-system'); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $submitter ? esc_html($submitter->display_name) : __('未知用户', 'product-review-system'); ?>
                                </td>
                                <td>
                                    <span class="prs-status prs-status-<?php echo esc_attr($review->status); ?>">
                                        <?php echo isset($status_options[$review->status]) ? esc_html($status_options[$review->status]) : esc_html($review->status); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html(mysql2date('Y-m-d H:i', $review->created_at)); ?></td>
                                <td>
                                    <a href="<?php echo esc_url(admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id)); ?>" class="button button-small">
                                        <?php _e('查看', 'product-review-system'); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <p>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=prs-pending-reviews')); ?>" class="button">
                        <?php _e('查看所有待审核', 'product-review-system'); ?>
                    </a>
                </p>
            </div>
            <?php endif; ?>
        </div>

        <style>
        .prs-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .prs-stat-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        .prs-stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        .prs-stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #23282d;
        }
        .prs-stat-pending .prs-stat-number { color: #ffb900; }
        .prs-stat-admin .prs-stat-number { color: #00a32a; }
        .prs-stat-approved .prs-stat-number { color: #007cba; }
        .prs-stat-rejected .prs-stat-number { color: #d63638; }
        .prs-pending-section {
            margin-top: 30px;
        }
        .prs-status {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .prs-status-pending_review { background: #fff3cd; color: #856404; }
        .prs-status-pending_admin { background: #d1ecf1; color: #0c5460; }
        .prs-status-approved { background: #d4edda; color: #155724; }
        .prs-status-rejected { background: #f8d7da; color: #721c24; }
        </style>
        <?php
    }

    /**
     * 查看审核详情页面
     */
    private function view_review_page($id) {
        $review = PRS_Database::get_review($id);
        if (!$review) {
            wp_die(__('审核记录不存在。', 'product-review-system'));
        }

        $product = wc_get_product($review->product_id);
        $submitter = isset($review->submitter_id) && $review->submitter_id ? get_user_by('id', $review->submitter_id) : null;
        $reviewer = isset($review->reviewer_id) && $review->reviewer_id ? get_user_by('id', $review->reviewer_id) : null;
        $admin = isset($review->admin_id) && $review->admin_id ? get_user_by('id', $review->admin_id) : null;

        $original_data = json_decode($review->original_data, true);
        $modified_data = json_decode($review->modified_data, true);

        $status_options = PRS_Database::get_status_options();
        $user_role = PRS_Permissions::get_user_review_role();

        // 处理表单提交 - 必须在任何输出之前处理
        if (isset($_POST['prs_action']) && isset($_POST['prs_nonce'])) {
            try {
                $this->handle_review_form_submission($id, sanitize_text_field($_POST['prs_action']));
                // 如果到达这里，说明重定向失败
                echo '<div class="notice notice-warning"><p>审核处理完成，但页面重定向失败。请手动刷新页面。</p></div>';
            } catch (Exception $e) {
                // 显示错误消息
                echo '<div class="notice notice-error"><p>处理审核时发生错误：' . esc_html($e->getMessage()) . '</p></div>';
            }
        }

        // 显示操作结果消息
        $this->display_review_messages();

        ?>
        <div class="wrap">
            <h1><?php printf(__('审核详情 #%d', 'product-review-system'), $review->id); ?></h1>

            <div id="poststuff">
                <div id="post-body" class="metabox-holder columns-2">
                    <div id="post-body-content">
                        <!-- 基本信息 -->
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2><?php _e('基本信息', 'product-review-system'); ?></h2>
                            </div>
                            <div class="inside">
                                <table class="form-table">
                                    <tr>
                                        <th><?php _e('审核ID', 'product-review-system'); ?></th>
                                        <td><?php echo esc_html($review->id); ?></td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('产品', 'product-review-system'); ?></th>
                                        <td>
                                            <?php if ($product): ?>
                                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $review->product_id . '&action=edit')); ?>">
                                                    <?php echo esc_html($product->get_name()); ?>
                                                </a>
                                                <br>
                                                <small>ID: <?php echo esc_html($review->product_id); ?> | 状态: <?php echo esc_html($product->get_status()); ?> | 可见性: <?php echo esc_html($product->get_catalog_visibility()); ?></small>
                                            <?php else: ?>
                                                <?php _e('产品不存在', 'product-review-system'); ?>
                                                <br>
                                                <small>产品ID: <?php echo esc_html($review->product_id); ?></small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('提交者', 'product-review-system'); ?></th>
                                        <td>
                                            <?php if ($submitter): ?>
                                                <a href="<?php echo esc_url(admin_url('user-edit.php?user_id=' . $review->submitter_id)); ?>">
                                                    <?php echo esc_html($submitter->display_name); ?>
                                                </a>
                                            <?php else: ?>
                                                <?php _e('未知用户', 'product-review-system'); ?>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('当前状态', 'product-review-system'); ?></th>
                                        <td>
                                            <span class="prs-status prs-status-<?php echo esc_attr($review->status); ?>">
                                                <?php echo isset($status_options[$review->status]) ? esc_html($status_options[$review->status]) : esc_html($review->status); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th><?php _e('提交时间', 'product-review-system'); ?></th>
                                        <td><?php echo esc_html(mysql2date('Y-m-d H:i:s', $review->created_at)); ?></td>
                                    </tr>
                                    <?php if (isset($review->reviewer_date) && $review->reviewer_date): ?>
                                    <tr>
                                        <th><?php _e('审核员审核时间', 'product-review-system'); ?></th>
                                        <td><?php echo esc_html(mysql2date('Y-m-d H:i:s', $review->reviewer_date)); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <?php if (isset($review->admin_date) && $review->admin_date): ?>
                                    <tr>
                                        <th><?php _e('管理员审核时间', 'product-review-system'); ?></th>
                                        <td><?php echo esc_html(mysql2date('Y-m-d H:i:s', $review->admin_date)); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>

                        <!-- 操作类型和摘要 -->
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2>
                                    <?php
                                    $operation_type = isset($review->operation_type) ? $review->operation_type : 'modify';
                                    if ($operation_type === 'delete') {
                                        _e('删除请求', 'product-review-system');
                                    } else {
                                        _e('变更摘要', 'product-review-system');
                                    }
                                    ?>
                                </h2>
                            </div>
                            <div class="inside">
                                <div class="prs-operation-info">
                                    <p><strong><?php _e('操作类型:', 'product-review-system'); ?></strong>
                                        <span class="prs-operation-type <?php echo esc_attr($operation_type); ?>">
                                            <?php
                                            $operation_types = PRS_Database::get_operation_type_options();
                                            echo esc_html($operation_types[$operation_type] ?? $operation_type);
                                            ?>
                                        </span>
                                    </p>
                                </div>
                                <div class="prs-change-summary">
                                    <?php echo nl2br(esc_html($review->change_summary)); ?>
                                </div>
                            </div>
                        </div>

                        <?php if ($operation_type === 'delete'): ?>
                        <!-- 产品信息（删除操作） -->
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2><?php _e('待删除产品信息', 'product-review-system'); ?></h2>
                            </div>
                            <div class="inside">
                                <?php $this->display_product_info_for_delete($original_data); ?>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- 详细变更对比（修改操作） -->
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2><?php _e('详细变更对比', 'product-review-system'); ?></h2>
                            </div>
                            <div class="inside">
                                <?php $this->display_change_comparison($original_data, $modified_data); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- 审核记录 -->
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2><?php _e('审核记录', 'product-review-system'); ?></h2>
                            </div>
                            <div class="inside">
                                <div class="prs-review-timeline">
                                    <div class="prs-timeline-item">
                                        <div class="prs-timeline-marker prs-timeline-submitted"></div>
                                        <div class="prs-timeline-content">
                                            <h4><?php _e('提交审核', 'product-review-system'); ?></h4>
                                            <p><?php printf(__('由 %s 于 %s 提交', 'product-review-system'),
                                                $submitter ? $submitter->display_name : __('未知用户', 'product-review-system'),
                                                mysql2date('Y-m-d H:i:s', $review->created_at)
                                            ); ?></p>
                                        </div>
                                    </div>

                                    <?php if (isset($review->reviewer_date) && $review->reviewer_date): ?>
                                    <div class="prs-timeline-item">
                                        <div class="prs-timeline-marker prs-timeline-<?php echo esc_attr(isset($review->reviewer_status) ? $review->reviewer_status : 'pending'); ?>"></div>
                                        <div class="prs-timeline-content">
                                            <h4><?php _e('审核员审核', 'product-review-system'); ?></h4>
                                            <p><?php printf(__('由 %s 于 %s %s', 'product-review-system'),
                                                $reviewer ? $reviewer->display_name : __('未知审核员', 'product-review-system'),
                                                mysql2date('Y-m-d H:i:s', $review->reviewer_date),
                                                (isset($review->reviewer_status) && $review->reviewer_status === 'approved') ? __('通过审核', 'product-review-system') : __('拒绝审核', 'product-review-system')
                                            ); ?></p>
                                            <?php if (isset($review->reviewer_notes) && $review->reviewer_notes): ?>
                                                <div class="prs-notes">
                                                    <strong><?php _e('审核员备注：', 'product-review-system'); ?></strong>
                                                    <p><?php echo nl2br(esc_html($review->reviewer_notes)); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (isset($review->admin_date) && $review->admin_date): ?>
                                    <div class="prs-timeline-item">
                                        <div class="prs-timeline-marker prs-timeline-<?php echo esc_attr(isset($review->admin_status) ? $review->admin_status : 'pending'); ?>"></div>
                                        <div class="prs-timeline-content">
                                            <h4><?php _e('管理员审核', 'product-review-system'); ?></h4>
                                            <p><?php printf(__('由 %s 于 %s %s', 'product-review-system'),
                                                $admin ? $admin->display_name : __('未知管理员', 'product-review-system'),
                                                mysql2date('Y-m-d H:i:s', $review->admin_date),
                                                (isset($review->admin_status) && $review->admin_status === 'approved') ? __('通过审核', 'product-review-system') : __('拒绝审核', 'product-review-system')
                                            ); ?></p>
                                            <?php if (isset($review->admin_notes) && $review->admin_notes): ?>
                                                <div class="prs-notes">
                                                    <strong><?php _e('管理员备注：', 'product-review-system'); ?></strong>
                                                    <p><?php echo nl2br(esc_html($review->admin_notes)); ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="postbox-container-1" class="postbox-container">
                        <!-- 审核操作 -->
                        <?php if ($this->can_user_review_this($review, $user_role)): ?>
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2><?php _e('审核操作', 'product-review-system'); ?></h2>
                            </div>
                            <div class="inside">
                                <?php $this->display_review_actions($review, $user_role); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- 操作历史 -->
                        <div class="postbox">
                            <div class="postbox-header">
                                <h2><?php _e('操作', 'product-review-system'); ?></h2>
                            </div>
                            <div class="inside">
                                <p>
                                    <a href="<?php echo esc_url(admin_url('admin.php?page=product-review-system')); ?>" class="button">
                                        <?php _e('返回列表', 'product-review-system'); ?>
                                    </a>
                                </p>
                                <?php if (current_user_can('delete_product_reviews')): ?>
                                <p>
                                    <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin.php?page=product-review-system&action=delete&id=' . $review->id), 'delete_review_' . $review->id)); ?>"
                                       class="button button-link-delete"
                                       onclick="return confirm('<?php _e('确定要删除这个审核记录吗？', 'product-review-system'); ?>')">
                                        <?php _e('删除记录', 'product-review-system'); ?>
                                    </a>
                                </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .prs-change-summary {
            background: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #0073aa;
            white-space: pre-line;
        }
        .prs-new-product-badge {
            display: inline-block;
            background: #ff6b35;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .prs-review-timeline {
            position: relative;
            padding-left: 30px;
        }
        .prs-review-timeline::before {
            content: '';
            position: absolute;
            left: 6px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ddd;
        }
        .prs-timeline-item {
            position: relative;
            margin-bottom: 25px;
            padding-bottom: 20px;
        }
        .prs-timeline-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .prs-timeline-marker {
            position: absolute;
            left: -35px;
            top: 5px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 0 0 2px #ddd;
            z-index: 1;
        }
        .prs-timeline-submitted { background: #0073aa; }
        .prs-timeline-approved { background: #00a32a; }
        .prs-timeline-rejected { background: #d63638; }
        .prs-timeline-pending { background: #ffb900; }
        .prs-timeline-content h4 {
            margin: 0 0 8px 0;
            font-size: 15px;
            font-weight: 600;
            color: #23282d;
        }
        .prs-timeline-content p {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 13px;
        }
        .prs-notes {
            background: #f0f0f1;
            padding: 12px;
            border-radius: 4px;
            margin-top: 10px;
            border-left: 3px solid #0073aa;
        }
        .prs-notes strong {
            display: block;
            margin-bottom: 6px;
            color: #23282d;
            font-size: 13px;
        }
        .prs-notes p {
            margin: 0;
            color: #333;
            font-size: 13px;
            line-height: 1.4;
        }
        </style>
        <?php
    }

    /**
     * 显示变更对比
     */
    private function display_change_comparison($original_data, $modified_data) {
        $field_labels = array(
            'name' => __('产品名称', 'product-review-system'),
            'description' => __('产品描述', 'product-review-system'),
            'short_description' => __('简短描述', 'product-review-system'),
            'sku' => __('SKU', 'product-review-system'),
            'regular_price' => __('常规价格', 'product-review-system'),
            'sale_price' => __('促销价格', 'product-review-system'),
            'stock_quantity' => __('库存数量', 'product-review-system'),
            'stock_status' => __('库存状态', 'product-review-system'),
            'weight' => __('重量', 'product-review-system'),
            'length' => __('长度', 'product-review-system'),
            'width' => __('宽度', 'product-review-system'),
            'height' => __('高度', 'product-review-system'),
            'categories' => __('产品分类', 'product-review-system'),
            'tags' => __('产品标签', 'product-review-system'),
            'brands' => __('产品品牌', 'product-review-system'),
            'status' => __('产品状态', 'product-review-system'),
            'featured' => __('特色产品', 'product-review-system'),
            'catalog_visibility' => __('目录可见性', 'product-review-system'),
            'virtual' => __('虚拟产品', 'product-review-system'),
            'downloadable' => __('可下载产品', 'product-review-system'),
            'downloadable_files' => __('下载文件', 'product-review-system'),
            // 添加图片字段的标签映射
            '_thumbnail_id' => __('产品主图', 'product-review-system'),
            '_product_image_gallery' => __('产品图库', 'product-review-system'),
            '_product_attributes' => __('产品属性', 'product-review-system'),
            '_default_attributes' => __('默认属性', 'product-review-system'),
        );

        echo '<div class="prs-comparison-table">';
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr>';
        echo '<th>' . __('字段', 'product-review-system') . '</th>';
        echo '<th>' . __('原始值', 'product-review-system') . '</th>';
        echo '<th>' . __('修改值', 'product-review-system') . '</th>';
        echo '</tr></thead>';
        echo '<tbody>';

        $has_changes = false;
        foreach ($field_labels as $field => $label) {
            $original_value = isset($original_data[$field]) ? $original_data[$field] : '';
            $modified_value = isset($modified_data[$field]) ? $modified_data[$field] : '';

            // 标准化值进行比较
            $original_normalized = $this->normalize_comparison_value($original_value);
            $modified_normalized = $this->normalize_comparison_value($modified_value);

            // 只显示真正有变化的字段
            // 对于分类、标签和品牌，使用特殊的比较逻辑
            $has_field_change = false;
            if (in_array($field, array('categories', 'tags', 'brands'))) {
                // 特殊处理：如果两个值都为空或都包含相同的有效值，则认为无变更
                $original_clean = $this->clean_taxonomy_array($original_value);
                $modified_clean = $this->clean_taxonomy_array($modified_value);
                $has_field_change = !$this->arrays_equal($original_clean, $modified_clean);
            } else {
                $has_field_change = ($original_normalized !== $modified_normalized);
            }

            if ($has_field_change) {
                $has_changes = true;

                // 特殊处理描述字段
                if (in_array($field, array('description', 'short_description'))) {
                    $original_display = $this->format_description_value($original_value);
                    $modified_display = $this->format_description_value($modified_value);
                } elseif (in_array($field, array('categories', 'tags', 'brands'))) {
                    // 特殊处理分类、标签和品牌字段
                    $original_display = $this->format_taxonomy_value($original_value, $field);
                    $modified_display = $this->format_taxonomy_value($modified_value, $field);
                } elseif ($field === 'downloadable_files') {
                    // 特殊处理下载文件字段
                    $original_display = $this->format_downloadable_files_display($original_value);
                    $modified_display = $this->format_downloadable_files_display($modified_value);
                } elseif ($field === '_thumbnail_id') {
                    // 特殊处理产品主图字段
                    $original_display = $this->format_image_display($original_value);
                    $modified_display = $this->format_image_display($modified_value);
                } elseif ($field === '_product_image_gallery') {
                    // 特殊处理产品图库字段
                    $original_display = $this->format_gallery_display($original_value);
                    $modified_display = $this->format_gallery_display($modified_value);
                } else {
                    // 格式化显示值
                    $original_display = $this->format_display_value($original_value);
                    $modified_display = $this->format_display_value($modified_value);
                }

                echo '<tr>';
                echo '<td><strong>' . esc_html($label) . '</strong></td>';
                echo '<td class="prs-original-value">' . wp_kses_post($original_display) . '</td>';
                echo '<td class="prs-modified-value">' . wp_kses_post($modified_display) . '</td>';
                echo '</tr>';
            }
        }

        if (!$has_changes) {
            echo '<tr><td colspan="3" style="text-align: center; color: #666; font-style: italic;">';
            echo __('没有检测到实质性变更', 'product-review-system');
            echo '</td></tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';

        echo '<style>
        .prs-comparison-table {
            margin: 15px 0;
        }
        .prs-comparison-table table {
            border-collapse: collapse;
        }
        .prs-comparison-table th,
        .prs-comparison-table td {
            padding: 12px;
            border: 1px solid #ddd;
            vertical-align: top;
            word-wrap: break-word;
        }
        .prs-comparison-table th {
            background: #f9f9f9;
            font-weight: bold;
        }
        .prs-original-value {
            background: #ffebee;
            color: #c62828;
            position: relative;
        }
        .prs-original-value::before {
            content: "−";
            color: #d32f2f;
            font-weight: bold;
            margin-right: 5px;
        }
        .prs-modified-value {
            background: #e8f5e8;
            color: #2e7d32;
            position: relative;
        }
        .prs-modified-value::before {
            content: "+";
            color: #388e3c;
            font-weight: bold;
            margin-right: 5px;
        }
        .prs-value-truncated {
            max-height: 100px;
            overflow: hidden;
            position: relative;
        }
        .prs-value-truncated::after {
            content: "...";
            position: absolute;
            bottom: 0;
            right: 0;
            background: inherit;
            padding-left: 10px;
        }

        /* 图片预览样式 */
        .image-preview-container img:hover {
            border-color: #0073aa !important;
            transform: scale(1.02);
        }

        .gallery-preview-container img:hover {
            border-color: #0073aa !important;
            transform: scale(1.05) !important;
        }

        .gallery-preview-container details summary {
            outline: none;
        }

        .gallery-preview-container details summary:hover {
            color: #005a87;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .image-preview-container {
                flex-direction: column !important;
                align-items: flex-start !important;
            }

            .image-preview-container img {
                margin-bottom: 8px !important;
                margin-right: 0 !important;
            }

            .gallery-preview-container > div {
                gap: 4px !important;
            }

            .gallery-preview-container img {
                width: 50px !important;
                height: 50px !important;
            }
        }
        </style>';
    }

    /**
     * 标准化比较值
     */
    private function normalize_comparison_value($value) {
        // 处理null和空字符串
        if (is_null($value) || $value === '') {
            return '';
        }

        // 处理数组
        if (is_array($value)) {
            // 特殊处理下载文件数组
            if ($this->is_downloadable_files_array($value)) {
                return $this->normalize_downloadable_files($value);
            }

            // 移除空值
            $value = array_filter($value, function($v) {
                return $v !== '' && $v !== null;
            });

            sort($value);
            return $value;
        }

        // 处理数字字符串
        if (is_numeric($value)) {
            return (string) $value;
        }

        // 处理布尔值
        if (is_bool($value)) {
            return $value ? '1' : '0';
        }

        // 处理字符串，去除首尾空格和多余的空白字符
        if (is_string($value)) {
            return trim(preg_replace('/\s+/', ' ', $value));
        }

        return $value;
    }



    /**
     * 格式化显示值
     */
    private function format_display_value($value) {
        if (is_null($value) || $value === '') {
            return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
        }

        if (is_array($value)) {
            if (empty($value)) {
                return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
            }
            return esc_html(implode(', ', $value));
        }

        if (is_bool($value)) {
            return $value ? __('是', 'product-review-system') : __('否', 'product-review-system');
        }

        $value = (string) $value;

        // 特殊值的中文化处理（但不要对长文本进行中文化）
        if (mb_strlen($value) < 50) { // 只对短值进行中文化处理
            $value = $this->localize_value($value);
        }

        // 如果内容太长，截断显示但保留更多内容
        if (mb_strlen($value) > 300) {
            return '<div class="prs-value-truncated" title="' . esc_attr($value) . '">' .
                   esc_html(mb_substr($value, 0, 300)) . '...' .
                   '</div>';
        }

        // 保留换行符
        return nl2br(esc_html($value));
    }

    /**
     * 本地化值显示
     */
    private function localize_value($value) {
        // 库存状态中文化
        $stock_status_map = array(
            'instock' => __('有库存', 'product-review-system'),
            'outofstock' => __('缺货', 'product-review-system'),
            'onbackorder' => __('延期交货', 'product-review-system'),
        );

        // 产品状态中文化
        $post_status_map = array(
            'publish' => __('已发布', 'product-review-system'),
            'draft' => __('草稿', 'product-review-system'),
            'private' => __('私密', 'product-review-system'),
            'pending' => __('待审核', 'product-review-system'),
            'trash' => __('回收站', 'product-review-system'),
        );

        // 目录可见性中文化
        $visibility_map = array(
            'visible' => __('可见', 'product-review-system'),
            'catalog' => __('仅目录', 'product-review-system'),
            'search' => __('仅搜索', 'product-review-system'),
            'hidden' => __('隐藏', 'product-review-system'),
        );

        // 检查并转换值
        if (isset($stock_status_map[$value])) {
            return $stock_status_map[$value];
        }

        if (isset($post_status_map[$value])) {
            return $post_status_map[$value];
        }

        if (isset($visibility_map[$value])) {
            return $visibility_map[$value];
        }

        return $value;
    }

    /**
     * 专门格式化描述字段的显示值
     */
    private function format_description_value($value) {
        if (is_null($value) || $value === '') {
            return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
        }

        if (is_array($value)) {
            if (empty($value)) {
                return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
            }
            return esc_html(implode(', ', $value));
        }

        $value = (string) $value;

        // 对于描述字段，不进行中文化处理，直接显示内容

        // 如果内容太长，截断显示但保留更多内容
        if (mb_strlen($value) > 500) {
            return '<div class="prs-description-truncated" title="' . esc_attr($value) . '">' .
                   nl2br(esc_html(mb_substr($value, 0, 500))) . '...<br>' .
                   '<small style="color: #666;">(' . sprintf(__('共%d个字符，点击查看完整内容', 'product-review-system'), mb_strlen($value)) . ')</small>' .
                   '</div>';
        }

        // 保留换行符，完整显示内容
        return '<div class="prs-description-content">' . nl2br(esc_html($value)) . '</div>';
    }

    /**
     * 格式化分类法值（分类、标签、品牌）
     */
    private function format_taxonomy_value($value, $field) {
        if (is_null($value) || $value === '' || (is_array($value) && empty($value))) {
            return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
        }

        if (!is_array($value)) {
            return '<em style="color: #999;">' . __('(无效数据)', 'product-review-system') . '</em>';
        }

        $names = array();
        $taxonomy = '';

        // 确定分类法名称
        switch ($field) {
            case 'categories':
                $taxonomy = 'product_cat';
                break;
            case 'tags':
                $taxonomy = 'product_tag';
                break;
            case 'brands':
                $taxonomy = 'product_brand';
                break;
            default:
                return esc_html(implode(', ', $value));
        }

        // 转换ID为名称
        foreach ($value as $id) {
            if (empty($id)) continue;

            $term = get_term($id, $taxonomy);
            if ($term && !is_wp_error($term)) {
                $names[] = $term->name;
            } else {
                // 如果无法获取term名称，显示ID
                $names[] = sprintf(__('ID: %s', 'product-review-system'), $id);
            }
        }

        if (empty($names)) {
            return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
        }

        return esc_html(implode(', ', $names));
    }

    /**
     * 显示审核操作结果消息
     */
    private function display_review_messages() {
        if (!isset($_GET['message'])) {
            return;
        }

        $message = sanitize_text_field($_GET['message']);
        $messages = array(
            'reviewer_approved' => array(
                'type' => 'success',
                'text' => __('审核通过，已提交给管理员进行最终审核。', 'product-review-system')
            ),
            'reviewer_rejected' => array(
                'type' => 'success',
                'text' => __('审核已拒绝，已通知提交者。', 'product-review-system')
            ),
            'admin_approved' => array(
                'type' => 'success',
                'text' => __('审核通过，产品修改已应用。', 'product-review-system')
            ),
            'admin_rejected' => array(
                'type' => 'success',
                'text' => __('审核已拒绝，已通知提交者。', 'product-review-system')
            ),
            'admin_error' => array(
                'type' => 'error',
                'text' => __('应用产品修改失败，请检查产品数据。', 'product-review-system')
            ),
            'error' => array(
                'type' => 'error',
                'text' => __('操作失败，请重试。', 'product-review-system')
            )
        );

        if (isset($messages[$message])) {
            $msg = $messages[$message];
            echo '<div class="notice notice-' . esc_attr($msg['type']) . ' is-dismissible">';
            echo '<p>' . esc_html($msg['text']) . '</p>';
            echo '</div>';
        }
    }

    /**
     * 检查用户是否可以审核此记录
     */
    private function can_user_review_this($review, $user_role) {
        if ($user_role === 'reviewer' && $review->status === 'pending_review') {
            return true;
        }
        if ($user_role === 'admin' && $review->status === 'pending_admin') {
            return true;
        }
        return false;
    }

    /**
     * 显示审核操作
     */
    private function display_review_actions($review, $user_role) {
        if ($user_role === 'reviewer' && $review->status === 'pending_review') {
            $current_url = admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id);
            ?>
            <form method="post" action="<?php echo esc_url($current_url); ?>" class="prs-review-form" id="reviewer-form">
                <?php wp_nonce_field('prs_review_action', 'prs_nonce'); ?>
                <input type="hidden" name="prs_action" id="prs_action" value="">
                <div class="prs-form-group">
                    <label for="reviewer_notes"><?php _e('审核备注', 'product-review-system'); ?></label>
                    <textarea name="reviewer_notes" id="reviewer_notes" rows="4" class="widefat"></textarea>
                </div>
                <div class="prs-form-actions">
                    <button type="button" onclick="submitReviewForm('reviewer_approve')" class="button button-primary">
                        <?php _e('通过审核', 'product-review-system'); ?>
                    </button>
                    <button type="button" onclick="submitReviewForm('reviewer_reject')" class="button button-secondary">
                        <?php _e('拒绝审核', 'product-review-system'); ?>
                    </button>
                </div>

            </form>

            <script>
            function submitReviewForm(action) {
                if (!action) {
                    alert('错误：缺少操作参数');
                    return false;
                }

                var actionField = document.getElementById('prs_action');
                var form = document.getElementById('reviewer-form');
                var submitButton = form.querySelector('button[onclick*="' + action + '"]');

                if (!actionField) {
                    alert('错误：找不到action字段');
                    return false;
                }

                if (!form) {
                    alert('错误：找不到表单');
                    return false;
                }

                // 确认操作
                var confirmMessage = action === 'reviewer_approve' ? '确定要通过这个审核吗？' : '确定要拒绝这个审核吗？';
                if (!confirm(confirmMessage)) {
                    return false;
                }

                // 显示加载状态
                if (submitButton) {
                    var originalText = submitButton.textContent;
                    submitButton.disabled = true;
                    submitButton.textContent = '处理中...';
                    submitButton.style.opacity = '0.6';
                }

                // 显示处理提示
                var notice = document.createElement('div');
                notice.className = 'notice notice-info';
                notice.innerHTML = '<p>正在处理审核请求，请稍候...</p>';
                notice.style.marginTop = '10px';
                form.parentNode.insertBefore(notice, form.nextSibling);

                // 设置action值
                actionField.value = action;

                // 添加时间戳防止缓存
                var timestampField = document.createElement('input');
                timestampField.type = 'hidden';
                timestampField.name = 'timestamp';
                timestampField.value = Date.now();
                form.appendChild(timestampField);

                // 提交表单
                form.submit();

                return false;
            }
            </script>


            <?php
        } elseif ($user_role === 'admin' && $review->status === 'pending_admin') {
            $current_url = admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id);
            ?>
            <form method="post" action="<?php echo esc_url($current_url); ?>" class="prs-review-form" id="admin-form">
                <?php wp_nonce_field('prs_review_action', 'prs_nonce'); ?>
                <input type="hidden" name="prs_action" id="admin_prs_action" value="">
                <div class="prs-form-group">
                    <label for="admin_notes"><?php _e('管理员备注', 'product-review-system'); ?></label>
                    <textarea name="admin_notes" id="admin_notes" rows="4" class="widefat"></textarea>
                </div>
                <div class="prs-form-actions">
                    <button type="button" onclick="submitAdminForm('admin_approve')" class="button button-primary">
                        <?php _e('最终通过', 'product-review-system'); ?>
                    </button>
                    <button type="button" onclick="submitAdminForm('admin_reject')" class="button button-secondary">
                        <?php _e('最终拒绝', 'product-review-system'); ?>
                    </button>
                </div>

            </form>

            <script>
            function submitAdminForm(action) {
                if (!action) {
                    alert('错误：缺少操作参数');
                    return false;
                }

                var actionField = document.getElementById('admin_prs_action');
                var form = document.getElementById('admin-form');
                var submitButton = form.querySelector('button[onclick*="' + action + '"]');

                if (!actionField) {
                    alert('错误：找不到action字段');
                    return false;
                }

                if (!form) {
                    alert('错误：找不到表单');
                    return false;
                }

                // 确认操作
                var confirmMessage = action === 'admin_approve' ? '确定要最终通过这个审核吗？产品修改将被应用。' : '确定要最终拒绝这个审核吗？';
                if (!confirm(confirmMessage)) {
                    return false;
                }

                // 显示加载状态
                if (submitButton) {
                    var originalText = submitButton.textContent;
                    submitButton.disabled = true;
                    submitButton.textContent = '处理中...';
                    submitButton.style.opacity = '0.6';
                }

                // 显示处理提示
                var notice = document.createElement('div');
                notice.className = 'notice notice-info';
                notice.innerHTML = '<p>正在处理审核请求，请稍候...</p>';
                notice.style.marginTop = '10px';
                form.parentNode.insertBefore(notice, form.nextSibling);

                // 设置action值
                actionField.value = action;

                // 添加时间戳防止缓存
                var timestampField = document.createElement('input');
                timestampField.type = 'hidden';
                timestampField.name = 'timestamp';
                timestampField.value = Date.now();
                form.appendChild(timestampField);

                // 提交表单
                form.submit();

                return false;
            }
            </script>


            <?php
        }
    }

    /**
     * 处理审核表单提交
     */
    private function handle_review_form_submission($id, $action) {
        // 清理所有输出缓冲
        while (ob_get_level()) {
            ob_end_clean();
        }

        // 开始新的输出缓冲
        ob_start();

        try {
            // 安全的调试日志记录
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS: Starting form submission handling - ID: ' . $id . ', Action: ' . $action);
            }

            // 验证nonce
            if (!wp_verify_nonce($_POST['prs_nonce'], 'prs_review_action')) {
                PRS_Security::log_security_event('form_nonce_verification_failed', array('action' => $action));
                wp_die(__('安全验证失败', 'product-review-system'));
            }

            // 验证action参数
            if (empty($action)) {
                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS: Missing action parameter');
                }
                wp_die(__('缺少操作参数', 'product-review-system'));
            }

            // 验证审核记录
            $review = PRS_Database::get_review($id);
            if (!$review) {
                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS: Review record not found - ID: ' . $id);
                }
                wp_die(__('审核记录不存在。', 'product-review-system'));
            }

            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS: Review found - Status: ' . $review->status);
            }
            $redirect_url = admin_url('admin.php?page=product-review-system&action=view&id=' . $id);

        switch ($action) {

            case 'reviewer_approve':
                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS: Processing reviewer_approve action');
                }

                if (!PRS_Permissions::can_user_review()) {
                    PRS_Security::log_security_event('unauthorized_reviewer_action', array(
                        'action' => 'reviewer_approve',
                        'review_id' => $id
                    ));
                    wp_die(__('您没有权限执行此操作。', 'product-review-system'));
                }

                if ($review->status !== 'pending_review') {
                    if (PRS_Security::is_debug_enabled()) {
                        error_log('PRS: Invalid review status for reviewer approval: ' . $review->status);
                    }
                    wp_die(__('该审核记录状态不正确，无法进行审核。', 'product-review-system'));
                }

                $update_data = array(
                    'reviewer_id' => get_current_user_id(),
                    'reviewer_status' => 'approved',
                    'status' => 'pending_admin',
                    'reviewer_date' => current_time('mysql'),
                    'reviewer_notes' => sanitize_textarea_field($_POST['reviewer_notes'])
                );

                if (PRS_Security::is_debug_enabled()) {
                    error_log('PRS: Updating review with sanitized data');
                }
                $result = PRS_Database::update_review($id, $update_data);

                if ($result) {
                    // 暂时简化处理，不发送通知
                    $redirect_url = add_query_arg('message', 'reviewer_approved', $redirect_url);
                } else {
                    $redirect_url = add_query_arg('message', 'error', $redirect_url);
                }
                break;

            case 'reviewer_reject':
                if (!PRS_Permissions::can_user_review()) {
                    wp_die(__('您没有权限执行此操作。', 'product-review-system'));
                }

                $update_data = array(
                    'reviewer_id' => get_current_user_id(),
                    'reviewer_status' => 'rejected',
                    'status' => 'rejected',
                    'reviewer_date' => current_time('mysql'),
                    'reviewer_notes' => sanitize_textarea_field($_POST['reviewer_notes'])
                );

                $result = PRS_Database::update_review($id, $update_data);
                if ($result) {
                    // 尝试异步发送通知，如果失败则同步发送
                    if (!wp_schedule_single_event(time() + 5, 'prs_send_submitter_notification', array($id, 'rejected'))) {
                        try {
                            PRS_Notifications::notify_submitter($id, 'rejected');
                        } catch (Exception $e) {
                            if (PRS_Security::is_debug_enabled()) {
                                error_log('PRS: Failed to send submitter notification: ' . $e->getMessage());
                            }
                        }
                    }
                    $success_message = __('审核已拒绝，已通知提交者。', 'product-review-system');
                    $redirect_url = add_query_arg('message', 'reviewer_rejected', $redirect_url);
                } else {
                    $redirect_url = add_query_arg('message', 'error', $redirect_url);
                }
                break;

            case 'admin_approve':
                if (!PRS_Permissions::can_user_admin_review()) {
                    wp_die(__('您没有权限执行此操作。', 'product-review-system'));
                }

                // 应用产品修改
                $success = $this->apply_product_changes($review);

                if ($success) {
                    $update_data = array(
                        'admin_id' => get_current_user_id(),
                        'admin_status' => 'approved',
                        'status' => 'approved',
                        'admin_date' => current_time('mysql'),
                        'admin_notes' => sanitize_textarea_field($_POST['admin_notes'])
                    );

                    $db_result = PRS_Database::update_review($id, $update_data);

                    // 尝试异步发送通知，如果失败则同步发送
                    if (!wp_schedule_single_event(time() + 5, 'prs_send_submitter_notification', array($id, 'approved'))) {
                        try {
                            PRS_Notifications::notify_submitter($id, 'approved');
                        } catch (Exception $e) {
                            if (PRS_Security::is_debug_enabled()) {
                                error_log('PRS: Failed to send submitter notification: ' . $e->getMessage());
                            }
                        }
                    }

                    $redirect_url = add_query_arg('message', 'admin_approved', $redirect_url);
                } else {
                    $redirect_url = add_query_arg('message', 'admin_error', $redirect_url);
                }
                break;

            case 'admin_reject':
                if (!PRS_Permissions::can_user_admin_review()) {
                    wp_die(__('您没有权限执行此操作。', 'product-review-system'));
                }

                $update_data = array(
                    'admin_id' => get_current_user_id(),
                    'admin_status' => 'rejected',
                    'status' => 'rejected',
                    'admin_date' => current_time('mysql'),
                    'admin_notes' => sanitize_textarea_field($_POST['admin_notes'])
                );

                $result = PRS_Database::update_review($id, $update_data);
                if ($result) {
                    // 尝试异步发送通知，如果失败则同步发送
                    if (!wp_schedule_single_event(time() + 5, 'prs_send_submitter_notification', array($id, 'rejected'))) {
                        try {
                            PRS_Notifications::notify_submitter($id, 'rejected');
                        } catch (Exception $e) {
                            if (PRS_Security::is_debug_enabled()) {
                                error_log('PRS: Failed to send submitter notification: ' . $e->getMessage());
                            }
                        }
                    }
                    $redirect_url = add_query_arg('message', 'admin_rejected', $redirect_url);
                } else {
                    $redirect_url = add_query_arg('message', 'error', $redirect_url);
                }
                break;
        }

        } catch (Exception $e) {
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS: Exception in form submission handling: ' . $e->getMessage());
            }
            PRS_Security::log_security_event('form_submission_exception', array(
                'message' => $e->getMessage(),
                'action' => $action,
                'review_id' => $id
            ));
            $redirect_url = add_query_arg('message', 'error', $redirect_url);
        }

        // 清理输出缓冲
        while (ob_get_level()) {
            ob_end_clean();
        }

        // 安全的重定向日志记录
        if (PRS_Security::is_debug_enabled()) {
            error_log('PRS: Redirecting to: ' . $redirect_url);
        }

        // 检查是否已经有输出
        if (headers_sent($file, $line)) {
            if (PRS_Security::is_debug_enabled()) {
                error_log('PRS: Headers already sent at ' . $file . ':' . $line . ', using JavaScript redirect');
            }

            // 输出JavaScript重定向页面
            ?>
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>处理中...</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .loading { font-size: 18px; color: #666; }
                </style>
            </head>
            <body>
                <div class="loading">
                    <p>审核处理完成，正在跳转...</p>
                    <p>如果页面没有自动跳转，请<a href="<?php echo esc_url($redirect_url); ?>">点击这里</a></p>
                </div>
                <script type="text/javascript">
                    setTimeout(function() {
                        window.location.href = "<?php echo esc_js($redirect_url); ?>";
                    }, 1000);
                </script>
            </body>
            </html>
            <?php
            exit;
        }

        // 使用WordPress重定向
        wp_redirect($redirect_url);
        exit;
    }

    /**
     * 应用产品修改
     */
    private function apply_product_changes($review) {
        $modified_data = json_decode($review->modified_data, true);
        if (!$modified_data) {
            return false;
        }

        $product_id = $review->product_id;
        $product = wc_get_product($product_id);
        if (!$product) {
            return false;
        }

        // 使用产品处理器的方法来应用修改
        if (class_exists('PRS_Product_Handler')) {
            $handler = new PRS_Product_Handler();
            return $handler->apply_approved_changes($product_id, $modified_data);
        }

        return false;
    }

    /**
     * 待审核列表页面
     */
    public function pending_reviews_page() {
        $user_role = PRS_Permissions::get_user_review_role();
        $per_page = get_user_option('prs_reviews_per_page') ?: 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;

        $reviews = PRS_Database::get_pending_reviews($user_role, $per_page, $offset);
        $total_reviews = count(PRS_Database::get_pending_reviews($user_role, 999999));
        $total_pages = ceil($total_reviews / $per_page);

        ?>
        <div class="wrap">
            <h1><?php _e('待审核列表', 'product-review-system'); ?></h1>

            <?php if (empty($reviews)): ?>
                <div class="notice notice-info">
                    <p><?php _e('当前没有待审核的记录。', 'product-review-system'); ?></p>
                </div>
            <?php else: ?>
                <form method="post" id="prs-pending-form">
                    <?php wp_nonce_field('prs_bulk_action', 'prs_bulk_nonce'); ?>

                    <div class="tablenav top">
                        <div class="alignleft actions bulkactions">
                            <select name="action">
                                <option value="-1"><?php _e('批量操作', 'product-review-system'); ?></option>
                                <?php if (current_user_can('delete_product_reviews')): ?>
                                    <option value="delete"><?php _e('删除', 'product-review-system'); ?></option>
                                <?php endif; ?>
                            </select>
                            <input type="submit" class="button action prs-bulk-action-btn" value="<?php _e('应用', 'product-review-system'); ?>" disabled>
                        </div>

                        <?php if ($total_pages > 1): ?>
                        <div class="tablenav-pages">
                            <span class="displaying-num"><?php printf(__('%d 项', 'product-review-system'), $total_reviews); ?></span>
                            <?php
                            echo paginate_links(array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => __('&laquo;'),
                                'next_text' => __('&raquo;'),
                                'total' => $total_pages,
                                'current' => $current_page
                            ));
                            ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <td class="manage-column column-cb check-column">
                                    <input type="checkbox" class="prs-select-all">
                                </td>
                                <th><?php _e('ID', 'product-review-system'); ?></th>
                                <th><?php _e('产品', 'product-review-system'); ?></th>
                                <th><?php _e('操作类型', 'product-review-system'); ?></th>
                                <th><?php _e('提交者', 'product-review-system'); ?></th>
                                <th><?php _e('状态', 'product-review-system'); ?></th>
                                <th><?php _e('提交时间', 'product-review-system'); ?></th>
                                <th><?php _e('操作', 'product-review-system'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reviews as $review): ?>
                                <?php
                                $product = wc_get_product($review->product_id);
                                $submitter = isset($review->submitter_id) && $review->submitter_id ? get_user_by('id', $review->submitter_id) : null;
                                $status_options = PRS_Database::get_status_options();
                                ?>
                                <tr>
                                    <th class="check-column">
                                        <input type="checkbox" name="review_ids[]" value="<?php echo esc_attr($review->id); ?>" class="prs-select-item">
                                    </th>
                                    <td><?php echo esc_html($review->id); ?></td>
                                    <td>
                                        <?php if ($product): ?>
                                            <strong>
                                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $review->product_id . '&action=edit')); ?>">
                                                    <?php echo esc_html($product->get_name()); ?>
                                                </a>
                                            </strong>
                                            <div class="row-actions">
                                                <span class="view">
                                                    <a href="<?php echo esc_url(admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id)); ?>">
                                                        <?php _e('查看详情', 'product-review-system'); ?>
                                                    </a>
                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <?php _e('产品不存在', 'product-review-system'); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $operation_type = isset($review->operation_type) ? $review->operation_type : 'modify';
                                        $operation_types = PRS_Database::get_operation_type_options();
                                        ?>
                                        <span class="prs-operation-type <?php echo esc_attr($operation_type); ?>">
                                            <?php echo esc_html($operation_types[$operation_type] ?? $operation_type); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($submitter): ?>
                                            <a href="<?php echo esc_url(admin_url('user-edit.php?user_id=' . $review->submitter_id)); ?>">
                                                <?php echo esc_html($submitter->display_name); ?>
                                            </a>
                                        <?php else: ?>
                                            <?php _e('未知用户', 'product-review-system'); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="prs-status prs-status-<?php echo esc_attr($review->status); ?>">
                                            <?php echo isset($status_options[$review->status]) ? esc_html($status_options[$review->status]) : esc_html($review->status); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo esc_html(mysql2date('Y-m-d H:i', $review->created_at)); ?>
                                        <br>
                                        <small><?php echo esc_html(human_time_diff(strtotime($review->created_at), current_time('timestamp'))); ?> <?php _e('前', 'product-review-system'); ?></small>
                                    </td>
                                    <td>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id)); ?>" class="button button-small">
                                            <?php _e('查看', 'product-review-system'); ?>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <div class="tablenav bottom">
                        <?php if ($total_pages > 1): ?>
                        <div class="tablenav-pages">
                            <?php
                            echo paginate_links(array(
                                'base' => add_query_arg('paged', '%#%'),
                                'format' => '',
                                'prev_text' => __('&laquo;'),
                                'next_text' => __('&raquo;'),
                                'total' => $total_pages,
                                'current' => $current_page
                            ));
                            ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </form>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * 审核历史页面
     */
    public function review_history_page() {
        $per_page = get_user_option('prs_reviews_per_page') ?: 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;
        $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';

        $reviews = PRS_Database::get_review_history($per_page, $offset, $status_filter);
        $total_reviews = count(PRS_Database::get_review_history(999999, 0, $status_filter));
        $total_pages = ceil($total_reviews / $per_page);

        $status_options = PRS_Database::get_status_options();

        ?>
        <div class="wrap">
            <h1><?php _e('审核历史', 'product-review-system'); ?></h1>

            <!-- 筛选器 -->
            <div class="tablenav top">
                <div class="alignleft actions">
                    <form method="get">
                        <input type="hidden" name="page" value="prs-review-history">
                        <select name="status">
                            <option value=""><?php _e('所有状态', 'product-review-system'); ?></option>
                            <?php foreach ($status_options as $status => $label): ?>
                                <option value="<?php echo esc_attr($status); ?>" <?php selected($status_filter, $status); ?>>
                                    <?php echo esc_html($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <input type="submit" class="button" value="<?php _e('筛选', 'product-review-system'); ?>">
                    </form>
                </div>
            </div>

            <?php if (empty($reviews)): ?>
                <div class="notice notice-info">
                    <p><?php _e('没有找到审核记录。', 'product-review-system'); ?></p>
                </div>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('ID', 'product-review-system'); ?></th>
                            <th><?php _e('产品', 'product-review-system'); ?></th>
                            <th><?php _e('提交者', 'product-review-system'); ?></th>
                            <th><?php _e('审核员', 'product-review-system'); ?></th>
                            <th><?php _e('管理员', 'product-review-system'); ?></th>
                            <th><?php _e('状态', 'product-review-system'); ?></th>
                            <th><?php _e('完成时间', 'product-review-system'); ?></th>
                            <th><?php _e('操作', 'product-review-system'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($reviews as $review): ?>
                            <?php
                            $product = wc_get_product($review->product_id);
                            $submitter = isset($review->submitter_id) && $review->submitter_id ? get_user_by('id', $review->submitter_id) : null;
                            $reviewer = isset($review->reviewer_id) && $review->reviewer_id ? get_user_by('id', $review->reviewer_id) : null;
                            $admin = isset($review->admin_id) && $review->admin_id ? get_user_by('id', $review->admin_id) : null;
                            ?>
                            <tr>
                                <td><?php echo esc_html($review->id); ?></td>
                                <td>
                                    <?php if ($product): ?>
                                        <a href="<?php echo esc_url(admin_url('post.php?post=' . $review->product_id . '&action=edit')); ?>">
                                            <?php echo esc_html($product->get_name()); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php _e('产品不存在', 'product-review-system'); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($submitter): ?>
                                        <a href="<?php echo esc_url(admin_url('user-edit.php?user_id=' . $review->submitter_id)); ?>">
                                            <?php echo esc_html($submitter->display_name); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php _e('未知用户', 'product-review-system'); ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($reviewer): ?>
                                        <a href="<?php echo esc_url(admin_url('user-edit.php?user_id=' . $review->reviewer_id)); ?>">
                                            <?php echo esc_html($reviewer->display_name); ?>
                                        </a>
                                        <br>
                                        <small class="prs-status prs-status-<?php echo esc_attr(isset($review->reviewer_status) ? $review->reviewer_status : 'pending'); ?>">
                                            <?php echo esc_html(isset($review->reviewer_status) ? $review->reviewer_status : 'pending'); ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="description"><?php _e('未审核', 'product-review-system'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($admin): ?>
                                        <a href="<?php echo esc_url(admin_url('user-edit.php?user_id=' . $review->admin_id)); ?>">
                                            <?php echo esc_html($admin->display_name); ?>
                                        </a>
                                        <br>
                                        <small class="prs-status prs-status-<?php echo esc_attr(isset($review->admin_status) ? $review->admin_status : 'pending'); ?>">
                                            <?php echo esc_html(isset($review->admin_status) ? $review->admin_status : 'pending'); ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="description"><?php _e('未审核', 'product-review-system'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="prs-status prs-status-<?php echo esc_attr($review->status); ?>">
                                        <?php echo isset($status_options[$review->status]) ? esc_html($status_options[$review->status]) : esc_html($review->status); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $admin_date = isset($review->admin_date) ? $review->admin_date : null;
                                    $reviewer_date = isset($review->reviewer_date) ? $review->reviewer_date : null;
                                    $complete_time = $admin_date ?: $reviewer_date;
                                    if ($complete_time):
                                    ?>
                                        <?php echo esc_html(mysql2date('Y-m-d H:i', $complete_time)); ?>
                                        <br>
                                        <small><?php echo esc_html(human_time_diff(strtotime($complete_time), current_time('timestamp'))); ?> <?php _e('前', 'product-review-system'); ?></small>
                                    <?php else: ?>
                                        <span class="description"><?php _e('进行中', 'product-review-system'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo esc_url(admin_url('admin.php?page=product-review-system&action=view&id=' . $review->id)); ?>" class="button button-small">
                                        <?php _e('查看', 'product-review-system'); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <?php if ($total_pages > 1): ?>
                <div class="tablenav bottom">
                    <div class="tablenav-pages">
                        <span class="displaying-num"><?php printf(__('%d 项', 'product-review-system'), $total_reviews); ?></span>
                        <?php
                        echo paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $current_page
                        ));
                        ?>
                    </div>
                </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * 设置页面
     */
    public function settings_page() {
        if ($_POST && isset($_POST['prs_settings_nonce'])) {
            if (wp_verify_nonce($_POST['prs_settings_nonce'], 'prs_settings') && current_user_can('manage_product_review_settings')) {
                // 处理设置保存
                $this->save_settings();
            }
        }

        $settings = get_option('prs_settings', array());
        $default_settings = array(
            'email_notifications' => true,
            'auto_delete_old_reviews' => false,
            'delete_after_days' => 90,
            'require_reviewer_notes' => false,
            'require_admin_notes' => false,
        );

        $settings = wp_parse_args($settings, $default_settings);

        ?>
        <div class="wrap">
            <h1><?php _e('审核设置', 'product-review-system'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('prs_settings', 'prs_settings_nonce'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('邮件通知', 'product-review-system'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="email_notifications" value="1" <?php checked($settings['email_notifications']); ?>>
                                <?php _e('启用邮件通知', 'product-review-system'); ?>
                            </label>
                            <p class="description"><?php _e('审核状态变更时发送邮件通知', 'product-review-system'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('自动清理', 'product-review-system'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_delete_old_reviews" value="1" <?php checked($settings['auto_delete_old_reviews']); ?>>
                                <?php _e('自动删除旧的审核记录', 'product-review-system'); ?>
                            </label>
                            <p class="description"><?php _e('定期清理已完成的审核记录', 'product-review-system'); ?></p>

                            <label>
                                <?php _e('保留天数：', 'product-review-system'); ?>
                                <input type="number" name="delete_after_days" value="<?php echo esc_attr($settings['delete_after_days']); ?>" min="1" max="365" class="small-text">
                                <?php _e('天', 'product-review-system'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('必填备注', 'product-review-system'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="require_reviewer_notes" value="1" <?php checked($settings['require_reviewer_notes']); ?>>
                                <?php _e('审核员必须填写备注', 'product-review-system'); ?>
                            </label>
                            <br>
                            <label>
                                <input type="checkbox" name="require_admin_notes" value="1" <?php checked($settings['require_admin_notes']); ?>>
                                <?php _e('管理员必须填写备注', 'product-review-system'); ?>
                            </label>
                        </td>
                    </tr>
                </table>

                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }

    /**
     * 保存设置
     */
    private function save_settings() {
        $settings = array(
            'email_notifications' => isset($_POST['email_notifications']),
            'auto_delete_old_reviews' => isset($_POST['auto_delete_old_reviews']),
            'delete_after_days' => intval($_POST['delete_after_days']),
            'require_reviewer_notes' => isset($_POST['require_reviewer_notes']),
            'require_admin_notes' => isset($_POST['require_admin_notes']),
        );

        update_option('prs_settings', $settings);

        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>' . __('设置已保存！', 'product-review-system') . '</p></div>';
        });
    }

    /*
     * 调试工具页面已移除 - 安全考虑
     * 如需调试功能，请在开发环境中临时启用
     */

    // 调试状态显示方法已移除

    // 调试操作处理方法已移除

    // 示例数据创建方法已移除

    // 数据清理方法已移除

    /**
     * 格式化下载文件显示
     */
    private function format_downloadable_files_display($files) {
        if (is_null($files) || $files === '' || (is_array($files) && empty($files))) {
            return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
        }

        if (!is_array($files)) {
            return '<em style="color: #999;">' . __('(无效数据)', 'product-review-system') . '</em>';
        }

        $file_list = array();
        foreach ($files as $file_id => $file_data) {
            if (is_array($file_data) && isset($file_data['name'])) {
                $file_name = esc_html($file_data['name']);
                $file_url = isset($file_data['file']) ? esc_url($file_data['file']) : '';

                if (!empty($file_url)) {
                    // 创建可点击的链接
                    $file_list[] = '<a href="' . $file_url . '" target="_blank" title="' . __('点击下载', 'product-review-system') . '">' . $file_name . '</a>';
                } else {
                    $file_list[] = $file_name;
                }
            }
        }

        if (empty($file_list)) {
            return '<em style="color: #999;">' . __('(空)', 'product-review-system') . '</em>';
        }

        $count = count($file_list);
        $files_html = '<div class="prs-downloadable-files">';
        $files_html .= '<strong>' . sprintf(__('%d 个文件:', 'product-review-system'), $count) . '</strong><br>';
        $files_html .= implode('<br>', $file_list);
        $files_html .= '</div>';

        return $files_html;
    }

    /**
     * 检查是否为下载文件数组
     */
    private function is_downloadable_files_array($value) {
        if (!is_array($value) || empty($value)) {
            return false;
        }

        // 检查数组的第一个元素是否具有下载文件的结构
        $first_item = reset($value);
        return is_array($first_item) && (isset($first_item['name']) || isset($first_item['file']));
    }

    /**
     * 标准化下载文件数组用于比较
     */
    private function normalize_downloadable_files($files) {
        if (!is_array($files)) {
            return array();
        }

        $normalized = array();
        foreach ($files as $file_id => $file_data) {
            if (is_array($file_data)) {
                $normalized[] = array(
                    'name' => isset($file_data['name']) ? trim($file_data['name']) : '',
                    'file' => isset($file_data['file']) ? trim($file_data['file']) : ''
                );
            }
        }

        // 按文件名排序以确保一致的比较
        usort($normalized, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        return $normalized;
    }

    /**
     * 比较分类、标签和品牌数组
     *
     * 特殊处理分类数组比较，确保类型一致性
     *
     * @param array $original 原始数组
     * @param array $modified 修改后数组
     * @return bool 是否有变更
     */
    /**
     * 清理分类数组，移除空值和无效值，并标准化类型
     */
    private function clean_taxonomy_array($array) {
        // 确保输入是数组
        if (!is_array($array)) {
            return array();
        }

        // 过滤空值和无效值
        $array = array_filter($array, function($v) {
            return $v !== '' && $v !== null && $v !== 0 && $v !== '0';
        });

        // 将所有元素转换为整数，确保类型一致
        $array = array_map('intval', $array);

        // 移除0值（转换后的无效值）
        $array = array_filter($array, function($v) { return $v > 0; });

        // 重新排序
        sort($array);

        return $array;
    }

    /**
     * 比较两个数组是否相等（忽略顺序）
     */
    private function arrays_equal($array1, $array2) {
        if (count($array1) !== count($array2)) {
            return false;
        }

        // 如果两个数组都为空，则认为相同
        if (empty($array1) && empty($array2)) {
            return true;
        }

        // 排序后比较
        sort($array1);
        sort($array2);

        return $array1 === $array2;
    }

    /**
     * 格式化产品主图显示
     */
    private function format_image_display($image_id) {
        if (empty($image_id)) {
            return '<em style="color: #999;">' . __('(无)', 'product-review-system') . '</em>';
        }

        $image_thumbnail = wp_get_attachment_image_src($image_id, 'thumbnail');
        $image_full = wp_get_attachment_image_src($image_id, 'full');

        if ($image_thumbnail && $image_full) {
            $image_name = get_the_title($image_id);
            if (empty($image_name)) {
                $image_name = basename($image_full[0]);
            }

            // 创建带预览和链接的图片显示
            $preview_html = '<div class="image-preview-container" style="display: flex; align-items: center;">';

            // 可点击的图片预览
            $preview_html .= '<a href="' . esc_url($image_full[0]) . '" target="_blank" title="' . esc_attr(__('点击查看大图', 'product-review-system')) . '">';
            $preview_html .= '<img src="' . esc_url($image_thumbnail[0]) . '" alt="' . esc_attr($image_name) . '" style="width: 80px; height: 80px; margin-right: 12px; border: 1px solid #ddd; border-radius: 4px; object-fit: cover; cursor: pointer; transition: border-color 0.2s;">';
            $preview_html .= '</a>';

            // 图片信息和链接
            $preview_html .= '<div>';
            $preview_html .= '<div style="font-weight: 500; margin-bottom: 4px;">';
            $preview_html .= '<a href="' . esc_url($image_full[0]) . '" target="_blank" style="text-decoration: none; color: #0073aa;">' . esc_html($image_name) . '</a>';
            $preview_html .= '</div>';
            $preview_html .= '<div style="font-size: 12px; color: #666; margin-bottom: 2px;">ID: ' . $image_id . '</div>';
            $preview_html .= '<div style="font-size: 11px; color: #999;">';
            $preview_html .= '<a href="' . esc_url($image_full[0]) . '" target="_blank" style="color: #0073aa; text-decoration: none;">🔗 查看原图</a>';
            $preview_html .= '</div>';
            $preview_html .= '</div>';

            $preview_html .= '</div>';

            return $preview_html;
        }

        return '<span style="color: #d63638;">图片 ID: ' . $image_id . ' (文件不存在)</span>';
    }

    /**
     * 格式化产品图库显示
     */
    private function format_gallery_display($gallery_ids) {
        if (empty($gallery_ids)) {
            return '<em style="color: #999;">' . __('(无)', 'product-review-system') . '</em>';
        }

        // 处理字符串格式的图库ID（逗号分隔）
        if (is_string($gallery_ids)) {
            $gallery_ids = array_filter(explode(',', $gallery_ids));
        }

        if (!is_array($gallery_ids) || empty($gallery_ids)) {
            return '<em style="color: #999;">' . __('(无)', 'product-review-system') . '</em>';
        }

        $image_count = count($gallery_ids);
        $preview_html = '<div class="gallery-preview-container">';

        // 图片网格容器
        $preview_html .= '<div style="display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 8px;">';

        // 最多显示前6个图片的缩略图
        $display_count = min(6, $image_count);
        $images_shown = 0;

        for ($i = 0; $i < $display_count; $i++) {
            $image_id = intval($gallery_ids[$i]);
            if ($image_id > 0) {
                $image_thumbnail = wp_get_attachment_image_src($image_id, 'thumbnail');
                $image_full = wp_get_attachment_image_src($image_id, 'full');

                if ($image_thumbnail && $image_full) {
                    $image_name = get_the_title($image_id);
                    if (empty($image_name)) {
                        $image_name = basename($image_full[0]);
                    }

                    // 可点击的图片缩略图
                    $preview_html .= '<div style="position: relative;">';
                    $preview_html .= '<a href="' . esc_url($image_full[0]) . '" target="_blank" title="' . esc_attr($image_name . ' - 点击查看大图') . '">';
                    $preview_html .= '<img src="' . esc_url($image_thumbnail[0]) . '" alt="' . esc_attr($image_name) . '" style="width: 60px; height: 60px; border: 1px solid #ddd; border-radius: 4px; object-fit: cover; cursor: pointer; transition: border-color 0.2s, transform 0.2s;" onmouseover="this.style.borderColor=\'#0073aa\'; this.style.transform=\'scale(1.05)\'" onmouseout="this.style.borderColor=\'#ddd\'; this.style.transform=\'scale(1)\'">';
                    $preview_html .= '</a>';
                    $preview_html .= '</div>';

                    $images_shown++;
                }
            }
        }

        $preview_html .= '</div>'; // 结束图片网格容器

        if ($images_shown === 0) {
            $preview_html .= '<span style="color: #d63638;">无法加载图片</span>';
        } else {
            // 图片统计和链接信息
            $preview_html .= '<div style="font-size: 12px; color: #666; margin-top: 4px;">';
            if ($image_count > $display_count) {
                $preview_html .= sprintf(__('显示 %d 张，共 %d 张图片', 'product-review-system'), $images_shown, $image_count);
            } else {
                $preview_html .= sprintf(__('共 %d 张图片', 'product-review-system'), $image_count);
            }
            $preview_html .= ' <span style="color: #0073aa;">• 点击图片查看大图</span>';
            $preview_html .= '</div>';

            // 添加所有图片的链接列表（折叠显示）
            if ($image_count > 3) {
                $preview_html .= '<div style="margin-top: 6px;">';
                $preview_html .= '<details style="font-size: 11px;">';
                $preview_html .= '<summary style="cursor: pointer; color: #0073aa;">查看所有图片链接</summary>';
                $preview_html .= '<div style="margin-top: 4px; padding-left: 12px;">';

                foreach ($gallery_ids as $index => $image_id) {
                    $image_id = intval($image_id);
                    if ($image_id > 0) {
                        $image_full = wp_get_attachment_image_src($image_id, 'full');
                        if ($image_full) {
                            $image_name = get_the_title($image_id);
                            if (empty($image_name)) {
                                $image_name = basename($image_full[0]);
                            }
                            $preview_html .= '<div style="margin: 2px 0;">';
                            $preview_html .= '<a href="' . esc_url($image_full[0]) . '" target="_blank" style="color: #0073aa; text-decoration: none;">';
                            $preview_html .= ($index + 1) . '. ' . esc_html($image_name);
                            $preview_html .= '</a>';
                            $preview_html .= '</div>';
                        }
                    }
                }

                $preview_html .= '</div>';
                $preview_html .= '</details>';
                $preview_html .= '</div>';
            }
        }

        $preview_html .= '</div>';

        return $preview_html;
    }

    /**
     * 显示待删除产品的信息
     */
    private function display_product_info_for_delete($product_data) {
        if (!$product_data) {
            echo '<p>' . __('无法获取产品信息。', 'product-review-system') . '</p>';
            return;
        }

        ?>
        <div class="prs-product-delete-info">
            <div class="prs-product-basic-info">
                <h4><?php _e('基本信息', 'product-review-system'); ?></h4>
                <table class="widefat">
                    <tr>
                        <td><strong><?php _e('产品名称:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['name'] ?? ''); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('产品类型:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['type'] ?? ''); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('SKU:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['sku'] ?? ''); ?></td>
                    </tr>
                    <tr>
                        <td><strong><?php _e('常规价格:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['regular_price'] ?? ''); ?></td>
                    </tr>
                    <?php if (!empty($product_data['sale_price'])): ?>
                    <tr>
                        <td><strong><?php _e('促销价格:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['sale_price']); ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <td><strong><?php _e('库存状态:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['stock_status'] ?? ''); ?></td>
                    </tr>
                    <?php if (isset($product_data['stock_quantity'])): ?>
                    <tr>
                        <td><strong><?php _e('库存数量:', 'product-review-system'); ?></strong></td>
                        <td><?php echo esc_html($product_data['stock_quantity']); ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>

            <?php if (!empty($product_data['description'])): ?>
            <div class="prs-product-description" style="margin-top: 20px;">
                <h4><?php _e('产品描述', 'product-review-system'); ?></h4>
                <div class="prs-description-content" style="border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                    <?php echo wp_kses_post($product_data['description']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($product_data['short_description'])): ?>
            <div class="prs-product-short-description" style="margin-top: 15px;">
                <h4><?php _e('简短描述', 'product-review-system'); ?></h4>
                <div class="prs-short-description-content" style="border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                    <?php echo wp_kses_post($product_data['short_description']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($product_data['gallery_ids'])): ?>
            <div class="prs-product-gallery" style="margin-top: 20px;">
                <h4><?php _e('产品图片', 'product-review-system'); ?></h4>
                <div class="prs-gallery-preview">
                    <?php echo $this->format_gallery_preview($product_data['gallery_ids']); ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($product_data['downloadable_files'])): ?>
            <div class="prs-product-downloads" style="margin-top: 20px;">
                <h4><?php _e('下载文件', 'product-review-system'); ?></h4>
                <div class="prs-downloads-list">
                    <?php foreach ($product_data['downloadable_files'] as $file): ?>
                    <div class="prs-download-item" style="margin: 5px 0; padding: 8px; border: 1px solid #ddd; background: #f9f9f9;">
                        <strong><?php echo esc_html($file['name'] ?? ''); ?></strong><br>
                        <small><?php echo esc_html($file['file'] ?? ''); ?></small>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <div class="prs-delete-warning" style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                <p style="margin: 0; color: #856404;">
                    <strong><?php _e('⚠️ 警告:', 'product-review-system'); ?></strong>
                    <?php _e('审核通过后，此产品将被移动到回收站。请仔细确认是否要删除此产品。', 'product-review-system'); ?>
                </p>
            </div>
        </div>

        <style>
        .prs-operation-type.delete {
            color: #d63384;
            font-weight: bold;
        }
        .prs-operation-type.modify {
            color: #0d6efd;
            font-weight: bold;
        }
        .prs-product-delete-info table.widefat td {
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
        }
        .prs-product-delete-info table.widefat tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        </style>
        <?php
    }
}
