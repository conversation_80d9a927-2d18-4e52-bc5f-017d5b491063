 .rsssl-404_blocking,  .rsssl-user_agents,.rsssl-content_security_policy_source_directives,
   .rsssl-firewall_block_list_listing, .rsssl-vulnerabilities-measures-overview, .rsssl-two_fa_users,
   .rsssl-firewall_list_listing, .rsssl-vulnerabilities_overview, .rsssl-permissions_policy, .rsssl-firewall_white_list_listing,
   .rsssl-firewall_logs_content, .rsssl-limit_login_attempts_country, .rsssl-limit_login_attempts_users,
   .rsssl-limit_login_attempts_event_log, .rsssl-mixed-content-scan, .rsssl-limit_login_attempts_ip_address,
   .rsssl-content_security_policy, .rsssl-hardening-xml {

  .rsssl-field-wrap {
    //wp-core also adds an svg for the select dropdown, so we hide the one from the react datatables component
    nav.rdt_Pagination > div > svg {
      display: none !important;
    }

    .rsssl-role-select .rsssl-search-bar {
      float: right;
      padding: 0;
    }

    .rsssl-role-select .rsssl-search-bar__inner {
      display: flex;
      align-items: center;
      border-radius: 3px;
      transition: background-color 0.3s ease;
    }

    .rsssl-search-bar__inner:focus-within {
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .rsssl-search-bar__icon {
      /* Add styles for the search icon */
    }

    .rsssl-search-bar__input {
      width: 150px; /* Adjust width as needed */
      transition: width 0.3s ease;
    }
    .rsssl-role-select__input {
      width: 500px; /* Adjust width as needed */
      select {
        width: 100%;
      }
    }

    .rsssl-search-bar__input:focus {
      width: 200px; /* Adjust width as needed */
    }

    .rsssl-container {
      padding: 2em;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .rsssl-multiselect-datatable-form {
      display: flex;
      align-items: center;
      Justify-content: space-between;
      width: 100%;
      padding: 1em 2em;
      background: var(--rsp-blue-faded);
    }

    .rdt_TableRow .rdt_TableCell:last-child {
      min-width: 20px;
      float: right;
    }

    .rdt_TableRow .rdt_TableCell:last-child button,
    .rdt_TableRow .rdt_TableCell:last-child span {
      min-width: 20px;
      float: right;
    }

    .rdt_TableCol, .rdt_TableCell, .rdt_TableCol_Sortable {
      //all text to the left
      flex-direction: row;
    }

    #cell-2-force_update > div > select {
      max-width: 100%;
    }

    #cell-2-quarantine > div > select {
      max-width: 100%;
    }

    .rdt_TableCol:first-child, .rdt_TableCell:first-child {
      min-width: initial;
    }

    .rdt_TableHeadRow {
      .rdt_TableCol:last-child {
        flex-grow: 0;
        flex-direction: row-reverse;
        min-width: initial;
      }
    }

    .rdt_TableRow {
      &:nth-child(odd) {
        background-color: var(--rsp-grey-200)
      }

      padding: var(--rsp-spacing-xs) 0;

      .rdt_TableCell:last-child {
        flex-grow: 0;
      }

      //.rsssl-status-allowed, .rsssl-status-revoked {
      //  min-width: 110px;
      //  margin-right: 10px;
      //}
    }

    .rsssl-csp-revoked > div:nth-child(-n+3) {
      opacity: 0.3;
    }
  }
}

.rsssl-content_security_policy, .rsssl-permissions_policy, .rsssl-field-wrap {
  .rdt_TableHeadRow {
    .rdt_TableCol:last-child {
      flex-grow: 0;
      min-width: initial;
      justify-content: flex-end;
    }
  }
}


.rdt_TableRow {
  .rdt_TableCell:last-child {
    flex-grow: 0;
    min-width: initial;
  }
}

.rsssl-csp-revoked > div:nth-child(-n+3) {
  opacity: 0.3;
}


.rsssl-content_security_policy, .rsssl-xml_rpc {
  .rsssl-field-wrap > div > div {
    overflow-x: inherit;
    overflow-y: inherit;
  }
}

.rsssl-mixed-content-datatable {
  & > div > div {
    display: flex;
  }
}
 .rsssl-vulnerabilities_measures {
   .rdt_TableRow .rdt_TableCell:nth-child(3) {
     max-width: 50%;
   }
 }

 .rsssl-two_fa_users {
   .rdt_TableHeadRow .rdt_TableCol:last-child {
     max-width: 20%!important;
   }
   .rdt_TableRow .rdt_TableCell:last-child {
     max-width: 20%!important;
   }
 }

.rsssl-vulnerabilities_measures, .rsssl-404_blocking, .rsssl-user_agents, .rsssl-firewall_logs, .rsssl-permissions_policy, .rsssl-two_fa_users,
.rsssl-vulnerabilities-measures-overview, .rsssl-content_security_policy_source_directives,
.rsssl-firewall_white_list_listing, .rsssl-firewall_block_list_listing, .rsssl-firewall_list_listing,
.rsssl-vulnerabilities_overview, .rsssl-limit_login_attempts_country, .rsssl-limit_login_attempts_users,
.rsssl-firewall_event_log_viewer, .rsssl-firewall_logs_content, .rsssl-limit_login_attempts_event_log,
.rsssl-mixed-content-scan, .rsssl-limit_login_attempts_ip_address, .rsssl-content_security_policy, .rsssl-hardening-xml {
  .rsssl-field-wrap {
    margin-left: CALC(-1 * var(--rsp-spacing-l));
    margin-right: CALC(-1 * var(--rsp-spacing-l));
    @media(max-width: $rsp-break-m) { // 1280px
      margin-left: CALC(-1 * var(--rsp-spacing-m));
      margin-right: CALC(-1 * var(--rsp-spacing-m));
    }
    @media(max-width: $rsp-break-s) { // 1280px
      margin-left: CALC(-1 * var(--rsp-spacing-s));
      margin-right: CALC(-1 * var(--rsp-spacing-s));
    }
    //should be s on <1280px
    > .components-base-control, .rsssl-comment,
      //.rsssl-grid-item-content-footer,
    .rsssl-progress-container,
    > div > button,
    .rsssl-learning-mode-footer,
    .rsssl-mixed-content-description,
    .rsssl-current-scan-action {
      margin-left: var(--rsp-spacing-l);
      margin-right: var(--rsp-spacing-l);
    }
  }

  .rdt_TableCell, .rdt_TableCol {
    &:first-child {
      padding-left: var(--rsp-spacing-l);
      padding-right: var(--rsp-spacing-l);
    }

    &:last-child {
      padding-right: var(--rsp-spacing-l);
    }
  }
}


/* Section for EdgeCases and other specific styling */
/* EdgeCase: EventLog */
.rsssl-content_security_policy,
.rsssl-permissions_policy,
.rsssl-firewall_list_listing,
.rsssl-user_agents,
.rsssl-404_blocking,
.rsssl-firewall_white_list_listing,
.rsssl-firewall_block_list_listing,
.rsssl-hardening-xml,
.rsssl-vulnerabilities_overview,
.rsssl-content_security_policy_source_directives,
.rsssl-vulnerabilities-measures-overview,
.rsssl-limit_login_attempts_ip_address,
.rsssl-limit_login_attempts_users,
.rsssl-limit_login_attempts_country,
.rsssl-two_fa_users,
.rsssl-firewall_logs,
.rsssl-firewall_logs_content,
.rsssl-limit_login_attempts_event_log {
  //first off we remove the min-width from table cell and table col
  .rdt_TableCell, .rdt_TableCol {
    min-width: initial;
  }

  .rdt_Pagination {
    margin-top: 0;
    padding: 0 25px;
  }

  .rdt_tableCell {
    &:has(div > .rsssl-action-buttons) {
      position: relative;
    }
  }

  .rsssl-container, .rdt_TableRow {
    //Horizontal padding;
    // padding: var(--rsp-spacing-m) var(--rsp-spacing-l);
  }

  .rsssl-field-wrap {
    padding: 0;

    .rdt_TableHeadRow {
      // Somehow the default for the last child is to make it small, well we dont want that.
      .rdt_TableCol:last-child {
        //we calculate the remaining width of the table and set it to the last column
        flex-grow: 1;
      }
    }

    .rdt_TableHeadRow {
      border: none;
    }

    .rdt_Pagination {
      border: none;
    }

    .rdt_TableBody {
      .rdt_TableRow {
        border: none;
        .rdt_TableCell:last-child {
          //we calculate the remaining width of the table and set it to the last column
          flex-grow: 1;
        }
      }

      //This is for the Expandable rows in the table
      .rdt_Expanding {
        .rdt_TableRow {
          .rdt_TableCell:first-child {
            //we remove all padding
            padding: 0;
            flex-direction: row;
          }
        }
      }

      //This is for the multiselect table
      .rdt_TableRow {
        .rdt_TableCell:first-child {
          //we remove all padding
          padding: 0 25px;
          flex-direction: row;
        }
      }
    }

    .rsssl-learning-mode-delete {
      float: right;
    }

    // in the tableCell we remove all previous styling of the last child
    .rdt_TableCell:last-child {
      div {
        width: 100%;

        button, a {
          margin-left: 10px;
        }
      }
    }

  }
}

.rsssl-limit_login_attempts_event_log, .rsssl-firewall_logs_content  {
  // making sure the last child of the tablehead is positioned to the left
    .rdt_TableHeadRow {
      .rdt_TableCol:last-child {
        justify-content: flex-start;
      }
    }
}

.rsssl-vulnerabilities-measures-overview {
  .allowRowEvents {
    overflow: visible !important;
    .wp-core-ui select {
      max-width: 100%;
    }
  }

  div:first-child {
    white-space: initial !important;
  }

  .rdt_TableCell {
    &:nth-child(2) {
      select {
        max-width: 100%;
      }
    }
  }

}

///* EdgeCase: CountryTable */
//.rsssl-vulnerabilities_measures {
//  .rdt_TableCell {
//    &:nth-child(2) {
//      select {
//        max-width: 100%;
//      }
//    }
//  }
//}

///* EdgeCase: hardening-xml */
//.rsssl-hardening-xml {
//
//  .rdt_Table {
//    height: 300px;
//    align-items: initial;
//
//    div:nth-child(2) {
//      align-items: baseline;
//    }
//
//  }
//
//  .rdt_TableHead {
//    padding-bottom: 10px;
//  }
//
//  .rdt_TableCol:first-child {
//    padding: 0 25px !important;
//  }
//
//  .rdt_TableCell:last-child {
//    padding: 0 25px !important;
//  }
//
//  .rdt_TableRow .rdt_TableCell:last-child {
//    flex: 1;
//    display: flex;
//    justify-content: flex-end;
//  }
//}

/* EdgeCase: Permission_Policy */
.rsssl-content_security_policy, .rsssl-permissions_policy {
  .rdt_TableCol {
    &:last-child {
      //we calculate the remaining width of the table and set it to the last column
      flex-grow: 1;
      text-align: right;

      div {
        width: 100%;
        display: flex;
        justify-content: flex-start;
      }
    }
  }


  .rdt_TableCell {
    &:last-child {
      //we calculate the remaining width of the table and set it to the last column
      flex-grow: 1;
      text-align: right;

      div {
        width: 100%;
        display: flex;
        justify-content: flex-end;

        button {
          margin-right: 0 !important;
        }
      }
    }
  }
}
.rsssl-vulnerabilities-measures-overview {
  .rdt_TableRow .rdt_TableCell:last-child {
    overflow: hidden;
    text-overflow: ellipsis;
    width: 200px;
  }
}

//For the shields
 .rsssl-content_security_policy_source_directives:has(.rsssl-shield-overlay) {
   min-height: 250px;
 }