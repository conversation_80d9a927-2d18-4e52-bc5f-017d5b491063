import React, { Component } from 'react';
import { styled } from '@mui/system';
import { Tabs} from '@mui/base/Tabs';
import { TabsList  } from '@mui/base/TabsList';
import { TabPanel  } from '@mui/base/TabPanel';
import { Tab , tabClasses } from '@mui/base/Tab';
import Integrate from './integrate';
import { sprintf, __ } from '@wordpress/i18n';

// custom components

const Integrations = () => {
    let zapierIcon = (
        <svg width="19" height="19" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_774_1607)">
        <path d="M14.9999 12.0052C15.0003 12.8706 14.8442 13.7289 14.5391 14.5387C13.729 14.8438 12.8705 15.0001 12.0049 15.0003H11.9948C11.1034 14.9992 10.2493 14.8362 9.46125 14.5391C9.15597 13.7292 8.99971 12.8708 9 12.0052V11.9948C8.99958 11.1295 9.15553 10.2714 9.46031 9.46163C10.2704 9.15601 11.1291 8.99961 11.9948 9H12.005C12.8707 8.99958 13.7293 9.15595 14.5392 9.46153C14.8443 10.2712 15.0004 11.1294 15 11.9947V12.0052L14.9999 12.0052ZM23.8333 10H16.8287L21.7814 5.04694C21.3927 4.50086 20.959 3.98817 20.485 3.51422V3.51384C20.011 3.0404 19.4985 2.60715 18.9527 2.21859L13.9996 7.17169V0.167062C13.3408 0.0562433 12.674 0.000363459 12.006 0L11.9936 0C11.3141 0.000375 10.6486 0.058125 10 0.167062V7.17169L5.04694 2.21859C4.50107 2.60705 3.98875 3.0406 3.51534 3.51469L3.51272 3.51656C3.03952 3.98993 2.6065 4.50181 2.21812 5.04694L7.17159 10H0.167062C0.167062 10 0 11.3156 0 11.9959V12.0041C0 12.6844 0.0576562 13.351 0.167062 14H7.17169L2.21822 18.9531C2.99744 20.0466 3.95344 21.0026 5.04694 21.7818L10 16.8283V23.8333C10.6581 23.9436 11.3241 23.9993 11.9913 24H12.0082C12.6755 23.9994 13.3415 23.9436 13.9995 23.8333V16.8283L18.9531 21.7818C19.4987 21.3931 20.0111 20.9597 20.485 20.4862L20.4862 20.485C20.9595 20.0111 21.3927 19.4986 21.7814 18.9531L16.8279 14H23.8333C23.9423 13.3522 23.9993 12.6875 24 12.0087V11.9913C23.9993 11.3125 23.9423 10.6478 23.8333 10Z" fill="#242424"/>
        </g>
        <defs>
        <clipPath id="clip0_774_1607">
        <rect width="24" height="24" fill="white"/>
        </clipPath>
        </defs>
        </svg> 
    )  

    return (

        <React.Fragment>
        <Tabs className = "integration_subtabs" defaultValue={1}>
        <TabsList className ="integration_subtabs_list zapier-tab">
            <Tab value={1}> {zapierIcon}{__('Zapier', 'new-user-approve') }</Tab>
        </TabsList>

        <TabPanel value={1}><Integrate platform = 'zapier'/></TabPanel>


    </Tabs>
    </React.Fragment>
    );
    
  }

export default Integrations;