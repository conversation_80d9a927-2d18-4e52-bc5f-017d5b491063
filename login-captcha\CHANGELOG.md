# 更新日志

所有重要的项目更改都将记录在此文件中。

## [1.0.8] - 2024-01-15

### 安全加固和测试文件清理
- 🔒 **安全漏洞修复**: 修复多个安全漏洞，提升插件安全性
- 🧹 **测试文件清理**: 删除所有测试页面和测试文件
- 🛡️ **CSRF防护**: 为AJAX端点添加nonce验证
- 🚫 **速率限制**: 添加验证码生成和验证的速率限制
- 🔐 **输入验证**: 加强所有用户输入的验证和清理
- 🐛 **UUID验证修复**: 修复UUID格式验证导致的400错误
- 💬 **错误提示优化**: 将"验证码加载失败"改为"频繁请求，请稍后重试"

### 安全改进详情
- **AJAX端点保护**: 验证码图片生成添加nonce验证
- **输入格式验证**: 严格验证captcha_id和用户输入格式
- **暴力破解防护**: 添加失败尝试计数和IP封锁
- **时序攻击防护**: 使用hash_equals进行验证码比较
- **数据存储安全**: 使用SHA256哈希处理transient键名
- **输出转义**: 确保所有输出都经过适当转义
- **权限检查**: 加强管理页面的权限验证
- **参数范围限制**: 限制配置参数的有效范围

### 文件清理
- 删除 `test-captcha.php` - 测试验证码生成
- 删除 `demo.html` - 演示页面
- 删除 `layout-test.html` - 布局测试页面
- 删除 `layout-preview.html` - 布局预览页面
- 删除 `alignment-test.html` - 对齐测试页面
- 删除 `font-size-test.html` - 字体大小测试页面
- 删除 `border-removal-test.html` - 边框移除测试页面
- 删除 `builtin-font-test.html` - 内置字体测试页面

### 文件更新
- `login-captcha.php` - 添加安全检查和速率限制
- `includes/class-captcha-generator.php` - 加强验证码验证安全性
- `includes/class-admin-settings.php` - 改进管理页面安全性
- `assets/js/login-captcha.js` - 添加nonce支持

## [1.0.7] - 2024-01-15

### 内置字体增大优化
- 🔤 **内置字体放大**: 显著增大PHP内置字体的显示效果
- 🎯 **智能放大算法**: 根据可用空间动态计算最佳放大倍数
- 💪 **多重绘制技术**: 通过重复绘制模拟放大和加粗效果
- 📐 **精确居中**: 优化字符在验证码图片中的位置和对齐
- 🔧 **兼容性保障**: 确保在没有TTF字体的环境下也有良好显示

### 技术实现
- 新增`draw_large_builtin_char()`方法处理内置字体放大
- 使用最大内置字体（字体5）作为基础
- 通过嵌套循环实现多重绘制放大效果
- 添加额外加粗处理提高可读性
- 优化数学验证码的内置字体显示

### 文件更新
- `includes/class-captcha-generator.php` - 添加内置字体放大功能
- `builtin-font-test.html` - 新增内置字体测试页面

## [1.0.6] - 2024-01-15

### 占位空间完全移除
- 🎯 **彻底修复**: 完全移除绿色边框的占位空间（padding、margin、text-indent）
- 🔧 **深度清理**: 不仅移除边框，还移除所有相关的空间占位
- 📐 **精确对齐**: 确保验证码字段与其他表单元素完美对齐
- 🛠️ **全面覆盖**: CSS、内联样式、JavaScript三重保障

### 技术改进
- 添加padding、margin、text-indent的重置规则
- 更新JavaScript动态检测，包含占位空间移除
- 优化内联样式，确保完全无占位
- 改进测试页面，支持占位空间测试

### 文件更新
- `assets/css/login-captcha.css` - 添加占位空间移除规则
- `assets/js/login-captcha.js` - 更新动态移除功能
- `login-captcha.php` - 更新内联样式
- `border-removal-test.html` - 更新测试功能

## [1.0.5] - 2024-01-15

### 样式修复
- 🎨 **移除绿色边框**: 完全移除验证码标签旁的绿色边框
- 🛠️ **多层级修复**: 使用CSS、内联样式和JavaScript多重保障
- 🎯 **高优先级规则**: 添加高特异性CSS选择器确保样式覆盖
- 🔧 **动态检测**: JavaScript定期检查并移除可能重新出现的边框

### 技术实现
- 添加高优先级CSS规则覆盖WordPress默认样式
- 使用内联样式作为额外保障
- JavaScript动态移除边框并定期检查
- 支持各种WordPress主题和插件环境

### 文件更新
- `assets/css/login-captcha.css` - 添加边框移除规则
- `assets/js/login-captcha.js` - 添加动态边框移除功能
- `login-captcha.php` - 添加内联样式保障

## [1.0.4] - 2024-01-15

### 字体大小优化
- 📝 **输入框字体调整**: 将输入框字体从24px调整为16px，提高可读性
- 🔤 **验证码字母放大**: 增大验证码图片中字母的显示大小
- 📐 **动态字体计算**: 使用动态算法计算验证码字体大小，确保最佳显示效果
- 🎯 **视觉平衡**: 优化输入框和验证码的视觉比例

### 技术改进
- 验证码字体大小从height*0.6增加到动态计算
- 减少字母倾斜角度，提高可读性
- 优化字符位置计算，确保居中显示
- 改进内置字体备选方案

### 文件更新
- `assets/css/login-captcha.css` - 调整输入框字体大小
- `includes/class-captcha-generator.php` - 优化验证码字体生成算法
- `demo.html` - 更新演示页面字体
- `layout-preview.html` - 更新预览页面字体
- `alignment-test.html` - 更新测试页面字体

## [1.0.3] - 2024-01-15

### 对齐和布局精细优化
- 🎯 **完美对齐**: 修复输入框和验证码图片的对齐问题
- 📍 **刷新链接位置**: 将"刷新验证码"链接移至验证码图片正下方
- 📏 **高度统一**: 确保输入框和验证码图片高度一致(40px)
- 🎨 **视觉改进**: 使用flex-start对齐，避免元素错位
- 🏗️ **结构优化**: 添加captcha-image-container容器，更好的布局控制

### 技术改进
- 使用`align-items: flex-start`确保顶部对齐
- 添加`.captcha-image-container`容器，垂直排列图片和刷新链接
- 统一输入框和图片高度为40px
- 优化刷新链接样式，居中显示在图片下方
- 改进响应式布局，保持对齐效果

### 文件更新
- `login-captcha.php` - 添加captcha-image-container结构
- `assets/css/login-captcha.css` - 优化对齐样式和容器布局
- `demo.html` - 更新演示页面结构
- `layout-test.html` - 更新测试页面结构
- `layout-preview.html` - 更新预览页面说明

## [1.0.2] - 2024-01-15

### 重要布局优化
- 🎨 **重要更新**: 调整布局顺序，输入框在前，验证码图片在后
- 📝 **用户体验改进**: 输入框占据主要空间，确保文字显示完整
- 🎯 **视觉优化**: 移除了输入框的size属性，使用flex布局自适应
- 📱 **响应式改进**: 优化移动设备布局，保持水平排列直到超小屏幕

### 技术改进
- 使用CSS order属性控制元素显示顺序
- 优化输入框最小宽度设置，确保文字显示完整
- 改进Flexbox布局，输入框flex:1，图片flex-shrink:0
- 优化移动设备响应式断点设置

### 文件更新
- `login-captcha.php` - 调整HTML元素顺序
- `assets/css/login-captcha.css` - 优化布局样式和响应式设计
- `demo.html` - 更新演示页面布局
- `layout-test.html` - 更新测试页面说明

## [1.0.1] - 2024-01-15

### 改进功能
- 🎨 **重要更新**: 验证码图片和输入框现在在同一行显示
- 🎨 优化了验证码布局，使用Flexbox实现更好的对齐
- 📱 改进了移动设备上的响应式布局
- 🖱️ 添加了点击验证码图片刷新功能
- ✨ 优化了加载状态的视觉反馈
- 🔧 改进了JavaScript事件处理

### 技术改进
- 使用`.login-captcha-container`容器实现水平布局
- 优化CSS Flexbox布局，支持自适应宽度
- 改进移动设备响应式设计（小于782px时垂直布局）
- 增强了无障碍功能支持

### 文件更新
- `assets/css/login-captcha.css` - 新增水平布局样式
- `login-captcha.php` - 更新HTML结构
- `assets/js/login-captcha.js` - 优化事件处理
- `layout-test.html` - 新增布局测试页面

## [1.0.0] - 2024-01-15

### 新增功能
- ✨ 初始版本发布
- ✨ 基本验证码生成功能
- ✨ 支持数字、字母、混合三种验证码类型
- ✨ 可自定义验证码长度（3-8位）
- ✨ 可调整验证码图片尺寸
- ✨ WordPress登录表单集成
- ✨ 管理后台设置页面
- ✨ 响应式设计支持
- ✨ 无障碍功能支持
- ✨ 验证码自动过期机制（5分钟）
- ✨ JavaScript交互功能
- ✨ 键盘快捷键支持（F5刷新）
- ✨ 多语言支持框架

### 安全特性
- 🔒 验证码图片包含干扰线和噪点
- 🔒 每次刷新生成新的随机验证码
- 🔒 验证码使用后立即失效
- 🔒 支持大小写不敏感验证
- 🔒 防止暴力破解攻击
- 🔒 使用WordPress transient API安全存储

### 技术特性
- ⚡ 使用GD库生成图片
- ⚡ 单例模式设计
- ⚡ 面向对象编程
- ⚡ WordPress编码标准
- ⚡ 完整的错误处理
- ⚡ 自动清理过期数据

### 文件结构
```
login-captcha/
├── login-captcha.php          # 主插件文件
├── README.md                  # 项目说明
├── INSTALL.md                 # 安装指南
├── CHANGELOG.md               # 更新日志
├── demo.html                  # 功能演示
├── test-captcha.php          # 测试工具
├── uninstall.php             # 卸载清理
├── index.php                 # 安全防护
├── includes/                 # 核心类文件
│   ├── class-captcha-generator.php
│   ├── class-admin-settings.php
│   └── index.php
├── assets/                   # 静态资源
│   ├── css/
│   │   ├── login-captcha.css
│   │   └── index.php
│   ├── js/
│   │   ├── login-captcha.js
│   │   └── index.php
│   └── index.php
└── languages/                # 语言文件
    ├── login-captcha.pot
    └── index.php
```

### 系统要求
- WordPress 5.0+
- PHP 7.4+
- GD扩展
- 文件写入权限

### 已知问题
- 无

### 开发者说明
- 遵循WordPress插件开发最佳实践
- 使用WordPress钩子和过滤器
- 支持主题自定义样式
- 提供开发者API接口

---

## 计划功能 (未来版本)

### [1.1.0] - 计划中
- 🔄 数学验证码支持
- 🔄 滑动验证码选项
- 🔄 验证码音频支持
- 🔄 更多字体选择
- 🔄 验证码模板系统

### [1.2.0] - 计划中
- 🔄 注册页面验证码
- 🔄 评论验证码
- 🔄 找回密码验证码
- 🔄 WooCommerce集成
- 🔄 多站点支持

### [1.3.0] - 计划中
- 🔄 验证码统计功能
- 🔄 IP白名单功能
- 🔄 验证失败限制
- 🔄 邮件通知功能
- 🔄 API接口支持

---

## 贡献指南

### 报告问题
1. 检查现有问题列表
2. 提供详细的错误信息
3. 包含系统环境信息
4. 提供重现步骤

### 功能请求
1. 描述功能需求
2. 说明使用场景
3. 提供设计建议
4. 考虑兼容性影响

### 代码贡献
1. Fork项目仓库
2. 创建功能分支
3. 遵循编码标准
4. 添加测试用例
5. 提交Pull Request

---

## 许可证

本项目基于 GPL v2 或更高版本许可证发布。

---

## 致谢

感谢以下项目和社区的支持：
- WordPress社区
- GD图像处理库
- 所有测试用户和贡献者

---

## 联系方式

- 项目主页：[插件官网]
- 技术支持：[<EMAIL>]
- 文档中心：[docs.example.com]
- 社区论坛：[forum.example.com]

---

**注意**：此更新日志遵循 [Keep a Changelog](https://keepachangelog.com/) 格式。
