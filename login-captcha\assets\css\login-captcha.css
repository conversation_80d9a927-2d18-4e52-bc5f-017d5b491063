/**
 * 登录验证码样式
 */

/* 重置所有可能的边框样式和占位空间 - 最高优先级 */
.login-captcha-field,
.login-captcha-field *,
p.login-captcha-field,
p.login-captcha-field *,
form p.login-captcha-field,
form p.login-captcha-field *,
#loginform p.login-captcha-field,
#loginform p.login-captcha-field *,
div#login form p.login-captcha-field,
div#login form p.login-captcha-field *,
body.login div#login form p.login-captcha-field,
body.login div#login form p.login-captcha-field * {
    border: none !important;
    border-left: none !important;
    border-left-color: transparent !important;
    border-left-width: 0 !important;
    border-left-style: none !important;
    box-shadow: none !important;
    outline: none !important;
    background-image: none !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
    text-indent: 0 !important;
}

/* 验证码字段容器 */
.login-captcha-field {
    margin-bottom: 16px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    border: none !important;
    border-left: none !important;
    box-shadow: none !important;
}

.login-captcha-field label {
    color: #3c434a;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 3px;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    display: block;
    border: none !important;
    border-left: none !important;
    box-shadow: none !important;
}

/* 验证码容器 - 输入框在前，图片在后 */
.login-captcha-container {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin: 8px 0;
}

/* 验证码输入框 - 在前面，占据主要空间 */
#login_captcha {
    background: #fff;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    color: #2c3338;
    font-size: 16px;
    flex: 1;
    padding: 8px 12px;
    line-height: 1.33333333;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.04);
    transition: border-color 0.05s ease-in-out;
    min-width: 120px;
    width: auto;
    height: 40px;
    box-sizing: border-box;
}

/* 验证码图片容器 */
.captcha-image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
}

/* 验证码图片样式 - 在后面，固定尺寸 */
#captcha-image {
    border: 1px solid #dcdcde;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
    height: 40px;
    cursor: pointer;
    display: block;
    margin-bottom: 5px;
}

#login_captcha:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: 2px solid transparent;
}

/* 移除所有可能的绿色边框 */
.login-captcha-field,
.login-captcha-field *,
.login-captcha-field::before,
.login-captcha-field::after,
.login-captcha-field label,
.login-captcha-field label::before,
.login-captcha-field label::after {
    border-left: none !important;
    border-left-color: transparent !important;
    border-left-width: 0 !important;
    border-left-style: none !important;
}

/* 移除WordPress默认的表单边框样式 */
.login form .login-captcha-field,
.login form .login-captcha-field label,
.login form .login-captcha-field::before,
.login form .login-captcha-field::after {
    border: none !important;
    border-left: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* 移除可能的主题样式 */
p.login-captcha-field,
p.login-captcha-field label,
p.login-captcha-field::before,
p.login-captcha-field::after {
    border: none !important;
    border-left: none !important;
    border-color: transparent !important;
    box-shadow: none !important;
}

/* 刷新验证码链接 - 在验证码图片下方 */
#refresh-captcha {
    color: #2271b1;
    text-decoration: none;
    font-size: 12px;
    line-height: 1.2;
    display: block;
    text-align: center;
    white-space: nowrap;
    transition: color 0.1s ease-in-out;
}

#refresh-captcha:hover {
    color: #135e96;
    text-decoration: underline;
}

#refresh-captcha:focus {
    color: #043959;
    box-shadow: 0 0 0 2px #2271b1;
    outline: 2px solid transparent;
    border-radius: 2px;
}

/* 响应式设计 */
@media screen and (max-width: 782px) {
    .login-captcha-field {
        margin-bottom: 20px !important;
    }

    .login-captcha-container {
        flex-direction: row;
        align-items: flex-start;
        gap: 8px;
    }

    #login_captcha {
        font-size: 14px;
        padding: 8px 12px;
        min-width: 100px;
        flex: 1;
        height: 40px;
    }

    .captcha-image-container {
        flex-shrink: 0;
    }

    #captcha-image {
        max-width: 120px;
        height: 40px;
    }
}

/* 小屏幕优化 - 垂直布局 */
@media screen and (max-width: 480px) {
    .login-captcha-container {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    #login_captcha {
        font-size: 14px;
        padding: 10px 12px;
        min-width: auto;
        height: 44px;
    }

    .captcha-image-container {
        align-self: center;
    }

    #captcha-image {
        height: 40px;
    }
}

/* 错误状态样式 */
.login-captcha-field.error #login_captcha {
    border-color: #d63638;
}

.login-captcha-field.error #captcha-image {
    border-color: #d63638;
}

/* 加载状态 */
.login-captcha-loading #captcha-image {
    opacity: 0.6;
    filter: grayscale(50%);
}

.login-captcha-loading #refresh-captcha {
    pointer-events: none;
    opacity: 0.6;
}

.login-captcha-container.login-captcha-loading {
    opacity: 0.8;
}

.login-captcha-container.login-captcha-loading #login_captcha {
    pointer-events: none;
}



.login-captcha-image-container {
    position: relative;
    display: inline-block;
}

.login-captcha-refresh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.login-captcha-refresh-overlay.active {
    display: flex;
}

.login-captcha-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: login-captcha-spin 1s linear infinite;
}

@keyframes login-captcha-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    #captcha-image {
        border: 2px solid #000;
    }
    
    #login_captcha {
        border: 2px solid #000;
    }
    
    #login_captcha:focus {
        border-color: #000;
        box-shadow: 0 0 0 3px #000;
    }
}

/* 强制移除绿色边框和占位空间 - 高优先级规则 */
body.login div#login form p.login-captcha-field,
body.login div#login form p.login-captcha-field label,
body.login div#login form .login-captcha-field,
body.login div#login form .login-captcha-field label,
body.login #loginform p.login-captcha-field,
body.login #loginform p.login-captcha-field label,
body.login #loginform .login-captcha-field,
body.login #loginform .login-captcha-field label {
    border: none !important;
    border-left: none !important;
    border-left-color: transparent !important;
    border-left-width: 0 !important;
    border-left-style: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-indent: 0 !important;
    position: relative !important;
}

/* 移除伪元素可能的边框 */
body.login div#login form p.login-captcha-field::before,
body.login div#login form p.login-captcha-field::after,
body.login div#login form p.login-captcha-field label::before,
body.login div#login form p.login-captcha-field label::after,
body.login #loginform p.login-captcha-field::before,
body.login #loginform p.login-captcha-field::after,
body.login #loginform p.login-captcha-field label::before,
body.login #loginform p.login-captcha-field label::after {
    display: none !important;
    content: none !important;
    border: none !important;
    background: none !important;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .login-captcha-field label {
        color: #f0f0f1;
    }

    #captcha-image {
        border-color: #3c434a;
        background: #1d2327;
    }

    #login_captcha {
        background: #1d2327;
        border-color: #3c434a;
        color: #f0f0f1;
    }

    #login_captcha:focus {
        border-color: #72aee6;
        box-shadow: 0 0 0 1px #72aee6;
    }

    #refresh-captcha {
        color: #72aee6;
    }

    #refresh-captcha:hover {
        color: #9ec2e6;
    }

    /* 深色模式下也移除绿色边框 */
    .login-captcha-field,
    .login-captcha-field label {
        border: none !important;
        border-left: none !important;
        box-shadow: none !important;
    }
}

/* JavaScript边框移除支持类 */
.no-border-override,
.no-border-override::before,
.no-border-override::after {
    border: none !important;
    border-left: none !important;
    border-left-color: transparent !important;
    border-left-width: 0 !important;
    border-left-style: none !important;
    box-shadow: none !important;
    outline: none !important;
    background-image: none !important;
    padding: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-indent: 0 !important;
}

/* 打印样式 */
@media print {
    .login-captcha-field {
        display: none;
    }
}

/* 辅助功能增强 */
.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal !important;
}

/* 验证码说明文字 */
.login-captcha-help {
    font-size: 12px;
    color: #646970;
    margin-top: 5px;
    line-height: 1.4;
}

/* 错误消息样式 */
.login-captcha-error {
    color: #d63638;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* 成功状态 */
.login-captcha-field.success #login_captcha {
    border-color: #00a32a;
}

.login-captcha-field.success #captcha-image {
    border-color: #00a32a;
}
