# 产品审核系统数据库修复工具使用指南

## 🎯 概述

数据库修复工具是为产品审核系统专门设计的数据库维护工具，用于检测和修复数据库表结构问题，确保审核记录能够正常创建和管理。

## 🚨 问题背景

当您遇到以下问题时，可以使用此修复工具：

- ❌ 无法创建审核记录
- ❌ 产品修改提交后没有生成审核记录
- ❌ 审核系统页面显示数据库错误
- ❌ 插件激活后数据库表未正确创建
- ❌ 升级插件后出现字段缺失问题

## 🔧 工具功能

### 1. 数据库诊断
- **表存在性检查**: 验证所有必需的数据库表是否存在
- **字段完整性检查**: 确保所有必需字段都已正确创建
- **索引状态检查**: 验证数据库索引是否完整
- **权限验证**: 检查数据库操作权限是否充足

### 2. 自动修复
- **创建缺失表**: 自动创建不存在的数据库表
- **添加缺失字段**: 为现有表添加缺失的字段
- **修复索引**: 重建缺失或损坏的索引
- **结构升级**: 将旧版本表结构升级到最新版本

### 3. 安全保护
- **权限验证**: 只有管理员才能使用修复工具
- **操作日志**: 详细记录所有修复操作
- **备份建议**: 提醒用户在修复前备份数据
- **回滚支持**: 提供操作撤销建议

## 📋 使用步骤

### 方法一：通过管理界面访问

1. **登录WordPress管理后台**
2. **进入产品审核系统**
   - 导航到 `产品审核` → `数据库修复`
3. **执行诊断**
   - 点击 `🔍 诊断数据库` 按钮
   - 等待诊断完成，查看结果
4. **执行修复**
   - 如果发现问题，点击 `🔧 自动修复` 按钮
   - 确认修复操作
   - 等待修复完成

### 方法二：直接访问修复工具

1. **访问修复工具页面**
   ```
   https://您的域名/wp-content/plugins/product-review-system/database-repair-tool.php
   ```
2. **按照界面提示操作**

### 方法三：使用诊断工具

1. **访问诊断页面**
   ```
   https://您的域名/wp-content/plugins/product-review-system/database-diagnostic.php
   ```
2. **查看详细的诊断报告**

## 🛡️ 安全注意事项

### 修复前准备
1. **备份数据库** ⚠️ 重要！
   ```sql
   -- 备份产品审核相关表
   CREATE TABLE wp_product_reviews_backup AS SELECT * FROM wp_product_reviews;
   ```

2. **确认权限**
   - 确保当前用户具有管理员权限
   - 验证数据库用户具有CREATE、ALTER权限

3. **检查环境**
   - 确保WordPress和WooCommerce正常运行
   - 验证服务器资源充足

### 修复过程中
- ⏳ 不要中断修复过程
- 📝 注意查看修复日志
- 🚫 避免同时进行其他数据库操作

### 修复后验证
1. **功能测试**
   - 尝试创建一个测试审核记录
   - 验证审核流程是否正常

2. **数据完整性检查**
   - 确认现有审核记录未受影响
   - 验证产品数据完整性

## 📊 诊断结果说明

### 整体状态
- **🟢 healthy**: 数据库状态正常
- **🟡 needs_repair**: 需要修复，但不影响基本功能
- **🔴 critical**: 严重问题，影响系统正常使用

### 表状态
- **🟢 healthy**: 表结构完整
- **🟡 minor_issues**: 轻微问题，如缺失非关键索引
- **🔴 needs_repair**: 需要修复，如缺失字段
- **⚫ missing**: 表不存在

### 修复优先级
- **🔴 critical**: 立即修复
- **🟡 high**: 尽快修复
- **🟢 medium**: 建议修复
- **⚪ low**: 可选修复

## 🔍 常见问题解决

### Q1: 修复工具无法访问
**原因**: 权限不足或文件缺失
**解决方案**:
1. 确认当前用户是管理员
2. 检查插件文件完整性
3. 重新上传插件文件

### Q2: 诊断显示权限不足
**原因**: 数据库用户权限不够
**解决方案**:
1. 联系主机提供商
2. 检查wp-config.php中的数据库配置
3. 确认数据库用户具有CREATE、ALTER权限

### Q3: 修复后仍然无法创建审核记录
**原因**: 可能存在其他问题
**解决方案**:
1. 重新运行诊断
2. 检查WordPress错误日志
3. 验证WooCommerce是否正常
4. 联系技术支持

### Q4: 修复过程中断
**原因**: 服务器超时或资源不足
**解决方案**:
1. 增加PHP执行时间限制
2. 分批执行修复操作
3. 联系主机提供商优化服务器配置

## 📝 技术细节

### 数据库表结构

#### 主表: wp_product_reviews
```sql
CREATE TABLE wp_product_reviews (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    product_id bigint(20) NOT NULL,
    original_data longtext NOT NULL,
    modified_data longtext NOT NULL,
    change_summary text DEFAULT '',
    submitter_id bigint(20) NOT NULL,
    reviewer_id bigint(20) DEFAULT NULL,
    admin_id bigint(20) DEFAULT NULL,
    status varchar(20) DEFAULT 'pending_review',
    reviewer_status varchar(20) DEFAULT 'pending',
    admin_status varchar(20) DEFAULT 'pending',
    reviewer_notes text DEFAULT '',
    admin_notes text DEFAULT '',
    reviewer_date datetime DEFAULT NULL,
    admin_date datetime DEFAULT NULL,
    is_new_product tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY product_id (product_id),
    KEY submitter_id (submitter_id),
    KEY reviewer_id (reviewer_id),
    KEY admin_id (admin_id),
    KEY status (status),
    KEY reviewer_status (reviewer_status),
    KEY admin_status (admin_status),
    KEY is_new_product (is_new_product),
    KEY created_at (created_at)
);
```

#### 历史表: wp_product_review_history (可选)
```sql
CREATE TABLE wp_product_review_history (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    review_id bigint(20) NOT NULL,
    user_id bigint(20) NOT NULL,
    action varchar(50) NOT NULL,
    comment text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY review_id (review_id),
    KEY user_id (user_id),
    KEY action (action),
    KEY created_at (created_at)
);
```

### 修复工具架构

修复工具基于SOLID原则设计：

1. **单一职责原则**: 每个类只负责一个特定功能
2. **开放封闭原则**: 易于扩展新的修复功能
3. **里氏替换原则**: 组件可以安全替换
4. **接口隔离原则**: 最小化接口依赖
5. **依赖倒置原则**: 依赖抽象而非具体实现

## 📞 技术支持

如果您在使用修复工具时遇到问题，请：

1. **收集信息**
   - 错误消息截图
   - 诊断报告
   - WordPress和插件版本信息

2. **检查日志**
   - WordPress错误日志
   - 修复工具日志
   - 服务器错误日志

3. **联系支持**
   - 提供详细的问题描述
   - 附上相关日志和截图
   - 说明已尝试的解决方案

## 📈 版本历史

- **v1.0.0**: 初始版本，基础诊断和修复功能
- 支持主表结构检测和修复
- 提供用户友好的Web界面
- 集成到产品审核系统管理界面

---

**⚠️ 重要提醒**: 在执行任何数据库修复操作之前，请务必备份您的数据库！
