<?php

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 产品审核系统安全配置类
 */
class PRS_Security_Config {
    
    /**
     * 安全配置常量
     */
    const RATE_LIMITS = array(
        'approve_review' => array('limit' => 10, 'window' => 60),
        'reject_review' => array('limit' => 10, 'window' => 60),
        'bulk_action' => array('limit' => 3, 'window' => 60),
        'publish_product' => array('limit' => 5, 'window' => 60),
        'login_attempt' => array('limit' => 5, 'window' => 300),
        'form_submission' => array('limit' => 20, 'window' => 300)
    );
    
    const BULK_ACTION_LIMITS = array(
        'max_items' => 50,
        'timeout' => 30
    );
    
    const ALLOWED_FILE_TYPES = array(
        'jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'
    );
    
    const MAX_FILE_SIZE = 5242880; // 5MB
    
    const SECURITY_HEADERS = array(
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'SAMEORIGIN',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin'
    );
    
    /**
     * 获取速率限制配置
     */
    public static function get_rate_limit($action) {
        return isset(self::RATE_LIMITS[$action]) ? self::RATE_LIMITS[$action] : array('limit' => 5, 'window' => 60);
    }
    
    /**
     * 获取允许的文件类型
     */
    public static function get_allowed_file_types() {
        return apply_filters('prs_allowed_file_types', self::ALLOWED_FILE_TYPES);
    }
    
    /**
     * 获取最大文件大小
     */
    public static function get_max_file_size() {
        return apply_filters('prs_max_file_size', self::MAX_FILE_SIZE);
    }
    
    /**
     * 获取批量操作限制
     */
    public static function get_bulk_action_limit($key = 'max_items') {
        return isset(self::BULK_ACTION_LIMITS[$key]) ? self::BULK_ACTION_LIMITS[$key] : 50;
    }
    
    /**
     * 获取安全头配置
     */
    public static function get_security_headers() {
        return apply_filters('prs_security_headers', self::SECURITY_HEADERS);
    }
    
    /**
     * 检查是否启用严格模式
     */
    public static function is_strict_mode_enabled() {
        return defined('PRS_STRICT_SECURITY') && PRS_STRICT_SECURITY;
    }
    
    /**
     * 获取敏感操作列表
     */
    public static function get_sensitive_actions() {
        return array(
            'approve_review',
            'reject_review',
            'delete_review',
            'bulk_action',
            'publish_product',
            'change_settings'
        );
    }
    
    /**
     * 检查操作是否为敏感操作
     */
    public static function is_sensitive_action($action) {
        return in_array($action, self::get_sensitive_actions());
    }
    
    /**
     * 获取IP白名单（如果配置了的话）
     */
    public static function get_ip_whitelist() {
        return apply_filters('prs_ip_whitelist', array());
    }
    
    /**
     * 检查IP是否在白名单中
     */
    public static function is_ip_whitelisted($ip) {
        $whitelist = self::get_ip_whitelist();
        if (empty($whitelist)) {
            return true; // 如果没有配置白名单，允许所有IP
        }
        
        return in_array($ip, $whitelist);
    }
    
    /**
     * 获取安全日志配置
     */
    public static function get_log_config() {
        return array(
            'enabled' => true,
            'log_failed_logins' => true,
            'log_permission_denials' => true,
            'log_rate_limit_hits' => true,
            'log_suspicious_activity' => true,
            'retention_days' => 30
        );
    }
    
    /**
     * 获取密码策略配置
     */
    public static function get_password_policy() {
        return array(
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_special_chars' => false,
            'max_age_days' => 90
        );
    }
    
    /**
     * 获取会话安全配置
     */
    public static function get_session_config() {
        return array(
            'timeout_minutes' => 120,
            'regenerate_on_login' => true,
            'secure_cookies' => is_ssl(),
            'httponly_cookies' => true
        );
    }
}
