# Copyright (C) 2024 Melapress
# This file is distributed under the GPL v3.
msgid ""
msgstr ""
"Project-Id-Version: CAPTCHA 4WP (Premium) 7.6.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/captcha-4wp-premium\n"
"Last-Translator: Melapress <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-09-27T08:49:43+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: advanced-nocaptcha-recaptcha\n"

#. Plugin Name of the plugin
#: advanced-nocaptcha-and-invisible-captcha-pro.php
msgid "CAPTCHA 4WP (Premium)"
msgstr ""

#. Plugin URI of the plugin
#: advanced-nocaptcha-and-invisible-captcha-pro.php
msgid "https://captcha4wp.com/"
msgstr ""

#. Description of the plugin
#: advanced-nocaptcha-and-invisible-captcha-pro.php
msgid "Easily add Google reCAPTCHA to WordPress forms. Upgrade to Premium and gain access to additional features, including hCaptcha and CloudFlare Turnstile integration, CAPTCHA one-click form integration with plugins such as WooCommerce, Contact Form 7, and WP Forms, and many other features."
msgstr ""

#. Author of the plugin
#: advanced-nocaptcha-and-invisible-captcha-pro.php
msgid "Melapress"
msgstr ""

#. Author URI of the plugin
#: advanced-nocaptcha-and-invisible-captcha-pro.php
msgid "https://captcha4wp.com/"
msgstr ""

#: admin/class-c4wp-settings.php:249
msgid "Important:"
msgstr ""

#: admin/class-c4wp-settings.php:250
msgid "To reconfigure the failover now, once you are redirected to the plugin's configuration page click"
msgstr ""

#: admin/class-c4wp-settings.php:251
#: admin/class-c4wp-settings.php:1990
msgid "Reconfigure CAPTCHA integration"
msgstr ""

#: admin/class-c4wp-settings.php:252
msgid "and click"
msgstr ""

#: admin/class-c4wp-settings.php:253
#: admin/class-c4wp-settings.php:1874
#: admin/class-c4wp-settings.php:1892
#: admin/class-c4wp-settings.php:1913
#: admin/class-c4wp-settings.php:1937
#: admin/class-c4wp-settings.php:1948
msgid "Next"
msgstr ""

#: admin/class-c4wp-settings.php:254
msgid "in the wizard until you get to the failover settings."
msgstr ""

#: admin/class-c4wp-settings.php:258
msgid "In the latest version of CAPTCHA 4WP you can configure a failover action for your CAPTCHA check. This means that you can configure the plugin to show a CAPTCHA checkbox or redirect the user when the current v3 reCAPTCHA check fails. Use the buttons below to configure the failover or close this admin notice."
msgstr ""

#: admin/class-c4wp-settings.php:260
msgid "Configure failover action now"
msgstr ""

#: admin/class-c4wp-settings.php:260
#: admin/class-c4wp-settings.php:1398
msgid "I'll configure it later"
msgstr ""

#. translators: link to the settings page with text "Settings page"
#: admin/class-c4wp-settings.php:347
msgid "Use the CAPTCHA configuration wizard to configure CAPTCHA service integration with your website. Once you set up the integration, navigate to the %s page to configure where CAPTCHA should be added on your website, whitelist IP addresses, and other settings."
msgstr ""

#: admin/class-c4wp-settings.php:348
msgid "Settings & placements"
msgstr ""

#: admin/class-c4wp-settings.php:367
msgid "In this page you can configure where on your website you want to add the CAPTCHA check as well as which forms you wish to enable location rules on."
msgstr ""

#: admin/class-c4wp-settings.php:391
msgid "In this page you can configure several settings, such as whitelisting IP addresses, excluding logged in users from CAPTCHA checks and more."
msgstr ""

#. translators: expression "very restrictive" in bold
#: admin/class-c4wp-settings.php:439
msgid "Any value above 0.5 is %s."
msgstr ""

#: admin/class-c4wp-settings.php:440
msgid "very restrictive"
msgstr ""

#: admin/class-c4wp-settings.php:442
msgid "This means that you might end up locked out from your website. Therefore test this on a staging website website beforehand."
msgstr ""

#: admin/class-c4wp-settings.php:444
msgid "You can add a CAPTCHA check to the below list of pages on WordPress."
msgstr ""

#: admin/class-c4wp-settings.php:445
msgid "Use the setting below to select the language of the text used in the CAPTCHA text."
msgstr ""

#. translators:link to upgrade page
#: admin/class-c4wp-settings.php:450
msgid "To add CAPTCHA checks to WooCommerce, Contact Form 7, BuddyPress and other forms created by third party plugins you need to %s"
msgstr ""

#: admin/class-c4wp-settings.php:451
msgid "upgrade to Premium"
msgstr ""

#: admin/class-c4wp-settings.php:453
msgid " In the Premium edition you can configure the plugin to automatically detect the language settings of the visitor's and use that language."
msgstr ""

#: admin/class-c4wp-settings.php:456
msgid "Comments form"
msgstr ""

#: admin/class-c4wp-settings.php:459
msgid "(Includes WooCommerce Reviews)"
msgstr ""

#: admin/class-c4wp-settings.php:463
msgid "(Incompatible with Jetpack comments)"
msgstr ""

#: admin/class-c4wp-settings.php:472
msgid "Step 1: Select the type of CAPTCHA you want to use on your website."
msgstr ""

#: admin/class-c4wp-settings.php:477
msgid "reCAPTCHA version"
msgstr ""

#: admin/class-c4wp-settings.php:483
msgid "Google reCAPTCHA Version 2 (Users have to check the \"I’m not a robot” checkbox)"
msgstr ""

#: admin/class-c4wp-settings.php:484
msgid "Google reCAPTCHA 2 (No user interaction needed, however, if traffic is suspicious, users are asked to solve a CAPTCHA)"
msgstr ""

#: admin/class-c4wp-settings.php:485
msgid "Google reCAPTCHA 3 (verify request with a score without user interaction)"
msgstr ""

#: admin/class-c4wp-settings.php:493
msgid "Step 2: Specify the Site & Secret keys"
msgstr ""

#. translators:link to help page
#: admin/class-c4wp-settings.php:504
msgid "To utilize the Google reCAPTCHA service on your website you need to get a Site and Secret key. If you do not have these keys yet, you can option them for free by registering to the Google reCAPTCHA service. Refer to the document %s for a step by step explanation of how to get these keys."
msgstr ""

#: admin/class-c4wp-settings.php:505
#: admin/class-c4wp-settings.php:1325
#: admin/class-c4wp-settings.php:1330
msgid "how to get the Google reCAPTCHA keys"
msgstr ""

#: admin/class-c4wp-settings.php:511
msgid "Site Key"
msgstr ""

#: admin/class-c4wp-settings.php:517
msgid "Secret Key"
msgstr ""

#: admin/class-c4wp-settings.php:525
#: admin/class-c4wp-settings.php:781
msgid "Key Validation"
msgstr ""

#: admin/class-c4wp-settings.php:526
msgid "Once you enter the correct Site key above, the CAPTCHA method you want to use on your website will appear below. If the key is incorrect you will instead see an error. If you see an error make sure the CAPTCHA version, website domain and the Site keys match. Before the plugin verifies your secret key, it needs a response to send to your CAPTCHA provider. If needed, please interact with/complete the CAPTCHA challenge below if presented to you to proceed."
msgstr ""

#: admin/class-c4wp-settings.php:534
msgid "Optional settings: Fine-tune CAPTCHA to your requirements"
msgstr ""

#: admin/class-c4wp-settings.php:544
msgid "Use the below settings to configure and fine-tune CAPTCHA to your requirements. All the below settings are optional and with them you can configure different aspects of the CAPTCHA checks on your website, such as look and feel and also sensitivity."
msgstr ""

#: admin/class-c4wp-settings.php:550
msgid "Captcha Score"
msgstr ""

#: admin/class-c4wp-settings.php:556
msgid "Use this setting to specify sensitivity of the CAPTCHA check. The closer to 1 the more sensitive the CAPTCHA check will be, which also means more traffic will be marked as spam. This option is only available for reCAPTCHA v3."
msgstr ""

#: admin/class-c4wp-settings.php:559
msgid "Load ReCAPTCHA v3 scripts on:"
msgstr ""

#: admin/class-c4wp-settings.php:565
msgid "All Pages"
msgstr ""

#: admin/class-c4wp-settings.php:566
msgid "Form Pages"
msgstr ""

#. translators: %s: link to article.
#: admin/class-c4wp-settings.php:570
msgid "By default, the ReCAPTCHA service can only assess user behavior via the scripts loaded on the form pages. However, when using V3, you can configure it to load on all pages. This allows ReCAPTCHA to get a better context of the traffic so that it can better determine what is spam and what is not. When ReCAPTCHA V3 is configured to load on all pages, it will never prompt or otherwise interrupt users on non-form pages. Note that the ReCAPTCHA V3 check still needs to be included in the form(s). Refer to the %1$s for more information on how to add CAPTCHA checks to your forms."
msgstr ""

#: admin/class-c4wp-settings.php:572
#: admin/class-c4wp-settings.php:1863
#: extensions/class-c4wp-pro-feature.php:114
#: extensions/class-c4wp-pro-feature.php:129
#: extensions/class-c4wp-pro-feature.php:144
#: extensions/class-c4wp-pro-feature.php:159
#: extensions/class-c4wp-pro-feature.php:174
#: extensions/class-c4wp-pro-feature.php:189
#: extensions/class-c4wp-pro-feature.php:204
#: extensions/class-c4wp-pro-feature.php:219
#: extensions/third-party/class-c4wp-mailchimp4wp-captcha.php:90
msgid "CAPTCHA 4WP knowledge base"
msgstr ""

#: admin/class-c4wp-settings.php:577
msgid "CAPTCHA language"
msgstr ""

#: admin/class-c4wp-settings.php:583
msgid "Select a language"
msgstr ""

#: admin/class-c4wp-settings.php:594
#: admin/class-c4wp-settings.php:649
msgid "Arabic"
msgstr ""

#: admin/class-c4wp-settings.php:595
msgid "Bulgarian"
msgstr ""

#: admin/class-c4wp-settings.php:596
msgid "Catalan"
msgstr ""

#: admin/class-c4wp-settings.php:597
#: admin/class-c4wp-settings.php:669
msgid "Chinese (Simplified)"
msgstr ""

#: admin/class-c4wp-settings.php:598
msgid "Croatian"
msgstr ""

#: admin/class-c4wp-settings.php:599
msgid "Czech"
msgstr ""

#: admin/class-c4wp-settings.php:600
msgid "Danish"
msgstr ""

#: admin/class-c4wp-settings.php:601
#: admin/class-c4wp-settings.php:659
msgid "Dutch"
msgstr ""

#: admin/class-c4wp-settings.php:602
msgid "English (UK)"
msgstr ""

#: admin/class-c4wp-settings.php:603
#: admin/class-c4wp-settings.php:651
msgid "English (US)"
msgstr ""

#: admin/class-c4wp-settings.php:604
msgid "Filipino"
msgstr ""

#: admin/class-c4wp-settings.php:605
msgid "Finnish"
msgstr ""

#: admin/class-c4wp-settings.php:606
#: admin/class-c4wp-settings.php:654
msgid "French"
msgstr ""

#: admin/class-c4wp-settings.php:607
msgid "French (Canadian)"
msgstr ""

#: admin/class-c4wp-settings.php:608
#: admin/class-c4wp-settings.php:650
msgid "German"
msgstr ""

#: admin/class-c4wp-settings.php:609
msgid "German (Austria)"
msgstr ""

#: admin/class-c4wp-settings.php:610
msgid "German (Switzerland)"
msgstr ""

#: admin/class-c4wp-settings.php:611
msgid "Greek"
msgstr ""

#: admin/class-c4wp-settings.php:612
msgid "Hebrew"
msgstr ""

#: admin/class-c4wp-settings.php:613
msgid "Hindi"
msgstr ""

#: admin/class-c4wp-settings.php:614
msgid "Hungarian"
msgstr ""

#: admin/class-c4wp-settings.php:615
#: admin/class-c4wp-settings.php:655
msgid "Indonesian"
msgstr ""

#: admin/class-c4wp-settings.php:616
#: admin/class-c4wp-settings.php:656
msgid "Italian"
msgstr ""

#: admin/class-c4wp-settings.php:617
#: admin/class-c4wp-settings.php:657
msgid "Japanese"
msgstr ""

#: admin/class-c4wp-settings.php:618
#: admin/class-c4wp-settings.php:658
msgid "Korean"
msgstr ""

#: admin/class-c4wp-settings.php:619
msgid "Latvian"
msgstr ""

#: admin/class-c4wp-settings.php:620
msgid "Lithuanian"
msgstr ""

#: admin/class-c4wp-settings.php:621
msgid "Norwegian"
msgstr ""

#: admin/class-c4wp-settings.php:622
#: admin/class-c4wp-settings.php:653
msgid "Persian"
msgstr ""

#: admin/class-c4wp-settings.php:623
#: admin/class-c4wp-settings.php:660
msgid "Polish"
msgstr ""

#: admin/class-c4wp-settings.php:624
#: admin/class-c4wp-settings.php:661
msgid "Portuguese"
msgstr ""

#: admin/class-c4wp-settings.php:625
#: admin/class-c4wp-settings.php:662
msgid "Portuguese (Brazil)"
msgstr ""

#: admin/class-c4wp-settings.php:626
msgid "Portuguese (Portugal)"
msgstr ""

#: admin/class-c4wp-settings.php:627
msgid "Romanian"
msgstr ""

#: admin/class-c4wp-settings.php:628
#: admin/class-c4wp-settings.php:663
msgid "Russian"
msgstr ""

#: admin/class-c4wp-settings.php:629
msgid "Serbian"
msgstr ""

#: admin/class-c4wp-settings.php:630
msgid "Slovak"
msgstr ""

#: admin/class-c4wp-settings.php:631
msgid "Slovenian"
msgstr ""

#: admin/class-c4wp-settings.php:632
#: admin/class-c4wp-settings.php:652
msgid "Spanish"
msgstr ""

#: admin/class-c4wp-settings.php:633
msgid "Spanish (Latin America)"
msgstr ""

#: admin/class-c4wp-settings.php:634
msgid "Swedish"
msgstr ""

#: admin/class-c4wp-settings.php:635
msgid "Thai"
msgstr ""

#: admin/class-c4wp-settings.php:636
#: admin/class-c4wp-settings.php:665
msgid "Turkish"
msgstr ""

#: admin/class-c4wp-settings.php:637
#: admin/class-c4wp-settings.php:666
msgid "Ukrainian"
msgstr ""

#: admin/class-c4wp-settings.php:638
msgid "Vietnamese"
msgstr ""

#: admin/class-c4wp-settings.php:648
msgid "Arabic (Egypt)"
msgstr ""

#: admin/class-c4wp-settings.php:664
msgid "Klingon"
msgstr ""

#: admin/class-c4wp-settings.php:667
msgid "Ukrainian (Ukraine)"
msgstr ""

#: admin/class-c4wp-settings.php:668
msgid "Chinese"
msgstr ""

#: admin/class-c4wp-settings.php:670
msgid "Chinese (Traditional)"
msgstr ""

#: admin/class-c4wp-settings.php:674
msgid "Error message"
msgstr ""

#: admin/class-c4wp-settings.php:676
msgid "Please solve the CAPTCHA to proceed"
msgstr ""

#: admin/class-c4wp-settings.php:677
msgid "Specify the message you want to show users who do not complete the CAPTCHA."
msgstr ""

#: admin/class-c4wp-settings.php:680
msgid "Theme"
msgstr ""

#: admin/class-c4wp-settings.php:686
msgid "Light"
msgstr ""

#: admin/class-c4wp-settings.php:687
msgid "Dark"
msgstr ""

#: admin/class-c4wp-settings.php:691
msgid "Size"
msgstr ""

#: admin/class-c4wp-settings.php:697
msgid "Normal"
msgstr ""

#: admin/class-c4wp-settings.php:698
msgid "Compact"
msgstr ""

#: admin/class-c4wp-settings.php:702
#: admin/class-c4wp-settings.php:715
msgid "Badge"
msgstr ""

#: admin/class-c4wp-settings.php:708
#: admin/class-c4wp-settings.php:721
msgid "Bottom Right"
msgstr ""

#: admin/class-c4wp-settings.php:709
#: admin/class-c4wp-settings.php:722
msgid "Bottom Left"
msgstr ""

#: admin/class-c4wp-settings.php:710
msgid "Inline"
msgstr ""

#: admin/class-c4wp-settings.php:712
msgid "Badge shows for invisible captcha"
msgstr ""

#: admin/class-c4wp-settings.php:724
msgid "Badge shows for invisible captcha v3"
msgstr ""

#: admin/class-c4wp-settings.php:727
msgid "reCAPTCHA domain"
msgstr ""

#: admin/class-c4wp-settings.php:737
msgid "Use this setting to change the domain if Google is not accessible or blocked."
msgstr ""

#: admin/class-c4wp-settings.php:740
msgid "Remove CSS"
msgstr ""

#: admin/class-c4wp-settings.php:744
msgid "Remove this plugin's css from login page?"
msgstr ""

#: admin/class-c4wp-settings.php:745
msgid "This css increase login page width to adjust with Captcha width."
msgstr ""

#: admin/class-c4wp-settings.php:749
msgid "v3 failover action:"
msgstr ""

#: admin/class-c4wp-settings.php:755
msgid "Show a v2 CAPTCHA checkbox"
msgstr ""

#: admin/class-c4wp-settings.php:756
msgid "Redirect the website visitor to a URL"
msgstr ""

#: admin/class-c4wp-settings.php:757
msgid "Take no action"
msgstr ""

#: admin/class-c4wp-settings.php:761
msgid "Redirect URL"
msgstr ""

#: admin/class-c4wp-settings.php:767
msgid "v2 Site key:"
msgstr ""

#: admin/class-c4wp-settings.php:773
msgid "v2 Secret key:"
msgstr ""

#: admin/class-c4wp-settings.php:782
msgid "Once you enter the correct Site and Secret keys above, the CAPTCHA method you want to use on your website will appear below. If the keys are incorrect you will instead see an error. If you see an error make sure the CAPTCHA version, website domain and both keys match."
msgstr ""

#: admin/class-c4wp-settings.php:793
msgid "Select where on your website you want to add the CAPTCHA check"
msgstr ""

#: admin/class-c4wp-settings.php:807
msgid "WordPress pages"
msgstr ""

#: admin/class-c4wp-settings.php:813
msgid "Login form"
msgstr ""

#: admin/class-c4wp-settings.php:814
msgid "Registration form"
msgstr ""

#: admin/class-c4wp-settings.php:815
msgid "Reset password form"
msgstr ""

#: admin/class-c4wp-settings.php:816
msgid "Lost password form"
msgstr ""

#: admin/class-c4wp-settings.php:848
msgid "Multisite pages"
msgstr ""

#: admin/class-c4wp-settings.php:854
msgid "Multisite Signup form"
msgstr ""

#: admin/class-c4wp-settings.php:872
#: admin/class-c4wp-settings.php:1576
#: admin/templates/upgrade/index.php:24
#: admin/templates/upgrade/index.php:329
msgid "Upgrade to Premium"
msgstr ""

#: admin/class-c4wp-settings.php:873
msgid "Find out more"
msgstr ""

#: admin/class-c4wp-settings.php:876
msgid "Checkout and login pages on WooCommerce stores"
msgstr ""

#: admin/class-c4wp-settings.php:877
msgid "Contact Form 7, Gravity Forms, WPForms, MailChimp 4 WordPress forms"
msgstr ""

#: admin/class-c4wp-settings.php:878
msgid "BuddyPress and bbPress"
msgstr ""

#: admin/class-c4wp-settings.php:879
msgid "And others"
msgstr ""

#. translators:field type
#: admin/class-c4wp-settings.php:1072
msgid "No hook defined for %s"
msgstr ""

#: admin/class-c4wp-settings.php:1185
#: admin/class-c4wp-settings.php:1186
#: admin/class-c4wp-settings.php:1237
#: admin/class-c4wp-settings.php:1238
#: admin/class-c4wp-settings.php:1396
msgid "CAPTCHA Configuration"
msgstr ""

#: admin/class-c4wp-settings.php:1185
#: admin/class-c4wp-settings.php:1237
#: extensions/third-party/class-c4wp-gravityforms-captcha.php:44
#: extensions/third-party/class-c4wp-gravityforms-captcha.php:81
#: extensions/third-party/class-c4wp-gravityforms-captcha.php:143
#: extensions/third-party/class-c4wp-wpforms-captcha.php:35
#: extensions/third-party/elementor/class-elementor-captcha-handler.php:44
msgid "CAPTCHA 4WP"
msgstr ""

#: admin/class-c4wp-settings.php:1187
#: admin/class-c4wp-settings.php:1239
msgid "CAPTCHA 4WP Forms"
msgstr ""

#: admin/class-c4wp-settings.php:1187
#: admin/class-c4wp-settings.php:1239
msgid "Form Placements"
msgstr ""

#: admin/class-c4wp-settings.php:1188
#: admin/class-c4wp-settings.php:1240
msgid "CAPTCHA 4WP Settings"
msgstr ""

#: admin/class-c4wp-settings.php:1188
#: admin/class-c4wp-settings.php:1240
#: admin/class-c4wp-settings.php:1639
msgid "Settings"
msgstr ""

#: admin/class-c4wp-settings.php:1189
#: admin/class-c4wp-settings.php:1241
msgid "Help & Contact Us"
msgstr ""

#: admin/class-c4wp-settings.php:1192
#: admin/class-c4wp-settings.php:1244
msgid "Premium Features ➤"
msgstr ""

#: admin/class-c4wp-settings.php:1203
#: admin/class-c4wp-settings.php:1255
msgid "Country Blocking Configuration"
msgstr ""

#: admin/class-c4wp-settings.php:1316
msgid "Please supply a valid IP"
msgstr ""

#: admin/class-c4wp-settings.php:1317
msgid "To switch the CAPTCHA method you need to replace the current Site and Secret keys. Do you want to proceed?"
msgstr ""

#: admin/class-c4wp-settings.php:1318
msgid "Confirm change of CAPTCHA integration"
msgstr ""

#: admin/class-c4wp-settings.php:1319
msgid "Confirm removal of CAPTCHA integration"
msgstr ""

#: admin/class-c4wp-settings.php:1320
msgid "This will remove the current CAPTCHA integration, which means all the CAPTCHA checks on your website will stop working. Would you like to proceed?"
msgstr ""

#. translators:link to help page
#: admin/class-c4wp-settings.php:1324
#: admin/class-c4wp-settings.php:1329
msgid "To utilize the Google reCAPTCHA service on your website you need to get a Site and Secret key. If you do not have these keys yet, you can get them for free by registering to the Google reCAPTCHA service. Refer to the document %s for a step by step explanation of how to get these keys."
msgstr ""

#. translators:link to help page
#: admin/class-c4wp-settings.php:1334
msgid "To utilize the hCaptcha service on your website you need to get a Site and Secret key. If you do not have these keys yet, you can get them for free by registering to the hCaptcha service. Refer to the document %s for a step by step explanation of how to get these keys."
msgstr ""

#: admin/class-c4wp-settings.php:1335
msgid "how to get the hCaptcha keys"
msgstr ""

#. translators:link to help page
#: admin/class-c4wp-settings.php:1339
msgid "To utilize the Cloudflare Turnstile service on your website you need to get a Site and Secret key. If you do not have these keys yet, you can get them for free by registering to the Cloudflare Turnstile service. Refer to the document %s for a step by step explanation of how to get these keys."
msgstr ""

#: admin/class-c4wp-settings.php:1340
msgid "how to get the Cloudflare Turnstile keys"
msgstr ""

#: admin/class-c4wp-settings.php:1364
msgid "Setup CAPTCHA 4WP per child-site instead?"
msgstr ""

#: admin/class-c4wp-settings.php:1365
msgid "CAPTCHA 4WP is currently configured as a network-wide plugin. If you prefer to allow each child site administrator to enable, disable and control CAPTCHA, please deactivate the plugin from the network dashboard and then activate it on the desired child-site(s)."
msgstr ""

#: admin/class-c4wp-settings.php:1368
msgid "Setup CAPTCHA 4WP globally instead?"
msgstr ""

#: admin/class-c4wp-settings.php:1369
msgid "CAPTCHA 4WP is currently configured on a per child-site based. If you prefer to setup CAPTCHA 4WP globally on your multisite network, and configure it for all the sites from the network dashboard, deactivate it from all the child-sites where it is currently activated, and activate the plugin from the network dashboard."
msgstr ""

#: admin/class-c4wp-settings.php:1376
msgid "Dismiss notice"
msgstr ""

#: admin/class-c4wp-settings.php:1397
msgid "Configure it now"
msgstr ""

#: admin/class-c4wp-settings.php:1413
msgid "CAPTCHA integration & configuration"
msgstr ""

#: admin/class-c4wp-settings.php:1415
msgid "Country blocking configuration"
msgstr ""

#: admin/class-c4wp-settings.php:1417
msgid "CAPTCHA Settings"
msgstr ""

#: admin/class-c4wp-settings.php:1419
msgid "CAPTCHA Placements"
msgstr ""

#: admin/class-c4wp-settings.php:1429
msgid "Confirmation"
msgstr ""

#: admin/class-c4wp-settings.php:1430
msgid "Are you sure to execute this action?"
msgstr ""

#: admin/class-c4wp-settings.php:1483
msgid "Help Captcha 4WP improve."
msgstr ""

#: admin/class-c4wp-settings.php:1484
msgid "You can help us improve the plugin by opting in to share non-sensitive data about the plugin usage. The technical data will be shared over a secure channel. Activity log data will never be shared. When you opt-in, you also subscribe to our announcement and newsletter (you can opt-out at any time). If you would rather not opt-in, we will not collect any data."
msgstr ""

#: admin/class-c4wp-settings.php:1484
msgid "Read more about what data we collect and how."
msgstr ""

#: admin/class-c4wp-settings.php:1486
msgid "Sure, opt-in"
msgstr ""

#: admin/class-c4wp-settings.php:1487
msgid "No, thank you"
msgstr ""

#: admin/class-c4wp-settings.php:1524
msgid "The site key that you have entered is invalid. Please try again."
msgstr ""

#: admin/class-c4wp-settings.php:1527
msgid "The secret key that you have entered is invalid. Please try again."
msgstr ""

#: admin/class-c4wp-settings.php:1530
msgid "CAPTCHA settings"
msgstr ""

#: admin/class-c4wp-settings.php:1534
msgid "CAPTCHA configuration"
msgstr ""

#: admin/class-c4wp-settings.php:1536
msgid " updated"
msgstr ""

#: admin/class-c4wp-settings.php:1560
msgid "Upgrade to Premium and benefit from the below features:"
msgstr ""

#: admin/class-c4wp-settings.php:1565
msgid "Add CAPTCHA antispam checks also from hCaptcha and Cloudflare Turnstile."
msgstr ""

#: admin/class-c4wp-settings.php:1566
#: admin/templates/upgrade/index.php:170
msgid "Use the language that your website viewers understand"
msgstr ""

#: admin/class-c4wp-settings.php:1567
#: admin/templates/upgrade/index.php:181
msgid "Spam protection for your WooCommerce stores"
msgstr ""

#: admin/class-c4wp-settings.php:1568
#: admin/templates/upgrade/index.php:192
msgid "Specify where to put the CAPTCHA test on WooCommerce checkout page"
msgstr ""

#: admin/class-c4wp-settings.php:1569
msgid "One-click spam protection for forms built with Contact Form 7, Gravity Forms, WPForms & MailChimp for WordPress"
msgstr ""

#: admin/class-c4wp-settings.php:1570
msgid "CAPTCHA tests & spam protection for BuddyPress, bbPress & other third party plugins"
msgstr ""

#: admin/class-c4wp-settings.php:1571
#: admin/templates/upgrade/index.php:261
msgid "Add CAPTCHA to any type of form, even PHP forms"
msgstr ""

#: admin/class-c4wp-settings.php:1572
msgid "Boost login security, add CAPTCHA tests only failed logins"
msgstr ""

#: admin/class-c4wp-settings.php:1573
msgid "Exempt logged in users, IP addresses and specific URLs from CAPTCHA checks."
msgstr ""

#: admin/class-c4wp-settings.php:1574
#: admin/templates/upgrade/index.php:315
msgid "No Ads!"
msgstr ""

#: admin/class-c4wp-settings.php:1716
msgid "Nonce verification failed."
msgstr ""

#: admin/class-c4wp-settings.php:1756
msgid "Freemius opt choice selected."
msgstr ""

#: admin/class-c4wp-settings.php:1763
msgid "Freemius opt choice not found."
msgstr ""

#: admin/class-c4wp-settings.php:1786
#: admin/class-c4wp-settings.php:2048
msgid "Nonce Verification Failed."
msgstr ""

#. translators:knowledge base link
#: admin/class-c4wp-settings.php:1861
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Contact Form 7, Gravity Forms, Ninja Forms, Everest Forms, Formidable Forms and more."
msgstr ""

#: admin/class-c4wp-settings.php:1871
msgid "Getting started with the CAPTCHA 4WP plugin"
msgstr ""

#: admin/class-c4wp-settings.php:1872
msgid "Thank you for installing the CAPTCHA 4WP plugin. This wizard will help you get started with the plugin so you can configure CAPTCHA and protect your website from spam, and fake registrations and orders."
msgstr ""

#: admin/class-c4wp-settings.php:1875
#: admin/class-c4wp-settings.php:1893
#: admin/class-c4wp-settings.php:1916
#: admin/class-c4wp-settings.php:1939
#: admin/class-c4wp-settings.php:1949
msgid "Cancel"
msgstr ""

#: admin/class-c4wp-settings.php:1878
#: admin/class-c4wp-settings.php:1904
#: admin/class-c4wp-settings.php:1914
#: admin/class-c4wp-settings.php:1938
msgid "Back"
msgstr ""

#: admin/class-c4wp-settings.php:1903
msgid "Proceed to secret key"
msgstr ""

#: admin/class-c4wp-settings.php:1907
msgid "Step 3 - Validation and saving"
msgstr ""

#: admin/class-c4wp-settings.php:1908
msgid "Using the response from your CAPTCHA input, we can validate your security key"
msgstr ""

#: admin/class-c4wp-settings.php:1912
msgid "Validate & proceed"
msgstr ""

#: admin/class-c4wp-settings.php:1915
msgid "Proceed"
msgstr ""

#: admin/class-c4wp-settings.php:1923
msgid "Step 4: Configure a failover action for reCAPTCHA v3 failure"
msgstr ""

#: admin/class-c4wp-settings.php:1924
msgid "reCAPTCHA v3 is fully automated. This means that by default, if the CAPTCHA check fails the website visitor cannot proceed with what they are doing unless you configure a failover action. Use the below setting to configure the failover action."
msgstr ""

#: admin/class-c4wp-settings.php:1927
msgid "Please specify the full URL, including the protocol (HTTP or HTTPS) where you would like the user to be redirected to. For example: "
msgstr ""

#: admin/class-c4wp-settings.php:1928
msgid "To show the v2 reCAPTCHA checkbox you need to specify the Site and Secret keys. Please specify them below:"
msgstr ""

#: admin/class-c4wp-settings.php:1943
msgid "Choose which forms to protect"
msgstr ""

#: admin/class-c4wp-settings.php:1944
msgid "Here you can choose which forms to begin protected with your chosen CAPTCHA method."
msgstr ""

#: admin/class-c4wp-settings.php:1953
msgid "All done - you can now add CAPTCHA checks to your website"
msgstr ""

#: admin/class-c4wp-settings.php:1954
msgid "Now that your chosen CAPTCHA service is fully integrated you can use the optional settings to fine-tune CAPTCHA to your requirements."
msgstr ""

#: admin/class-c4wp-settings.php:1957
msgid "Finish"
msgstr ""

#: admin/class-c4wp-settings.php:1979
msgid "V2 Checkbox"
msgstr ""

#: admin/class-c4wp-settings.php:1980
msgid "V2 Invisible CAPTCHA"
msgstr ""

#: admin/class-c4wp-settings.php:1981
msgid "V3 Invisible CAPTCHA"
msgstr ""

#: admin/class-c4wp-settings.php:1982
msgid "hCaptcha"
msgstr ""

#: admin/class-c4wp-settings.php:1983
msgid "Cloudflare Turnstile"
msgstr ""

#: admin/class-c4wp-settings.php:1990
msgid "Remove CAPTCHA integration"
msgstr ""

#: admin/class-c4wp-settings.php:2000
msgid "CAPTCHA version:"
msgstr ""

#: admin/class-c4wp-settings.php:2001
#: admin/class-c4wp-settings.php:2006
msgid "Site key:"
msgstr ""

#: admin/class-c4wp-settings.php:2002
#: admin/class-c4wp-settings.php:2007
msgid "Secret key:"
msgstr ""

#: admin/class-c4wp-settings.php:2005
#: admin/class-c4wp-settings.php:2009
msgid "Failover action:"
msgstr ""

#: admin/class-c4wp-settings.php:2005
msgid "v2 checkbox"
msgstr ""

#: admin/class-c4wp-settings.php:2009
msgid "Redirect to a URL"
msgstr ""

#: admin/class-c4wp-settings.php:2010
msgid "Failover redirect URL:"
msgstr ""

#: admin/class-c4wp-settings.php:2022
msgid "Configure CAPTCHA integration"
msgstr ""

#: admin/class-c4wp-settings.php:2026
msgid "Configure Google reCAPTCHA integration"
msgstr ""

#: admin/class-c4wp-settings.php:2075
msgid "Override validation response"
msgstr ""

#: admin/class-c4wp-settings.php:2081
msgid "Do no override"
msgstr ""

#: admin/class-c4wp-settings.php:2082
msgid "Return false (failure)"
msgstr ""

#: admin/class-c4wp-settings.php:2083
msgid "Return true (pass)"
msgstr ""

#: admin/class-c4wp-settings.php:2170
msgid "Force submission locale (testing):"
msgstr ""

#: admin/class-c4wp-settings.php:2198
msgid "Disable submit button until CAPTCHA response is provided?"
msgstr ""

#: admin/class-c4wp-settings.php:2208
msgid "When using a visible CAPTCHA method that requires users' interaction, should the plugin disable the submit button until the CAPTCHA challenge is completed?"
msgstr ""

#: admin/class-c4wp-settings.php:2212
msgid "Disable submit button"
msgstr ""

#: admin/class-c4wp-settings.php:2226
msgid "Should CAPTCHA 4WP pass or fail a submission if no CAPTCHA field is found?"
msgstr ""

#: admin/class-c4wp-settings.php:2236
msgid "If a form is passed through our plugin for verification and no CAPTCHA field is present, you can choose to either allow the submission or return a failure."
msgstr ""

#: admin/class-c4wp-settings.php:2240
msgid "How to handle failed submissions"
msgstr ""

#: admin/class-c4wp-settings.php:2247
msgid "Proceed with submission"
msgstr ""

#: admin/class-c4wp-settings.php:2248
msgid "Fail submission"
msgstr ""

#: admin/class-c4wp-settings.php:2259
msgid "Load scripts inline or via file?"
msgstr ""

#: admin/class-c4wp-settings.php:2269
msgid "By default the plugin loads its JS inline within wp_footer, would you like to switch to loading via a JS file instead? In can lead to increased performance in many instances."
msgstr ""

#: admin/class-c4wp-settings.php:2273
msgid "Script loading method"
msgstr ""

#: admin/class-c4wp-settings.php:2280
msgid "Load inline"
msgstr ""

#: admin/class-c4wp-settings.php:2281
msgid "Load file"
msgstr ""

#: admin/class-c4wp-settings.php:2285
msgid "File loading method"
msgstr ""

#: admin/class-c4wp-settings.php:2292
msgid "Footer"
msgstr ""

#: admin/class-c4wp-settings.php:2293
msgid "Header"
msgstr ""

#: admin/class-c4wp-settings.php:2304
msgid "Do you want delete all plugin data when uninstalling the plugin?"
msgstr ""

#: admin/class-c4wp-settings.php:2308
msgid "Delete data"
msgstr ""

#: admin/templates/help/help.php:23
msgid "Getting Started"
msgstr ""

#: admin/templates/help/help.php:25
msgid "Adding CAPTCHA checks on your website with CAPTCHA 4WP is really easy. All you need to do is:"
msgstr ""

#: admin/templates/help/help.php:27
msgid "Configure the CAPTCHA & Get the API keys"
msgstr ""

#: admin/templates/help/help.php:28
msgid "Configure the CAPTCHA and add specify the keys in the plugin"
msgstr ""

#: admin/templates/help/help.php:29
msgid "Configure on which pages you want to add the CAPTCHA test"
msgstr ""

#: admin/templates/help/help.php:32
msgid "It should only take you a few minutes to get started. Should you encounter any problems or require assistance, you can use any of the following options:"
msgstr ""

#: admin/templates/help/help.php:39
msgid "Plugin Support"
msgstr ""

#: admin/templates/help/help.php:41
msgid "You can post your question on our support forum or send us an email for 1 to 1 support. Email support is provided to both free and premium plugin users."
msgstr ""

#: admin/templates/help/help.php:43
msgid "Free support forum"
msgstr ""

#: admin/templates/help/help.php:44
msgid "Free email support"
msgstr ""

#: admin/templates/help/help.php:51
msgid "Plugin Documentation"
msgstr ""

#: admin/templates/help/help.php:53
msgid "For more technical information about the WP Activity Log plugin please visit the plugin’s knowledge base. Refer to the list of WordPress security events for a complete list of Events and IDs that the plugin uses to keep a log of all the changes in the WordPress activity log."
msgstr ""

#: admin/templates/help/help.php:55
msgid "Knowledge Base"
msgstr ""

#: admin/templates/help/help.php:61
msgid "Rate CAPTCHA 4WP"
msgstr ""

#: admin/templates/help/help.php:63
msgid "We work really hard to deliver a plugin that enables you to add CAPTCHA checks and tests on your WordPress website to protect it against spam bots and other automated malicious attacks. It takes thousands of man-hours every year and an endless amount of dedication to research, develop and maintain the free edition of CAPTCHA 4WP. If you like what you see, and find CAPTCHA 4WP useful we ask you nothing more than to please rate our plugin. We appreciate every star!"
msgstr ""

#: admin/templates/help/help.php:65
msgid "Rate plugin"
msgstr ""

#: admin/templates/help/index.php:22
msgid "Help"
msgstr ""

#: admin/templates/help/index.php:23
msgid "System Info"
msgstr ""

#: admin/templates/help/sidebar.php:17
msgid "Other plugins developed by us:"
msgstr ""

#: admin/templates/help/sidebar.php:25
msgid "Keep a log of users and under the hood site activity."
msgstr ""

#: admin/templates/help/sidebar.php:27
#: admin/templates/help/sidebar.php:40
#: admin/templates/help/sidebar.php:53
msgid "Discover plugin"
msgstr ""

#: admin/templates/help/sidebar.php:38
msgid "Add an extra layer of security to your login pages with 2FA & require your users to use it."
msgstr ""

#: admin/templates/help/sidebar.php:51
msgid "Easily implement login and password policies for your WordPress users."
msgstr ""

#: admin/templates/help/system-info.php:22
msgid "System information"
msgstr ""

#: admin/templates/upgrade/index.php:21
msgid "Add antispam protection to block bots and allow real humans to interact with your website. Add CAPTCHA from services such as Google reCAPTCHA, hCaptcha and Cloudflare Turnstile to any form on your website such as comments forms, login pages and checkout forms."
msgstr ""

#: admin/templates/upgrade/index.php:22
msgid "Get started with just a few clicks with CAPTCHA 4WP, the only plugin that supports multiple CAPTCHA services. Upgrade today and benefit from numerous features, some of which are listed below."
msgstr ""

#: admin/templates/upgrade/index.php:36
msgid "Premium"
msgstr ""

#: admin/templates/upgrade/index.php:39
msgid "Free"
msgstr ""

#: admin/templates/upgrade/index.php:44
msgid "Add CAPTCHA checks from Google reCAPTCHA"
msgstr ""

#: admin/templates/upgrade/index.php:44
msgid "Add CAPTCHA antispam checks on any website from Google reCAPTCHA."
msgstr ""

#: admin/templates/upgrade/index.php:55
msgid "Add CAPTCHA checks from other services"
msgstr ""

#: admin/templates/upgrade/index.php:55
msgid "Add CAPTCHA antispam checks on any website from hCaptcha and Cloudflare Turnstile."
msgstr ""

#: admin/templates/upgrade/index.php:67
msgid "Email support and forum access"
msgstr ""

#: admin/templates/upgrade/index.php:67
msgid "We stand behind all of our products with world-class support and a team of professionals who absolutely care."
msgstr ""

#: admin/templates/upgrade/index.php:78
msgid "Easy to set up and use"
msgstr ""

#: admin/templates/upgrade/index.php:78
msgid "Set up CAPTCHA in minutes and add it to virtually any form you want at the click of a button."
msgstr ""

#: admin/templates/upgrade/index.php:90
msgid "Choose from different types of tests"
msgstr ""

#: admin/templates/upgrade/index.php:90
msgid "With support for multiple CAPTCHA methods available straight out of the box, you can choose the one that best fits your requirements."
msgstr ""

#: admin/templates/upgrade/index.php:101
msgid "WordPress login and comments spam protection"
msgstr ""

#: admin/templates/upgrade/index.php:101
msgid "Easily add CAPTCHA to your WordPress login page and comments section to protect your website from spam."
msgstr ""

#: admin/templates/upgrade/index.php:112
msgid "WordPress registration and lost password/reset form protection"
msgstr ""

#: admin/templates/upgrade/index.php:112
msgid "Easily add CAPTCHA to protect your website from spam and fake users registration and lost password/reset pages from spam."
msgstr ""

#: admin/templates/upgrade/index.php:123
msgid "Use CAPTCHA in any country"
msgstr ""

#: admin/templates/upgrade/index.php:123
msgid "Select from different domains to prevent service outages due to domain restrictions."
msgstr ""

#: admin/templates/upgrade/index.php:134
msgid "Set CAPTCHA passmark score"
msgstr ""

#: admin/templates/upgrade/index.php:134
msgid "Fine-tune CAPTCHA tests in real-time by setting the passmark score to avoid false positives."
msgstr ""

#: admin/templates/upgrade/index.php:145
msgid "Configurable CAPTCHA language"
msgstr ""

#: admin/templates/upgrade/index.php:145
msgid "Choose from multiple CAPTCHA text languages."
msgstr ""

#: admin/templates/upgrade/index.php:158
msgid "Failover check for automated CAPTCHA"
msgstr ""

#: admin/templates/upgrade/index.php:158
msgid "Configure CAPTCHA failover to eliminate false positives and ensure customers’ journeys are not disrupted. Choose from multiple failover options, depending on your requirements."
msgstr ""

#: admin/templates/upgrade/index.php:170
msgid "Avoid confusion and configure the plugin to automatically change the CAPTCHA text language to match that of the visitor."
msgstr ""

#: admin/templates/upgrade/index.php:181
msgid "Add CAPTCHA to WooCommerce forms such as the login page and checkout at the click of a mouse."
msgstr ""

#: admin/templates/upgrade/index.php:192
msgid "Personalize your customers' experience and meet your branding requirements."
msgstr ""

#: admin/templates/upgrade/index.php:203
msgid "1-click Contact Form 7 spam protection"
msgstr ""

#: admin/templates/upgrade/index.php:203
msgid "Add CAPTCHA to any Contact Form 7 form at the click of a button. No shortcodes required."
msgstr ""

#: admin/templates/upgrade/index.php:215
msgid "1-click spam protection for forms built with Gravity Forms"
msgstr ""

#: admin/templates/upgrade/index.php:215
msgid "Add CAPTCHA to any form built with Gravity Forms at the click of a button. No shortcodes required."
msgstr ""

#: admin/templates/upgrade/index.php:227
msgid "1-click spam protection for forms built with WPForms"
msgstr ""

#: admin/templates/upgrade/index.php:227
msgid "Add CAPTCHA to any form built with WPForms at the click of a button. No shortcodes required."
msgstr ""

#: admin/templates/upgrade/index.php:239
msgid "1-click spam protection for Mailchimp for WordPress forms"
msgstr ""

#: admin/templates/upgrade/index.php:239
msgid "Add CAPTCHA to any MC4WP form at the click of a button. No shortcodes required."
msgstr ""

#: admin/templates/upgrade/index.php:250
msgid "CAPTCHA spam protection for BuddyPress, bbPress, & many other third-party plugins"
msgstr ""

#: admin/templates/upgrade/index.php:250
msgid "Out of the box support for many popular third-party plugins."
msgstr ""

#: admin/templates/upgrade/index.php:261
msgid "Use the plugin's shortcode or code snippet to add CAPTCHA tests to any type of forms, even non-WordPress PHP forms."
msgstr ""

#: admin/templates/upgrade/index.php:272
msgid "Strike a balance between security and usability"
msgstr ""

#: admin/templates/upgrade/index.php:272
msgid "Request CAPTCHA on failed logins only for an improved user experience that doesn't compromise security."
msgstr ""

#: admin/templates/upgrade/index.php:283
msgid "White-list logged in users"
msgstr ""

#: admin/templates/upgrade/index.php:283
msgid "Logged in users can be trusted, so there is no need for CAPTCHA. You can also configure this by user role."
msgstr ""

#: admin/templates/upgrade/index.php:294
msgid "White-list specific IP addresses"
msgstr ""

#: admin/templates/upgrade/index.php:294
msgid "White-list IPs to ensure automated process & integrations running to your website continue to operate smoothly."
msgstr ""

#: admin/templates/upgrade/index.php:305
msgid "White-list specific URLs"
msgstr ""

#: admin/templates/upgrade/index.php:305
msgid "If you configure CAPTCHA to run invisibly on all pages, use this setting to exclude it from specific URLs."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:79
#: extensions/third-party/class-c4wp-cf7-captcha.php:226
msgid "Add CAPTCHA"
msgstr ""

#: extensions/class-c4wp-pro-feature.php:95
msgid "Multisite User Signup Form"
msgstr ""

#: extensions/class-c4wp-pro-feature.php:99
msgid "Automatically detect and match the visitor's language settings (better user experience)."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:106
msgid "Contact Form 7"
msgstr ""

#. translators:knowlegdebase link
#: extensions/class-c4wp-pro-feature.php:112
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Contact Form 7."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:121
msgid "Elementor Pro"
msgstr ""

#. translators:knowlegdebase link
#: extensions/class-c4wp-pro-feature.php:127
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Elementor."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:136
msgid "Gravity Forms"
msgstr ""

#. translators:knowlegdebase link
#: extensions/class-c4wp-pro-feature.php:142
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Gravity Forms."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:151
msgid "WP Forms"
msgstr ""

#. translators:knowldgetbase link
#: extensions/class-c4wp-pro-feature.php:157
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with WP Forms."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:166
msgid "Ninja Forms"
msgstr ""

#. translators:knowldgetbase link
#: extensions/class-c4wp-pro-feature.php:172
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Ninja Forms."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:181
msgid "Everest Forms"
msgstr ""

#. translators:knowldgetbase link
#: extensions/class-c4wp-pro-feature.php:187
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Everest Forms."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:196
msgid "Fluent Forms"
msgstr ""

#. translators:knowldgetbase link
#: extensions/class-c4wp-pro-feature.php:202
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Fluent Forms."
msgstr ""

#: extensions/class-c4wp-pro-feature.php:211
msgid "Formidable Forms"
msgstr ""

#. translators:knowldgetbase link
#: extensions/class-c4wp-pro-feature.php:217
msgid "Please refer to the %1$s for more information on how to add CAPTCHA checks from our plugin on forms created with Formidable Forms."
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:356
msgid "Do you want a CAPTCHA test on the login page only when there are failed logins?"
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:366
#: extensions/hide-captcha/class-c4wp-hide-captcha.php:217
msgid "By default the CAPTCHA tests are always active. However, you can disable CAPTCHA tests for logged in users, or for users with a specific user role."
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:370
msgid "Show CAPTCHA test only when there are failed logins"
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:378
msgid "Number of failed logins required to trigger CAPTCHA tests"
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:389
msgid "Specify how often should the plugin flush the list of IP addresses"
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:395
msgid "Number of days can be between 1 and 10 days."
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:396
msgid "Every"
msgstr ""

#: extensions/failed-logins/class-c4wp-failed-logins-captcha.php:397
msgid "days."
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:207
msgid "Do you want to disable CAPTCHA tests for logged in users?"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:221
msgid "Disable CAPTCHA tests for logged in users"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:228
msgid "No"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:229
msgid "Remove CAPTCHA tests for all logged in users"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:230
msgid "Remove CAPTCHA tests for users with these user roles"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:248
msgid "Do you want to disable CAPTCHA tests for some IP addresses?"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:258
msgid "If you do not want any CAPTCHA tests from traffic coming from specific IP addresses, add these IP addresses in the option above."
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:262
msgid "No CAPTCHA tests for these IP addresses"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:282
msgid "Do you want to remove reCAPTCHA v3 tests on some specific website pages / URLs?"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:292
msgid "When using reCAPTCHA v3 you can configure it to work on all pages. This is very useful for CAPTCHA itself to learn about the type of traffic / spam traffic your website receives. If you are using reCAPTCHA v3 and would like to exclude reCAPTCHA v3 from specific URLs, use the below setting. You should only specify the path of the page / URL, without the domain."
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:293
msgid "For example"
msgstr ""

#: extensions/hide-captcha/class-c4wp-hide-captcha.php:297
msgid "Remove CAPTCHA from these web pages:"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:821
#: extensions/hide-captcha/class-geo-blocking.php:841
msgid "Unable to proceed"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:823
#: extensions/hide-captcha/class-geo-blocking.php:843
msgid "ERROR"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:861
msgid ""
"When a comment contains any of these words in its content, author name, URL, email, IP address, or browser’s user agent string, it will be put in the Trash. One word or IP address per line. It will match inside words, so “press” will match “WordPress”.\n"
"\n"
"\t\t"
msgstr ""

#. translators:link to upgrade page
#: extensions/hide-captcha/class-geo-blocking.php:869
msgid "This setting and more can be adjusted in your %s"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:870
msgid "Discussion settings"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:887
msgid "Do you want block/allow protected form submissions based on a users location?"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:900
msgid "ISO country code"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:901
msgid "comment form settings"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:906
msgid "Location rule:"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:913
#: extensions/hide-captcha/class-geo-blocking.php:1012
msgid "Do nothing"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:914
msgid "Block submissions from the below countries"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:915
msgid "Allow submissions from the below countries only"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:919
msgid "Applicable Country Codes:"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:931
msgid "Blocked Message"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:934
#: extensions/hide-captcha/class-geo-blocking.php:1023
msgid "Display the following message if a user is blocked due to location rules."
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:936
#: extensions/third-party/woocommerce-blocks/class-woocommerce-block-checkout-init.php:90
msgid "Unable to submit due to location"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:946
msgid "Third Party integrations"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:956
msgid "Here you can enter keys for third party services used in CAPTCHA 4WP."
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:961
msgid "IPLocate API Key:"
msgstr ""

#. translators:link to help page
#: extensions/hide-captcha/class-geo-blocking.php:970
msgid "IP checking is handled by IPLocate.io, please %s to get your own key."
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:971
msgid "click here"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:986
msgid "IMPORTANT:"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:987
msgid "This Geoblocking feature works alongside CAPTCHA. This means that if you enable CAPTCHA on the comments form, users from countries which are allowed to comment will still have to successfully complete the CAPTCHA to submit a comment."
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:992
msgid "Country Codes:"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1006
msgid "Comment Handling:"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1013
msgid "Allow comments from the above countries only"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1014
msgid "Send comments from the above countries to spam"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1015
msgid "Mark comments from the above countries for moderation"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1016
msgid "Block comments from the above countries only and show the below message"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1020
msgid "Comment Blocked Message"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1025
msgid "Your comment was blocked"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1032
msgid "Further protection of your comments form"
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1096
msgid "You can add a country based check to the below list of pages on WordPress."
msgstr ""

#: extensions/hide-captcha/class-geo-blocking.php:1104
msgid "Select where on your website you want to add the location check"
msgstr ""

#: extensions/third-party/class-c4wp-bbpress-captcha.php:97
msgid "bbPress pages"
msgstr ""

#: extensions/third-party/class-c4wp-bbpress-captcha.php:103
msgid "bbPress New topic"
msgstr ""

#: extensions/third-party/class-c4wp-bbpress-captcha.php:104
msgid "bbPress reply to topic"
msgstr ""

#: extensions/third-party/class-c4wp-buddypress-captcha.php:100
msgid "BuddyPress pages"
msgstr ""

#: extensions/third-party/class-c4wp-buddypress-captcha.php:106
msgid "BuddyPress register"
msgstr ""

#: extensions/third-party/class-c4wp-buddypress-captcha.php:107
msgid "BuddyPress comments form"
msgstr ""

#: extensions/third-party/class-c4wp-buddypress-captcha.php:108
msgid "BuddyPress create group"
msgstr ""

#: extensions/third-party/class-c4wp-cf7-captcha.php:245
msgid "Add a recaptcha field to your contact form."
msgstr ""

#: extensions/third-party/class-c4wp-cf7-captcha.php:266
msgid "Insert Tag"
msgstr ""

#: extensions/third-party/class-c4wp-gravityforms-captcha.php:106
msgid "CAPTCHA 4WP field is not editable."
msgstr ""

#: extensions/third-party/class-c4wp-gravityforms-captcha.php:114
#: extensions/third-party/class-c4wp-wpforms-captcha.php:112
#: extensions/third-party/class-c4wp-wpforms-captcha.php:135
#: extensions/third-party/templates/formidable-form-field-preview.php:10
msgid "Please ensure you have setup your CAPTCHA settings prior to use "
msgstr ""

#: extensions/third-party/class-c4wp-gravityforms-captcha.php:114
#: extensions/third-party/class-c4wp-wpforms-captcha.php:112
#: extensions/third-party/class-c4wp-wpforms-captcha.php:135
#: extensions/third-party/templates/formidable-form-field-preview.php:10
msgid "here."
msgstr ""

#. translators: Field label
#: extensions/third-party/class-c4wp-gravityforms-captcha.php:170
msgid "Add a %s field to your form."
msgstr ""

#: extensions/third-party/class-c4wp-mailchimp4wp-captcha.php:82
msgid "Mailchimp 4 WP forms"
msgstr ""

#. translators: %s: link to article.
#: extensions/third-party/class-c4wp-mailchimp4wp-captcha.php:88
msgid "Please refer to the %1$s for more information setting up our plugin with Mailchimp 4 WP."
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:153
msgid "WooCommerce pages"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:159
msgid "WooCommerce Checkout"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:160
msgid "WooCommerce Login"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:161
msgid "WooCommerce Registration page"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:162
msgid "WooCommerce Reset password page"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:163
msgid "WooCommerce Lost password page"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:184
msgid "WooCommerce checkout position"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:191
msgid "Below checkout"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:192
msgid "Above checkout button"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:193
msgid "Above payment selection"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:195
msgid "Choose a location for the captcha input (Does not apply to block based checkout)"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:199
msgid "WooCommerce checkout login"
msgstr ""

#: extensions/third-party/class-c4wp-woocommerce-captcha.php:205
msgid "Remove CAPTCHA from login form if on checkout page?"
msgstr ""

#: extensions/third-party/elementor/class-elementor-captcha-handler.php:61
msgid "Please ensure you have setup your CAPTCHA 4WP settings prior to use "
msgstr ""

#: extensions/third-party/elementor/class-elementor-captcha-handler.php:61
msgid " here."
msgstr ""

#: includes/methods/class-c4wp-method-loader.php:182
msgid "Business or Enterprise"
msgstr ""

#: includes/methods/class-c4wp-method-loader.php:183
msgid "Update my license"
msgstr ""

#: includes/methods/class-c4wp-method-loader.php:184
msgid "Close and update settings"
msgstr ""

#: includes/methods/class-cloudflare.php:62
msgid "Cloudflare Turnstile (users might have to check a checkbox if service thinks it is spam traffic)"
msgstr ""

#: includes/methods/class-hcaptcha.php:63
msgid "hCaptcha (Users have to check the \"I’m not a robot” checkbox)"
msgstr ""
