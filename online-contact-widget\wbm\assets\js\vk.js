import{e as _,f as k,g as o,o as n,h as e,i as p,t,u as l,F as h,j as g,d as v}from"./vendor.js";import{u as y,a as x}from"./useI18N.js";import{A}from"./fetch.js";const $={class:"wbm-hd"},j={key:0,class:"table table-data-list"},B={class:"w20"},D={class:"w30"},L=["href"],N={key:1,class:"wbm-pd-title"},P={class:"wbm-wk"},F={key:1,class:"com-empty-rec"},V={class:"cer-inner"},C=e("svg",{class:"wb-icon wbm-sico-nodata"},[e("use",{"xlink:href":"#wbm-sico-nodata"})],-1),E={__name:"App",setup(G){const{$api:w,$cnf:d}=y(),{wb_e:a}=x(d),i=_(!1),c=_([]),b=_(0),f=()=>{if(!d.wpvk_order_nonce)return;const m={action:"wpvk-order",do:"my-order",_ajax_nonce:d.wpvk_order_nonce};w.getData(m).then(r=>{c.value=r.data.data,b.value=r.data.total,i.value=!0}).catch(r=>{console.log(r)})};return k(()=>{f()}),(m,r)=>(n(),o(h,null,[e("div",$,[e("h1",null,t(l(a)("我的订单")),1)]),i.value&&c.value.length>0?(n(),o("table",j,[e("thead",null,[e("tr",null,[e("th",B,t(l(a)("订单号")),1),e("th",D,t(l(a)("文章")),1),e("th",null,t(l(a)("金额")),1),e("th",null,t(l(a)("日期")),1)])]),e("tbody",null,[(n(!0),o(h,null,g(c.value,(s,I)=>(n(),o("tr",{key:s.order_no},[e("td",null,t(s.order_no),1),e("td",null,[s.detail_url?(n(),o("a",{key:0,class:"wbm-pd-title wbm-link",href:s.detail_url},t(s.name),9,L)):(n(),o("div",N,t(s.name),1))]),e("td",null,t(l(a)("¥"))+t(s.money),1),e("td",null,[e("span",P,t(s.created),1)])]))),128))])])):p("",!0),i.value&&!c.value.length?(n(),o("div",F,[e("div",V,[C,e("p",null,t(l(a)("暂无记录")),1)])])):p("",!0)],64))}},u=v(E);u.config.globalProperties.$api=A;u.config.globalProperties.$cnf=window.wbm_js_cnf||{};u.mount("#wbm-vk");
