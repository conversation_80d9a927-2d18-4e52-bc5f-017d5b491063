.rsssl-modal {

  //.rsssl-modal-header, .components-modal__header {
  //  background-color: var(--rsp-yellow-faded);
  //  padding: var(--rsp-spacing-m) var(--rsp-spacing-m)!important;
  //  //no border radius on bottom
  //  border-bottom-left-radius: 0;
  //  border-bottom-right-radius: 0;
  //  border-bottom: 0;
  //}
  //
  //.components-modal__content {
  //  padding: 0;
  //  &:before {
  //    margin-bottom:initial;
  //  }
  //}

  //.rsssl-header-extension {
  //  background-color: var(--rsp-yellow-faded);
  //  margin: 0;
  //  padding: 0 var(--rsp-spacing-m);
  //  p {
  //    margin: 0;
  //    padding: var(--rsp-spacing-m) 0;
  //    padding-top: 0;
  //  }
  //}

  .rsssl-intro-logo {
    width: 7em;
    position: absolute;
    right: 1.6em;
    height: auto;
    bottom: 0.15em;
    margin: 0;
    padding: 0;
  }
  @media (max-width: 768px) {
    .rsssl-intro-logo {
      display:none;
    }
  }

  .rsssl-ssl-intro-container {
    .rsssl-details {
      display:flex;
      padding: var(--rsp-spacing-xs) var(--rsp-spacing-m);
      gap: var(--rsp-spacing-xs);
      .rsssl-icon {
        min-width:25px;
      }
    }
  }

  .rsssl-modal-footer {
    padding: var(--rsp-spacing-m) var(--rsp-spacing-m);
  }

  /* spinner for React Icons */
  .icon-spin {
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}