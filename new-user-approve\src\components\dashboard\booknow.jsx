import React from 'react';
import { sprintf, __ } from '@wordpress/i18n';

const icons = require.context('../../assets/icons', false, /\.svg$/);
const BookNow = () => {
    let get_advice_icon = icons('./get-advise.svg')
    function booknow(event) {
        window.open('https://objectsws.atlassian.net/servicedesk/customer/portal/3/group/3/create/17', '_blank', 'noopener,noreferrer');
        return null;
        
    }

    let helpImg = (
        <svg width="97" height="79" viewBox="0 0 97 79" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M55.0119 0.0686035H16.0692C7.59874 0.0686035 0.731934 6.93541 0.731934 15.4061V30.8838C0.731934 36.4946 3.7467 41.3988 8.24328 44.0721C8.8254 44.4183 9.18396 45.0431 9.18396 45.7203V54.0917C9.18396 55.1164 10.423 55.6297 11.1477 54.9051L19.2701 46.7827C19.6296 46.4232 20.1173 46.2212 20.6258 46.2212H55.0122C63.4829 46.2212 70.3495 39.3544 70.3495 30.884V15.4062C70.3491 6.93541 63.4825 0.0686035 55.0119 0.0686035Z" fill="#BCD7BB"/>
        <path d="M59.3646 0.698975C59.7724 2.07925 59.9951 3.53907 59.9951 5.05172V20.5294C59.9951 28.9999 53.1283 35.8667 44.6578 35.8667H5.71513C4.20154 35.8667 2.7406 35.6439 1.35938 35.2353C2.46589 38.9781 4.96247 42.1237 8.251 44.0764C8.83049 44.4205 9.18397 45.0458 9.18397 45.7199V54.0913C9.18397 55.116 10.423 55.6293 11.1477 54.9046L19.2701 46.7822C19.6296 46.4227 20.1173 46.2208 20.6258 46.2208H55.0122C63.4829 46.2208 70.3495 39.354 70.3495 30.8835V15.4058C70.3491 8.4478 65.7144 2.57564 59.3646 0.698975Z" fill="#A2C3A1"/>
        <path d="M13.4396 19.5813C14.6235 19.5813 15.5832 18.6215 15.5832 17.4376C15.5832 16.2537 14.6235 15.2939 13.4396 15.2939C12.2556 15.2939 11.2959 16.2537 11.2959 17.4376C11.2959 18.6215 12.2556 19.5813 13.4396 19.5813Z" fill="#3B423B"/>
        <path d="M19.9322 19.5813C21.1161 19.5813 22.0759 18.6215 22.0759 17.4376C22.0759 16.2537 21.1161 15.2939 19.9322 15.2939C18.7483 15.2939 17.7886 16.2537 17.7886 17.4376C17.7886 18.6215 18.7483 19.5813 19.9322 19.5813Z" fill="#3B423B"/>
        <path d="M26.3863 19.5813C27.5702 19.5813 28.53 18.6215 28.53 17.4376C28.53 16.2537 27.5702 15.2939 26.3863 15.2939C25.2024 15.2939 24.2427 16.2537 24.2427 17.4376C24.2427 18.6215 25.2024 19.5813 26.3863 19.5813Z" fill="#3B423B"/>
        <path d="M81.6626 21.6191H42.5282C34.0577 21.6191 27.1909 28.4859 27.1909 36.9564V52.4341C27.1909 60.9048 34.0577 67.7714 42.5282 67.7714H75.6536C76.162 67.7714 76.6497 67.9733 77.0092 68.3328L86.7393 78.0629C87.4639 78.7875 88.703 78.2742 88.703 77.2495V67.1772C88.703 66.5124 89.0457 65.8921 89.6139 65.5469C94.0408 62.8576 96.9999 57.9928 96.9999 52.4341V36.9564C96.9999 28.4858 90.1331 21.6191 81.6626 21.6191Z" fill="#D7F2D6"/>
        <path d="M86.0151 22.2495C86.4231 23.6298 86.6456 25.0896 86.6456 26.6023V42.08C86.6456 50.5506 79.7788 57.4172 71.3083 57.4172H32.174C30.6616 57.4172 29.2016 57.1948 27.8213 56.7868C29.6978 63.1365 35.5701 67.7713 42.5281 67.7713H75.6535C76.1619 67.7713 76.6497 67.9733 77.0092 68.3328L86.7392 78.0628C87.4638 78.7874 88.7029 78.2741 88.7029 77.2494V67.1772C88.7029 66.5123 89.0457 65.892 89.6139 65.5468C94.0407 62.8573 96.9998 57.9928 96.9998 52.434V36.9563C96.9998 29.9983 92.3649 24.126 86.0151 22.2495Z" fill="#C4E1C3"/>
        <path d="M55.5008 58.0124C54.7068 58.0124 54.063 57.3688 54.063 56.5746C54.063 55.7803 54.7068 55.1367 55.5008 55.1367C58.3441 55.1367 60.6572 52.8235 60.6572 49.9802V40.2385C60.6572 34.5894 56.0613 29.9933 50.4122 29.9933C44.763 29.9933 40.1671 34.5894 40.1671 40.2385V45.327C40.1671 46.1212 39.5233 46.7648 38.7293 46.7648C37.9353 46.7648 37.2915 46.1212 37.2915 45.327V40.2385C37.2915 33.0037 43.1774 27.1177 50.4122 27.1177C57.6469 27.1177 63.5328 33.0036 63.5328 40.2385V49.9802C63.533 54.4091 59.9299 58.0124 55.5008 58.0124Z" fill="#87A287"/>
        <path d="M38.7928 51.138H38.6665C36.8884 51.138 35.4468 49.6964 35.4468 47.9183V42.6072C35.4468 40.829 36.8884 39.3875 38.6665 39.3875H38.7928C40.571 39.3875 42.0126 40.829 42.0126 42.6072V47.9183C42.0126 49.6964 40.571 51.138 38.7928 51.138Z" fill="#87A287"/>
        <path d="M62.1586 51.138H62.0322C60.2541 51.138 58.8125 49.6964 58.8125 47.9183V42.6072C58.8125 40.829 60.2541 39.3875 62.0322 39.3875H62.1586C63.9367 39.3875 65.3783 40.829 65.3783 42.6072V47.9183C65.3783 49.6964 63.9367 51.138 62.1586 51.138Z" fill="#87A287"/>
        <path d="M39.3582 39.4492C39.3977 39.6527 39.4199 39.8625 39.4199 40.0774V45.2622C39.4199 47.0753 37.9501 48.545 36.137 48.545C35.9219 48.545 35.7122 48.5229 35.5088 48.4834C35.8021 49.9956 37.132 51.1379 38.73 51.1379C40.5431 51.1379 42.0129 49.6681 42.0129 47.855V42.6703C42.0127 41.0724 40.8705 39.7425 39.3582 39.4492Z" fill="#3B423B"/>
        <path d="M62.7239 39.4492C62.7634 39.6527 62.7856 39.8625 62.7856 40.0774V45.2622C62.7856 47.0753 61.3158 48.545 59.5027 48.545C59.2876 48.545 59.078 48.5229 58.8745 48.4834C59.1678 49.9956 60.4977 51.1379 62.0957 51.1379C63.9088 51.1379 65.3786 49.6681 65.3786 47.855V42.6703C65.3786 41.0724 64.2362 39.7425 62.7239 39.4492Z" fill="#3B423B"/>
        <path d="M53.9065 59.2292C55.7728 59.2292 57.2857 58.0406 57.2857 56.5743C57.2857 55.1081 55.7728 53.9194 53.9065 53.9194C52.0402 53.9194 50.5273 55.1081 50.5273 56.5743C50.5273 58.0406 52.0402 59.2292 53.9065 59.2292Z" fill="#87A287"/>
        <path d="M55.405 54.1974C55.458 54.3841 55.4882 54.5778 55.4882 54.7771C55.4882 56.2433 53.9752 57.432 52.1091 57.432C51.57 57.432 51.0625 57.3302 50.6104 57.1539C50.9473 58.341 52.2937 59.2293 53.9064 59.2293C55.7727 59.2293 57.2856 58.0406 57.2856 56.5744C57.2856 55.5316 56.5184 54.6317 55.405 54.1974Z" fill="#3B423B"/>
        <path d="M82.0048 35.2663H70.5521C69.7581 35.2663 69.1143 34.6227 69.1143 33.8284C69.1143 33.0342 69.7581 32.3906 70.5521 32.3906H82.0048C82.7988 32.3906 83.4426 33.0342 83.4426 33.8284C83.4426 34.6227 82.7988 35.2663 82.0048 35.2663Z" fill="#87A287"/>
        <path d="M79.383 41.7028H70.5521C69.7581 41.7028 69.1143 41.0592 69.1143 40.265C69.1143 39.4708 69.7581 38.8271 70.5521 38.8271H79.383C80.177 38.8271 80.8208 39.4708 80.8208 40.265C80.8208 41.0592 80.177 41.7028 79.383 41.7028Z" fill="#87A287"/>
        </svg>  
     );

    return (

    <div className='nua-bookNow'>
            <div className="nua-icon">
        {helpImg}
        </div>
        <div className='booknow-content'>
            <p> {__('Need Help with NUA Plugin? We’re Here for Support!', 'new-user-approve') } </p>
            <button type="submit" onClick={booknow}> {__('Get Support', 'new-user-approve') }</button>
        </div>
        </div>
    );

}

export default BookNow;