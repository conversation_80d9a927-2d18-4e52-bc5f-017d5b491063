import{K as a,L as s,aw as e,ax as t,a as l,U as n,o as c,X as i,q as o,m as r,N as u,Y as d,s as p,J as m,n as v,t as w,u as y,l as b,O as f,aZ as _,aK as g,Q as k,cH as h,R as x,S as $,P as C,w as T,v as H,$ as L,a0 as D,a6 as M}from"./wbs-Dtem2-xP.js";import{E as j}from"./el-link-QQwewaSH.js";const q={class:"wbs-content content-cd wp-contact-detail"},z={class:"cd-wp"},N={class:"wb-header"},O=["innerHTML"],B={class:"wb-main-title"},R={class:"ml"},U={key:0,class:"meta-item"},V={class:"meta-item"},K={class:"meta-item"},S={key:0,class:"ml-l"},E={class:"hd-ctrl-area"},J={class:"rec-content-box cd-main"},P={class:"msg-hd"},Q={class:"user-name"},X={class:"wk"},Y={class:"msg-detail"},Z=["innerHTML"],A={class:"reply-box"},F={class:"rb-ft"},G={class:"rb-ft"},I={__name:"Detail",setup(I){const{wbsCnf:W}=a(),{wb_e:aa}=s(W),sa=e(),ea=t(),ta=l(!1),la=l(!1),na=n({}),ca=l([]),ia=n({}),oa=l({}),ra=n({content:""}),ua=l(0),da=async()=>{try{la.value=!0;const a=await L.getData({action:"ocw_contact",op:"get_detail",id:sa.query.id});Object.assign(na,a.row),ca.value=a.list,Object.assign(ia,a.cnf),oa.value=a.contact_ways,ta.value=!0,la.value=!1}catch(a){D.error(aa("数据加载失败")),la.value=!1}},pa=async(a,s)=>{M.confirm(a).then((async()=>{await L.saveData(s),D.success(aa("操作成功")),da()}))},ma=()=>pa(aa("确认标记为已处理？"),{action:"ocw_contact",op:"processed",id:na.id}),va=a=>pa(aa("确认关闭？"),{action:"ocw_contact",op:"set_close",id:a}),wa=()=>ea.push("/wo-list"),ya=a=>{const s=["qq","mobile","wx"];if(a.email){const s=a.email;if(s.indexOf(":")>-1){const a=s.split(":");return`${oa.value[a[0]].label}: ${a[1]}`}return`${oa.value.email.label}: ${a.email}`}for(let e in s){const t=s[e];if(a[t]){return`${oa.value[t].label}: ${a[t]}`}}return aa("未填写")};return c((()=>{da()})),i(((a,s,e)=>{ua.value>0?M({dangerouslyUseHTMLString:!0,confirmButtonText:aa("保存并离开"),cancelButtonText:aa("放弃修改"),showCancelButton:!0,message:aa("您修改的设置尚未保存，确定离开此页面吗？"),beforeClose:async(a,s,t)=>{if("confirm"===a)try{await updateData(),t(),e()}catch(l){t(),e(!1)}else t(),e()}}):e()})),(a,s)=>{const e=k,t=C,l=j,n=d;return r(),o("div",q,[u((r(),o("div",{class:p(["wbs-content-inner",{"wb-page-loaded":ta.value}])},[m("div",z,[m("div",N,[m("div",{innerHTML:(i=na,2==i.status?`<span class="wb-status-tag done">${aa("已关闭")}</span>`:1==i.is_new?`<span class="wb-status-tag new">${aa("待处理")}</span>`:3==i.status?`<span class="wb-status-tag success">${aa("已处理")}</span>`:"-")},null,8,O),m("div",B,[m("span",null,"["+w((c=na.type,isNaN(Number(c))?c:ia[c]))+"]",1),m("strong",R,w(na.title),1)]),na.name?(r(),o("p",U,[m("span",null,w(y(aa)("联系人"))+": "+w(na.name),1)])):v("",!0),m("p",V,[m("span",null,w(ya(na)),1)]),m("p",K,[m("span",null,w(y(aa)("发起时间"))+": "+w(na.create_date),1),na.update_time?(r(),o("span",S,w(y(aa)("更新时间"))+": "+w(na.update_time),1)):v("",!0)]),m("div",E,[1==na.status?(r(),b(e,{key:0,onClick:s[0]||(s[0]=g((a=>va(na.id)),["stop","prevent"])),title:y(aa)("关闭工单"),icon:y(_),circle:""},null,8,["title","icon"])):v("",!0),f(e,{onClick:wa,title:y(aa)("返回列表"),type:"primary",icon:y(h),circle:""},null,8,["title","icon"])])]),m("div",J,[m("ul",null,[(r(!0),o(x,null,$(ca.value,((a,s)=>(r(),o("li",{class:p(["rec-detail",s>0?"by-me":"by-wb"]),key:"item"+s},[m("div",P,[m("div",Q,[m("strong",null,w(s>0?a.display_name+y(aa)("备注:"):y(aa)("留言内容:")),1),m("span",X,w(a.create_date),1)])]),m("div",Y,[m("div",{innerHTML:a.content},null,8,Z)])],2)))),128))])]),m("div",A,[f(t,{clearable:"",type:"textarea",autosize:{minRows:1,maxRows:3},placeholder:y(aa)("请输入备注"),modelValue:ra.content,"onUpdate:modelValue":s[1]||(s[1]=a=>ra.content=a)},null,8,["placeholder","modelValue"]),m("div",F,[f(e,{class:"ml",size:"mini",plain:"",type:"primary",onClick:s[2]||(s[2]=g((a=>(async()=>{ra.content?(await L.saveData({action:"ocw_contact",op:"reply",id:na.id,content:ra.content}),ea.go(0)):M.alert(aa("请输入内容再提交"))})()),["stop","prevent"]))},{default:T((()=>[H(w(y(aa)("添加备注")),1)])),_:1})]),m("div",G,[2!=na.status?(r(),b(l,{key:0,class:"ml",size:"mini",plain:"",onClick:s[3]||(s[3]=a=>va(na.id))},{default:T((()=>[H(w(y(aa)("关闭")),1)])),_:1})):v("",!0),f(l,{class:"ml",plain:"",size:"mini",type:"danger",onClick:s[4]||(s[4]=a=>{return s=na.id,pa(aa("删除后将无法恢复，确认删除？"),{action:"ocw_contact",op:"delete",id:s});var s})},{default:T((()=>[H(w(y(aa)("删除")),1)])),_:1}),1==na.status?(r(),b(e,{key:1,class:"ml",type:"success",onClick:g(ma,["stop","prevent"])},{default:T((()=>[H(w(y(aa)("标记为已处理")),1)])),_:1})):v("",!0)])])])],2)),[[n,!ta.value]])]);var c,i}}};export{I as default};
