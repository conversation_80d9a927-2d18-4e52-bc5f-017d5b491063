# 产品删除审核功能故障排除指南

## 🚨 问题：管理员点击移到回收站没有反应

### 可能的原因和解决方案

#### 1. 钩子注册问题
**症状**：点击删除按钮没有任何反应，页面不刷新，没有错误消息

**检查方法**：
```php
// 在WordPress管理后台运行以下代码检查钩子是否注册
global $wp_filter;
if (isset($wp_filter['pre_trash_post'])) {
    echo "pre_trash_post 过滤器已注册";
} else {
    echo "pre_trash_post 过滤器未注册";
}
```

**解决方案**：
- 确保 `PRS_Product_Handler` 类被正确实例化
- 检查 `setup_product_delete_intercept()` 方法是否被调用
- 确认过滤器在 `init` 钩子中注册

#### 2. JavaScript冲突
**症状**：删除按钮点击后页面无响应，浏览器控制台有JavaScript错误

**检查方法**：
1. 打开浏览器开发者工具
2. 查看控制台是否有JavaScript错误
3. 检查网络请求是否被发送

**解决方案**：
- 禁用其他插件测试是否有冲突
- 检查主题是否有自定义的删除处理逻辑
- 确保jQuery正确加载

#### 3. 权限问题
**症状**：删除操作被阻止但没有显示错误消息

**检查方法**：
```php
// 检查当前用户权限
if (current_user_can('delete_posts')) {
    echo "用户有删除权限";
} else {
    echo "用户没有删除权限";
}
```

**解决方案**：
- 确保用户有 `delete_posts` 权限
- 检查 Members 插件的权限设置

#### 4. 数据库问题
**症状**：删除拦截工作但审核记录创建失败

**检查方法**：
1. 查看WordPress错误日志
2. 检查数据库表是否存在
3. 验证 `operation_type` 字段是否已添加

**解决方案**：
- 运行数据库升级：访问 `wp-admin` 触发升级
- 手动添加缺失的字段
- 检查数据库用户权限

### 调试步骤

#### 第一步：启用调试模式
在 `wp-config.php` 中添加：
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

#### 第二步：检查错误日志
查看 `/wp-content/debug.log` 文件，寻找以 `PRS:` 开头的日志条目。

#### 第三步：运行调试工具
- 访问 `debug-delete-intercept.php` 文件检查系统状态
- 访问 `test-redirect.php` 文件测试重定向功能

#### 第四步：手动测试
1. 创建一个测试产品
2. 尝试删除该产品
3. 观察浏览器控制台和网络请求
4. 检查错误日志
5. 验证是否跳转到审核详情页面

### 常见错误日志

#### 正常工作的日志
```
PRS: intercept_product_delete called with post ID: 123
PRS: Processing delete request for product ID: 123
PRS: Delete review required for product ID: 123
PRS: Creating delete review for product ID: 123
PRS: Delete review created successfully with ID: 48, blocking delete
```

#### 问题日志
```
PRS: intercept_product_delete called with post ID: null
// 表示过滤器参数有问题

PRS: Not a product, allowing delete. Post type: null
// 表示产品类型检测失败

PRS: Failed to create delete review: [错误信息]
// 表示审核记录创建失败
```

#### 5. 重定向问题
**症状**：删除审核记录创建成功，但没有跳转到审核详情页面

**检查方法**：
1. 查看浏览器控制台是否有JavaScript错误
2. 检查网络请求中是否有重定向
3. 使用 `test-redirect.php` 测试重定向功能

**解决方案**：
- 检查是否有其他插件阻止重定向
- 确认JavaScript正常执行
- 验证transient是否正确设置

### 手动修复方法

#### 重新注册过滤器
如果过滤器没有正确注册，可以在主题的 `functions.php` 中添加：
```php
add_action('init', function() {
    if (class_exists('PRS_Product_Handler')) {
        $handler = new PRS_Product_Handler();
        add_filter('pre_trash_post', array($handler, 'intercept_product_delete'), 10, 2);
    }
});
```

#### 手动添加数据库字段
如果 `operation_type` 字段缺失：
```sql
ALTER TABLE wp_product_reviews 
ADD COLUMN operation_type varchar(20) DEFAULT 'modify' 
AFTER is_new_product;
```

### 测试验证

#### 验证删除拦截工作
1. 点击产品的"移到回收站"
2. 应该看到成功消息而不是产品被删除
3. 检查产品审核系统中是否有新的删除审核记录

#### 验证审核流程
1. 进入产品审核系统
2. 找到删除审核记录
3. 完成审核流程
4. 验证产品最终被移到回收站

### 联系支持

如果以上方法都无法解决问题，请提供以下信息：

1. **WordPress版本**
2. **WooCommerce版本**
3. **错误日志内容**
4. **浏览器控制台错误**
5. **调试工具输出结果**
6. **其他已安装的插件列表**

### 临时解决方案

如果需要紧急删除产品，可以：

1. **直接在数据库中删除**（不推荐）
2. **临时禁用产品审核插件**
3. **使用WP-CLI命令删除**：
   ```bash
   wp post delete [产品ID] --force
   ```

⚠️ **注意**：临时解决方案会绕过审核流程，请谨慎使用。
