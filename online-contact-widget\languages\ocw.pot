# Copyright (C) 2025 多合一在线客服插件
# This file is distributed under the same license as the 多合一在线客服插件 package.
msgid ""
msgstr ""
"Project-Id-Version: 1.2.0\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-04-23 04:02+0000\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: classes/admin.class.php:69, classes/admin.class.php:70
msgctxt "label"
msgid "返回顶部"
msgstr ""

#: classes/admin.class.php:77
msgid "电话"
msgstr ""

#: classes/admin.class.php:78
msgctxt "label"
msgid "电话联系"
msgstr ""

#: classes/admin.class.php:83
msgid "电话号码"
msgstr ""

#: classes/admin.class.php:86
msgid "电话或手机号"
msgstr ""

#: classes/admin.class.php:91, classes/admin.class.php:116, classes/admin.class.php:170, classes/admin.class.php:205, classes/admin.class.php:236, classes/admin.class.php:268, classes/admin.class.php:300, classes/admin.class.php:332, classes/admin.class.php:364, classes/admin.class.php:396, classes/admin.class.php:428
msgid "名称"
msgstr ""

#: classes/admin.class.php:104, classes/admin.class.php:105
msgid "电子邮件"
msgstr ""

#: classes/admin.class.php:106, classes/admin.class.php:111
msgid "邮箱地址"
msgstr ""

#: classes/admin.class.php:129
msgid "留言"
msgstr ""

#: classes/admin.class.php:130, classes/admin.class.php:1311, classes/front.class.php:413
msgid "在线留言"
msgstr ""

#: classes/admin.class.php:133
msgid "昵称"
msgstr ""

#: classes/admin.class.php:134
msgid "内容"
msgstr ""

#: classes/admin.class.php:136
msgid "邮箱通知"
msgstr ""

#: classes/admin.class.php:136
msgid "短信通知"
msgstr ""

#: classes/admin.class.php:141, classes/admin.class.php:142
msgid "我的订单"
msgstr ""

#: classes/admin.class.php:149
msgid "自定义"
msgstr ""

#: classes/admin.class.php:155
msgid "图片(如二维码图片)"
msgstr ""

#: classes/admin.class.php:160
msgid "图标"
msgstr ""

#: classes/admin.class.php:165
msgid "链接/账号"
msgstr ""

#: classes/admin.class.php:183, classes/admin.class.php:495
msgid "微信"
msgstr ""

#: classes/admin.class.php:184
msgid "关注我们"
msgstr ""

#: classes/admin.class.php:185, classes/admin.class.php:195
msgid "微信号"
msgstr ""

#: classes/admin.class.php:190, classes/admin.class.php:255, classes/admin.class.php:287, classes/admin.class.php:319, classes/admin.class.php:351, classes/admin.class.php:383, classes/admin.class.php:415
msgid "二维码图片"
msgstr ""

#: classes/admin.class.php:200
msgid "联系人"
msgstr ""

#: classes/admin.class.php:208
msgid "* 如职称，职位等"
msgstr ""

#: classes/admin.class.php:222, classes/admin.class.php:223
msgid "QQ客服"
msgstr ""

#: classes/admin.class.php:228, classes/admin.class.php:231
msgid "QQ号"
msgstr ""

#: classes/admin.class.php:249, classes/admin.class.php:250
msgid "WhatsApp"
msgstr ""

#: classes/admin.class.php:260
msgid "Phone Number"
msgstr ""

#: classes/admin.class.php:263
msgid "全格式国际电话号码（需包含国家代码）"
msgstr ""

#: classes/admin.class.php:281, classes/admin.class.php:282
msgid "Telegram"
msgstr ""

#: classes/admin.class.php:292
msgid "Username"
msgstr ""

#: classes/admin.class.php:295
msgid "必须以@开头（如@mycompany）"
msgstr ""

#: classes/admin.class.php:313, classes/admin.class.php:314
msgid "Messenger"
msgstr ""

#: classes/admin.class.php:324
msgid "Page User Name"
msgstr ""

#: classes/admin.class.php:327
msgid "Messenger用户名"
msgstr ""

#: classes/admin.class.php:356
msgid "Line ID"
msgstr ""

#: classes/admin.class.php:359
msgid "企业LINE官方账号ID（数字格式）"
msgstr ""

#: classes/admin.class.php:388
msgid "Account"
msgstr ""

#: classes/admin.class.php:391
msgid "Viber账号名"
msgstr ""

#: classes/admin.class.php:420
msgid "Number"
msgstr ""

#: classes/admin.class.php:423
msgid "完整国际格式号码（包含国家代码）, 如 +*************"
msgstr ""

#: classes/admin.class.php:443
msgid "左上"
msgstr ""

#: classes/admin.class.php:447
msgid "左中"
msgstr ""

#: classes/admin.class.php:451
msgid "左下"
msgstr ""

#: classes/admin.class.php:455
msgid "右上"
msgstr ""

#: classes/admin.class.php:459
msgid "右中"
msgstr ""

#: classes/admin.class.php:463
msgid "右下"
msgstr ""

#: classes/admin.class.php:468
msgid "所有页面"
msgstr ""

#: classes/admin.class.php:469
msgid "指定页面"
msgstr ""

#: classes/admin.class.php:470
msgid "例外页面"
msgstr ""

#: classes/admin.class.php:476, classes/admin.class.php:481
msgid "默认"
msgstr ""

#: classes/admin.class.php:477
msgid "圆角"
msgstr ""

#: classes/admin.class.php:482
msgid "大尺寸"
msgstr ""

#: classes/admin.class.php:537
msgid "其他"
msgstr ""

#: classes/admin.class.php:557
msgctxt "label"
msgid "手机"
msgstr ""

#: classes/admin.class.php:561
msgctxt "label"
msgid "邮箱"
msgstr ""

#: classes/admin.class.php:565
msgctxt "label"
msgid "微信"
msgstr ""

#: classes/admin.class.php:569
msgctxt "label"
msgid "QQ"
msgstr ""

#: classes/admin.class.php:573
msgctxt "label"
msgid "LINE"
msgstr ""

#: classes/admin.class.php:577
msgctxt "label"
msgid "Whats App"
msgstr ""

#: classes/admin.class.php:581
msgctxt "label"
msgid "Telegram"
msgstr ""

#: classes/admin.class.php:585
msgctxt "label"
msgid "Messenger"
msgstr ""

#: classes/admin.class.php:589
msgctxt "label"
msgid "Viber"
msgstr ""

#: classes/admin.class.php:593
msgctxt "label"
msgid "Signal"
msgstr ""

#: classes/admin.class.php:632, classes/admin.class.php:642, classes/admin.class.php:701, classes/contact.class.php:46
msgid "多合一在线客服插件"
msgstr ""

#: classes/admin.class.php:633
msgid "多合一客服"
msgstr ""

#: classes/admin.class.php:643
msgid "插件设置"
msgstr ""

#: classes/admin.class.php:669
msgid "升至Pro版"
msgstr ""

#: classes/admin.class.php:673
msgid "设置"
msgstr ""

#: classes/admin.class.php:770
msgid "插件主页"
msgstr ""

#: classes/admin.class.php:771
msgid "说明文档"
msgstr ""

#: classes/admin.class.php:772
msgid "反馈"
msgstr ""

#: classes/admin.class.php:1171
msgid "测试短信"
msgstr ""

#: classes/admin.class.php:1300
msgid "业务咨询"
msgstr ""

#: classes/admin.class.php:1301
msgid "市场合作"
msgstr ""

#: classes/admin.class.php:1302
msgid "其他事宜"
msgstr ""

#: classes/admin.class.php:1314
msgid "您的工单我们已经收到，我们将会尽快跟您联系！"
msgstr ""

#: classes/admin.class.php:1333
msgctxt "邮局设置"
msgid "PHP"
msgstr ""

#: classes/admin.class.php:1337
msgctxt "邮局设置"
msgid "QQ邮箱"
msgstr ""

#: classes/admin.class.php:1346
msgctxt "邮局设置"
msgid "163邮箱"
msgstr ""

#: classes/admin.class.php:1355
msgctxt "邮局设置"
msgid "其他邮箱"
msgstr ""

#: classes/admin.class.php:1399
msgctxt "浮标label"
msgid "在线客服"
msgstr ""

#: classes/admin.class.php:1402
msgid "我们将24小时内回复。"
msgstr ""

#: classes/admin.class.php:1403
msgid "您好，有任何疑问请与我们联系！"
msgstr ""

#: classes/admin.class.php:1404
msgid "选择聊天工具："
msgstr ""

#: classes/captcha.class.php:100
msgid "验证码不能为空"
msgstr ""

#: classes/captcha.class.php:107
msgid "验证码错误，请重新输入"
msgstr ""

#: classes/captcha.class.php:164
msgid "reCAPTCHA验证失败，请稍后再试"
msgstr ""

#: classes/contact.class.php:47
msgid "工单管理"
msgstr ""

#: classes/contact.class.php:192
msgid "关闭工单"
msgstr ""

#: classes/contact.class.php:326
msgid "网友"
msgstr ""

#: classes/contact.class.php:463
msgid "您的工单长时间未反馈信息，系统自动关闭此工单，如需继续联系，请重新发起工单。"
msgstr ""

#: classes/contact.class.php:493
msgid "未登录访客"
msgstr ""

#: classes/front.class.php:38
msgctxt "会员中心标题"
msgid "我的咨询"
msgstr ""

#: classes/front.class.php:369
msgid "需登录后才可留言。您尚未登录网站账户。"
msgstr ""

#: classes/front.class.php:375, classes/front.class.php:626
msgid "请输入您的大名"
msgstr ""

#: classes/front.class.php:379, classes/front.class.php:630
msgid "姓名长度不能超过100字符"
msgstr ""

#: classes/front.class.php:385
msgid "请输入您的联系方式"
msgstr ""

#: classes/front.class.php:391, classes/front.class.php:646
msgid "您要咨询的是?"
msgstr ""

#: classes/front.class.php:396
msgid "咨询内容过长，可留下联系方式，我们联系您进一步沟通。"
msgstr ""

#: classes/front.class.php:403
msgid "请输入验证码"
msgstr ""

#: classes/front.class.php:445, classes/front.class.php:719
msgid "工单保存失败，请联系管理员。"
msgstr ""

#: classes/front.class.php:621
msgid "工单未启用"
msgstr ""

#: classes/front.class.php:636
msgid "请输入咨询主题"
msgstr ""

#: classes/front.class.php:640
msgid "咨询主题长度不能超过200字符"
msgstr ""

#: classes/front.class.php:650
msgid "咨询内容不能超过1000字符"
msgstr ""

#: classes/front.class.php:770
msgctxt "js提示"
msgid "微信号已复制"
msgstr ""

#: classes/front.class.php:771
msgctxt "在线咨询提交后字段名"
msgid "姓名："
msgstr ""

#: classes/front.class.php:772
msgctxt "在线咨询提交后字段名"
msgid "联系方式："
msgstr ""

#: classes/front.class.php:773
msgctxt "在线咨询提交后字段名"
msgid "咨询类型："
msgstr ""

#: classes/front.class.php:774
msgctxt "在线咨询提交后字段名"
msgid "咨询内容："
msgstr ""

#: classes/front.class.php:775
msgctxt "表单提交提示"
msgid "请输入正确格式的邮箱地址"
msgstr ""

#: classes/front.class.php:776
msgctxt "表单提交提示"
msgid "请输入正确格式的11位手机号"
msgstr ""

#: classes/front.class.php:777
msgctxt "表单提交提示"
msgid "咨询内容过长，可简单描述，我们联系您进一步沟通。"
msgstr ""

#: classes/front.class.php:778
msgctxt "表单提交提示"
msgid "智能验证不通过，若有疑问可联系站点管理员。"
msgstr ""

#: classes/front.class.php:779
msgctxt "表单提交提示"
msgid "智能验证异常，请联系站点管理员。"
msgstr ""

#: classes/image_captcha.php:3
msgid "图片验证码"
msgstr ""

#: classes/mail.class.php:57
msgctxt "邮件提醒, %s为工单类型"
msgid "关于%s"
msgstr ""

#: classes/mail.class.php:59
msgctxt "邮件提醒"
msgid "收到%s的留言"
msgstr ""

#: classes/mail.class.php:63
msgctxt "联系方式"
msgid "邮箱"
msgstr ""

#: classes/mail.class.php:64
msgctxt "联系方式"
msgid "QQ"
msgstr ""

#: classes/mail.class.php:65
msgctxt "联系方式"
msgid "微信"
msgstr ""

#: classes/mail.class.php:66
msgctxt "联系方式"
msgid "手机"
msgstr ""

#: classes/mail.class.php:74
msgctxt "邮件提醒"
msgid "时间: "
msgstr ""

#: classes/mail.class.php:111, classes/mail.class.php:177, classes/mail.class.php:237, classes/mail.class.php:249
msgid "邮件发送失败"
msgstr ""

#: classes/mail.class.php:115, classes/mail.class.php:240
msgid "不是有效的邮箱地址"
msgstr ""

#: classes/mail.class.php:120
msgid "邮箱配置无效"
msgstr ""

#: classes/mail.class.php:129, classes/mail.class.php:134
msgid "邮箱SMTP配置参数不能为空"
msgstr ""

#: classes/mail.class.php:168
msgid "测试邮件"
msgstr ""

#: classes/mail.class.php:168
msgid "这是一封测试邮件 -- By OCW"
msgstr ""

#: classes/mail.class.php:175
msgid "邮件发送失败,"
msgstr ""

#: classes/mail.class.php:172, classes/mail.class.php:247
msgid "邮件发送成功"
msgstr ""

#: classes/sms.class.php:85
msgid "短信配置为空"
msgstr ""

#: classes/sms.class.php:89
msgid "短信接口为空"
msgstr ""

#: classes/sms.class.php:97, classes/sms.class.php:120, classes/sms.class.php:137
msgid "短信接口参数不完整"
msgstr ""

#: classes/sms.class.php:111
msgid "发送失败"
msgstr ""

#: classes/sms.class.php:115
msgid "发送失败,"
msgstr ""

#: classes/sms.class.php:150
msgid "发送失败[code:"
msgstr ""

#: classes/sms.class.php:150
msgid "desc:"
msgstr ""

#: classes/sms.class.php:154
msgid "没有找到对应短信发送接口"
msgstr ""

#: classes/_localize.php:7
msgctxt "vue部分"
msgid "数据加载失败"
msgstr ""

#: classes/_localize.php:8
msgctxt "vue部分"
msgid "设置保存成功"
msgstr ""

#: classes/_localize.php:9
msgctxt "vue部分"
msgid "保存失败"
msgstr ""

#: classes/_localize.php:10
msgctxt "vue部分"
msgid "保存并离开"
msgstr ""

#: classes/_localize.php:11
msgctxt "vue部分"
msgid "放弃修改"
msgstr ""

#: classes/_localize.php:12
msgctxt "vue部分"
msgid "您修改的设置尚未保存，确定离开此页面吗？"
msgstr ""

#: classes/_localize.php:13
msgctxt "vue部分"
msgid "预览"
msgstr ""

#: classes/_localize.php:14
msgctxt "vue部分"
msgid "展示位置"
msgstr ""

#: classes/_localize.php:15
msgctxt "vue部分"
msgid "主色调"
msgstr ""

#: classes/_localize.php:16
msgctxt "vue部分"
msgid "* 建议跟网站主色调一致。"
msgstr ""

#: classes/_localize.php:17
msgctxt "vue部分"
msgid "暗黑模式"
msgstr ""

#: classes/_localize.php:18
msgctxt "vue部分"
msgid "已设置为暗黑风格，"
msgstr ""

#: classes/_localize.php:19
msgctxt "vue部分"
msgid "未启用，"
msgstr ""

#: classes/_localize.php:20
msgctxt "vue部分"
msgid "该选项适用于某些固定为暗黑风格的站点。"
msgstr ""

#: classes/_localize.php:21
msgctxt "vue部分"
msgid "展示模式"
msgstr ""

#: classes/_localize.php:22
msgctxt "vue部分"
msgid "展开"
msgstr ""

#: classes/_localize.php:23
msgctxt "vue部分"
msgid "浮标"
msgstr ""

#: classes/_localize.php:24
msgctxt "vue部分"
msgid "浮标+窗口"
msgstr ""

#: classes/_localize.php:25
msgctxt "vue部分"
msgid "展开设置"
msgstr ""

#: classes/_localize.php:26
msgctxt "vue部分"
msgid "组件形状"
msgstr ""

#: classes/_localize.php:27
msgctxt "vue部分"
msgid "默认"
msgstr ""

#: classes/_localize.php:28
msgctxt "vue部分"
msgid "圆角"
msgstr ""

#: classes/_localize.php:29
msgctxt "vue部分"
msgid "组件尺寸"
msgstr ""

#: classes/_localize.php:30
msgctxt "vue部分"
msgid "大尺寸"
msgstr ""

#: classes/_localize.php:31
msgctxt "vue部分"
msgid "浮标名称"
msgstr ""

#: classes/_localize.php:32
msgctxt "vue部分"
msgid "显示浮标名称"
msgstr ""

#: classes/_localize.php:33
msgctxt "vue部分"
msgid "隐藏浮标名称"
msgstr ""

#: classes/_localize.php:34
msgctxt "vue部分"
msgid "浮标动效"
msgstr ""

#: classes/_localize.php:35
msgctxt "vue部分"
msgid "显示动效"
msgstr ""

#: classes/_localize.php:36
msgctxt "vue部分"
msgid "隐藏动效"
msgstr ""

#: classes/_localize.php:37
msgctxt "vue部分"
msgid "出现时间间隔："
msgstr ""

#: classes/_localize.php:38
msgctxt "vue部分"
msgid "秒"
msgstr ""

#: classes/_localize.php:39
msgctxt "vue部分"
msgid "界面元素"
msgstr ""

#: classes/_localize.php:40
msgctxt "vue部分"
msgid "头像"
msgstr ""

#: classes/_localize.php:41
msgctxt "vue部分"
msgid "联系人昵称"
msgstr ""

#: classes/_localize.php:42
msgctxt "vue部分"
msgid "欢迎语"
msgstr ""

#: classes/_localize.php:43
msgctxt "vue部分"
msgid "响应文字"
msgstr ""

#: classes/_localize.php:44
msgctxt "vue部分"
msgid "选择提示"
msgstr ""

#: classes/_localize.php:45
msgctxt "vue部分"
msgid "会员中心"
msgstr ""

#: classes/_localize.php:46
msgctxt "vue部分"
msgid "展示时高度为60像素，建议准备高度至少为120像素（包括上下留白）的图片。"
msgstr ""

#: classes/_localize.php:47
msgctxt "vue部分"
msgid "会员中心路径:"
msgstr ""

#: classes/_localize.php:48
msgctxt "vue部分"
msgid "兼容小部件"
msgstr ""

#: classes/_localize.php:49
msgctxt "vue部分"
msgid "* 填写主题小部件模块的css类名，在插件激活的页面隐藏该模块。"
msgstr ""

#: classes/_localize.php:50
msgctxt "vue部分"
msgid "如何找到小部件css类名?"
msgstr ""

#: classes/_localize.php:51
msgctxt "vue部分"
msgid "兼容暗黑模式"
msgstr ""

#: classes/_localize.php:52
msgctxt "vue部分"
msgid "* 填写主题暗黑模式激活时的css类名，以响应模式间的切换。"
msgstr ""

#: classes/_localize.php:53
msgctxt "vue部分"
msgid "如何找到暗黑模式css类名?"
msgstr ""

#: classes/_localize.php:54
msgctxt "vue部分"
msgid "左中"
msgstr ""

#: classes/_localize.php:55
msgctxt "vue部分"
msgid "左下"
msgstr ""

#: classes/_localize.php:56
msgctxt "vue部分"
msgid "右中"
msgstr ""

#: classes/_localize.php:57
msgctxt "vue部分"
msgid "右下"
msgstr ""

#: classes/_localize.php:58
msgctxt "vue部分"
msgid "自定义"
msgstr ""

#: classes/_localize.php:59
msgctxt "vue部分"
msgid "请至少选择一种联系方式"
msgstr ""

#: classes/_localize.php:60
msgctxt "vue部分"
msgid "请输入自定义类型的别名"
msgstr ""

#: classes/_localize.php:61
msgctxt "vue部分"
msgid "确认删除?"
msgstr ""

#: classes/_localize.php:62
msgctxt "vue部分"
msgid "类型"
msgstr ""

#: classes/_localize.php:63
msgctxt "vue部分"
msgid "请选择"
msgstr ""

#: classes/_localize.php:64
msgctxt "vue部分"
msgid "别名"
msgstr ""

#: classes/_localize.php:65
msgctxt "vue部分"
msgid "* 以简短全小写字母定义, 如telegram, viber等。"
msgstr ""

#: classes/_localize.php:66
msgctxt "vue部分"
msgid "显示名称"
msgstr ""

#: classes/_localize.php:67
msgctxt "vue部分"
msgid "咨询类型"
msgstr ""

#: classes/_localize.php:68
msgctxt "vue部分"
msgid "按回车确认"
msgstr ""

#: classes/_localize.php:69
msgctxt "vue部分"
msgid "联系方式"
msgstr ""

#: classes/_localize.php:70
msgctxt "vue部分"
msgid "* 请至少选择一种联系方式"
msgstr ""

#: classes/_localize.php:71
msgctxt "vue部分"
msgid "提交成功提示语"
msgstr ""

#: classes/_localize.php:72
msgctxt "vue部分"
msgid "输入自动回复的内容。"
msgstr ""

#: classes/_localize.php:73
msgctxt "vue部分"
msgid "验证码方式"
msgstr ""

#: classes/_localize.php:74
msgctxt "vue部分"
msgid "无"
msgstr ""

#: classes/_localize.php:75
msgctxt "vue部分"
msgid "一般验证码"
msgstr ""

#: classes/_localize.php:76
msgctxt "vue部分"
msgid "谷歌reCAPTCHA v3"
msgstr ""

#: classes/_localize.php:77
msgctxt "vue部分"
msgid "网站秘钥"
msgstr ""

#: classes/_localize.php:78
msgctxt "vue部分"
msgid "通讯秘钥"
msgstr ""

#: classes/_localize.php:79
msgctxt "vue部分"
msgid "分数阈值"
msgstr ""

#: classes/_localize.php:80
msgctxt "vue部分"
msgid "注：要启用谷歌reCAPTCHA验证注册，请参照"
msgstr ""

#: classes/_localize.php:81
msgctxt "vue部分"
msgid "谷歌reCAPTCHA v3申请及配置教程"
msgstr ""

#: classes/_localize.php:82
msgctxt "vue部分"
msgid "留言方式"
msgstr ""

#: classes/_localize.php:83
msgctxt "vue部分"
msgid "已开启需要登录留言"
msgstr ""

#: classes/_localize.php:84
msgctxt "vue部分"
msgid "无登录访客可留言"
msgstr ""

#: classes/_localize.php:85
msgctxt "vue部分"
msgid "登录链接"
msgstr ""

#: classes/_localize.php:86
msgctxt "vue部分"
msgid "* 若无改写登录链接，留空使用默认即可。"
msgstr ""

#: classes/_localize.php:87
msgctxt "vue部分"
msgid "留言通知"
msgstr ""

#: classes/_localize.php:88
msgctxt "vue部分"
msgid "邮件通知"
msgstr ""

#: classes/_localize.php:89
msgctxt "vue部分"
msgid "短信通知"
msgstr ""

#: classes/_localize.php:90
msgctxt "vue部分"
msgid "添加"
msgstr ""

#: classes/_localize.php:91
msgctxt "vue部分"
msgid "取消"
msgstr ""

#: classes/_localize.php:92
msgctxt "vue部分"
msgid "保存"
msgstr ""

#: classes/_localize.php:93
msgctxt "vue部分"
msgid "建议至多设定%s个选项。"
msgstr ""

#: classes/_localize.php:94
msgctxt "vue部分"
msgid "默认为: %s"
msgstr ""

#: classes/_localize.php:95
msgctxt "vue部分"
msgid "设置成功"
msgstr ""

#: classes/_localize.php:96
msgctxt "vue部分"
msgid "请先配置选项再启用。"
msgstr ""

#: classes/_localize.php:97
msgctxt "vue部分"
msgid "启用中"
msgstr ""

#: classes/_localize.php:98
msgctxt "vue部分"
msgid "未启用"
msgstr ""

#: classes/_localize.php:99
msgctxt "vue部分"
msgid "删除"
msgstr ""

#: classes/_localize.php:100
msgctxt "vue部分"
msgid "编辑"
msgstr ""

#: classes/_localize.php:101
msgctxt "vue部分"
msgid "停用"
msgstr ""

#: classes/_localize.php:102
msgctxt "vue部分"
msgid "启用"
msgstr ""

#: classes/_localize.php:103
msgctxt "vue部分"
msgid "组件"
msgstr ""

#: classes/_localize.php:104
msgctxt "vue部分"
msgid "选项"
msgstr ""

#: classes/_localize.php:105
msgctxt "vue部分"
msgid "状态"
msgstr ""

#: classes/_localize.php:106
msgctxt "vue部分"
msgid "操作"
msgstr ""

#: classes/_localize.php:107
msgctxt "vue部分"
msgid "* 可拖拽对组件排序"
msgstr ""

#: classes/_localize.php:108
msgctxt "vue部分"
msgid "短信发送成功"
msgstr ""

#: classes/_localize.php:109
msgctxt "vue部分"
msgid "短信发送失败"
msgstr ""

#: classes/_localize.php:110
msgctxt "vue部分"
msgid "请确认手机号码格式"
msgstr ""

#: classes/_localize.php:111
msgctxt "vue部分"
msgid "请输入测试收件箱。"
msgstr ""

#: classes/_localize.php:112
msgctxt "vue部分"
msgid "邮件发送失败"
msgstr ""

#: classes/_localize.php:113
msgctxt "vue部分"
msgid "邮局设置"
msgstr ""

#: classes/_localize.php:114
msgctxt "vue部分"
msgid "通知邮箱"
msgstr ""

#: classes/_localize.php:115
msgctxt "vue部分"
msgid "* 多个邮箱用英文逗号(,)分隔"
msgstr ""

#: classes/_localize.php:116
msgctxt "vue部分"
msgid "邮件服务"
msgstr ""

#: classes/_localize.php:117
msgctxt "vue部分"
msgid "检测到当前正在使用闪电博主题，将使用主题邮件服务设置。"
msgstr ""

#: classes/_localize.php:118
msgctxt "vue部分"
msgid "发件邮箱"
msgstr ""

#: classes/_localize.php:119
msgctxt "vue部分"
msgid "* 如果启用SMTP邮局，发件邮箱必须与SMTP邮局邮箱保持一致。"
msgstr ""

#: classes/_localize.php:120
msgctxt "vue部分"
msgid "发件人名称"
msgstr ""

#: classes/_localize.php:121
msgctxt "vue部分"
msgid "邮件程序"
msgstr ""

#: classes/_localize.php:122
msgctxt "vue部分"
msgid "* 由于大部分国内服务器禁用了邮局端口，无法通过WP默认邮局发信。建议使用QQ、163或者其他SMTP邮局执行WP发信任务。"
msgstr ""

#: classes/_localize.php:123
msgctxt "vue部分"
msgid "查看SMTP邮局设置教程"
msgstr ""

#: classes/_localize.php:124
msgctxt "vue部分"
msgid "SMTP配置"
msgstr ""

#: classes/_localize.php:125
msgctxt "vue部分"
msgid "SMTP服务器"
msgstr ""

#: classes/_localize.php:126
msgctxt "vue部分"
msgid "加密方式"
msgstr ""

#: classes/_localize.php:127
msgctxt "vue部分"
msgid "端口"
msgstr ""

#: classes/_localize.php:128
msgctxt "vue部分"
msgid "SMTP用户名"
msgstr ""

#: classes/_localize.php:129
msgctxt "vue部分"
msgid "SMTP密码/授权码"
msgstr ""

#: classes/_localize.php:130
msgctxt "vue部分"
msgid "发送测试邮件"
msgstr ""

#: classes/_localize.php:131
msgctxt "vue部分"
msgid "<EMAIL>"
msgstr ""

#: classes/_localize.php:132
msgctxt "vue部分"
msgid "发送"
msgstr ""

#: classes/_localize.php:133
msgctxt "vue部分"
msgid "* 测试邮件收件人必须与发件人邮箱地址保持一致，否则测试邮件将会发送失败。"
msgstr ""

#: classes/_localize.php:134
msgctxt "vue部分"
msgid "短信设置"
msgstr ""

#: classes/_localize.php:135
msgctxt "vue部分"
msgid "通知手机号"
msgstr ""

#: classes/_localize.php:136
msgctxt "vue部分"
msgid "* 多个手机用英文逗号(,)分隔"
msgstr ""

#: classes/_localize.php:137
msgctxt "vue部分"
msgid "服务商"
msgstr ""

#: classes/_localize.php:138
msgctxt "vue部分"
msgid "又拍云"
msgstr ""

#: classes/_localize.php:139
msgctxt "vue部分"
msgid "阿里云"
msgstr ""

#: classes/_localize.php:140
msgctxt "vue部分"
msgid "华为云"
msgstr ""

#: classes/_localize.php:141
msgctxt "vue部分"
msgid "参数"
msgstr ""

#: classes/_localize.php:142
msgctxt "vue部分"
msgid "账号ID"
msgstr ""

#: classes/_localize.php:143
msgctxt "vue部分"
msgid "进入又拍云短信服务控制台>短信发送>API发送，点击流程步骤2-获取账号token获取"
msgstr ""

#: classes/_localize.php:144
msgctxt "vue部分"
msgid "Token"
msgstr ""

#: classes/_localize.php:145
msgctxt "vue部分"
msgid "账号token或者模板token（推荐后者）"
msgstr ""

#: classes/_localize.php:146
msgctxt "vue部分"
msgid "模板编号"
msgstr ""

#: classes/_localize.php:147
msgctxt "vue部分"
msgid "进入又拍云短信服务控制台>配置中心>模板配置获取"
msgstr ""

#: classes/_localize.php:148
msgctxt "vue部分"
msgid "短信类型"
msgstr ""

#: classes/_localize.php:149
msgctxt "vue部分"
msgid "行业短信-通用"
msgstr ""

#: classes/_localize.php:150
msgctxt "vue部分"
msgid "模板名称"
msgstr ""

#: classes/_localize.php:151
msgctxt "vue部分"
msgid "自定义命名"
msgstr ""

#: classes/_localize.php:152
msgctxt "vue部分"
msgid "短信内容"
msgstr ""

#: classes/_localize.php:153
msgctxt "vue部分"
msgid "自定义。模板文本变量{$var1}。例如：收到{$var1}的留言。"
msgstr ""

#: classes/_localize.php:154
msgctxt "vue部分"
msgid "签名"
msgstr ""

#: classes/_localize.php:155
msgctxt "vue部分"
msgid "测试短信"
msgstr ""

#: classes/_localize.php:156
msgctxt "vue部分"
msgid "输入手机号"
msgstr ""

#: classes/_localize.php:157
msgctxt "vue部分"
msgid "测试短信返回结果："
msgstr ""

#: classes/_localize.php:158
msgctxt "vue部分"
msgid "Access Key Id"
msgstr ""

#: classes/_localize.php:159
msgctxt "vue部分"
msgid "登录阿里云工作台>点击主账号头像>AccessKey管理，创建生成。"
msgstr ""

#: classes/_localize.php:160
msgctxt "vue部分"
msgid "Access Key Secret"
msgstr ""

#: classes/_localize.php:161
msgctxt "vue部分"
msgid "短信签名"
msgstr ""

#: classes/_localize.php:162
msgctxt "vue部分"
msgid "登录阿里云短信服务工作台>国内消息>模板管理>查看短信模板详情>关联签名。"
msgstr ""

#: classes/_localize.php:163
msgctxt "vue部分"
msgid "模板CODE"
msgstr ""

#: classes/_localize.php:164
msgctxt "vue部分"
msgid "登录阿里云短信服务工作台>国内消息>模板管理，填入对应模板的CODE。"
msgstr ""

#: classes/_localize.php:165
msgctxt "vue部分"
msgid "模板类型"
msgstr ""

#: classes/_localize.php:166
msgctxt "vue部分"
msgid "通知短信"
msgstr ""

#: classes/_localize.php:167
msgctxt "vue部分"
msgid "模版内容"
msgstr ""

#: classes/_localize.php:168
msgctxt "vue部分"
msgid "自定义，建议使用阿里云提供的常用模版库修改。变量${name}。如：收到${name}的留言。"
msgstr ""

#: classes/_localize.php:169
msgctxt "vue部分"
msgid "申请说明"
msgstr ""

#: classes/_localize.php:170
msgctxt "vue部分"
msgid "使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。"
msgstr ""

#: classes/_localize.php:171
msgctxt "vue部分"
msgid "接入地址"
msgstr ""

#: classes/_localize.php:172
msgctxt "vue部分"
msgid "登录华为云消息&短信控制台>国内短信>应用管理，填入对应的APP接入地址。"
msgstr ""

#: classes/_localize.php:173
msgctxt "vue部分"
msgid "Application Key"
msgstr ""

#: classes/_localize.php:174
msgctxt "vue部分"
msgid "登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Key。"
msgstr ""

#: classes/_localize.php:175
msgctxt "vue部分"
msgid "Application Secret"
msgstr ""

#: classes/_localize.php:176
msgctxt "vue部分"
msgid "登录华为云消息&短信控制台>国内短信>应用管理，填入对应的Application Secret。"
msgstr ""

#: classes/_localize.php:177
msgctxt "vue部分"
msgid "登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名名称。"
msgstr ""

#: classes/_localize.php:178
msgctxt "vue部分"
msgid "签名通道号"
msgstr ""

#: classes/_localize.php:179
msgctxt "vue部分"
msgid "登录华为云消息&短信控制台>国内短信>签名管理，填入对应的签名通道号。"
msgstr ""

#: classes/_localize.php:180
msgctxt "vue部分"
msgid "模板ID"
msgstr ""

#: classes/_localize.php:181
msgctxt "vue部分"
msgid "登录华为云消息&短信控制台>国内短信>模板管理，填入对应的模板ID。"
msgstr ""

#: classes/_localize.php:182
msgctxt "vue部分"
msgid "自定义。使用变量${1}。如：收到${1}的留言。"
msgstr ""

#: classes/_localize.php:183
msgctxt "vue部分"
msgid "使用场景：用于xx网站的用户留言及时通知站长；产品链接：您的网站域名地址。。"
msgstr ""

#: classes/_localize.php:184
msgctxt "vue部分"
msgid "当前为Free版，高级功能需"
msgstr ""

#: classes/_localize.php:185
msgctxt "vue部分"
msgid "升级为PRO版"
msgstr ""

#: classes/_localize.php:186
msgctxt "vue部分"
msgid "显示设备"
msgstr ""

#: classes/_localize.php:187
msgctxt "vue部分"
msgid "桌面端"
msgstr ""

#: classes/_localize.php:188
msgctxt "vue部分"
msgid "移动端"
msgstr ""

#: classes/_localize.php:189
msgctxt "vue部分"
msgid "显示页面"
msgstr ""

#: classes/_localize.php:190
msgctxt "vue部分"
msgid "全部页面"
msgstr ""

#: classes/_localize.php:191
msgctxt "vue部分"
msgid "指定页面"
msgstr ""

#: classes/_localize.php:192
msgctxt "vue部分"
msgid "例外页面"
msgstr ""

#: classes/_localize.php:193
msgctxt "vue部分"
msgid "缓存功能"
msgstr ""

#: classes/_localize.php:194
msgctxt "vue部分"
msgid "已开启，"
msgstr ""

#: classes/_localize.php:195
msgctxt "vue部分"
msgid "该功能会将模块静态化处理（适用于性能优化）。"
msgstr ""

#: classes/_localize.php:196
msgctxt "vue部分"
msgid "细节定制"
msgstr ""

#: classes/_localize.php:197
msgctxt "vue部分"
msgid "浮标模式"
msgstr ""

#: classes/_localize.php:198
msgctxt "vue部分"
msgid "浮标图标尺寸"
msgstr ""

#: classes/_localize.php:199
msgctxt "vue部分"
msgid "浮标文字"
msgstr ""

#: classes/_localize.php:200
msgctxt "vue部分"
msgid "浮标图标"
msgstr ""

#: classes/_localize.php:201
msgctxt "vue部分"
msgid "自定义浮标"
msgstr ""

#: classes/_localize.php:202
msgctxt "vue部分"
msgid "基础字号"
msgstr ""

#: classes/_localize.php:203
msgctxt "vue部分"
msgid "窗口宽度"
msgstr ""

#: classes/_localize.php:204
msgctxt "vue部分"
msgid "窗口头部"
msgstr ""

#: classes/_localize.php:205
msgctxt "vue部分"
msgid "背景颜色"
msgstr ""

#: classes/_localize.php:206
msgctxt "vue部分"
msgid "文字颜色"
msgstr ""

#: classes/_localize.php:207
msgctxt "vue部分"
msgid "展开模式"
msgstr ""

#: classes/_localize.php:208
msgctxt "vue部分"
msgid "尺寸"
msgstr ""

#: classes/_localize.php:209
msgctxt "vue部分"
msgid "留言窗口宽度"
msgstr ""

#: classes/_localize.php:210
msgctxt "vue部分"
msgid "位置调整"
msgstr ""

#: classes/_localize.php:211
msgctxt "vue部分"
msgid "横向偏移值"
msgstr ""

#: classes/_localize.php:212
msgctxt "vue部分"
msgid "纵向偏移值"
msgstr ""

#: classes/_localize.php:213
msgctxt "vue部分"
msgid "自定义CSS样式"
msgstr ""

#: classes/_localize.php:214
msgctxt "vue部分"
msgid "升级为Pro版"
msgstr ""

#: classes/_localize.php:215
msgctxt "vue部分"
msgid "操作成功"
msgstr ""

#: classes/_localize.php:216
msgctxt "vue部分"
msgid "确认标记为已处理？"
msgstr ""

#: classes/_localize.php:217
msgctxt "vue部分"
msgid "确认关闭？"
msgstr ""

#: classes/_localize.php:218
msgctxt "vue部分"
msgid "删除后将无法恢复，确认删除？"
msgstr ""

#: classes/_localize.php:219
msgctxt "vue部分"
msgid "请输入内容再提交"
msgstr ""

#: classes/_localize.php:220
msgctxt "vue部分"
msgid "已关闭"
msgstr ""

#: classes/_localize.php:221
msgctxt "vue部分"
msgid "待处理"
msgstr ""

#: classes/_localize.php:222
msgctxt "vue部分"
msgid "已处理"
msgstr ""

#: classes/_localize.php:223
msgctxt "vue部分"
msgid "未填写"
msgstr ""

#: classes/_localize.php:224
msgctxt "vue部分"
msgid "联系人"
msgstr ""

#: classes/_localize.php:225
msgctxt "vue部分"
msgid "发起时间"
msgstr ""

#: classes/_localize.php:226
msgctxt "vue部分"
msgid "更新时间"
msgstr ""

#: classes/_localize.php:227
msgctxt "vue部分"
msgid "关闭工单"
msgstr ""

#: classes/_localize.php:228
msgctxt "vue部分"
msgid "返回列表"
msgstr ""

#: classes/_localize.php:229
msgctxt "vue部分"
msgid "备注:"
msgstr ""

#: classes/_localize.php:230
msgctxt "vue部分"
msgid "留言内容:"
msgstr ""

#: classes/_localize.php:231
msgctxt "vue部分"
msgid "请输入备注"
msgstr ""

#: classes/_localize.php:232
msgctxt "vue部分"
msgid "添加备注"
msgstr ""

#: classes/_localize.php:233
msgctxt "vue部分"
msgid "关闭"
msgstr ""

#: classes/_localize.php:234
msgctxt "vue部分"
msgid "标记为已处理"
msgstr ""

#: classes/_localize.php:235
msgctxt "vue部分"
msgid "全部"
msgstr ""

#: classes/_localize.php:236
msgctxt "vue部分"
msgid "工单类型"
msgstr ""

#: classes/_localize.php:237
msgctxt "vue部分"
msgid "输入关键字"
msgstr ""

#: classes/_localize.php:238
msgctxt "vue部分"
msgid "筛选"
msgstr ""

#: classes/_localize.php:239
msgctxt "vue部分"
msgid "时间"
msgstr ""

#: classes/_localize.php:240
msgctxt "vue部分"
msgid "详情"
msgstr ""

#: classes/_localize.php:241
msgctxt "vue部分"
msgid "多合一在线客服插件"
msgstr ""

#: classes/_localize.php:242
msgctxt "vue部分"
msgid "插件设置"
msgstr ""

#: classes/_localize.php:243
msgctxt "vue部分"
msgid "基本设置"
msgstr ""

#: classes/_localize.php:244
msgctxt "vue部分"
msgid "组件设置"
msgstr ""

#: classes/_localize.php:245
msgctxt "vue部分"
msgid "高级设置"
msgstr ""

#: classes/_localize.php:246
msgctxt "vue部分"
msgid "组件编辑"
msgstr ""

#: classes/_localize.php:247
msgctxt "vue部分"
msgid "工单管理"
msgstr ""

#: classes/_localize.php:248
msgctxt "vue部分"
msgid "工单详情"
msgstr ""

#: classes/_localize.php:249
msgctxt "vue部分"
msgid "主题推荐"
msgstr ""

#: classes/_localize.php:250
msgctxt "vue部分"
msgid "插件推荐"
msgstr ""

#: classes/_localize.php:251
msgctxt "vue部分"
msgid "WP教程"
msgstr ""

#: classes/_localize.php:252
msgctxt "vue部分"
msgid "全屏"
msgstr ""

#: classes/_localize.php:253
msgctxt "vue部分"
msgid "闪电博"
msgstr ""

#: classes/_localize.php:254
msgctxt "vue部分"
msgid "免费插件"
msgstr ""

#: classes/_localize.php:255
msgctxt "vue部分"
msgid "说明文档"
msgstr ""

#: classes/_localize.php:256
msgctxt "vue部分"
msgid "服务协议"
msgstr ""

#: classes/_localize.php:257
msgctxt "vue部分"
msgid "隐私条例"
msgstr ""

#: classes/_localize.php:258
msgctxt "vue部分"
msgid "版本：%s"
msgstr ""

#: classes/_localize.php:259
msgctxt "vue部分"
msgid "保存设置"
msgstr ""

#: classes/_localize.php:260
msgctxt "vue部分"
msgid "首页"
msgstr ""

#: classes/_localize.php:261
msgctxt "vue部分"
msgid "PRO版"
msgstr ""

#: classes/_localize.php:262
msgctxt "vue部分"
msgid "Free版"
msgstr ""

#: classes/_localize.php:263
msgctxt "vue部分"
msgid "插件主页"
msgstr ""

#: classes/_localize.php:264
msgctxt "vue部分"
msgid "激活插件"
msgstr ""

#: classes/_localize.php:265
msgctxt "vue部分"
msgid "或"
msgstr ""

#: classes/_localize.php:266
msgctxt "vue部分"
msgid "现在更新"
msgstr ""

#: classes/_localize.php:267
msgctxt "vue部分"
msgid "当前%s有新版本可用."
msgstr ""

#: classes/_localize.php:268
msgctxt "vue部分"
msgid "查看版本%s详情"
msgstr ""

#: classes/_localize.php:269
msgctxt "vue部分"
msgid "温馨提示"
msgstr ""

#: classes/_localize.php:270
msgctxt "vue部分"
msgid "输入关键字筛选"
msgstr ""

#: classes/_localize.php:271
msgctxt "vue部分"
msgid "完成"
msgstr ""

#: classes/_localize.php:272
msgctxt "vue部分"
msgid "没有匹配 %s 的记录，请换个关键词试试..."
msgstr ""

#: classes/_localize.php:273
msgctxt "vue部分"
msgid "请输入标签"
msgstr ""

#: classes/_localize.php:274
msgctxt "vue部分"
msgid "已存在"
msgstr ""

#: classes/_localize.php:275
msgctxt "vue部分"
msgid "已移除"
msgstr ""

#: classes/_localize.php:276
msgctxt "vue部分"
msgid "别名:"
msgstr ""

#: classes/_localize.php:277
msgctxt "vue部分"
msgid "选择文件"
msgstr ""

#: classes/_localize.php:278
msgctxt "vue部分"
msgid "确认"
msgstr ""

#: classes/_localize.php:279
msgctxt "vue部分"
msgid "选择"
msgstr ""

#: classes/_localize.php:280
msgctxt "vue部分"
msgid "插入URL"
msgstr ""

#: classes/_localize.php:281
msgctxt "vue部分"
msgid "设置链接"
msgstr ""

#: classes/_localize.php:282
msgctxt "vue部分"
msgid "激活KEY"
msgstr ""

#: classes/_localize.php:283
msgctxt "vue部分"
msgid "请输入激活KEY"
msgstr ""

#: classes/_localize.php:284
msgctxt "vue部分"
msgid "获取KEY"
msgstr ""

#: classes/_localize.php:285
msgctxt "vue部分"
msgid "绑定信息确认"
msgstr ""

#: classes/_localize.php:286
msgctxt "vue部分"
msgid "提交验证"
msgstr ""

#: classes/_localize.php:287
msgctxt "vue部分"
msgid "请输入激活码"
msgstr ""

#: classes/_localize.php:288
msgctxt "vue部分"
msgid "验证中..."
msgstr ""

#: classes/_localize.php:289
msgctxt "vue部分"
msgid "验证成功"
msgstr ""

#: classes/_localize.php:290
msgctxt "vue部分"
msgid "绑定域名后将不可更改，确认绑定到域名：%s"
msgstr ""

#: inc/contact_form.php:29
msgctxt "表单字段名"
msgid "姓名"
msgstr ""

#: inc/contact_form.php:55
msgctxt "表单字段名"
msgid "联系方式"
msgstr ""

#: inc/contact_form.php:59
msgctxt "表单字段名"
msgid "留言"
msgstr ""

#: inc/contact_form.php:67
msgctxt "表单字段名"
msgid "验证码"
msgstr ""

#: inc/contact_form.php:68
msgctxt "表单字段名"
msgid "点击更换验证码"
msgstr ""

#: inc/contact_form.php:75
msgctxt "按钮"
msgid "提交"
msgstr ""

#: inc/contact_form.php:76, inc/contact_form.php:21
msgctxt "按钮"
msgid "取消"
msgstr ""

#: inc/contact_form.php:14
msgctxt "在线工单, 登录链接label"
msgid "立即登录"
msgstr ""

#: inc/contact_form.php:18
msgctxt "在线工单, 未登录时提示, %s 为登录链接"
msgid "需登录后才可留言。<br>您尚未登录网站账户，%s。"
msgstr ""

#: inc/fold.tpl.php:13
msgctxt "功能入口"
msgid "我的订单"
msgstr ""

#: inc/fold.tpl.php:79
msgid "点击复制微信号"
msgstr ""

#: inc/panel.tpl.php:155
msgid "注：点击复制微信号并打开微信APP，添加好友后进行聊天。"
msgstr ""

#: wbm/wbm.php:318
msgctxt "会员中心菜单"
msgid "退出"
msgstr ""

#: Plugin Name
msgid "多合一在线客服插件"
msgstr ""

#: Description
msgid "Online Contact Widget（多合一在线客服插件），旨在为WordPress网站提供一系列可配置在线客服支持，包括QQ、微信（微信号、公众号和小程序QR-code）、电话、Email和工单等（海外站点还可以选WhatsApp，Telegram，Line，Messenger,Viber和Signal）。"
msgstr ""

#: Author
msgid "闪电博"
msgstr ""
