import{a,aB as s,a2 as e,q as l,m as t,J as n,R as c,S as i,l as o,w as r,t as m}from"./wbs-Dtem2-xP.js";const u={class:"wbs-content"},d={class:"wbs-content-inner"},b={class:"wbs-menu-list"},v={class:"cell-items"},p={class:"cell-bd primary"},h={__name:"IndexForMobile",setup(h){const w=a([]);return Object.values(s).forEach((a=>{a.tab&&w.value.push({name:a.meta.label?a.meta.label:a.name,path:a.path})})),(a,s)=>{const h=e("router-link");return t(),l("div",u,[n("div",d,[n("div",b,[n("div",v,[(t(!0),l(c,null,i(w.value,((a,e)=>(t(),o(h,{class:"cell-item with-link",key:"index"+e,to:a.path},{default:r((()=>[n("div",p,m(a.name),1),s[0]||(s[0]=n("div",{class:"cell-ft"},null,-1))])),_:2},1032,["to"])))),128))])])])])}}};export{h as default};
