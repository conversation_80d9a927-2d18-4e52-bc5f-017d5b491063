// popup-modal.js
import React from 'react';
import { __ } from '@wordpress/i18n';

const PopupModal = ({ isVisible, onClose }) => {
    const icons = require.context('../assets/icons', false, /\.(png|svg|jpe?g|)$/);
    const AddBanner =icons(`./pro-banner-vector-laptop.svg`);
  if (!isVisible) return null;
  
  let logo =(
<svg width="148" height="54" viewBox="0 0 148 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M81.2414 23.285L78.3977 15.1202V23.3066H75.7549V8.99658H78.3977L81.0723 16.9022V8.99658H83.7152V23.285H81.2414Z" fill="white"/>
<path d="M89.4026 23.544C88.798 23.5576 88.1993 23.4204 87.6583 23.1444C87.1606 22.8923 86.7292 22.5219 86.4003 22.0644C86.0489 21.5388 85.7912 20.9538 85.6392 20.3364C85.4574 19.6317 85.3685 18.9052 85.3749 18.1764C85.3681 17.4489 85.4498 16.7234 85.618 16.0164C85.7574 15.4039 85.9968 14.8197 86.3263 14.2884C86.6275 13.8143 87.0329 13.4186 87.5103 13.1328C88.0163 12.8411 88.5891 12.692 89.17 12.7008C89.747 12.6853 90.3172 12.8309 90.8192 13.122C91.2686 13.3898 91.6519 13.7593 91.9398 14.202C92.2524 14.7271 92.4708 15.305 92.5846 15.9084C92.7362 16.6177 92.8071 17.3425 92.796 18.0684V18.9324H87.6583C87.6489 19.6008 87.8209 20.2587 88.1552 20.8332C88.3182 21.083 88.5428 21.2844 88.8064 21.4172C89.07 21.5499 89.3631 21.6093 89.6563 21.5892C89.9632 21.5768 90.2617 21.4839 90.5232 21.3192C90.842 21.1259 91.1161 20.8643 91.3266 20.5524L92.4895 21.9024C92.1156 22.4139 91.6354 22.8344 91.0835 23.1336C90.5638 23.4068 89.9871 23.5476 89.4026 23.544ZM90.5338 17.2044C90.5337 16.9116 90.5125 16.6192 90.4703 16.3296C90.4329 16.0436 90.3583 15.7639 90.2483 15.498C90.1566 15.2649 90.0116 15.0575 89.8255 14.8932C89.6227 14.7263 89.3667 14.6417 89.1066 14.6556C88.8477 14.6397 88.5931 14.729 88.3983 14.904C88.211 15.0764 88.06 15.2858 87.9543 15.5196C87.8408 15.785 87.7626 16.0647 87.7218 16.3512C87.6804 16.6337 87.6592 16.9188 87.6583 17.2044H90.5338Z" fill="white"/>
<path d="M101.105 23.2848L99.657 16.2L98.1876 23.2416H95.7139L93.3882 12.96H95.8408L97.141 20.034L98.4625 12.96H100.704L102.173 20.034L103.452 12.96H105.926L103.6 23.2848H101.105Z" fill="white"/>
<path d="M114.478 23.5442C113.97 23.5459 113.466 23.4582 112.987 23.285C112.511 23.1187 112.075 22.8498 111.708 22.4966C111.327 22.1096 111.028 21.6463 110.831 21.1358C110.592 20.5023 110.477 19.8272 110.493 19.1486V8.99658H113.114V19.2674C113.062 19.783 113.194 20.3005 113.484 20.7254C113.606 20.8646 113.756 20.9746 113.924 21.0474C114.093 21.1202 114.274 21.1541 114.457 21.1466C114.641 21.1542 114.824 21.1204 114.994 21.0476C115.164 20.9749 115.316 20.8648 115.44 20.7254C115.727 20.299 115.854 19.7817 115.799 19.2674V8.99658H118.442V19.1486C118.463 19.8276 118.348 20.5037 118.104 21.1358C117.91 21.6484 117.611 22.1125 117.227 22.4966C116.866 22.8518 116.433 23.1211 115.958 23.285C115.482 23.4558 114.982 23.5434 114.478 23.5442Z" fill="white"/>
<path d="M123.104 23.5441C122.431 23.5627 121.761 23.4376 121.138 23.1769C120.628 22.9646 120.178 22.627 119.827 22.1941L120.789 20.5201C121.07 20.8333 121.394 21.1026 121.751 21.3193C122.095 21.5324 122.491 21.6411 122.893 21.6325C123.22 21.6473 123.542 21.544 123.802 21.3409C123.914 21.2375 124.003 21.1107 124.064 20.9691C124.124 20.8275 124.154 20.6744 124.151 20.5201C124.155 20.388 124.131 20.2566 124.08 20.1352C124.029 20.0138 123.952 19.9055 123.855 19.8181C123.629 19.618 123.385 19.4408 123.125 19.2889L122.153 18.7813C121.805 18.601 121.482 18.376 121.191 18.1117C120.883 17.8352 120.635 17.4969 120.461 17.1181C120.247 16.649 120.145 16.1342 120.165 15.6169C120.165 15.246 120.236 14.8788 120.377 14.5369C120.515 14.1864 120.724 13.8698 120.99 13.6081C121.298 13.3235 121.657 13.1033 122.047 12.9601C122.495 12.7971 122.967 12.7167 123.443 12.7225C124.005 12.7216 124.562 12.8354 125.081 13.0573C125.569 13.2714 126.016 13.5711 126.403 13.9429L125.462 15.5737C125.23 15.306 124.95 15.0861 124.637 14.9257C124.291 14.731 123.901 14.6305 123.506 14.6341C123.364 14.6246 123.221 14.6446 123.087 14.6928C122.952 14.7411 122.829 14.8166 122.724 14.9149C122.627 14.9985 122.55 15.1028 122.497 15.2203C122.444 15.3379 122.416 15.4658 122.417 15.5953C122.408 15.8173 122.484 16.0343 122.629 16.2001C122.859 16.3926 123.107 16.5626 123.369 16.7077L124.352 17.2261C124.705 17.4061 125.032 17.635 125.324 17.9065C125.634 18.2165 125.885 18.5827 126.064 18.9865C126.283 19.4546 126.388 19.9694 126.371 20.4877C126.377 20.9192 126.29 21.3468 126.117 21.7405C125.954 22.1112 125.72 22.4452 125.43 22.7233C125.121 23.0001 124.762 23.2129 124.373 23.3497C123.964 23.4865 123.535 23.5522 123.104 23.5441Z" fill="white"/>
<path d="M131.382 23.544C130.777 23.5576 130.178 23.4204 129.637 23.1444C129.14 22.8923 128.708 22.5219 128.379 22.0644C128.032 21.5363 127.775 20.9521 127.618 20.3364C127.436 19.6317 127.347 18.9052 127.354 18.1764C127.347 17.4489 127.429 16.7234 127.597 16.0164C127.736 15.4039 127.976 14.8197 128.305 14.2884C128.61 13.8138 129.019 13.4182 129.5 13.1328C130.002 12.8415 130.571 12.6923 131.149 12.7008C131.726 12.6853 132.296 12.8309 132.798 13.122C133.248 13.3898 133.631 13.7593 133.919 14.202C134.231 14.7285 134.452 15.3056 134.574 15.9084C134.717 16.619 134.784 17.3432 134.775 18.0684V18.9324H129.637C129.628 19.6008 129.8 20.2587 130.134 20.8332C130.297 21.083 130.522 21.2844 130.785 21.4172C131.049 21.5499 131.342 21.6093 131.635 21.5892C131.942 21.5768 132.241 21.4839 132.502 21.3192C132.822 21.1234 133.099 20.8624 133.316 20.5524L134.468 21.9024C134.095 22.4139 133.614 22.8344 133.062 23.1336C132.543 23.4081 131.966 23.5489 131.382 23.544ZM132.513 17.2044C132.513 16.9116 132.491 16.6192 132.449 16.3296C132.412 16.0436 132.337 15.7639 132.227 15.498C132.139 15.263 131.993 15.0548 131.804 14.8932C131.602 14.7263 131.346 14.6417 131.086 14.6556C130.827 14.6409 130.573 14.73 130.377 14.904C130.19 15.0764 130.039 15.2858 129.933 15.5196C129.82 15.785 129.742 16.0647 129.701 16.3512C129.659 16.6337 129.638 16.9188 129.637 17.2044H132.513Z" fill="white"/>
<path d="M136.224 23.2847V12.9599H138.57V14.3963C138.784 13.912 139.12 13.4946 139.543 13.1867C139.92 12.8822 140.384 12.7115 140.864 12.7007V15.0767C140.71 15.0555 140.554 15.0555 140.399 15.0767C140.014 15.071 139.64 15.2049 139.342 15.4547C139.012 15.7139 138.736 16.0368 138.528 16.4051V23.3603L136.224 23.2847Z" fill="white"/>
<path d="M81.8754 44.712L81.3997 42.12H78.334L77.8477 44.7552H74.6763L78.0803 30.4668H81.6428L85.0363 44.712H81.8754ZM79.8668 33.4152L78.6828 39.6792H81.0191L79.8668 33.4152Z" fill="white"/>
<path d="M91.2735 44.9711C90.8169 44.961 90.37 44.8348 89.9732 44.6039C89.5025 44.3425 89.1306 43.9282 88.9161 43.4267V48.6539H86.1675V34.3655H88.9161V35.6399C89.1588 35.1635 89.5244 34.764 89.9732 34.4843C90.3562 34.2364 90.799 34.1018 91.2523 34.0955C91.7792 34.0712 92.3024 34.1954 92.7648 34.4544C93.2272 34.7135 93.6109 35.0974 93.8741 35.5643C94.5238 36.7799 94.8312 38.1556 94.762 39.5387C94.7692 40.2829 94.6947 41.0255 94.54 41.7527C94.4165 42.3567 94.1911 42.934 93.8741 43.4591C93.6041 43.8945 93.2431 44.2633 92.8169 44.5391C92.3502 44.8231 91.8167 44.9724 91.2735 44.9711ZM90.3643 42.8111C90.6404 42.8216 90.9108 42.7296 91.1255 42.5519C91.3333 42.3614 91.496 42.125 91.6012 41.8607C91.742 41.517 91.8277 41.1523 91.8549 40.7807C91.9192 40.3733 91.9581 39.962 91.9712 39.5495C91.9748 39.1199 91.9501 38.6905 91.8972 38.2643C91.8687 37.8929 91.7831 37.5285 91.6435 37.1843C91.5382 36.9138 91.3758 36.6704 91.1678 36.4715C90.9531 36.2939 90.6827 36.2018 90.4066 36.2123C90.0964 36.2202 89.7996 36.3431 89.5715 36.5579C89.3087 36.7885 89.1054 37.0815 88.9795 37.4111V41.5799C89.1008 41.9213 89.3044 42.2259 89.5715 42.4655C89.7856 42.6773 90.0669 42.8037 90.3643 42.8219V42.8111Z" fill="white"/>
<path d="M101.644 44.9713C101.188 44.9585 100.742 44.8325 100.344 44.6041C99.8751 44.34 99.5039 43.9266 99.2867 43.4269V48.6541H96.5381V34.3657H99.3712V35.6401C99.6079 35.1593 99.9749 34.7582 100.428 34.4845C100.808 34.2385 101.247 34.104 101.697 34.0957C102.225 34.0718 102.75 34.196 103.214 34.4549C103.678 34.7138 104.063 35.0975 104.329 35.5645C104.972 36.7823 105.276 38.157 105.207 39.5389C105.214 40.283 105.139 41.0257 104.985 41.7529C104.865 42.3562 104.643 42.9336 104.329 43.4593C104.058 43.894 103.698 44.2626 103.272 44.5393C102.781 44.8405 102.216 44.9905 101.644 44.9713ZM100.746 42.8113C101.019 42.8275 101.288 42.7346 101.496 42.5521C101.71 42.3652 101.877 42.128 101.982 41.8609C102.112 41.5135 102.197 41.1505 102.236 40.7809C102.289 40.3546 102.313 39.9253 102.31 39.4957C102.314 39.0661 102.289 38.6367 102.236 38.2105C102.197 37.8408 102.112 37.4778 101.982 37.1305C101.863 36.8872 101.694 36.6734 101.486 36.5041C101.278 36.3215 101.009 36.2286 100.735 36.2449C100.424 36.2508 100.127 36.3739 99.8998 36.5905C99.635 36.8192 99.4314 37.1127 99.3078 37.4437V41.5801C99.4253 41.9234 99.6295 42.229 99.8998 42.4657C100.126 42.6915 100.429 42.8192 100.746 42.8221V42.8113Z" fill="white"/>
<path d="M106.951 44.7119V34.3655H109.7V35.8019C109.953 35.2896 110.341 34.8595 110.82 34.5599C111.285 34.2578 111.823 34.0933 112.374 34.0847V36.4607C112.198 36.4179 112.017 36.3961 111.835 36.3959C111.408 36.3992 110.991 36.5347 110.641 36.7847C110.27 37.0322 109.95 37.3514 109.7 37.7243V44.7119H106.951Z" fill="white"/>
<path d="M117.819 44.9712C117.127 44.9894 116.441 44.8337 115.821 44.5176C115.274 44.2335 114.8 43.823 114.436 43.3188C114.058 42.7994 113.785 42.2083 113.633 41.58C113.462 40.91 113.377 40.2203 113.379 39.528C113.377 38.8321 113.462 38.1389 113.633 37.4652C113.787 36.8411 114.06 36.254 114.436 35.7372C114.795 35.2306 115.27 34.8225 115.821 34.5492C116.45 34.2571 117.133 34.106 117.824 34.106C118.515 34.106 119.198 34.2571 119.828 34.5492C120.374 34.8286 120.849 35.2356 121.212 35.7372C121.592 36.2533 121.868 36.8404 122.026 37.4652C122.197 38.1389 122.282 38.8321 122.28 39.528C122.282 40.2203 122.197 40.91 122.026 41.58C121.87 42.2089 121.594 42.7999 121.212 43.3188C120.843 43.8181 120.37 44.2275 119.828 44.5176C119.205 44.8354 118.515 44.9912 117.819 44.9712ZM117.819 42.8112C118.103 42.821 118.381 42.7251 118.601 42.5412C118.825 42.3548 118.994 42.1078 119.088 41.8284C119.215 41.4805 119.301 41.1177 119.341 40.7484C119.401 40.344 119.44 39.9366 119.458 39.528C119.456 39.1093 119.431 38.6911 119.384 38.2752C119.345 37.9056 119.26 37.5426 119.13 37.1952C119.034 36.9141 118.866 36.6643 118.644 36.4716C118.423 36.2878 118.145 36.1918 117.861 36.2016C117.722 36.1922 117.581 36.2112 117.449 36.2575C117.316 36.3039 117.194 36.3766 117.09 36.4716C116.871 36.6696 116.7 36.9175 116.593 37.1952C116.463 37.5426 116.378 37.9056 116.339 38.2752C116.286 38.6906 116.262 39.1092 116.265 39.528C116.262 39.9504 116.287 40.3726 116.339 40.7916C116.38 41.1609 116.465 41.5237 116.593 41.8716C116.698 42.1477 116.868 42.3927 117.09 42.5844C117.295 42.7539 117.555 42.8387 117.819 42.822V42.8112Z" fill="white"/>
<path d="M126.107 44.7119L122.936 34.3655H125.864L127.555 41.6879L129.247 34.3655H132.186L129.014 44.7119H126.107Z" fill="white"/>
<path d="M137.661 44.9712C136.965 44.9817 136.274 44.8457 135.632 44.5716C135.064 44.3309 134.558 43.9616 134.152 43.4916C133.747 42.9811 133.445 42.3938 133.264 41.7636C133.052 41.0643 132.949 40.3355 132.957 39.6036C132.951 38.8741 133.044 38.1473 133.232 37.4436C133.402 36.8226 133.685 36.24 134.067 35.7264C134.436 35.2356 134.91 34.8368 135.452 34.56C136.057 34.2599 136.724 34.1118 137.397 34.128C138.062 34.1082 138.722 34.2526 139.321 34.5492C139.827 34.8027 140.266 35.1768 140.6 35.64C140.962 36.1477 141.221 36.7242 141.361 37.3356C141.534 38.0418 141.615 38.7679 141.604 39.4956V40.3488H135.632C135.615 41.0323 135.815 41.703 136.202 42.2604C136.418 42.5159 136.689 42.7164 136.993 42.8458C137.298 42.9752 137.628 43.0299 137.957 43.0056C138.326 43.0077 138.69 42.9148 139.014 42.7356C139.378 42.5517 139.702 42.2945 139.966 41.9796L141.308 43.3296C140.857 43.8577 140.298 44.278 139.67 44.5608C139.037 44.843 138.352 44.983 137.661 44.9712ZM138.993 38.6208C138.989 38.3278 138.964 38.0355 138.919 37.746C138.873 37.4602 138.784 37.1833 138.655 36.9252C138.541 36.6816 138.371 36.4703 138.158 36.3096C137.91 36.1452 137.619 36.0623 137.323 36.072C137.025 36.0587 136.732 36.146 136.488 36.3204C136.275 36.4904 136.098 36.7037 135.97 36.9468C135.837 37.203 135.747 37.4806 135.706 37.7676C135.656 38.0492 135.631 38.3347 135.632 38.6208H138.993Z" fill="white"/>
<path d="M15.5508 49.3886C15.5508 49.3886 16.7454 51.8618 19.0816 51.3326L41.1125 46.1486H60.0142C60.0142 46.1486 62.9848 45.911 62.3082 41.537C62.3082 41.537 65.7968 41.6558 65.8814 38.3726L62.4245 22.3454V7.89502C62.3146 7.18752 61.9573 6.54475 61.4188 6.08597C60.8803 5.62718 60.1972 5.38353 59.4962 5.40022H49.7916L23.3631 29.4194L16.2802 44.6366L15.5508 49.3886Z" fill="#E2FDE1"/>
<path d="M60.3099 40.2948L16.1741 49.0429C15.2925 49.2174 14.3792 49.0278 13.6341 48.5157C12.8891 48.0035 12.3731 47.2105 12.1993 46.3105L6.76557 17.7229C6.6873 17.3178 6.68819 16.901 6.76817 16.4963C6.84815 16.0917 7.00565 15.7071 7.23162 15.3649C7.45758 15.0226 7.74755 14.7294 8.08484 14.502C8.42214 14.2746 8.8001 14.1176 9.197 14.0401L53.4701 5.28125C54.4457 5.08772 55.4566 5.29741 56.281 5.86431C57.1054 6.43122 57.6761 7.30905 57.8679 8.30525L63.1536 36.018C63.2425 36.4897 63.2394 36.9746 63.1445 37.445C63.0496 37.9155 62.8647 38.3621 62.6006 38.7594C62.3364 39.1567 61.9981 39.4969 61.605 39.7603C61.2119 40.0238 60.7718 40.2055 60.3099 40.2948Z" fill="#F8FFF8"/>
<path d="M53.4808 4.56824L17.9714 11.6206L9.68338 13.2622C9.24372 13.3489 8.82512 13.5234 8.45156 13.7756C8.078 14.0278 7.75681 14.3528 7.5064 14.732C7.25598 15.1112 7.08127 15.5372 6.99225 15.9855C6.90324 16.4339 6.90168 16.8957 6.98767 17.3446L7.23081 18.6514L57.7305 8.63984L57.4768 7.32224C57.3029 6.4165 56.7847 5.61809 56.0357 5.1019C55.2867 4.58571 54.368 4.39381 53.4808 4.56824Z" fill="#BADCB9"/>
<path d="M53.4068 4.1798L17.8657 11.2214L9.60936 12.863C9.11857 12.9601 8.65129 13.1549 8.23424 13.4365C7.81718 13.7181 7.45851 14.0809 7.17873 14.5041C6.89894 14.9274 6.70352 15.4028 6.60362 15.9032C6.50372 16.4036 6.5013 16.9192 6.59651 17.4206L6.85022 18.7274L12.1359 46.3538C12.3302 47.3645 12.9091 48.2551 13.7455 48.8301C14.5819 49.4052 15.6075 49.6177 16.5971 49.421L24.8534 47.7902L60.3945 40.7378C60.8853 40.6408 61.3526 40.4459 61.7696 40.1643C62.1867 39.8827 62.5454 39.5199 62.8251 39.0967C63.1049 38.6734 63.3004 38.198 63.4003 37.6976C63.5002 37.1972 63.5026 36.6816 63.4074 36.1802L61.5785 26.5682V26.3522L58.1428 8.5646L57.8997 7.247C57.8036 6.74326 57.611 6.26391 57.3327 5.83661C57.0545 5.40932 56.6962 5.04252 56.2785 4.75738C55.8609 4.47224 55.3921 4.2744 54.8992 4.17527C54.4063 4.07614 53.8991 4.07768 53.4068 4.1798ZM61.1451 26.6438L62.9739 36.2558C63.0597 36.7063 63.0573 37.1696 62.967 37.6191C62.8766 38.0686 62.7001 38.4954 62.4475 38.8749C62.195 39.2544 61.8714 39.5791 61.4955 39.8302C61.1195 40.0814 60.6986 40.254 60.2571 40.3382L24.7371 47.3906L16.4808 49.0322C15.5939 49.2037 14.6766 49.0106 13.9283 48.4949C13.18 47.9792 12.6613 47.1826 12.4848 46.2782L7.19908 18.6518L6.95594 17.345C6.86995 16.8961 6.87151 16.4342 6.96053 15.9859C7.04954 15.5376 7.22426 15.1116 7.47467 14.7324C7.72508 14.3532 8.04627 14.0281 8.41983 13.7759C8.79339 13.5237 9.21199 13.3493 9.65165 13.2626L17.9714 11.621L53.4808 4.5686C54.368 4.39417 55.2867 4.58606 56.0357 5.10226C56.7847 5.61845 57.3029 6.41686 57.4768 7.3226L57.7305 8.6402L61.1134 26.4386L61.1451 26.6438Z" fill="#3B423B"/>
<path d="M15.8572 49.6802C14.9336 49.6885 14.0356 49.3705 13.3151 48.7801C12.5946 48.1897 12.0959 47.3631 11.9035 46.4402L6.40632 17.453C6.20783 16.3891 6.43015 15.2883 7.02459 14.3915C7.61902 13.4947 8.5371 12.8752 9.57775 12.6686L53.3752 4.02862C54.4178 3.82563 55.4967 4.05246 56.3762 4.65954C57.2557 5.26662 57.8642 6.20462 58.0689 7.26862L63.566 36.191C63.7676 37.2565 63.5468 38.3602 62.9521 39.2594C62.3574 40.1586 61.4375 40.7797 60.3946 40.9862L16.5972 49.6802C16.3509 49.6993 16.1035 49.6993 15.8572 49.6802ZM60.8386 40.3814C60.6796 40.436 60.5171 40.4793 60.3523 40.511C60.506 40.485 60.6578 40.4489 60.8069 40.403L60.8386 40.3814ZM54.168 4.74142C53.962 4.74162 53.7566 4.76333 53.5549 4.80622L9.74689 13.4462C9.32907 13.5199 8.92997 13.6785 8.57319 13.9126C8.2164 14.1466 7.90917 14.4514 7.66968 14.8088C7.43018 15.1663 7.26327 15.5691 7.17883 15.9936C7.09438 16.4181 7.09411 16.8556 7.17803 17.2802L12.6857 46.235C12.851 47.0903 13.3414 47.8437 14.0495 48.3302C14.3973 48.5714 14.7885 48.7395 15.2005 48.8248C15.6125 48.91 16.0369 48.9108 16.4492 48.827L60.2572 40.187C60.6732 40.1096 61.0699 39.9479 61.4239 39.7116C61.7779 39.4752 62.0821 39.1688 62.3185 38.8106C62.5549 38.4524 62.7188 38.0494 62.8005 37.6255C62.8822 37.2016 62.8801 36.7653 62.7943 36.3422L57.2866 7.36582C57.1419 6.61581 56.7463 5.94058 56.1677 5.45586C55.5891 4.97114 54.8634 4.70711 54.1152 4.70902L54.168 4.74142ZM13.5526 48.4706C13.7214 48.609 13.9018 48.7319 14.0917 48.8378C13.9964 48.7866 13.9046 48.7289 13.8169 48.665L13.5526 48.4706ZM12.2946 46.3106C12.2946 46.3106 12.2946 46.3106 12.2946 46.397C12.2946 46.4834 12.2946 46.343 12.2946 46.3106ZM61.3143 26.6006L63.1432 36.2234L57.6672 7.29022L61.3143 26.6006ZM7.01946 15.2066C6.72845 15.8856 6.6473 16.6392 6.78689 17.3666C6.649 16.6463 6.73016 15.9001 7.01946 15.2282V15.2066ZM9.13375 13.187C8.63461 13.3697 8.18017 13.6608 7.80175 14.0402C8.17851 13.6691 8.62925 13.3854 9.12317 13.2086L9.13375 13.187ZM55.87 4.76302C55.9654 4.81422 56.0572 4.87195 56.1449 4.93582L56.4092 5.13022C56.2404 4.99185 56.06 4.86898 55.87 4.76302Z" fill="#3B423B"/>
<path d="M57.4133 44.9714H11.2056V9.2666H57.223C58.1987 9.2666 59.1344 9.66257 59.8243 10.3674C60.5143 11.0722 60.9019 12.0282 60.9019 13.025V41.4182C60.9019 41.8857 60.8115 42.3487 60.6361 42.7804C60.4607 43.2122 60.2035 43.6044 59.8794 43.9345C59.5554 44.2646 59.1707 44.5261 58.7475 44.704C58.3243 44.882 57.8709 44.9728 57.4133 44.9714Z" fill="#F8FFF8"/>
<path d="M9.26074 12.0638V41.537C9.26213 41.9951 9.35182 42.4484 9.5247 42.8711C9.69758 43.2938 9.95026 43.6775 10.2683 44.0005C10.5864 44.3234 10.9636 44.5792 11.3784 44.7532C11.7932 44.9272 12.2375 45.016 12.6859 45.0146H21.143V8.64016H12.6859C11.7893 8.63716 10.9276 8.99452 10.2868 9.63508C9.64592 10.2756 9.27739 11.148 9.26074 12.0638Z" fill="#F0FBF0"/>
<path d="M9.26074 8.58594V30.1643H21.0902V8.58594H9.26074Z" fill="#D1EAD0"/>
<path d="M57.2869 8.64014H12.6861C11.7823 8.64013 10.9154 9.00618 10.2753 9.65804C9.63526 10.3099 9.27428 11.1944 9.27148 12.1177V13.4569H60.7015V12.0637C60.6849 11.1498 60.3178 10.2791 59.6792 9.63885C59.0407 8.9986 58.1816 8.63998 57.2869 8.64014Z" fill="#BADCB9"/>
<path d="M57.2865 8.1758H12.6856C11.6763 8.1758 10.7083 8.58542 9.99455 9.31456C9.28084 10.0437 8.87988 11.0326 8.87988 12.0638V41.537C8.87988 42.5682 9.28084 43.5571 9.99455 44.2862C10.7083 45.0154 11.6763 45.425 12.6856 45.425H57.297C58.3064 45.425 59.2744 45.0154 59.9881 44.2862C60.7018 43.5571 61.1027 42.5682 61.1027 41.537V12.0638C61.1027 11.5523 61.004 11.0458 60.812 10.5734C60.6201 10.101 60.3388 9.67192 59.9843 9.31074C59.6298 8.94956 59.209 8.6634 58.7461 8.46865C58.2831 8.27389 57.7871 8.17438 57.2865 8.1758ZM60.701 31.7414V41.537C60.6982 42.4603 60.3372 43.3448 59.6972 43.9967C59.0571 44.6486 58.1902 45.0146 57.2865 45.0146H12.6856C11.7818 45.0146 10.9149 44.6486 10.2749 43.9967C9.63481 43.3448 9.27382 42.4603 9.27103 41.537V12.0638C9.28765 11.1499 9.65472 10.2792 10.2933 9.63891C10.9318 8.99866 11.7909 8.64004 12.6856 8.6402H57.297C58.2008 8.64019 59.0677 9.00624 59.7078 9.6581C60.3478 10.31 60.7088 11.1945 60.7116 12.1178V31.7414H60.701Z" fill="#3B423B"/>
<path d="M57.2869 45.619H12.6861C12.1599 45.6205 11.6387 45.516 11.152 45.3116C10.6654 45.1072 10.2229 44.8069 9.84991 44.4278C9.47689 44.0487 9.1806 43.5983 8.97797 43.1022C8.77534 42.6061 8.67033 42.0742 8.66895 41.5366V12.0634C8.66894 11.525 8.77293 10.9918 8.97494 10.4945C9.17695 9.99722 9.47301 9.5455 9.84618 9.16527C10.2193 8.78504 10.6623 8.48377 11.1496 8.2787C11.6369 8.07363 12.159 7.96881 12.6861 7.97023H57.2975C57.8246 7.96881 58.3467 8.07363 58.834 8.2787C59.3213 8.48377 59.7643 8.78504 60.1374 9.16527C60.5106 9.5455 60.8067 9.99722 61.0087 10.4945C61.2107 10.9918 61.3147 11.525 61.3147 12.0634V41.5366C61.3133 42.0751 61.2079 42.6079 61.0046 43.1047C60.8013 43.6015 60.5041 44.0524 60.13 44.4316C59.7558 44.8109 59.3121 45.111 58.8242 45.3147C58.3364 45.5185 57.814 45.6219 57.2869 45.619ZM12.6861 8.78023C11.845 8.78023 11.0383 9.12158 10.4435 9.7292C9.84879 10.3368 9.51466 11.1609 9.51466 12.0202V41.5366C9.51466 42.3959 9.84879 43.22 10.4435 43.8277C11.0383 44.4353 11.845 44.7766 12.6861 44.7766H57.2975C58.1386 44.7766 58.9453 44.4353 59.5401 43.8277C60.1348 43.22 60.4689 42.3959 60.4689 41.5366V12.0634C60.4689 11.2041 60.1348 10.38 59.5401 9.7724C58.9453 9.16478 58.1386 8.82343 57.2975 8.82343L12.6861 8.78023ZM12.6861 8.38063H57.2975C58.0357 8.38283 58.7555 8.61583 59.3603 9.0483C59.965 9.48077 60.4257 10.092 60.6804 10.7998C60.4268 10.0883 59.9652 9.4735 59.3583 9.03884C58.7513 8.60419 58.0282 8.37064 57.2869 8.36983L12.6861 8.38063Z" fill="#3B423B"/>
<path d="M15.6036 20.6605C20.1576 20.6605 23.8493 16.8889 23.8493 12.2365C23.8493 7.58405 20.1576 3.8125 15.6036 3.8125C11.0496 3.8125 7.35791 7.58405 7.35791 12.2365C7.35791 16.8889 11.0496 20.6605 15.6036 20.6605Z" fill="#B6DEB5"/>
<path d="M15.0114 26.9136C16.5411 26.9136 17.7811 25.6467 17.7811 24.084C17.7811 22.5212 16.5411 21.2544 15.0114 21.2544C13.4817 21.2544 12.2417 22.5212 12.2417 24.084C12.2417 25.6467 13.4817 26.9136 15.0114 26.9136Z" fill="#F8FFF8"/>
<path d="M19.3668 34.0199H11.8188C11.6983 34.0199 11.5826 33.971 11.4974 33.8839C11.4122 33.7968 11.3643 33.6787 11.3643 33.5555C11.3642 33.495 11.3761 33.4352 11.399 33.3794C11.422 33.3237 11.4557 33.2732 11.498 33.2309C11.5404 33.1886 11.5906 33.1555 11.6457 33.1333C11.7008 33.1112 11.7596 33.1005 11.8188 33.1019H19.3668C19.426 33.1005 19.4849 33.1112 19.54 33.1333C19.5951 33.1555 19.6453 33.1886 19.6876 33.2309C19.73 33.2732 19.7636 33.3237 19.7866 33.3794C19.8096 33.4352 19.8214 33.495 19.8214 33.5555C19.8214 33.6787 19.7735 33.7968 19.6883 33.8839C19.603 33.971 19.4874 34.0199 19.3668 34.0199Z" fill="#D1EAD0"/>
<path d="M17.1997 36.4501H11.8188C11.6983 36.4501 11.5826 36.4012 11.4974 36.3141C11.4122 36.227 11.3643 36.1089 11.3643 35.9857C11.3642 35.9252 11.3761 35.8653 11.399 35.8096C11.422 35.7538 11.4557 35.7034 11.498 35.6611C11.5404 35.6188 11.5906 35.5856 11.6457 35.5635C11.7008 35.5413 11.7596 35.5307 11.8188 35.5321H17.1997C17.2589 35.5307 17.3178 35.5413 17.3728 35.5635C17.4279 35.5856 17.4781 35.6188 17.5205 35.6611C17.5629 35.7034 17.5965 35.7538 17.6195 35.8096C17.6424 35.8653 17.6543 35.9252 17.6543 35.9857C17.6543 36.1089 17.6064 36.227 17.5211 36.3141C17.4359 36.4012 17.3202 36.4501 17.1997 36.4501Z" fill="#D1EAD0"/>
<path d="M22.4607 13.3435C23.5068 8.8155 20.7618 4.27844 16.3296 3.20973C11.8974 2.14102 7.45633 4.94535 6.41024 9.47339C5.36414 14.0014 8.10912 18.5385 12.5413 19.6072C16.9735 20.6759 21.4146 17.8716 22.4607 13.3435Z" fill="#74B27B"/>
<path d="M14.4407 20.2499C12.73 20.252 11.057 19.7357 9.63359 18.7662C8.21014 17.7968 7.10013 16.4178 6.444 14.8037C5.78788 13.1896 5.61512 11.413 5.94759 9.69858C6.28006 7.98416 7.10281 6.40901 8.31175 5.17242C9.5207 3.93582 11.0615 3.09335 12.7392 2.7516C14.417 2.40985 16.1562 2.58417 17.7369 3.2525C19.3177 3.92084 20.6688 5.05316 21.6195 6.5062C22.5701 7.95924 23.0776 9.66771 23.0776 11.4155C23.0748 13.7557 22.1643 15.9995 20.5455 17.6554C18.9266 19.3112 16.7315 20.2441 14.4407 20.2499ZM14.4407 3.40185C12.8863 3.39972 11.3663 3.8688 10.073 4.74971C8.77968 5.63062 7.7713 6.88375 7.1755 8.35046C6.5797 9.81716 6.42326 11.4315 6.72599 12.9891C7.02873 14.5467 7.77702 15.9775 8.87613 17.1004C9.97525 18.2232 11.3758 18.9877 12.9004 19.297C14.425 19.6063 16.0052 19.4464 17.4408 18.8378C18.8765 18.2291 20.1031 17.1989 20.9654 15.8776C21.8277 14.5564 22.2868 13.0034 22.2847 11.4155C22.2819 9.29099 21.4546 7.25436 19.9842 5.75214C18.5137 4.24991 16.5202 3.40471 14.4407 3.40185Z" fill="#3B423B"/>
<path d="M13.3622 15.1198C13.2694 15.1199 13.1776 15.1009 13.0922 15.0637C13.0068 15.0266 12.9296 14.9722 12.8654 14.9038L10.0111 11.9878C9.87092 11.8517 9.78938 11.6644 9.78442 11.4669C9.77946 11.2694 9.8515 11.078 9.98467 10.9348C10.1178 10.7916 10.3013 10.7083 10.4946 10.7032C10.6879 10.6981 10.8752 10.7717 11.0154 10.9078L13.3305 13.273L17.8234 8.02419C17.8838 7.9484 17.9585 7.88589 18.0432 7.84042C18.1279 7.79496 18.2207 7.76748 18.316 7.75965C18.4113 7.75182 18.5072 7.7638 18.5979 7.79486C18.6886 7.82593 18.7722 7.87543 18.8436 7.9404C18.9151 8.00536 18.9729 8.08443 19.0136 8.17285C19.0544 8.26127 19.0771 8.35719 19.0806 8.45484C19.084 8.55249 19.0681 8.64984 19.0337 8.74103C18.9993 8.83221 18.9472 8.91534 18.8805 8.98539L13.8908 14.8174C13.8253 14.8923 13.7457 14.953 13.6567 14.9956C13.5676 15.0383 13.4711 15.0622 13.3728 15.0658L13.3622 15.1198Z" fill="white"/>
<path d="M13.3943 15.2602C13.1537 15.2567 12.9232 15.1603 12.7494 14.9902L9.89515 12.0742C9.81085 11.9875 9.74412 11.8847 9.69877 11.7717C9.65342 11.6586 9.63034 11.5375 9.63087 11.4154C9.63058 11.2948 9.6538 11.1755 9.69917 11.0642C9.74455 10.9529 9.81117 10.852 9.89515 10.7674C10.0685 10.5971 10.2996 10.502 10.54 10.502C10.7805 10.502 11.0116 10.5971 11.1849 10.7674L13.2992 12.9274L17.6546 7.85137C17.7321 7.7575 17.8272 7.68045 17.9343 7.6248C18.0414 7.56915 18.1584 7.53602 18.2783 7.52737C18.3976 7.51925 18.5173 7.53614 18.63 7.577C18.7428 7.61786 18.8461 7.68183 18.9337 7.76497C19.0256 7.84412 19.101 7.94129 19.1555 8.05074C19.21 8.16019 19.2424 8.27969 19.2509 8.40217C19.2588 8.52409 19.2423 8.64637 19.2023 8.76152C19.1623 8.87667 19.0997 8.98225 19.0183 9.07177L14.0286 14.9038C13.9474 15.0006 13.847 15.0786 13.7339 15.1326C13.6209 15.1866 13.4979 15.2154 13.3732 15.217L13.3943 15.2602ZM10.5083 10.9402C10.3737 10.9402 10.2447 10.9948 10.1495 11.092C10.0543 11.1892 10.0009 11.3211 10.0009 11.4586C10 11.5995 10.0532 11.7351 10.1489 11.8366L13.0032 14.7526C13.0992 14.8487 13.2281 14.9029 13.3626 14.9038C13.432 14.9018 13.5001 14.8844 13.5622 14.8526C13.6243 14.8209 13.6788 14.7757 13.722 14.7202L18.7434 8.85577C18.7893 8.80318 18.8243 8.74157 18.8461 8.67472C18.8679 8.60788 18.8761 8.53719 18.8703 8.46697C18.8601 8.33057 18.7997 8.2032 18.7012 8.11057C18.6497 8.06368 18.5894 8.028 18.5239 8.00572C18.4585 7.98344 18.3893 7.97502 18.3206 7.98097C18.1871 7.99135 18.0624 8.05311 17.9717 8.15377L13.3414 13.5538L10.8783 11.0374C10.7773 10.9435 10.6447 10.8932 10.5083 10.897V10.9402Z" fill="white"/>
<path d="M41.6832 17.4529C39.4248 17.455 37.2177 18.1407 35.3405 19.4235C33.4633 20.7062 32.0002 22.5284 31.136 24.66C30.2717 26.7915 30.0451 29.137 30.4847 31.4C30.9242 33.6631 32.0103 35.7424 33.6058 37.3754C35.2012 39.0083 37.2345 40.1217 39.4489 40.575C41.6632 41.0282 43.9594 40.801 46.0475 39.922C48.1356 39.043 49.9219 37.5517 51.1809 35.6363C52.44 33.7209 53.1152 31.4673 53.1215 29.1601C53.1243 27.6237 52.8305 26.1018 52.2569 24.6816C51.6833 23.2613 50.8412 21.9705 49.7788 20.8831C48.7164 19.7957 47.4545 18.9331 46.0653 18.3445C44.6762 17.7558 43.1871 17.4529 41.6832 17.4529ZM41.6832 38.9557C39.7797 38.9535 37.9195 38.3745 36.3382 37.2918C34.7569 36.2092 33.5256 34.6716 32.8 32.8737C32.0745 31.0758 31.8874 29.0984 32.2623 27.1918C32.6373 25.2852 33.5575 23.5351 34.9065 22.163C36.2554 20.7909 37.9725 19.8585 39.8404 19.4838C41.7083 19.1091 43.643 19.309 45.3997 20.0582C47.1563 20.8073 48.6559 22.072 49.7087 23.6923C50.7615 25.3125 51.3201 27.2154 51.3138 29.1601C51.3026 31.762 50.283 34.2535 48.4782 36.0893C46.6733 37.9251 44.2301 38.9557 41.6832 38.9557Z" fill="#3B423B"/>
<path d="M41.6832 31.2012C40.0176 31.2465 38.3854 31.6891 36.917 32.4935C35.4486 33.298 34.1847 34.4421 33.2261 35.8344L34.6638 36.9144C35.4572 35.749 36.5058 34.7888 37.7262 34.1102C38.9466 33.4315 40.3051 33.053 41.6938 33.0048C43.0839 33.053 44.444 33.4314 45.6661 34.11C46.8882 34.7886 47.9387 35.7488 48.7344 36.9144L50.1721 35.8344C49.2091 34.439 47.9399 33.2931 46.4658 32.4886C44.9918 31.6841 43.3539 31.2432 41.6832 31.2012Z" fill="#3B423B"/>
<path d="M41.6832 21.6C40.7452 21.5935 39.8265 21.8718 39.0435 22.3996C38.2605 22.9273 37.6485 23.6807 37.2852 24.5642C36.9219 25.4477 36.8236 26.4216 37.0027 27.3623C37.1818 28.303 37.6304 29.1681 38.2914 29.848C38.9525 30.5279 39.7963 30.992 40.7159 31.1813C41.6354 31.3706 42.5893 31.2767 43.4565 30.9115C44.3238 30.5462 45.0653 29.9261 45.5871 29.1298C46.1089 28.3334 46.3875 27.3967 46.3875 26.4384C46.3903 25.1599 45.8966 23.9324 45.0147 23.0254C44.1328 22.1183 42.9347 21.6057 41.6832 21.6ZM41.6832 29.4192C41.1023 29.4256 40.5327 29.2553 40.0467 28.9301C39.5608 28.6048 39.1805 28.1392 38.9543 27.5925C38.7281 27.0458 38.6662 26.4428 38.7765 25.8601C38.8867 25.2773 39.1641 24.7413 39.5734 24.3201C39.9827 23.8989 40.5054 23.6116 41.075 23.4947C41.6446 23.3778 42.2353 23.4367 42.772 23.6638C43.3088 23.8909 43.7673 24.276 44.0892 24.7701C44.4111 25.2641 44.5819 25.8449 44.5798 26.4384C44.5868 26.832 44.5172 27.2231 44.3749 27.589C44.2326 27.9549 44.0205 28.2882 43.751 28.5696C43.4814 28.851 43.1598 29.0748 42.8048 29.2281C42.4499 29.3814 42.0686 29.461 41.6832 29.4624V29.4192Z" fill="#3B423B"/>
</svg>
  );
  

  return (
    <div className="nua-parent-popup" style={styles.popupOverlay}>
      <div className="nua-pro-small-banner">
        <div style={styles.popupContent}>
          <div className="nua-pro-close">
            <span className="nua-pro-close-empty"></span>
            <span style={styles.closeButton} onClick={onClose}>
              &times;
            </span>
          </div>
          <div className="nua-popup-inner-content">
            <div className="popup-inner-img">
                <img src={AddBanner} alt="Popup" />
            </div>
          
            <div className="nua-pro-small-banner-content">
                <div className="nua-banner-logo">
                    {logo}
                </div>
                <h1>{__('Authenticate User Approval Process With New User Approve', 'new-user-approve')}</h1>
                <p>
                {__(
                    'Join over 20,000 users who are making their WordPress sites free from spam registration and fake user signups',
                    'new-user-approve'
                )}
                </p>
                <div className="nua-pro-small-banner-btns">
                <button onClick={() => window.open('https://newuserapprove.com/pricing/?utm_source=plugin&utm_medium=pro_popup', '_blank')}>
                    {__('Get Pro Now', 'new-user-approve')}
                </button>
        
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Include styles in this file or import them as needed
const styles = {
  popupOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10000,
    pointerEvents: 'auto',
    backdropFilter: 'blur(3px)',
  },
  popupContent: {
    padding: '40px'
  },
  closeButton: {
    fontSize: '35px',
    cursor: 'pointer',
  },
};

export default PopupModal;
