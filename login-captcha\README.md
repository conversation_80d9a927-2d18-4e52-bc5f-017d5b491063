# 登录验证码插件

一个简单而有效的WordPress登录验证码插件，为您的网站提供额外的安全保护。

## 功能特点

- ✅ 支持数字、字母或混合验证码
- ✅ 可自定义验证码长度和图片尺寸
- ✅ **输入框在前，验证码图片在后的优化布局**
- ✅ **输入框占据主要空间，确保文字显示完整**
- ✅ 响应式设计，适配移动设备
- ✅ 无障碍功能支持
- ✅ 简洁的管理界面
- ✅ 验证码自动过期机制
- ✅ 支持键盘快捷键操作
- ✅ 点击图片即可刷新验证码

## 安装说明

1. 将插件文件夹上传到 `/wp-content/plugins/` 目录
2. 在WordPress管理后台激活插件
3. 进入 "设置" > "登录验证码" 配置选项

## 系统要求

- WordPress 5.0 或更高版本
- PHP 7.4 或更高版本
- GD 图像处理扩展

## 使用方法

### 基本设置

1. 激活插件后，访问 "设置" > "登录验证码"
2. 启用验证码功能
3. 选择验证码类型（数字、字母或混合）
4. 设置验证码长度（建议4-6位）
5. 调整图片尺寸以适配您的主题

### 高级功能

- **自动刷新**: 验证码会在5分钟后自动过期
- **键盘支持**: 按F5可快速刷新验证码
- **无障碍**: 支持屏幕阅读器和键盘导航
- **响应式**: 自动适配不同屏幕尺寸

## 安全特性

- 验证码图片包含干扰线和噪点
- 每次刷新生成新的随机验证码
- 验证码使用后立即失效
- 支持大小写不敏感验证
- 防止暴力破解攻击

## 自定义开发

### 钩子和过滤器

```php
// 自定义验证码字符集
add_filter('login_captcha_characters', function($chars, $type) {
    if ($type === 'custom') {
        return '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'; // 排除易混淆字符
    }
    return $chars;
}, 10, 2);

// 自定义验证码验证
add_filter('login_captcha_validate', function($is_valid, $input, $stored) {
    // 自定义验证逻辑
    return $is_valid;
}, 10, 3);
```

### JavaScript事件

```javascript
// 监听验证码刷新事件
$(document).on('loginCaptchaRefreshed', function() {
    console.log('验证码已刷新');
});

// 监听初始化完成事件
$(document).on('loginCaptchaReady', function() {
    console.log('验证码功能已就绪');
});
```

## 故障排除

### 常见问题

**Q: 验证码图片不显示**
A: 检查服务器是否安装了GD扩展，确保temp目录有写入权限

**Q: 验证码总是提示错误**
A: 检查服务器时间设置，确保WordPress缓存插件没有缓存验证码

**Q: 移动设备上显示异常**
A: 检查主题CSS是否与插件样式冲突，可以调整图片尺寸

### 调试模式

在wp-config.php中添加以下代码启用调试：

```php
define('LOGIN_CAPTCHA_DEBUG', true);
```

## 安全特性

- **CSRF防护**: 所有AJAX请求都包含nonce验证
- **输入验证**: 严格验证所有用户输入
- **速率限制**: 防止暴力破解攻击
- **时序攻击防护**: 使用安全的字符串比较
- **输出转义**: 防止XSS攻击
- **权限检查**: 适当的用户权限验证

## 更新日志

### 1.0.8 (2024-01-15)
- 🔒 **重大安全更新**: 修复多个安全漏洞
- 🧹 **测试文件清理**: 删除所有测试页面
- 🛡️ **CSRF防护**: 添加nonce验证机制
- 🚫 **速率限制**: 防止暴力破解攻击
- 🔐 **输入验证**: 加强所有用户输入验证

### 1.0.0
- 初始版本发布
- 基本验证码功能
- 管理界面
- 响应式设计
- 无障碍支持

## 技术支持

如果您遇到问题或有功能建议，请联系技术支持。

## 许可证

本插件基于GPL v2或更高版本许可证发布。

## 贡献

欢迎提交bug报告和功能请求。

---

**注意**: 建议配合强密码策略和登录失败限制插件一起使用，以获得最佳安全效果。
