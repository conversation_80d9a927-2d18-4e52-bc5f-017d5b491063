<?php
/**
 * 产品审核系统安全类
 * 
 * @package ProductReviewSystem
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

class PRS_Security {
    
    /**
     * 初始化安全措施
     */
    public static function init() {
        // 添加安全头
        add_action('send_headers', array(__CLASS__, 'add_security_headers'));
        
        // 限制调试信息
        add_filter('prs_debug_enabled', array(__CLASS__, 'is_debug_enabled'));
        
        // 验证文件上传
        add_filter('wp_handle_upload_prefilter', array(__CLASS__, 'validate_file_upload'));
    }
    
    /**
     * 添加安全HTTP头
     */
    public static function add_security_headers() {
        if (!headers_sent()) {
            // 防止点击劫持
            header('X-Frame-Options: SAMEORIGIN');
            
            // 防止MIME类型嗅探
            header('X-Content-Type-Options: nosniff');
            
            // XSS保护
            header('X-XSS-Protection: 1; mode=block');
            
            // 引用者策略
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    }
    
    /**
     * 检查是否启用调试模式
     */
    public static function is_debug_enabled() {
        return defined('WP_DEBUG') && WP_DEBUG && defined('PRS_DEBUG') && PRS_DEBUG;
    }
    
    /**
     * 安全的输入验证
     */
    public static function validate_input($input, $type = 'text', $options = array()) {
        switch ($type) {
            case 'int':
                return intval($input);
                
            case 'email':
                return sanitize_email($input);
                
            case 'url':
                return esc_url_raw($input);
                
            case 'textarea':
                return sanitize_textarea_field($input);
                
            case 'html':
                $allowed_html = isset($options['allowed_html']) ? $options['allowed_html'] : array();
                return wp_kses($input, $allowed_html);
                
            case 'array':
                if (!is_array($input)) {
                    return array();
                }
                return array_map('sanitize_text_field', $input);
                
            case 'text':
            default:
                return sanitize_text_field($input);
        }
    }
    
    /**
     * 安全的数据库查询
     */
    public static function safe_query($sql, $params = array()) {
        global $wpdb;
        
        if (empty($params)) {
            return $wpdb->get_results($sql);
        }
        
        return $wpdb->get_results($wpdb->prepare($sql, $params));
    }
    
    /**
     * 验证用户权限
     */
    public static function verify_permission($action, $context = null) {
        $current_user_id = get_current_user_id();

        if (!$current_user_id) {
            return false;
        }

        // 检查基本权限
        switch ($action) {
            case 'view_reviews':
                return PRS_Permissions::can_user_view_reviews();

            case 'review':
                return PRS_Permissions::can_user_review();

            case 'admin_review':
                return PRS_Permissions::can_user_admin_review();

            case 'manage_reviews':
                return PRS_Permissions::can_user_manage_reviews();

            case 'delete_reviews':
                return current_user_can('delete_product_reviews');

            default:
                return false;
        }
    }

    /**
     * 验证CSRF令牌
     */
    public static function verify_csrf_token($action, $nonce_field = 'nonce') {
        if (!isset($_POST[$nonce_field])) {
            return false;
        }

        return wp_verify_nonce($_POST[$nonce_field], $action);
    }

    /**
     * 防止SQL注入的安全查询
     */
    public static function safe_table_name($table_name) {
        global $wpdb;

        // 验证表名只包含允许的字符
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $table_name)) {
            return false;
        }

        // 确保表名以wp_前缀开始
        if (strpos($table_name, $wpdb->prefix) !== 0) {
            return false;
        }

        return esc_sql($table_name);
    }

    /**
     * 验证审核记录所有权
     */
    public static function verify_review_ownership($review_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        $review = PRS_Database::get_review($review_id);
        if (!$review) {
            return false;
        }

        // 管理员可以访问所有记录
        if (current_user_can('manage_product_reviews')) {
            return true;
        }

        // 审核员可以访问分配给他们的记录
        if (PRS_Permissions::can_user_review() &&
            ($review->status === 'pending_review' || $review->reviewer_id == $user_id)) {
            return true;
        }

        // 提交者可以查看自己的记录
        if ($review->submitter_id == $user_id) {
            return true;
        }

        return false;
    }
    
    /**
     * 验证nonce并检查权限
     */
    public static function verify_nonce_and_permission($nonce_action, $permission_action, $context = null) {
        // 验证nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], $nonce_action)) {
            return array(
                'success' => false,
                'message' => __('安全验证失败', 'product-review-system')
            );
        }
        
        // 验证权限
        if (!self::verify_permission($permission_action, $context)) {
            return array(
                'success' => false,
                'message' => __('权限不足', 'product-review-system')
            );
        }
        
        return array('success' => true);
    }
    
    /**
     * 安全的文件上传验证
     */
    public static function validate_file_upload($file) {
        // 检查文件类型
        $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx');
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_ext, $allowed_types)) {
            $file['error'] = __('不允许的文件类型', 'product-review-system');
            return $file;
        }
        
        // 检查文件大小 (5MB限制)
        $max_size = 5 * 1024 * 1024;
        if ($file['size'] > $max_size) {
            $file['error'] = __('文件大小超过限制', 'product-review-system');
            return $file;
        }
        
        return $file;
    }
    
    /**
     * 生成安全的随机字符串
     */
    public static function generate_secure_token($length = 32) {
        return wp_generate_password($length, false);
    }

    /**
     * 防止暴力破解的速率限制
     */
    public static function check_rate_limit($action, $identifier = null, $limit = 5, $window = 300) {
        if (!$identifier) {
            $identifier = self::get_client_ip();
        }

        $key = 'prs_rate_limit_' . $action . '_' . md5($identifier);
        $attempts = get_transient($key);

        if ($attempts === false) {
            $attempts = 0;
        }

        if ($attempts >= $limit) {
            return false;
        }

        set_transient($key, $attempts + 1, $window);
        return true;
    }

    /**
     * 获取客户端IP地址
     */
    public static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }

    /**
     * 记录安全事件
     */
    public static function log_security_event($event_type, $details = array()) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'ip_address' => self::get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'event_type' => $event_type,
            'details' => $details
        );

        // 记录到WordPress错误日志
        error_log('PRS Security Event: ' . json_encode($log_entry));

        // 可以扩展为记录到数据库或发送警报
        do_action('prs_security_event', $log_entry);
    }

    /**
     * 验证产品ID是否有效且用户有权限访问
     */
    public static function verify_product_access($product_id, $action = 'edit') {
        $product = wc_get_product($product_id);
        if (!$product) {
            return false;
        }

        // 检查用户是否有权限编辑此产品
        if ($action === 'edit' && !current_user_can('edit_product', $product_id)) {
            return false;
        }

        return true;
    }
    
    /**
     * 安全的重定向
     */
    public static function safe_redirect($url, $status = 302) {
        $url = wp_sanitize_redirect($url);
        $url = wp_validate_redirect($url, admin_url());
        wp_redirect($url, $status);
        exit;
    }

    /**
     * 清理敏感数据
     */
    public static function sanitize_sensitive_data($data) {
        $sensitive_keys = array('password', 'token', 'key', 'secret', 'nonce');
        
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (in_array(strtolower($key), $sensitive_keys)) {
                    $data[$key] = '[REDACTED]';
                } elseif (is_array($value)) {
                    $data[$key] = self::sanitize_sensitive_data($value);
                }
            }
        }
        
        return $data;
    }
}

// 初始化安全措施
PRS_Security::init();
