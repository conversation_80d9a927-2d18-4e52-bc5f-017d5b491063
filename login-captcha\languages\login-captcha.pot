# Copyright (C) 2024 登录验证码
# This file is distributed under the same license as the 登录验证码 package.
msgid ""
msgstr ""
"Project-Id-Version: 登录验证码 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/login-captcha\n"
"POT-Creation-Date: 2024-01-01 00:00:00+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: login-captcha.php:95
msgid "验证码"
msgstr ""

#: login-captcha.php:97
msgid "点击刷新验证码"
msgstr ""

#: login-captcha.php:98
msgid "请输入验证码"
msgstr ""

#: login-captcha.php:101
msgid "刷新验证码"
msgstr ""

#: login-captcha.php:135
msgid "验证码错误，请重新输入"
msgstr ""

#: login-captcha.php:150
msgid "刷新验证码"
msgstr ""

#: login-captcha.php:165
msgid "登录验证码设置"
msgstr ""

#: login-captcha.php:166
msgid "登录验证码"
msgstr ""

#: includes/class-admin-settings.php:25
msgid "基本设置"
msgstr ""

#: includes/class-admin-settings.php:31
msgid "外观设置"
msgstr ""

#: includes/class-admin-settings.php:38
msgid "启用验证码"
msgstr ""

#: includes/class-admin-settings.php:45
msgid "验证码类型"
msgstr ""

#: includes/class-admin-settings.php:52
msgid "验证码长度"
msgstr ""

#: includes/class-admin-settings.php:59
msgid "图片宽度"
msgstr ""

#: includes/class-admin-settings.php:66
msgid "图片高度"
msgstr ""

#: includes/class-admin-settings.php:76
msgid "您没有权限访问此页面。"
msgstr ""

#: includes/class-admin-settings.php:84
msgid "设置已保存。"
msgstr ""

#: includes/class-admin-settings.php:91
msgid "验证码预览"
msgstr ""

#: includes/class-admin-settings.php:92
msgid "这是当前设置下验证码的预览效果："
msgstr ""

#: includes/class-admin-settings.php:95
msgid "验证码预览"
msgstr ""

#: includes/class-admin-settings.php:98
msgid "刷新预览"
msgstr ""

#: includes/class-admin-settings.php:109
msgid "使用说明"
msgstr ""

#: includes/class-admin-settings.php:111
msgid "启用验证码后，用户在登录时需要输入正确的验证码才能继续。"
msgstr ""

#: includes/class-admin-settings.php:112
msgid "验证码类型支持纯数字、纯字母或数字字母混合。"
msgstr ""

#: includes/class-admin-settings.php:113
msgid "验证码长度建议设置为4-6位，太短不安全，太长影响用户体验。"
msgstr ""

#: includes/class-admin-settings.php:114
msgid "图片尺寸可以根据您的主题样式进行调整。"
msgstr ""

#: includes/class-admin-settings.php:118
msgid "安全提示"
msgstr ""

#: includes/class-admin-settings.php:120
msgid "验证码可以有效防止暴力破解登录密码。"
msgstr ""

#: includes/class-admin-settings.php:121
msgid "建议配合强密码策略和登录失败限制使用。"
msgstr ""

#: includes/class-admin-settings.php:122
msgid "验证码会在5分钟后自动过期，提高安全性。"
msgstr ""

#: includes/class-admin-settings.php:137
msgid "配置验证码的基本功能设置。"
msgstr ""

#: includes/class-admin-settings.php:144
msgid "调整验证码图片的外观样式。"
msgstr ""

#: includes/class-admin-settings.php:152
msgid "在登录页面显示验证码"
msgstr ""

#: includes/class-admin-settings.php:160
msgid "纯数字"
msgstr ""

#: includes/class-admin-settings.php:161
msgid "纯字母"
msgstr ""

#: includes/class-admin-settings.php:162
msgid "数字字母混合"
msgstr ""

#: includes/class-admin-settings.php:170
msgid "选择验证码包含的字符类型。"
msgstr ""

#: includes/class-admin-settings.php:178
msgid "验证码字符数量，建议3-8位。"
msgstr ""

#: includes/class-admin-settings.php:186
msgid "验证码图片宽度，建议80-300像素。"
msgstr ""

#: includes/class-admin-settings.php:194
msgid "验证码图片高度，建议30-100像素。"
msgstr ""

#: login-captcha.php:290
msgid "频繁请求，请稍后重试"
msgstr ""

#: login-captcha.php:259
msgid "无效的验证码ID"
msgstr ""

#: login-captcha.php:263
msgid "安全验证失败"
msgstr ""
