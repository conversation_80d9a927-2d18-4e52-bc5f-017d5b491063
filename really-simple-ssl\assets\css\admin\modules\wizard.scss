.rsssl {
  div[class^="rsssl-wizard-"] { // starts with selector
    .rsssl-helplink {
      color: var(--rsp-text-color);
    }

    .rsssl-grid-item {
      position: relative; //to ensure the rsssl-lock stays within the div
      margin-bottom: var(--rsp-grid-gap);
      @media(max-width: $rsp-break-s) {
        grid-column: span 4;
      }
    }
    &.rsssl-column-2{
      grid-column: span 2;
      @media(max-width: $rsp-break-s) {
        grid-column: span 4;
      }
    }

    .rsssl-locked {
      position: absolute;
      z-index: 20;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background: rgba(255, 255, 255, 0.8);
      border-radius: var(--rsp-border-radius);
      .rsssl-shield-overlay {
        position: absolute;
        top: 50%; /* Center vertically */
        left: 50%; /* Center horizontally */
        transform: translate(-50%, -50%); /* Offset for centering */
        display: flex;
        align-items: center;
        justify-content: center;
        height: calc(100px + 2 * var(--rsp-spacing-s)); /* Adjust shield height dynamically */
        width: calc(200px + 2 * var(--rsp-spacing-s)); /* Ensure consistent width for a square shield */
        z-index: 19; /* Place it below the overlay */
      }

      .rsssl-locked:has(.rsssl-shield-overlay) {
        /* Apply dynamic styles when .rsssl-shield-overlay is present */
        min-height: calc(100px + 4 * var(--rsp-spacing-s)); /* Adjust to fit the shield and extra spacing */
        display: flex;
        align-items: center; /* Vertically center contents */
        justify-content: center; /* Horizontally center contents */
        position: relative; /* Context for absolutely positioned children */
        padding: var(--rsp-spacing-s); /* Add padding to avoid cutting off elements */
      }

      .rsssl-locked-overlay {
        z-index: 20; /* Ensure this stays on top */
        position: absolute;
        display: flex;
        align-items: center;
        gap: var(--rsp-spacing-s);
        background-color: var(--rsp-white);
        margin: var(--rsp-spacing-s);
        border-radius: var(--rsp-border-radius-s);
        box-shadow: var(--rsp-box-shadow);
        bottom: 0;
        width: calc(100% - (2 * var(--rsp-spacing-s)));
        @include rsssl-block-padding;

        .rsssl-open {
          float: left;
          margin-right: 12px;
        }

        .rsssl-progress-status {
          float: left;
          margin-right: 20px;
        }
      }
    }

    //.rsssl-grid-item-footer {
    //  justify-content: flex-end;
    //  padding: 0;
    //  display: flex;
    //  flex-wrap: wrap;
    //  align-items: center;
    //  gap: var(--rsp-grid-margin);
    //  width: 100%;
    //  min-height: 20px;
    //  box-sizing: border-box;
    //  align-self: flex-end;
    //
    //  .rsssl-legend {
    //    display: flex;
    //    span {
    //      padding-left: 5px;
    //    }
    //  }
    //
    //  &:empty {
    //    display: none;
    //  }
    //}

    & > div:nth-last-of-type(2) {
      margin-bottom: 0;
      border-radius: var(--rsp-border-radius) var(--rsp-border-radius) 0 0;
    }

    .rsssl-grid-item-footer-container {
      position: sticky;
      bottom: 0;
      display: flex;
      flex-direction: column;
      z-index: 20; //should be above 10, for the text editor, which has 10.

      .rsssl-grid-item-footer-buttons {
        a.button, button {
          box-shadow: none !important;

          &:focus, &:active {
            box-shadow: none !important;
          }
        }
      }

    }
    .rsssl-grid-item-footer-scroll-progress-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 3px;
      background-color: var(--rsp-grey-300);
      overflow: hidden;
    }

    .rsssl-grid-item-footer-scroll-progress {
      height: 100%;
      background-color: var(--rsp-blue);
    }
    .rsssl-grid-item-footer {
      background: var(--rsp-grey-100);
      border-radius: 0 0 var(--rsp-border-radius) var(--rsp-border-radius);
      border-top: 1px solid var(--rsp-grey-300);
      box-sizing: border-box;
      align-items: center;
      @include rsssl-block-padding();
      box-shadow: var(--rsp-box-shadow);
      gap: var(--rsp-grid-margin);
      width: 100%;
      min-height: 20px;
      justify-content: space-around;
      // last item within the footer
      & > div:last-of-type {
        margin-left: auto;
      }
      &-buttons{
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: var(--rsp-spacing-s);
      }
      .rsssl-legend {
        display: flex;
        span {
          padding-left: 5px;
        }
      }
      &-upsell-bar {
        .button {
          display:inline-block !important;
        }
      }

      &:empty {
        display: none;
      }
    }
  }
}
