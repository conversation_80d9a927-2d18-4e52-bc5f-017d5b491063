@font-face {
  font-family: "figtree";
  src: url(".//assets/fonts/Figtree-Medium.ttf") format("truetype");
}

span#nua-required {
  color: #a00 !important;
}

#nua_dashboard_layout,
#nua-settings-layout,
#nua-invitation-layout {
  font-family: "figtree", sans-serif;
}
.nua_inner_settings h3 {
    font-size: 1.5em;
}
h2.enable-whitelist-label{
font-weight: 500!important;  
margin-top: 0px;
}
.menu-top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    background-color: #FFFFFF;
    height: 84px;
  }
  
  .logo.nua-logo-wrapper  {
    align-items: center;
    background: #fff;
    display: flex;
    height: 84px;
    padding: 0 24px;
  }

  .Toastify__progress-bar--success{
  background-color: #618E5F;  
  }
  .logo img {
    height: 50px; /* Adjust height as needed */
  }

body h4, body h2{
  font-family: "figtree", sans-serif!important;
  font-weight: 600!important;
}

/* .get-pro-button {
  background-color: #333;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
} */
.ProButton {
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 148px;
    height: 40px;
    gap: 4px;
    justify-content: center;
    background: #6EA26D;
    color: #fff;
    font-size: 16px;
    border: unset;
    line-height: 24;
    padding: 8px 32px 8px 32px;
    border-radius: 8px;
  }
  

/* Add media queries for responsive adjustments */
@media (max-width: 768px) {
  .menu-top-bar {
    flex-wrap: wrap; /* Wrap elements on smaller screens */
  }
  .get-pro-button {
    margin-top: 10px; /* Add spacing on smaller screens */
  }
}
.toplevel_page_new-user-approve-admin #wpcontent{
 padding-left: unset; 
}
.toplevel_page_new-user-approve-admin .wrap{
  margin: unset;
}

.logo.nua-logo-wrapper{
  padding: 0px 24px 0px 24px;
  background: #fff; 
  display: flex;
  align-items: center;
  height: 84px;
}
.user-found-error {
  display: flex;
  align-items: center;
  flex-direction: column;
  margin: 0 auto;
}
.user-found-error span {
  padding: 2px;
  font-size: 18px;
  color: #707070;
  line-height: 18px;
  margin-top: 10px;
}
.user-found-error p {
  color: #858585;
}
.user-found-error.inv-code{
  width: 100%;
  margin-top: 15px; 
}
.nua_dash_parent_tablist button:hover::after {
  width: 100%;
}
.nua_dash_parent_tablist button::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -16px;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background-color: #618E5F;
  transition: width 0.3s ease-in-out;
}
.nua-editor-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

button.nua_active_tab:after,
.nua_settings_parent_tablist button.nua_active_tab:after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -16px;
  transform: translateX(-50%);
  transform-origin: center;
  width: 100%;
  height: 3px;
  background-color: #618E5F;
  transition: all 0.3s ease-in-out;
}

.nua_dash_parent_tabs,
.nua_settings_parent_tabs {
  padding: 32px;
  padding-bottom: 0;
  text-align: center;
  /* background-color: #fff; */
  border: none;
  margin-top: 10px;
  border-radius: 15px;
}

.nua_dash_parent_tablist {
  display: flex;
  gap: 40px;
  align-items: center;
  padding: 6px 24px 6px 24px;
  background-color: #ffffff;
  border-radius: 12px;
  height: 68px;
}
.nua_dash_parent_tablist button {
  width: auto;
  height: 50px;
  border: 0px solid;
  border-radius: 10px;
  background-color: #ffffff;
  cursor: pointer;
  position: relative;
  padding: 0;
}

.nua_dash_parent_tablist button:hover {
  color: #537a52;
  height: 50px;
  border-radius: 0px;
}
.add-code-tabPanel h4 {
  font-size: 20px;
  font-weight: 600;
  line-height: 120%;
  color: #242424;
}
p.description{
  font-size: 14px;
  color: #707070!important;
  line-height: 20px;
  margin-top: 8px;  
}



/* p.description.codeDescription {
  margin-top: -80px;
} */
/* .usage-limit {
width: 318px;
border-color: #7CB77A !important;
height: 54px;
border-radius: 8px !important;
box-shadow: 0px 4px 16px 0px #79897A40 !important;
font-size: 17px;
} */
html:not([lang^="en"]) .nua_dash_parent_tablist button {
  width: 140px !important;
}
html:not([lang^="en"]) .nua_dash_parent_tablist button:hover {
  width: 140px !important;
}

.dash-tabPanel,
.setting-main-tabpanel {
  padding: 24px;
  margin-top: 12px;
  background-color: #ffffff;
  border-radius: 10px;
}
.dash-tabPanel,
.setting-main-tabpanel {
  padding-top: 0;
}

.nua-bookNow {
  background: linear-gradient(-50deg, #375136 46%, #537952 100%);
  background-size: 100% 258%;
  border-radius: 12px;
  padding: 40px 20px;
  color: white;
  width: auto;
  max-width: 320px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.nua-bookNow .nua-icon {
  margin-bottom: 16px;
}

.nua-bookNow .nua-icon img {
  width: 64px;
  height: auto;
  object-fit: contain;
}

.nua-bookNow .booknow-content p {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 20px;
  line-height: 1.5;
  color: #D7F2D6;
}

.nua-bookNow .booknow-content button {
  background-color: #FFBF46;
  color: #3B423B;
  font-weight: 400;
  border: none;
  width: 133px;
  height: 40px;
  padding: 10px 20px;
  border-radius: 50px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nua-bookNow .booknow-content button:hover {
  background-color: #e0a800;
}

/* user sub tabs start */
.users_subtabs,
.auto-approve-tabs,
.integration_subtabs,
.invitation_code_subtabs,
.nua_notification_sub_tabs {
  text-align: center;
  /* background-color: #fff; */
  border: none;
  margin-top: 12px;
  border-radius: 15px;
  background-color: #ffffff;
}
.invitation_code_subtabs{
  padding: 24px;
  margin-top: 0px;
}
.users_subtabs_list,
.auto-approve-tab-list,
.integration_subtabs_list,
.invitation_code_subtabs_list,
.nua_notification_sub_tabstablist {
  display: flex;
  padding: 3px 30px;
  border-bottom: 1px solid;
  border-color: #f0f0f0;
  border-radius: 10px 10px 0px 0px;
  background-color: #ffffff;
  gap: 30px;
}
.nua_notification_sub_tabstablist{
  padding: unset;
}

.users_subtabs_list button,
.auto-approve-tab-list button,
.integration_subtabs_list button,
.nua_notification_sub_tabstablist button {
  color: #858585;
  height: 44px;
  border: 0px solid;
  background-color: #ffffff;
  cursor: pointer;
  padding: 0;
}

/* user sub tabs end */

/* Recent Users Start */
.nua_recent_users {
  text-align: left;
  /* background-color: #fff; */
  margin-top: 20px;
  border-radius: 15px;
  background-color: #ffffff;
}

.recent_users_filter {
  display: flex;
  justify-content: space-between;
  padding-top: 24px;
}
#users-filter {
  border: 1px solid;
  border-color: #d0edd3;
}

.select-container {
  margin-top: 10px;
}
/* recent update */

.recent-update-frame-container {
  height: 421px;
  width: 276px !important;
}
.recent-update-frame-container table tbody {
  background-color: #fafafa;
}
.recent-update-frame-container table tbody tr td {
  height: 140px;
}
.activity-log-frame {
  max-height: 140px;
  height: 140px;
  padding: "10px";
  box-sizing: "border-box";
}
.frame-1-table-row,
.frame-2-table-row {
  border-bottom: 1px solid rgb(194, 194, 194, 0.2);
}
.frame-1-table-row {
  border-left: 4px solid #74b27b;
}
.frame-2-table-row {
  border-left: 4px solid #fbb653;
}
.frame-3-table-row {
  border-left: 4px solid #e57373;
  border-bottom: 1px solid rgb(194, 194, 194, 0.2);
}
.activity-container {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 20px;
  padding: 15px;
  height: 107px;
  min-height: 107px;
}

.activity-header {
  display: flex;
  align-items: center;
  gap: 80px;
}
.activity-title {
font-size: 18px;
color: #707070;
text-transform: capitalize;
font-weight: 600;
}
.activity-desc {
  font-size: 15px;
  color: #858585;
  text-align: left;
}
.activity-period {
  font-size: 12px;
  color: #c2c2c2;
}
/* sell all activity button */

.see-all-activity {
  position: absolute;
  bottom: 45px;
  left: 95px;
}

.see-all-activity-btn {
  background-color: #fff;
  color: #426377;
  border: 1px solid #fff;
  border-radius: 30px;
  position: absolute;
  min-width: 90px;
  width: 90px;
  font-size: 12px;
  padding: 6px;
  cursor: pointer;
  text-decoration: none;
  box-shadow: -3px 2px 11px 0px #eeeeee;
}
td.actionsRow{
  padding-left: 0;
}
.viewCode-dialog{
  border-bottom: 0 !important;
  padding: 32px 40px !important;
}

.invitation_code_subtabs_list .btn {
  padding: 8px 16px;
  background: #fff;
  border: 1px solid #ddd;
  cursor: pointer;
  flex: 0 0 calc((100% - 30px * 2 - 27px * 4 - 339px) / 4);
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  text-align: center;
  white-space: nowrap;
  height: 48px;
  line-height: 24px;
  color: #242424;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.code-search-icon {
  position: absolute;
  right: 6px;
  top: 14px;
}

.invitation_code_subtabs_list .btn:hover {
  background-color: #f1f5f9;
}
.nua_edit_invitation_code span{
  width: 45%;
}
.nua_edit_invitation_code .edit_invitation_code{
  display: block;
  width: 100%;
  margin-bottom: 24px; 
} 
.inv-code-details {
  background: #F2F8F3;
  padding: 32px 40px;
}
.inv-field-group{
  display: flex;
  gap: 16px; 
  margin-top: 25px;
}
.inv-field-group-initial {
  display: flex;
  flex-direction: column;
}
.invitation-field{
  width: 120px; 
}
.invitation-field__input{
  width: 100%;
  height: 40px;
  background-color: #fff !important;
  border: unset;
  border-left: 3px solid #7CB77A;
  padding: 10px 9px;
  font-size: 14px;
  color: #707070;
}
.inv-code-details label {
  margin-bottom: 12px;
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
}
.users-reg-text{
  font-size: 14px;
  line-height: 20px;
  display: block;
}
.users-reg-table{
  box-shadow: none !important; 
}
.users-reg-table tbody tr{
  height: 53px;  
}
.users-reg-table thead tr th, .invitation-codes-header table th{
  color: #242424!important;
  padding-left: 0;
}
.users-reg-table tbody tr td{
  font-size: 14px;
  padding-left: 0;
  letter-spacing: 0px;
  font-weight: 500 !important;
}


.MuiTextField-root.nua-code-search{
  width: 100%;
  margin-bottom: 0px;
}
.MuiTextField-root.nua-code-search > div{
  height: 48px;
}
.MuiTextField-root.nua-code-search input{
  border: 0px;
  padding-left: 20px;
}
.MuiTextField-root.nua-code-search input:focus{
  box-shadow: unset;
}
.invitation_code_subtabs_list {
  gap: 12px;
  border-bottom: unset;
  padding: 0px;
  height: 66px;
}
.openNuaModal{
  font-family: "figtree", sans-serif!important;
}
.openNuaModal.NuaModal .MuiModal-backdrop{
  opacity: 0.3 !important;  
}
.openNuaModal .MuiDialog-paper,
.openNuaTabsModal .MuiDialog-paper {
  box-shadow: unset;
    margin-top: 64px;
    max-width: 560px;
    border-radius: 12px;
}
.openNuaModal.delete-inv-modal .MuiDialog-paper{
  max-width: 400px;
}
.MuiDialogContent-dividers.delete-confirmation{
  display: flex;
  align-items: center;
  gap: 20px;
  border-top: 0px;
  padding-top: 0!important;
}
.openNuaModal.delete-inv-modal .nua-modal-close{
top: 8px;
}
.openNuaTabsModal .MuiDialog-paper h2{
  font-size: 16px;
  color: #242424;
}
.openNuaTabsModal .MuiDialog-paper .MuiDialogContent-dividers{
  border-top-color: #E6EBEF;
  padding: 20px 40px;
}
.code-deactivate-btn {
  background: #ff0000fc !important;
}
.invitation-codes-header, .usersTable{
  padding: 0px!important;
  margin-top: 0!important;
  border-radius: 12px !important;
  box-shadow: unset!important;
  border: 1px solid #E6EBEF;
}
.nua-field-col{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.add-codes-modal .nua-field-col{
  margin-bottom: 24px;
}

.nua-auto-code .nua-field-col-a{
  display: flex;
  gap: 17px;
  justify-content: unset;
  flex-wrap: unset;
}
.nua-auto-code .nua-field-col-b{
  display: flex;
  justify-content: space-between;
}
.nua-auto-code .auto-code-usage-limit, .nua-auto-code span.auto-code-expiry{
  width: 47%;
}




span.actionsIcon {
  border: 1px solid #E6EBEF;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 4px;
}

.invitation-codes-header thead.MuiTableHead-root, .usersTable thead tr{
  background-color: #F3F5F7;
}
.usersTable thead tr th{
  width: 25%;
  text-align: initial!important;
}
.usersTable thead tr th:last-child{
  padding-right: 45px !important; 
}

.invitation-codes-header table th:first-child{
  border-radius: 12px 0 0 0px;
}

.invitation-codes-header table th:last-child{
  border-radius: 0px 12px 0 0px;
  padding: unset;
}
td.actionsRow button{
  padding: 0;
  padding-right: 6px;  
}
.invitation-codes-header table th strong{
  font-size: 16px;
  color: #242424;
  font-weight: 500;
}
/* .inactive-icon {
  opacity: 0.6;
  cursor: not-allowed;
} */
.invitation-codes-header table tbody tr:hover, .usersTable table tbody tr:hover, .users-reg-table tbody tr:hover{
  background-color: #FAFAFA;
}
.invitation-codes-header table tbody tr td, .usersTable table tbody tr td {
  height: 38px!important;
  padding-bottom: 10px;
  padding-top: 10px;
  line-height: 20px;
  color: #242424!important;
  text-align: left!important;
  padding-left: 0;
}
.user_roles_tbl_container.usersTable table tbody tr td{
  height: 28px !important;
  padding-bottom: 0px;
  padding-top: 0px; 
}
.usersTable table thead tr th:nth-child(1), .usersTable table tbody tr td:nth-child(1){
  padding-left: 15px!important;
}
.invitation-codes-header table th:nth-child(1), .invitation-codes-header table td:nth-child(1){
  width: 25%;
  padding-left: 15px;
}

.invitation-codes-header table th:nth-child(2), .invitation-codes-header table td:nth-child(2){
  width: 20%;
}

.invitation-codes-header table th:nth-child(3), .invitation-codes-header table td:nth-child(3){
  width: 20%;
}

.invitation-codes-header table th:nth-child(4), .invitation-codes-header table td:nth-child(4){
  width: 20%;
}

.invitation-codes-header table th:nth-child(5), .invitation-codes-header table td:nth-child(5){
  width: 25%;
}


.usersTable table thead tr th{
  color: #242424!important;
  font-weight: 500!important;
  padding-left: 0!important;
}
.usersTable table tbody tr{
  border-bottom: 1px solid #E6EBEF; 
}
.usersTable table tbody tr td a, .users-reg-table tbody tr td a{
  color: #618E5F !important;
  text-decoration: unset;
}

/* end */

/* counter */

.recent_user_counter {
  display: flex;
  gap: 15px;
  width: 50%;
  padding: 20px;
}
/* .total_requests, .pending_requests, .approved_requests {

        border-right:1px solid;
        border-color:#E7ECEE;

      } */
.total_requests {
  background-color: #edf4ff;
  border: 0px;
  border-radius: 12px;
}
.pending_requests {
  background-color: #fff8ed;
  border: 0px;
  border-radius: 12px;
}
.approved_requests {
  background-color: #edffef;
  border: 0px;
  border-radius: 12px;
}
.denied_requests {
  background-color: #ffeded;
  border: 0px;
  border-radius: 12px;
}
.approve-status {
  color: #68a56f;
}
.deny-status {
  color: #e57373;
}
#nua-status-menu-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.recent_user_number_status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.recent_user_number_status span.counter__icon--right {
  color: #858585;
}

.status-icon {
  line-height: 31px;
  height: 20px;
}

.nua_user_counter_list {
  display: flex;
  gap: 12px;
  height: 100%;
}
.counter__number {
  font-weight: 700;
  color: #707070;
  font-size: xx-large;
  padding: 5px;
}
.users_counter_container {
  height: 115px;
  min-height: 115px;
  max-height: 115px;
  border-radius: 10px;
}
/* end */

/* recent users section */
#recent_users_section {
  display: flex;
  padding: 0px;
  margin-top: 20px;
}

.recent_users_tbl_gird {
  display: grid;
  /* grid-template-columns: repeat(2, 1fr); */
  /* grid-gap: 20px; */
  grid-column-gap: 40px;
  grid-template-columns: 4fr 1fr;
}

/* end */

/* nua-pro-banner */

.nua-pro-small-banner.dashboard-screen {
  background: linear-gradient(-50deg, #375136 46%, #537952 100%);
  background-size: 100% 258%;
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 40px;
  color: white;
  font-family: 'Inter', sans-serif;
  overflow: hidden;
  width: auto;
  margin-top: 40px;
}

.dashboard-screen .nua-popup-inner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 40px;
  flex-wrap: wrap;
}

.dashboard-screen .nua-pro-small-banner-content {
  max-width: 800px;
  flex: 1;
}

.dashboard-screen .nua-pro-small-banner-content h1 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.4;
}

.dashboard-screen .nua-pro-small-banner-content p {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #e0e0e0;
  max-width: 80%;
}

.dashboard-screen .nua-pro-small-banner-btns {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-screen .nua-pro-small-banner-btns button {
  background-color: #FFBF46;
  color: #3B423B;
  font-weight: 400;
  border: none;
  padding: 10px 18px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-screen .nua-pro-small-banner-btns button:hover {
  background-color: #e0a800;
}


.dashboard-screen .popup-inner-img img {
  width: 240px;
  max-width: 100%;
  height: auto;
  object-fit: contain;
}


.nua-parent-popup{
top: 50% !important;
left: 50% !important;
transform: translate(-50%, -50%);
}
.nua-parent-popup .nua-pro-small-banner{
margin: 0 auto;  
}
.nua-pro-small-banner{
display: flex;
flex-direction: column;
align-items: center;
background: linear-gradient(-50deg, #375136 46%, #537952 100%);
background-size: 100% 258%;
width: 800px;
border-radius: 10px;  
}
.nua-pro-close{
text-align: right;
color: #D7F2D6;  
}
.nua-popup-inner-content{
display: flex;
flex-direction: row-reverse;  
}
.popup-inner-img img{
width: 280px;
height: 377px;  
}
.nua-pro-small-banner-content{
padding-right: 39px;
display: flex;
flex-direction: column;
gap: 4px;  
}
.nua-banner-logo{
text-align: left;
margin-bottom: 35px;  
}
.nua-pro-small-banner h1{
font-family: 'figtree';
color: #D7F2D6;
font-size: 22px;
font-weight: 500;
text-align: left;
line-height: 30px;
margin-bottom: 0;  
}
.nua-pro-small-banner p{
font-family: 'figtree';
color: #D7F2D6;
font-size: 16px;
text-align: left;
line-height: 24px;  
}
.nua-pro-small-banner .nua-pro-small-banner-btns{
text-align: left;
}
.nua-pro-small-banner button{
background-color: #FFBF46;
border: 0px;
padding: 12px 12px;
color: white;
border-radius: 8px;
width: 142px;
font-family: 'figtree';
font-size: 16px;
cursor: pointer;
color: #3B423B;
height: 48px;  
}





/* end */

/* guide and doc item list */
#nua_guide_doc_section {
  display: grid;
grid-template-columns: 4fr 1fr;
margin-top: 40px;
grid-column-gap: 30px;
}

.nua_doc_item_lists {
  display: grid;
  align-items: center;
  grid-template-columns: auto auto auto;
  justify-content: center;
  justify-items: center;
  grid-row-gap: 13px;
  grid-column-gap: 8%;
  padding: 20px 0px 20px 0px;
  height: 200px;
}
.nua_doc_item_lists a {
  text-decoration: unset;
}

html[lang^="en"] span.nua-doc-item {
  display: flex;
  gap: 5px;
  align-content: center;
  align-items: center;
  flex-wrap: nowrap;
  font-size: 14px;
  border: 1px solid;
  padding: 5px;
  width: 200px;
  height: 30px;
  text-align: 20px;
  border-color: #d3d3d3;
  border-radius: 5px;
  padding: 8px 20px 8px 20px;
}

html:not([lang^="en"]) span.nua-doc-item {
  display: flex;
  gap: 5px;
  align-content: center;
  align-items: center;
  flex-wrap: nowrap;
  font-size: 14px;
  border: 1px solid;
  padding: 5px;
  width: 245px;
  height: 30px;
  /* line-height: 30px; */
  max-width: 252px;
  border-color: #d3d3d3;
  border-radius: 5px;
  padding: 13px 2px 12px 4px;
  text-align: left;
}

a span.nua-doc-item {
  color: #537a52;
  text-decoration: none;
}
.guide_doc_cell {
  background-color: #fafafa;
  font-weight: 600;
}

/* end  */

/* users list*/

/* .users_list {
  display: grid;
  grid-column-gap: 52px;
  grid-template-columns: 4fr 1fr;
} */
.users_list > .nua-bookNow {
  margin-top: 50px;
}

.users_list_title {
  text-align: left;
  font-size: 20px;
}

.user-list-empty {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 0 auto;
  margin-top: 10px!important;
}
.rowsCount {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.rowsCount span {
  font-size: 14px;
  line-height: 14px;
  color: #242424;
}
/* auto approve */
.nua-auto-settings span.setting-label{
position: relative;
width: unset;  
}
.nua-tooltip-wrapper{
position: relative;
display: inline-block;
margin-left: 8px;  
}
.nua-tooltip-icon{
font-size: 16px;
color: #ABABAB;  
}

.nua-inv-code span.nua-tooltip-wrapper:hover .nua-tooltip-text {
    visibility:unset;
    opacity:1;
}
.nua-pro-icon{
position: relative;
top: 2px;
left: 5px;
}
button.download-button{
padding: 0px;
padding-top: 5px;  
}
.nua-tooltip-text{
visibility: hidden;
opacity: 0;
width: max-content;
max-width: 175px;
background-color: #333;
color: #fff;
text-align: center;
padding: 8px 15px;
border-radius: 1px;
position: absolute;
z-index: 999;
bottom: 125%;
left: 50%;
transform: translateX(-50%);
transition: opacity 0.3s;
font-size: 12px;
white-space: pre-line;  
}

.auto-approve-tabs .nua-tooltip-wrapper{
top: 5px;  
}

.auto-approve-main-tabpanel,
.integration-main-tabpanel,
.users-main-tabpanel, .invitation-main-tabpanel {
  background-color: #fff;
  border-radius: 12px;
  margin-top: 15px;
}
.auto-approve-whitelist,
.auto-approve-blacklist {
  display: flex;
  flex-direction: column;
  gap: 67px;
  padding: 20px;
}
.nua-domain-text {
  display: flex;
  gap: 58px;
}
.nua-domain-text-a{
  display: flex;
  flex-direction: column;
}
.nua-domain-text-a span {
  font-size: 20px;
  line-height: 140%;
}
.nua-zapier-section .nua-domain-text.triggers{
  align-items: baseline;  
}
.nua-domain-text .nua-setting-textarea{
  width: 500px;
  max-height: 184px;
  border-radius: 8px;
}
.auto-approve-tabs .setting-option{
  gap: 27px;
}

.blacklist-customMessage input,
.invitation-code-usage input {
  border-color: #f4f4f4;
}

.blacklist-customMessage input:hover,
.invitation-code-usage input:hover {
  border-color: #f4f4f4;
}

.nua-setting-textarea {
  background-color: #FAFAFA;
  border-color: #D5D5D5;
  border-radius: 10px;
  max-height: 150px !important;
  min-height: 150px !important;
  width: 100%;
}

.auto-approve-textarea-desc {
  text-align: left;
    margin-top: 0px;
    color: #707070;
}
.nua-custom-message-b input.auto-code-field{
  max-width: 500px;
  width: 500px; 
}
.nua-custom-message-b p{
  margin-top: 14px;
  font-size: 13px;
  color: #707070; 
}
.nua-domain-text.custom-message{
  gap: 48px;
}
.nua-zapier-section .nua-domain-text{
align-items: center;  
}
.zapier-triggers-names-list ul li span {
    margin-left: 5px;
}
.nua-custom-message-b .auto-approve-save-btn-box{
  padding-left: 0px;
  margin-top: 50px;
}
.blacklist-customMessage-desc {
  text-align: left;
  margin-top: -11px;
}
.auto-approve-tabs .setting-label, .nua-help-section .setting-label{
  font-size: 20px;
  line-height: 140%;
  color: #242424;
}
.nua-help-section .setting-label{
  min-width: 154px;
}
.auto-code-field {
  border-color: #D5D5D5 !important;
  height: 40px;
  border-radius: 6px!important;
  background-color: #FAFAFA!important;
  width: 100%;
}
.auto-code-field input{
  background-color: #FAFAFA;
}
.nua-field-col span.invitation-code-usage{
  width: 100%;
}
.nua-field-col span.nua-code-status, span.invitation-code-expiry{
  width: 45%;
}

.invitation-code-expiry-date input,
.auto-code-expiry-date input,
#invite_email_subject {
  height: 40px; 
  border: unset;
  border: 1px solid #D5D5D5;
  background-color: #FAFAFA
}
.auto-code-expiry-date input{
 border: unset; 
}
.auto-code-expiry-date{
 width: 100%!important; 
}
.nua-field-col .invitation-code-expiry-date{
  width: 100%;
}
.nua-field-col h4{
  font-size: 14px;
  margin-bottom: 24px;
  margin-top: 0;
}
.add-codes-modal .nua-field-col h4 {
  margin-bottom: 12px;
}
.invitation-code-expiry-date input{
  border: unset;
  background: #FAFAFA;
}
/* .invitation-code-expiry-date input {
  height: 48px;
  border-color: #E7ECEE !important;
  border-radius:8px;
  border-style:unset;
} */
/* .invitation-code-expiry-date div{
  border-radius:8px;
  border-style:unset;
 } */

table.form-table.invitation_code_table tr th {
  display: block;
  text-align: left;
  padding-top: 10px;
  padding-bottom: 10px;
  font-family: "figtree", sans-serif;
  font-size: 18px;
}
table.form-table.invitation_code_table tr td {
  display: block;
  padding-left: 0;
}

table.invitation_code_users tr th {
  display: table-cell !important;
  text-align: unset !important;
}
table.invitation_code_users tr td {
  display: table-cell !important;
  text-align: unset !important;
}

table.form-table.invitation_code_table .nua_codetxt {
  border-color: #7cb77a !important;
}

.post-type-invitation_code #wpcontent {
  background-color: #fff;
}
.post-type-invitation_code div#nua_invitation {
  border: 1px solid #80808054;
  border-radius: 4px;
}

.post-type-invitation_code input#publish {
  width: 100px;
  height: 34px;
  font-size: 14px;
  text-align: center;
  background-color: #618e5f;
  border: unset;
}

.invitation_code_users {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-family: "figtree", sans-serif;
}
.invitation_code_users tr {
  border: 1px solid #ccc;
}
.invite-email-select input {
  box-shadow: unset !important;
}

.invitation_code_users th,
.invitation_code_users td {
  border: 1px solid #ccc;
  padding: 10px;
  text-align: left;
  padding-left: 10px !important;
}

.invitation_code_users th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.invitation_code_users tbody tr:nth-child(even) {
  background-color: #f9f9f9;
}

.invitation_code_users tbody tr:hover {
  background-color: #e0e0e0;
}

span.success-message-modal {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 21px;
  color: #618e5f;
}
div#select-roles input {
  box-shadow: unset;
}

.add-invitation-code {
    margin-bottom: 24px;
    display: block;
}
.add-invitation-code h4{
  font-size: 14px;
  margin-top: 0px;
}
#invitation-code-booknow-container {
  width: 100%;
}
#invitation-code-booknow-container .nua-bookNow {
  margin-left: auto;
  width: 250px;
  max-width: 300px;
}
#invitation-code-booknow-container .nua-bookNow img {
  margin-left: 85px;
  margin-top: 30px !important;
}
#invitation-code-booknow-container .nua-bookNow .booknow-content {
  margin-top: 25px !important;
  left: 56px !important;
}
#invitation-code-booknow-container .nua-bookNow .booknow-content button {
  margin-left: 20px;
}
/* new user approve buttons */

.nua-btn, .importBtn.nua-btn {
display: flex;
align-items: center;
padding: 10px;
width: 130px;
height: 40px;
font-weight: 500;
background-color: #618E5F;
color: white;
border: 0px;
border-radius: 4px;
cursor: pointer;
justify-content: center;
font-size: 14px;
text-transform: capitalize;
gap: 6px;
}
button.nua-btn.reset-nua-number-of-register{
  width: 108px; 
  margin-left: 10px;
}
.nua-btn:hover, .importBtn.nua-btn:hover {
  background-color: #8ab48a;
}
.openNuaModal.delete-inv-modal .nua-btn{
  width: 82px;
}
/* zapier */
.nua-zapier-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24px;
  gap: 50px;
}

.zapier-buttons {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.zapier-triggers-names {
  text-align: left;
  width: 100%;
}

.zapier-triggers-names-list li {
  list-style: disc;
}
.nua-zapier-section p code {
  background-color: #e8f5e7;
  color: #537a52;
}
.nua_weburl {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
  min-width: 369px;
}

.nua_weburl svg{
  cursor: copy;
}
.nua_weburl .content-copy path{
 color: #618E5F; 
}
.nua_weburl .web_value{
  border: 0;
  background: #F0F8F0;
  width: 100%;
  min-width: 100%;
  color: #537A52;
  font-size: 16px;
}
.nua_weburl .api-key{
 min-width: 100%;
 border-radius: 8px;
 height: 42px;
}
.zapierApi {
  display: flex;
  width: 100%;
  gap: 15px;
}
button.generate-api-btn.nua-btn {
  min-width: 180px;
}

.zapierApi input {
  width: 100%;
}
.zapierApi .generate-api-btn{
  min-width: 186px;
  gap: 5px;
}
.zapier-triggers-names-list{
 flex-direction: column; 
 gap: 0;
}
.zapier-triggers-names-list .setting-option{
margin: 0;
width: 100%;  
}

.zapier-triggers-names-list ul li{
  background: #FAFAFA;
  height: 30px;
  border-radius: 8px;
  list-style: none;
  align-items: center;
  padding: 8px 16px 8px 16px;
  width: fit-content;
  font-size: 14px;
  display: flex;
  color: #5C5C5C;
  margin: 0;
  margin-bottom: 9px;
}
.zapier-tab button{
  display: flex;
  align-items: center;
  gap: 5px; 
}
/* invitation Code */

.invitation_code_subtabs_list .right-tabs {
  margin-left: auto;
}

.nua_inviCode_parent_tabs {
  padding: 32px;
  padding-top: 16px;
  text-align: center;
  /* background-color: #fff; */
  border: none;
  margin-top: 0px;
  border-radius: 15px;
}

.nua_inviCode_parent_tabs_tablist {
  display: flex;
  gap: 5px;
  align-items: center;
  padding: 0px;
  background-color: #ffffff;
  border-radius: 10px;
  height: 50px;
  min-height: 50px;
}
.nua_inviCode_parent_tabs_tablist button {
  width: 130px;
  height: 50px;
  border: 0px solid;
  border-radius: 10px;
  background-color: #ffffff;
  cursor: pointer;
}

.nua_inviCode_parent_tabs_tablist button:hover {
  color: #537a52;
  width: 130px;
  height: 50px;
}
.nua_main_settings, .registration_settings, .nua-help-section{
padding-top: 20px;
}

/* invitation code : add code */

.add-code-tabPanel {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
  text-align: left;
}

/* auto code generate */

.auto-code-generate {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
  text-align: left;
}

/* impot code */

.import-code-section{
  margin-bottom: 25px;
}

.import-code {
  display: flex;
  flex-direction: column;
  position: relative;
}
.import-code .import-csv{
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  top: 0;
  left: 0;
  cursor: pointer; 
}
.import-code .import-csv input{
  width: 100%;
  height: 176px;
  padding: 0px;
}
.import-csv-file{
  border: 3px dashed #D5D5D5; 
  text-align: center;
  flex-direction: column;
  align-items: center;
  padding: 30px;
  background: #FAFAFA;
  border-radius: 6px;
}
.import-code .import-csv-file h4 {
  font-size: 14px;
  margin-bottom: 0;
}
.import-code .import-csv-file p {
  font-size: 13px;
  color: #707070;
  margin-top: 5px;
}

.download-sample-codes-csv {
  display: flex;
  justify-content: end;
}
.download-sample-codes-csv h3{
  margin: 0;
  font-size: 12px;
  color: #618E5F; 
}
.selected-file-wrapper {
  background: #fff;
  width: 180px;
  margin: 0 auto;
  padding: 5px 15px 5px 15px;
  border-radius: 100px;
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
  z-index: 1;
}
.selected-file-wrapper .file-name{
 font-size: 14px;
 color: #242424;
 font-weight: 400; 
 flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
button.clear-file-btn{
  border: unset;
  background: unset; 
}
/* ivitation code email */

.invitation-email-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: #fff;
  border-radius: 10px;
  text-align: left;
}
.invitation-email-box h4, .nua_edit_invitation_code h4{
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 14px;
  color: #242424;
  line-height: 20%;
}

.invitation-email-box .nua-field-col, .nua_edit_invitation_code .nua-field-col{
  width: 100%;
  margin-bottom: 24px;
}
span.nua-code-email{
  width: 47%;
}
.invite-email-select .css-13cymwt-control{
  background-color: #FAFAFA;
}
.invite-email-select .css-1u9des2-indicatorSeparator{
  display: none;
}
.openNuaTabsModal .invitation-email-box .setting-option{
  display: block;
  width: 100%;
  margin-top: 0px;
}
/* import code message or status */

.statusMessage {
  display: block;
  margin-top: 10px;
}

/* dashboard icons */

.nua_dash_parent_tablist button span {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 4px;
  font-size: 16px;
  padding: 0px;
}

.usersTable table tr:last-child td {
  border-bottom: unset;
}

/* users status color */



.status-icon.inactive path {
  fill: #ADADAD;
}
.user-action-btn{
  position: relative;
}
.save-changes.loading {
  opacity: 0.5;
  pointer-events: none; /* optional: prevent clicks while loading */
}
.user-action-btn .MuiIconButton-root:hover, .actionsRow .MuiIconButton-root:hover, .user_roles_tbl_container.usersTable table tbody tr td button:hover {
  background: unset!important;
}

.user-action-btn button.MuiIconButton-root .statusDiv {
  display: flex;
  gap: 16px;
}

.user-action-btn button.MuiIconButton-root span{
 display: flex; 
}
.user-action-btn .new-user-approve-loading {
  position: absolute;
  right: 32px;
  top: 26px;
}

.recent_user_tbl_container .user-action-btn .new-user-approve-loading{
  right: 10px;
  top: 24px;
}

.user-pending {
  color: #fbb653;
  background-color: #fff8ed;
  border-radius: 12px;
  padding: 5px 20px 5px 20px;
  width: 55px;
  text-align: center;
}
.user-approved {
  color: #74b27b;
  background-color: #edffef;
  border-radius: 12px;
  padding: 5px 15px 5px 15px;
  width: 64px;
  text-align: center;
}
.user-denied {
  color: #e57373;
  background-color: #ffeded;
  border-radius: 12px;
  padding: 5px 22px 5px 22px;
  width: 50px;
  text-align: center;
}

/* book now */
.nua-bookNow {
  background-color: #e9fde7;
  border-radius: 8px;
  position: relative;
}
.nua-bookNow img {
  margin-top: 26px;
}

.booknow-content p {
  font-weight: 600;
  color: #375136;
  text-align: center;
}

/* settings */
/* general setting */
.nua_settings_parent_tabs {
  padding: 16px;
  text-align: center;
  /* background-color: #fff; */
  border: none;
  margin-top: 10px;
  border-radius: 15px;
}
.nua_settings_parent_tablist {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: center;
  padding: 0px 0px 0px 10px;
  background-color: #ffffff;
  border-radius: 10px;
  height: 50px;
  min-height: 50px;
}

.nua_settings_parent_tablist button {
  max-height: 140px;
  height: 50px;
  border: 0px solid;
  background-color: #ffffff;
  cursor: pointer;
  font-size: 16px;
}
.nua_settings_parent_tablist button:hover {
  color: #537a52;
  max-height: 140px;
  height: 50px;
}
button.nua-btn.save-changes::after {
  content: unset;
}
.nua_settings_parent_tabs button {
  position: relative;
}

.nua_settings_parent_tabs button::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 0; /* Initially hidden */
  height: 3px; /* Thickness of underline */
  background-color: #537a52;
  transition: width 0.3s ease-in-out; /* Smooth effect */
}

.nua_settings_parent_tabs button:hover::after {
  width: 100%; /* Expands underline on hover */
}

.help_setting_tab {
  width: 10% !important;
}

.nua_settings {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}
.nua_switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.nua_switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.nua_slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #E1F4E1 !important;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.nua_slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  top: 2px;
  background-color: #618E5F !important;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .nua_slider {
  background-color: #618E5F !important;
  border: 1px solid #618E5F;
}

input:focus + .nua_slider {
  box-shadow: 0 0 1px #6ea26d !important;
}

input:checked + .nua_slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
  background-color: #E1F4E1 !important;
  top: 2px;
}

/* Rounded sliders */
.nua_slider.round {
  border-radius: 63px;
  height: 20px;
  width: 40px;
  border: 1px solid #B6CAB4
}

.nua_slider.round:before {
  border-radius: 63%;
}
.openNuaTabsModal .nua-modal-close{
  color: #242424;
}
.nua-modal-close svg{
  width: 16px;
  height: 16px;
}
.openNuaTabsModal .setting-option{
  justify-content: end;
  gap: 25px; 
}
.openNuaTabsModal .MuiDialogActions-root .cancelBtn{
  font-size: 14px;
  color:#618E5F;
  text-transform: capitalize;
}
.openNuaTabsModal .nua-modal-footer{
  padding-left: 24px;
  padding-right: 24px; 
}
.setting-option {
  display: flex;
  gap: 70px;
  font-weight: 500;
  margin-top: 24px;
}
.setting-label {
  width: 185px;
  max-width: 185px;
  text-align: left;
}
.setting-element {
  display: flex;
  flex-direction: column;
  font-weight: 400;
  gap: 5px;
  text-align: left;
}
.nua-user-role-request-shortcode-element {
  background: #F0F8F0;
  border-radius: 6px;
  min-width: 250px;
}

/* nua react-select multi-fields border color */

/* nua fields border color */
#nua_admin_email_address,
#nua_notification_admin_email_subject,
#approve_notification_email_subject,
#nua_deny_notification_email_subject,
#nua_welcome_notification_email_subject {
  border-color: #7cb77a;
}
#nua_admin_email_address{
  max-width: 250px; 
}
.nua-user-role-list-element.setting-element{
  min-width: 250px; 
}
/* nua buttons color */
/* button focus */
p.help-email {
  display: flex;
  align-items: center;
  gap: 8px;
}
.help-email .nua-support-email {
font-size: 16px;
text-align: center;
display: block;
color: #618E5F;
border: 0px;
background: unset !important;
padding: 0;
}

a.button.nua-support-email:hover {
  color: #8ab48a;
}
/* .nua_notification_sub_tabstablist button:hover{
  position:unset;
} */
.nua_notification_sub_tabstablist button:hover::after {
  width: unset;
}
.nua_notification_sub_tabstablist button::after {
  content: unset;
}

#nua-to-copy-shortcode {
  margin: 10px 15px;
  margin-left: 0px;
  color: #537A52;
  font-size: 14px;
  display: flex;
  padding-left: 6px;
  justify-content: space-between;
  background: #f0f8f0;
  padding: 8px;
  border-radius: 6px;
}
.nua_user_role_request_shortcode.setting-option{
align-items: center;  
}

#nua-copy-icon {
  font-size: 20px;
  margin-left: 15px;
  cursor: pointer;
}
#nua_copied_text {
  display: none;
  color: grey;
  font-size: 14px;
  position: absolute;
  margin-top: 10px;
  margin-left: 581px;
}

#dashicons-shortcode {
  font-size: 17px;
}
.nua_user_roles span.select2-container span.selection {
  text-align: left;
}

.Roles__input:focus,
#react-select-2-input:focus {
  box-shadow: none !important;
}

/* general setting end */

/* registration settings */

.registration_settings {
  align-items: flex-start;
}
.registration_settings .wp-editor-wrap{
  max-width: 550px;
  width: 550px;
  top: -28px;
  height: 183px;
}
.registration_settings p.description{
  max-width: 530px;
  margin-top: 12px; 
}
#registration_heading,
.setting-section {
  text-align: left;
}
.nua-help-section p.description{
width: 100%;
}
.nua-reset-count {
  margin-left: 10px;
}
.Toastify__toast--success .Toastify__toast-icon svg{
  fill: #618E5F!important;
}
.setting-editor-area {
  width: 500px;
  min-width: 500px;
  height: 150px;
  min-height: 150px;
}
.registration-section {
  text-align: left;
}

.nua-remain-user-count {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.nua_inner_settings .setting-option .auto-code-field{
  min-width: 250px;
}
.nua_inner_settings input.nua-number-of-register.auto-code-field{
width: auto;
}
/* registration setting end */

/* admin notificaiton settings */

.nua_admin_email_specificEmail {
  min-width: 250px;
}

#wp-admin-notification-email-wrap {
  z-index: 0;
}
/* settings End */

/* whitelist2 */

/* .whitelist2 {

    width: 500px;
    min-width: 500px;
    height: 150px;
    min-height: 150px;

}

.whitelist2:hover{

  border-color:#7CB77A;
}
.whitelist2:focus {
  border-color:#7CB77A;
  box-shadow: 0 0 0 0px #7CB77A;


} */

/* active tabs */
button.nua_active_tab {
  color: #537a52;
}

button.nua_active_subtab {
  background-color: #ffffff;
  color: #333333;
  position: relative;
  /* border-radius: 15px; */
}
.nua_dash_parent_tablist button:hover svg path,
button.nua_active_tab svg path {
  fill: #537a52;
}
.integration_subtabs_list button.base--selected {
  background-color: #ffffff;
  color: #333333;
  position: relative;
}

.users_subtabs_list button.nua_active_subtab::after,
.integration_subtabs_list button.base--selected::after,
.auto-approve-tabs button.nua_active_subtab::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -4px;
  transform: translateX(-50%);
  width: 100%;
  height: 1px;
  background-color: #333333;
  transition: width 0.3s ease;
}

.invitation_code_subtabs_list button.nua_active_subtab::after,
.nua_notification_sub_tabstablist button.nua_active_subtab::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  width: 100%;
  height: 1px;
  background-color: #333333;
  transition: width 0.3s ease;
}


.invitation_code_subtabs_list button.nua_active_subtab:nth-child(2)::after {
  left: 50% !important;
  width: 75% !important;
}

/* new user approve loading */

.nua-spinner {
  width: 12px;
  height: 12px;
  border: 1px solid #7cb77a;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.new-user-approve-loading {
  display: flex;
  justify-content: center;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* save changes button */


/* fix new changes */
.all-user-empty-list .new-user-approve-loading,
.pending-user-empty-list .new-user-approve-loading,
.approved-user-empty-list .new-user-approve-loading,
.denied-user-empty-list .new-user-approve-loading,
.recent-user-empty-list .new-user-approve-loading {
  width: 100%;
}
/* fix new changes */
.all-user-empty-list .new-user-approve-loading .nua-spinner,
.pending-user-empty-list .new-user-approve-loading .nua-spinner,
.approved-user-empty-list .new-user-approve-loading .nua-spinner,
.denied-user-empty-list .new-user-approve-loading .nua-spinner,
.recent-user-empty-list .new-user-approve-loading .nua-spinner {
  margin-right: auto;
  margin-left: 90px;
}


/* .recent-user-empty-list,
.denied-user-empty-list,
.approved-user-empty-list,
.all-user-empty-list,
.pending-user-empty-list {
  width: 200px;
} */
.invitation-codes-header .user-list-empty{
  margin-top: 25px;
}
/* fix new changes */
.user-role-empty-list .user-found-error {
  width: 190px;
}
/* fix new changes */

/* user role edit button */
.MuiTableCell-root > .MuiButtonBase-root:hover {
  background-color: white;
}
.MuiTableBody-root .MuiTableRow-root .MuiTableCell-root {
  font-family: "figtree", sans-serif;
  color: #858585;
}

.MuiTable-root .MuiTableHead-root .MuiTableRow-root .MuiTableCell-root {
  font-family: "figtree", sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  color: #707070;
}
.edit_menu_btn p {
  font-family: "figtree", sans-serif;
  font-size: 14px;
  color: #618e5f;
}

.edit_menu_btn{
  border: 1px solid #E6EBEF!important;
  border-radius: 4px!important;
}

/* wp editor */

.nua-editor-element textarea {
  height: 126px;
  width: 100%;
  border: 0px;
}

/* settings help */


.nua_setting_tab.nua_main_settings .nua_inner_settings, .registration_settings .nua_inner_settings{ 
  margin-bottom: 32px;
}
.nua_setting_tab.nua_main_settings .setting-option{
  gap: 180px; 
  margin-bottom: 32px;
}
svg.proicon{
margin-left: 10px;  
}

.invite-code-email-as-html-element.setting-element {
  flex-direction: row;
  justify-content: space-between;
  margin-top: 24px;
  align-items: center;
}
.invite-code-email-as-html-element.setting-element label.nua_switch{
  height: auto;
}
span.help-siteUrl {
  color: #242424;
}
.nua-path-class{
 fill: beige !important;
}
.nua-setting-pro{
opacity: 0.5;  
}
#nua-diagnostics-textarea {
  width: 600px;
  margin-right: auto;
  height: 342px;
  max-height: 342px;
  font-family: "figtree", sans-serif;
  background: 0 0;
  border-color: #D5D5D5;
  background-color: #fafafa;
  border-radius: 10px;
  white-space: pre;
  overflow: auto;
  display: block;
  color: #242424;
  font-size: 14px;
  padding: 16px;
}
#nua-download-diagnostics {
  width: auto;
  margin-right: auto;
  margin-top: 10px;
}

.recent_update_container {
  position: relative;
}
.recent_update_container h2 {
  position: absolute;
  top: -48px;
}
.nua-dashboard-wrap {
  display: block;
}
.nua-status-container {
  display: flex;
  gap: 5px;
  align-items: center;
}
.nua-table-pagination{
  padding-bottom: 15px;
  padding-top: 15px;  
}

.nua-nav-pagination ul li button.Mui-selected {
  background-color: #6EA26D;
  color: white;
  border-color: #6EA26D;
}

.nua-nav-pagination ul li button.Mui-selected:hover {
  background-color: #74b27b;
  color: white;
  border-color: #74b27b;
}
.nua-nav-pagination ul li button{
  width: 42px;
  height: 32px;
}

.all_users_list_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.nua-search-field-box{
  width: 320px !important;
}

.nua-search-field {
  border-color: #E6EBEF !important;
  height: 40px;
  margin-bottom: 15px;
  width: 100%;
}
.MuiFormControl-root.role-control{
  margin: unset;
}
.nua-search-loading {
  position: absolute;
  right: 9px;
  top: 36%;
  transform: translateY(-50%);
  color: #888;
  font-size: 16px;
}

.recent_update_container {
  position: relative;
}

.recent_update_container h2 {
  position: absolute;
  top: -48px;
}

.nua-setting-row {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  gap: unset;
}

.nua-setting-label {
  flex: 1;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  /* background: red; */
}

.nua-setting-control {
  flex: 2;
  display: flex;
  align-items: center;
}
.settting-section.nua-help-section .nua-setting-control{
  flex: 4;
}
/* recent */