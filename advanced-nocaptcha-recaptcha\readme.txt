=== CAPTCHA 4WP - Antispam CAPTCHA solution for WordPress ===
Contributors: WPKube
Plugin URI: https://captcha4wp.com/
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl.html
Tags: captcha, recaptcha, hcaptcha, cloudflare turnstile, antispam protection
Requires at least: 5.0
Tested up to: 6.8.1
Stable tag: 7.6.0
Requires PHP: 7.4.0

Use CAPTCHA to stop spam and allow customers & users to interact with your website easily. Block fake accounts and orders. Avoid false positives.

== Description ==

#### IMPORTANT NOTICE

CAPTCHA 4WP has been acquired by WPKube. [Read the announcement](https://melapress.com/captcha-4-wp-plugin-acquired-by-wpkube/) for more information.
We, at Melapress, would like to take this opportunity to thank everyone who has used and supported CAPTCHA 4WP.

### A free and easy-to-use CAPTCHA plugin for WordPress

Protect your WordPress forms and login pages from spam and automated attacks with [CAPTCHA 4WP](https://captcha4wp.com/). Choose from multiple ReCAPTCHA versions and strike the right balance between security and user experience.


[Features](https://captcha4wp.com/features/) | [Get the Premium!](https://captcha4wp.com/pricing/) | [Getting Started](https://captcha4wp.com/docs/getting-started-with-captcha-4wp/)

Use the CAPTCHA 4WP plugin to add CAPTCHA checks to WordPress forms and logins. Choose from V2 (I’m not a robot), V2 Invisible, and V3 noCAPTCHA to ensure the best user experience at all times without compromosing security. Avoid false positives falling through the crack with V3 failover to ensure humans can still pass the test even if the result comes back below the passmark.
 
### CAPTCHA 4WP key plugin features and capabilities
- Add CAPTCHA to WordPress native forms such as login pages, user registration and comments forms etc
- Supports multiple ReCAPTCHA versions
- User-friendly wizards for easy, hassle-free setup
- Set ReCAPTCHA V3 passmark score 
- ReCAPTCHA failover configuration (ensure no prospect is incorrectly marked as spam)
- Plugin automatically detects visitors’ language and shows CAPTCHA in that language
- Much more
 
### Upgrade to CAPTCHA 4WP Premium and get even more

With the premium edition of CAPTCHA 4WP, you can choose from a wider range of CAPTCHA service providers, 1-click WooCommerce and other 3rd party plugin support, whitelisting, and much more.

### Premium features list

-   Everything in the free version
-   Add CAPTCHA from hCaptcha and Cloudflare Turnstile on your websites, both free and GDPR compliant
-	Add Geoblocking on forms and WordPress comments form (block / limit form submissions or comment posting by country)
-   Customization options for the CAPTCHA checks text, visual and position on the forms
-   One-click integration with WooCommerce
-   One-click integration with Contact Form 7, Gravity Forms, WPForms, BuddyPress & other plugins
-   Much more

Refer to the [CAPTCHA 4WP plugin features and benefits page](https://captcha4wp.com/features/) to learn more about the benefits of upgrading to the Premium version of CAPTCHA 4WP.
    
## Free and premium support

Support for CAPTCHA 4WP is free through the WordPress support forums.

Premium support for paid customer support is provided via one-to-one email. Upgrade to Premium to benefit from premium support.

For any other queries, feedback, or if you simply want to get in touch with us, please use our [contact form](https://captcha4wp.com/submit-ticket/).

## As featured on:

- [WP Beginner](https://www.wpbeginner.com/plugins/how-to-add-captcha-in-wordpress-login-and-registration-form/)
- [Elegant Themes](https://www.elegantthemes.com/blog/wordpress/wordpress-captcha)
- [IsItWP](https://www.isitwp.com/best-wordpress-captcha-plugins/)
- [WPLift](https://wplift.com/best-wordpress-captcha-plugins)
- [TesterWP](https://testerwp.com/best-free-captcha-wordpress-plugins/)

## Related links and documentation:

You can find more detailed information about CAPTCHA tests and the benefits you can take advantage of, and the plugin in the links below:

- [Why you need CAPTCHA on your WordPress website](https://captcha4wp.com/why-need-captcha-wordpress-website/)
- [The different types of CAPTCHA for websites](https://captcha4wp.com/different-types-captcha-checks-wordpress/)
- [What is the difference between CAPTCHA, ReCAPTCHA, and NoCAPTCHA?](https://captcha4wp.com/captcha-recaptcha-nocaptcha-differences/)
- [Getting started with CAPTCHA 4WP](https://captcha4wp.com/docs/getting-started-with-captcha-4wp/)
- [How to add CAPTCHA on WooCommerce forms](https://captcha4wp.com/docs/how-to-add-captcha-to-woocommerce-forms/)
- [How to add CAPTCHA on WPForms](https://captcha4wp.com/docs/how-to-add-captcha-on-wpforms-forms/)
- [How to add CAPTCHA on Gravity Forms](https://captcha4wp.com/docs/how-to-add-captcha-on-gravity-forms-forms/)
- [How to show CAPTCHA on failed logins](https://captcha4wp.com/docs/how-to-show-captcha-on-failed-logins/)
- [Official Melapress website](https://captcha4wp.com/)

== Installing CAPTCHA 4WP ==

###From within WordPress

1.  Navigate to "Plugins"page and click the "Add New" button
2.  Search for "CAPTCHA 4WP"
3.  Cick install and after that activate the plugin
  
###Manually

1.  Download the plugin from the WordPress plugins repository
2.  Unzip the zip file and upload the folder to the /wp-content/plugins/ directory
3.  Activate CAPTCHA 4WP through the "Plugins" menu in WordPress

== Frequently Asked Questions ==

= Can i use this plugin to my language? =
Yes. this plugin is translator ready.

= Can i show multiple CAPTCHA's on the same page? =
Yes. You can show an unlimited number of CAPTCHA's on the same page.

= How can I add CAPTCHA to a form created with Contact Form 7? =
Use the unique 1-click feature: simply click the "Add CAPTCHA" button in the Contact Form 7 form builder to add the CAPTCHA to the form. Support for Contact Form 7, WPForms, Gravity Forms and other popular third party plugins is only available in [CAPTCHA 4WP Premium](https://captcha4wp.com/features/).

= How can I add Captcha to WooCommerce checkout and other pages? =
Simply select the WooCommerce page you want to add CAPTCHA to in the plugin's CAPTCHA placement settings. You can also specify where exactly you want to add the CAPTCHA test on the checkout page. Support for WooCommerce and other third party plugins is only available in CAPTCHA 4WP Premium.


== Screenshots ==

1. The wizard makes configuration a breeze.
2. Use the wizard to choose your preferred CAPTCHA version and seamlessly connect to the service provider.
3. When using ReCAPTCHA V3, you can also choose a failover action to avoid false negatives falling through the cracks.
4. ReCAPTCA V3 in the WordPress login form. 
5. ReCAPTCA V2 "I'm not a robot" in the WordPress comments form. 
6. ReCAPTCA V2 "I'm not a robot" in the password reset request form. 
7. ReCAPTCA V2 "I'm not a robot" in the password reset form. 
8. The CAPTCHA configuration can easily be seen in the plugin's configuration page.

== Changelog ==

= 7.6.0 (20250514) =

Version 7.6.0 is the first update of the CAPTCHA 4WP plugin released under WPKube. Read the [announcement](https://melapress.com/captcha-4-wp-plugin-acquired-by-wpkube/) for more information.

* **New features & functionality**
	* Introduced an option for users to choose how the plugin’s JavaScript files are loaded — either inline or as separate files — and where they are loaded from: the header or footer.
	* Added native, out-of-the-box support for Elementor forms.
	* Introduced a new filter to support automatic language detection, enhancing WPML integration and improving message translation across all CAPTCHA methods.

* **Improvements**
	* Improved compatibility with the latest WooCommerce versions (8.x and 9.x).
	* Refined the plugin's CSS for better UI consistency and overall user experience across plugin pages.
	* Geoblocking settings have been moved to a dedicated page ("Country Blocking Configuration") and can now be enabled or disabled from a new tab in the "Form Placements" page.
	* Redesigned the upgrade banner notice for improved appearance and usability.
	* Increased the minimum required WordPress version to 5.5.
	* Standardized UTM parameters across all plugin pages for better tracking.
	* Improved help text across various settings pages for enhanced clarity.
	* All plugin pages are now accessible on new installations, even before CAPTCHA is configured.
	* Applied a good number of JS tweaks and improvements to ensure proper compatibility with the latest versions of all the 3rd party plugins supported as out of the box: Contact Form 7, Woocommerce, Elementor Forms, Buddypress, Bbpress, Gravity Forms, WPForms, Everest Forms, Fluent Forms, Formidable Forms, Ninja Forms.

* **Bug fixes**
	* Resolved a compatibility issue with WooCommerce's "High-Performance Order Storage" feature.
	* Removed the outdated "Country Blocking Configuration" page from the discontinued "Starter" Premium plan.
	* Fixed a reported issue with Contact Form 7 where, in certain cases, the form confirmation message could be replaced with an error message from the plugin.
	* Fixed a PHP error reported when using hCaptcha.
	* Resolved JavaScript errors appearing on the multisite signup form when hCaptcha was enabled.
	* Fixed an issue where exempted users were still unable to post comments due to CAPTCHA validation.
	* Improved CAPTCHA handling for Ninja Forms when forms are resubmitted due to validation errors or incomplete fields.
	* BuddyPress: Fixed several JavaScript errors affecting comment and group submission forms.

