{"name": "freemius/wordpress-sdk", "description": "Freemius WordPress SDK", "keywords": ["<PERSON><PERSON><PERSON>", "wordpress", "plugin", "wordpress-plugin", "theme", "wordpress-theme", "sdk"], "homepage": "https://freemius.com", "license": "GPL-3.0-only", "scripts": {"phpcs": ["Composer\\Config::disableProcessTimeout", "phpcs -p -s --colors"], "phpcs:ci": ["Composer\\Config::disableProcessTimeout", "phpcs --standard=phpcompat.xml -p -s --colors"], "phpstan": "./vendor/bin/phpstan analyse phpstan.neon --memory-limit 2G", "phpstan:log": "./vendor/bin/phpstan analyse -c phpstan.neon --memory-limit 2G > error-phpstan.xml"}, "require": {"php": ">=5.6"}, "autoload": {"files": ["start.php"]}, "require-dev": {"wp-coding-standards/wpcs": "^2.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "squizlabs/php_codesniffer": "^3.7", "phpcompatibility/php-compatibility": "^9.3", "phpcompatibility/phpcompatibility-wp": "^2.1", "szepeviktor/phpstan-wordpress": "^1.3", "phpstan/extension-installer": "^1.3"}, "prefer-stable": true, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}, "platform": {"php": "8.0"}}}