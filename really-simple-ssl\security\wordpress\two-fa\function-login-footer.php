<?php
/**
 * Extracted from wp-login.php since that file also loads WP core which already have.
 *
 * @package REALLY_SIMPLE_SSL
 */

/**
 * Outputs the footer for the login page.
 *
 * @param string $input_id Which input to auto-focus.
 *
 * @global bool|string $interim_login Whether interim login modal is being displayed. String 'success'
 *                                    upon successful login.
 *
 * @since 3.1.0
 */
function login_footer( string $input_id = '' ) {
	global $interim_login;

	// Don't allow interim logins to navigate away from the page.
	if ( ! $interim_login ) {
		?>
		<p id="backtoblog">
			<?php
			$html_link = sprintf(
				'<a href="%s">%s</a>',
				esc_url( home_url( '/' ) ),
				sprintf(
				/* translators: %s: Site title. */
					_x( '&larr; Go to %s', 'site' ),
					get_bloginfo( 'title', 'display' )
				)
			);
			/**
			 * Filter the "Go to site" link displayed in the login page footer.
			 *
			 * @since 5.7.0
			 *
			 * @param string $link HTML link to the home URL of the current site.
			 */
			echo esc_url( apply_filters( 'login_site_html_link', $html_link ) );
			?>
		</p>
		<?php

		the_privacy_policy_link( '<div class="privacy-policy-page-link">', '</div>' );
	}

	?>
	</div><?php // End of <div id="login">. ?>

	<?php

	if ( ! empty( $input_id ) ) {
		?>
		<script type="text/javascript">
			try{document.getElementById('<?php echo esc_html( $input_id ); ?>').focus();}catch(e){}
			if(typeof wpOnload==='function')wpOnload();
		</script>
		<?php
	}

	/**
	 * Fires in the login page footer.
	 *
	 * @since 3.1.0
	 */
	do_action( 'login_footer' );

	?>
	<div class="clear"></div>
	</body>
	</html>
	<?php
}

/**
 * Outputs the JavaScript to handle the form shaking on the login page.
 *
 * @since 3.0.0
 */
function wp_shake_js() {
	?>
	<script type="text/javascript">
		document.querySelector('form').classList.add('shake');
	</script>
	<?php
}
