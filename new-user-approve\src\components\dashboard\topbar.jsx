import React from 'react';
import { redirect } from 'react-router-dom';

const images = require.context('../../assets/images', false, /\.(png|jpe?g|svg)$/);

function Logo() {
  // require(`./images/${imageName}`).default;
  let dash_icon = images(`./nua-logo.png`);

  return (
    <div className="logo">
      {/* Add your logo image here */}
      <img src={dash_icon} alt={'nua-logo'} />
    </div>
  );
}

function getPro(e) {
  window.open('https://newuserapprove.com/pricing/?utm_source=plugin&utm_medium=header_pro_btn', '_blank', 'noopener,noreferrer');
    return null;
}

function GetProButton() {
  let getProBtn = images(`./getProBtn.png`);
  let crownIcon = (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M3.51819 10.3058C3.13013 9.23176 2.9361 8.69476 3.01884 8.35065C3.10933 7.97427 3.377 7.68084 3.71913 7.58296C4.03193 7.49346 4.51853 7.70973 5.49173 8.14227C6.35253 8.52486 6.78293 8.71615 7.18732 8.70551C7.63257 8.69379 8.06088 8.51524 8.4016 8.19931C8.71105 7.91237 8.91861 7.45513 9.33373 6.54064L10.2486 4.52525C11.0128 2.84175 11.3949 2 12 2C12.6051 2 12.9872 2.84175 13.7514 4.52525L14.6663 6.54064C15.0814 7.45513 15.289 7.91237 15.5984 8.19931C15.9391 8.51524 16.3674 8.69379 16.8127 8.70551C17.2171 8.71615 17.6475 8.52486 18.5083 8.14227C19.4815 7.70973 19.9681 7.49346 20.2809 7.58296C20.623 7.68084 20.8907 7.97427 20.9812 8.35065C21.0639 8.69476 20.8699 9.23176 20.4818 10.3057L18.8138 14.9222C18.1002 16.897 17.7435 17.8844 16.9968 18.4422C16.2502 19 15.2854 19 13.3558 19H10.6442C8.71459 19 7.74977 19 7.00315 18.4422C6.25654 17.8844 5.89977 16.897 5.18622 14.9222L3.51819 10.3058Z" stroke="white" strokeWidth="1.5"/>
  <path d="M12 14H12.009" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  <path d="M7 22H17" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
  );

  return (
    <button className='ProButton' onClick={getPro}>{crownIcon}Get Pro</button>
    // <img src={getProBtn} alt={'getProBtn'} className='ProButton' onClick={getPro}/>

  );
}

function MenuTopBar() {
  return (
    <div className="menu-top-bar">
      <Logo />
      <GetProButton />
    </div>
  );
}

export default MenuTopBar;