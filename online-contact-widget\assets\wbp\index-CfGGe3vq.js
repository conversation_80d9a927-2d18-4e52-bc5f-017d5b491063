import{co as e,bp as t,cp as n,c as r,_ as o,d as a,a as i,h as s,r as l,p as u,j as p,i as f,W as c,b as d,q as v,m as g,a8 as m,u as h,s as y,e as b,cq as w,N as x,cr as O,bS as R,R as A,cs as E,bZ as T,O as C,o as S,bW as k,l as M,n as j,w as B,x as P,ct as F,cu as L,aO as _,aR as D,aM as I,A as H,cv as $,cw as W,a$ as N,ak as q,cx as z,y as U,cy as V,E as K,cz as Z,F as X,af as Y,g as G,a9 as J,c4 as Q,cA as ee,bT as te,cB as ne,cC as re,aI as oe,Z as ae,cD as ie,am as se,cE as le,cF as ue,t as pe}from"./wbs-Dtem2-xP.js";var fe=/\s/;var ce=/^\s+/;function de(e){return e?e.slice(0,function(e){for(var t=e.length;t--&&fe.test(e.charAt(t)););return t}(e)+1).replace(ce,""):e}var ve=/^[-+]0x[0-9a-f]+$/i,ge=/^0b[01]+$/i,me=/^0o[0-7]+$/i,he=parseInt;function ye(n){if("number"==typeof n)return n;if(e(n))return NaN;if(t(n)){var r="function"==typeof n.valueOf?n.valueOf():n;n=t(r)?r+"":r}if("string"!=typeof n)return 0===n?n:+n;n=de(n);var o=ge.test(n);return o||me.test(n)?he(n.slice(2),o?2:8):ve.test(n)?NaN:+n}var be=function(){return n.Date.now()},we=Math.max,xe=Math.min;function Oe(e,n,r){var o,a,i,s,l,u,p=0,f=!1,c=!1,d=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function v(t){var n=o,r=a;return o=a=void 0,p=t,s=e.apply(r,n)}function g(e){var t=e-u;return void 0===u||t>=n||t<0||c&&e-p>=i}function m(){var e=be();if(g(e))return h(e);l=setTimeout(m,function(e){var t=n-(e-u);return c?xe(t,i-(e-p)):t}(e))}function h(e){return l=void 0,d&&o?v(e):(o=a=void 0,s)}function y(){var e=be(),t=g(e);if(o=arguments,a=this,u=e,t){if(void 0===l)return function(e){return p=e,l=setTimeout(m,n),f?v(e):s}(u);if(c)return clearTimeout(l),l=setTimeout(m,n),v(u)}return void 0===l&&(l=setTimeout(m,n)),s}return n=ye(n)||0,t(r)&&(f=!!r.leading,i=(c="maxWait"in r)?we(ye(r.maxWait)||0,n):i,d="trailing"in r?!!r.trailing:d),y.cancel=function(){void 0!==l&&clearTimeout(l),p=0,o=u=a=l=void 0},y.flush=function(){return void 0===l?s:h(be())},y}const Re=Symbol("popper"),Ae=Symbol("popperContent"),Ee=r({role:{type:String,values:["dialog","grid","group","listbox","menu","navigation","tooltip","tree"],default:"tooltip"}}),Te=a({name:"ElPopper",inheritAttrs:!1});var Ce=o(a({...Te,props:Ee,setup(e,{expose:t}){const n=e,r={triggerRef:i(),popperInstanceRef:i(),contentRef:i(),referenceRef:i(),role:s((()=>n.role))};return t(r),u(Re,r),(e,t)=>l(e.$slots,"default")}}),[["__file","popper.vue"]]);const Se=r({arrowOffset:{type:Number,default:5}}),ke=a({name:"ElPopperArrow",inheritAttrs:!1});var Me=o(a({...ke,props:Se,setup(e,{expose:t}){const n=e,r=p("popper"),{arrowOffset:o,arrowRef:a,arrowStyle:i}=f(Ae,void 0);return c((()=>n.arrowOffset),(e=>{o.value=e})),d((()=>{a.value=void 0})),t({arrowRef:a}),(e,t)=>(g(),v("span",{ref_key:"arrowRef",ref:a,class:y(h(r).e("arrow")),style:m(h(i)),"data-popper-arrow":""},null,6))}}),[["__file","arrow.vue"]]);const je=r({virtualRef:{type:b(Object)},virtualTriggering:Boolean,onMouseenter:{type:b(Function)},onMouseleave:{type:b(Function)},onClick:{type:b(Function)},onKeydown:{type:b(Function)},onFocus:{type:b(Function)},onBlur:{type:b(Function)},onContextmenu:{type:b(Function)},id:String,open:Boolean}),Be=Symbol("elForwardRef"),Pe=a({name:"ElOnlyChild",setup(e,{slots:t,attrs:n}){var r;const o=f(Be),a=(i=null!=(r=null==o?void 0:o.setForwardRef)?r:w,{mounted(e){i(e)},updated(e){i(e)},unmounted(){i(null)}});var i;return()=>{var e;const r=null==(e=t.default)?void 0:e.call(t,n);if(!r)return null;if(r.length>1)return null;const o=Fe(r);return o?x(O(o,n),[[a]]):null}}});function Fe(e){if(!e)return null;const t=e;for(const n of t){if(R(n))switch(n.type){case T:continue;case E:case"svg":return Le(n);case A:return Fe(n.children);default:return n}return Le(n)}return null}function Le(e){const t=p("only-child");return C("span",{class:t.e("content")},[e])}const _e=a({name:"ElPopperTrigger",inheritAttrs:!1});var De=o(a({..._e,props:je,setup(e,{expose:t}){const n=e,{role:r,triggerRef:o}=f(Re,void 0);var a;a=o,u(Be,{setForwardRef:e=>{a.value=e}});const i=s((()=>v.value?n.id:void 0)),p=s((()=>{if(r&&"tooltip"===r.value)return n.open&&n.id?n.id:void 0})),v=s((()=>{if(r&&"tooltip"!==r.value)return r.value})),m=s((()=>v.value?`${n.open}`:void 0));let y;const b=["onMouseenter","onMouseleave","onClick","onKeydown","onFocus","onBlur","onContextmenu"];return S((()=>{c((()=>n.virtualRef),(e=>{e&&(o.value=F(e))}),{immediate:!0}),c(o,((e,t)=>{null==y||y(),y=void 0,k(e)&&(b.forEach((r=>{var o;const a=n[r];a&&(e.addEventListener(r.slice(2).toLowerCase(),a),null==(o=null==t?void 0:t.removeEventListener)||o.call(t,r.slice(2).toLowerCase(),a))})),L(e)&&(y=c([i,p,v,m],(t=>{["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach(((n,r)=>{_(t[r])?e.removeAttribute(n):e.setAttribute(n,t[r])}))}),{immediate:!0}))),k(t)&&L(t)&&["aria-controls","aria-describedby","aria-haspopup","aria-expanded"].forEach((e=>t.removeAttribute(e)))}),{immediate:!0})})),d((()=>{if(null==y||y(),y=void 0,o.value&&k(o.value)){const e=o.value;b.forEach((t=>{const r=n[t];r&&e.removeEventListener(t.slice(2).toLowerCase(),r)})),o.value=void 0}})),t({triggerRef:o}),(e,t)=>e.virtualTriggering?j("v-if",!0):(g(),M(h(Pe),P({key:0},e.$attrs,{"aria-controls":h(i),"aria-describedby":h(p),"aria-expanded":h(m),"aria-haspopup":h(v)}),{default:B((()=>[l(e.$slots,"default")])),_:3},16,["aria-controls","aria-describedby","aria-expanded","aria-haspopup"]))}}),[["__file","trigger.vue"]]),Ie="top",He="bottom",$e="right",We="left",Ne="auto",qe=[Ie,He,$e,We],ze="start",Ue="end",Ve="viewport",Ke="popper",Ze=qe.reduce((function(e,t){return e.concat([t+"-"+ze,t+"-"+Ue])}),[]),Xe=[].concat(qe,[Ne]).reduce((function(e,t){return e.concat([t,t+"-"+ze,t+"-"+Ue])}),[]),Ye=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Ge(e){return e?(e.nodeName||"").toLowerCase():null}function Je(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Qe(e){return e instanceof Je(e).Element||e instanceof Element}function et(e){return e instanceof Je(e).HTMLElement||e instanceof HTMLElement}function tt(e){return"undefined"!=typeof ShadowRoot&&(e instanceof Je(e).ShadowRoot||e instanceof ShadowRoot)}var nt={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];!et(o)||!Ge(o)||(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});!et(r)||!Ge(r)||(Object.assign(r.style,a),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};function rt(e){return e.split("-")[0]}var ot=Math.max,at=Math.min,it=Math.round;function st(e,t){void 0===t&&(t=!1);var n=e.getBoundingClientRect(),r=1,o=1;if(et(e)&&t){var a=e.offsetHeight,i=e.offsetWidth;i>0&&(r=it(n.width)/i||1),a>0&&(o=it(n.height)/a||1)}return{width:n.width/r,height:n.height/o,top:n.top/o,right:n.right/r,bottom:n.bottom/o,left:n.left/r,x:n.left/r,y:n.top/o}}function lt(e){var t=st(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function ut(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&tt(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function pt(e){return Je(e).getComputedStyle(e)}function ft(e){return["table","td","th"].indexOf(Ge(e))>=0}function ct(e){return((Qe(e)?e.ownerDocument:e.document)||window.document).documentElement}function dt(e){return"html"===Ge(e)?e:e.assignedSlot||e.parentNode||(tt(e)?e.host:null)||ct(e)}function vt(e){return et(e)&&"fixed"!==pt(e).position?e.offsetParent:null}function gt(e){for(var t=Je(e),n=vt(e);n&&ft(n)&&"static"===pt(n).position;)n=vt(n);return n&&("html"===Ge(n)||"body"===Ge(n)&&"static"===pt(n).position)?t:n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&et(e)&&"fixed"===pt(e).position)return null;var n=dt(e);for(tt(n)&&(n=n.host);et(n)&&["html","body"].indexOf(Ge(n))<0;){var r=pt(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function mt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function ht(e,t,n){return ot(e,at(t,n))}function yt(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function bt(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var wt={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,i=n.modifiersData.popperOffsets,s=rt(n.placement),l=mt(s),u=[We,$e].indexOf(s)>=0?"height":"width";if(a&&i){var p=function(e,t){return yt("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:bt(e,qe))}(o.padding,n),f=lt(a),c="y"===l?Ie:We,d="y"===l?He:$e,v=n.rects.reference[u]+n.rects.reference[l]-i[l]-n.rects.popper[u],g=i[l]-n.rects.reference[l],m=gt(a),h=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,y=v/2-g/2,b=p[c],w=h-f[u]-p[d],x=h/2-f[u]/2+y,O=ht(b,x,w),R=l;n.modifiersData[r]=((t={})[R]=O,t.centerOffset=O-x,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"==typeof r&&!(r=t.elements.popper.querySelector(r))||!ut(t.elements.popper,r)||(t.elements.arrow=r))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function xt(e){return e.split("-")[1]}var Ot={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Rt(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,s=e.position,l=e.gpuAcceleration,u=e.adaptive,p=e.roundOffsets,f=e.isFixed,c=i.x,d=void 0===c?0:c,v=i.y,g=void 0===v?0:v,m="function"==typeof p?p({x:d,y:g}):{x:d,y:g};d=m.x,g=m.y;var h=i.hasOwnProperty("x"),y=i.hasOwnProperty("y"),b=We,w=Ie,x=window;if(u){var O=gt(n),R="clientHeight",A="clientWidth";if(O===Je(n)&&("static"!==pt(O=ct(n)).position&&"absolute"===s&&(R="scrollHeight",A="scrollWidth")),o===Ie||(o===We||o===$e)&&a===Ue)w=He,g-=(f&&O===x&&x.visualViewport?x.visualViewport.height:O[R])-r.height,g*=l?1:-1;if(o===We||(o===Ie||o===He)&&a===Ue)b=$e,d-=(f&&O===x&&x.visualViewport?x.visualViewport.width:O[A])-r.width,d*=l?1:-1}var E,T=Object.assign({position:s},u&&Ot),C=!0===p?function(e){var t=e.x,n=e.y,r=window.devicePixelRatio||1;return{x:it(t*r)/r||0,y:it(n*r)/r||0}}({x:d,y:g}):{x:d,y:g};return d=C.x,g=C.y,l?Object.assign({},T,((E={})[w]=y?"0":"",E[b]=h?"0":"",E.transform=(x.devicePixelRatio||1)<=1?"translate("+d+"px, "+g+"px)":"translate3d("+d+"px, "+g+"px, 0)",E)):Object.assign({},T,((t={})[w]=y?g+"px":"",t[b]=h?d+"px":"",t.transform="",t))}var At={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,i=void 0===a||a,s=n.roundOffsets,l=void 0===s||s,u={placement:rt(t.placement),variation:xt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Rt(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Rt(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Et={passive:!0};var Tt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,i=r.resize,s=void 0===i||i,l=Je(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&u.forEach((function(e){e.addEventListener("scroll",n.update,Et)})),s&&l.addEventListener("resize",n.update,Et),function(){a&&u.forEach((function(e){e.removeEventListener("scroll",n.update,Et)})),s&&l.removeEventListener("resize",n.update,Et)}},data:{}},Ct={left:"right",right:"left",bottom:"top",top:"bottom"};function St(e){return e.replace(/left|right|bottom|top/g,(function(e){return Ct[e]}))}var kt={start:"end",end:"start"};function Mt(e){return e.replace(/start|end/g,(function(e){return kt[e]}))}function jt(e){var t=Je(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Bt(e){return st(ct(e)).left+jt(e).scrollLeft}function Pt(e){var t=pt(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function Ft(e){return["html","body","#document"].indexOf(Ge(e))>=0?e.ownerDocument.body:et(e)&&Pt(e)?e:Ft(dt(e))}function Lt(e,t){var n;void 0===t&&(t=[]);var r=Ft(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=Je(r),i=o?[a].concat(a.visualViewport||[],Pt(r)?r:[]):r,s=t.concat(i);return o?s:s.concat(Lt(dt(i)))}function _t(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Dt(e,t){return t===Ve?_t(function(e){var t=Je(e),n=ct(e),r=t.visualViewport,o=n.clientWidth,a=n.clientHeight,i=0,s=0;return r&&(o=r.width,a=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=r.offsetLeft,s=r.offsetTop)),{width:o,height:a,x:i+Bt(e),y:s}}(e)):Qe(t)?function(e){var t=st(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):_t(function(e){var t,n=ct(e),r=jt(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=ot(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=ot(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-r.scrollLeft+Bt(e),l=-r.scrollTop;return"rtl"===pt(o||n).direction&&(s+=ot(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:s,y:l}}(ct(e)))}function It(e,t,n){var r="clippingParents"===t?function(e){var t=Lt(dt(e)),n=["absolute","fixed"].indexOf(pt(e).position)>=0&&et(e)?gt(e):e;return Qe(n)?t.filter((function(e){return Qe(e)&&ut(e,n)&&"body"!==Ge(e)})):[]}(e):[].concat(t),o=[].concat(r,[n]),a=o[0],i=o.reduce((function(t,n){var r=Dt(e,n);return t.top=ot(r.top,t.top),t.right=at(r.right,t.right),t.bottom=at(r.bottom,t.bottom),t.left=ot(r.left,t.left),t}),Dt(e,a));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function Ht(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?rt(o):null,i=o?xt(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(a){case Ie:t={x:s,y:n.y-r.height};break;case He:t={x:s,y:n.y+n.height};break;case $e:t={x:n.x+n.width,y:l};break;case We:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var u=a?mt(a):null;if(null!=u){var p="y"===u?"height":"width";switch(i){case ze:t[u]=t[u]-(n[p]/2-r[p]/2);break;case Ue:t[u]=t[u]+(n[p]/2-r[p]/2)}}return t}function $t(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.boundary,i=void 0===a?"clippingParents":a,s=n.rootBoundary,l=void 0===s?Ve:s,u=n.elementContext,p=void 0===u?Ke:u,f=n.altBoundary,c=void 0!==f&&f,d=n.padding,v=void 0===d?0:d,g=yt("number"!=typeof v?v:bt(v,qe)),m=p===Ke?"reference":Ke,h=e.rects.popper,y=e.elements[c?m:p],b=It(Qe(y)?y:y.contextElement||ct(e.elements.popper),i,l),w=st(e.elements.reference),x=Ht({reference:w,element:h,placement:o}),O=_t(Object.assign({},h,x)),R=p===Ke?O:w,A={top:b.top-R.top+g.top,bottom:R.bottom-b.bottom+g.bottom,left:b.left-R.left+g.left,right:R.right-b.right+g.right},E=e.modifiersData.offset;if(p===Ke&&E){var T=E[o];Object.keys(A).forEach((function(e){var t=[$e,He].indexOf(e)>=0?1:-1,n=[Ie,He].indexOf(e)>=0?"y":"x";A[e]+=T[n]*t}))}return A}var Wt={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,i=n.altAxis,s=void 0===i||i,l=n.fallbackPlacements,u=n.padding,p=n.boundary,f=n.rootBoundary,c=n.altBoundary,d=n.flipVariations,v=void 0===d||d,g=n.allowedAutoPlacements,m=t.options.placement,h=rt(m),y=l||(h===m||!v?[St(m)]:function(e){if(rt(e)===Ne)return[];var t=St(e);return[Mt(e),t,Mt(t)]}(m)),b=[m].concat(y).reduce((function(e,n){return e.concat(rt(n)===Ne?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,i=n.padding,s=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?Xe:l,p=xt(r),f=p?s?Ze:Ze.filter((function(e){return xt(e)===p})):qe,c=f.filter((function(e){return u.indexOf(e)>=0}));0===c.length&&(c=f);var d=c.reduce((function(t,n){return t[n]=$t(e,{placement:n,boundary:o,rootBoundary:a,padding:i})[rt(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}(t,{placement:n,boundary:p,rootBoundary:f,padding:u,flipVariations:v,allowedAutoPlacements:g}):n)}),[]),w=t.rects.reference,x=t.rects.popper,O=new Map,R=!0,A=b[0],E=0;E<b.length;E++){var T=b[E],C=rt(T),S=xt(T)===ze,k=[Ie,He].indexOf(C)>=0,M=k?"width":"height",j=$t(t,{placement:T,boundary:p,rootBoundary:f,altBoundary:c,padding:u}),B=k?S?$e:We:S?He:Ie;w[M]>x[M]&&(B=St(B));var P=St(B),F=[];if(a&&F.push(j[C]<=0),s&&F.push(j[B]<=0,j[P]<=0),F.every((function(e){return e}))){A=T,R=!1;break}O.set(T,F)}if(R)for(var L=function(e){var t=b.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return A=t,"break"},_=v?3:1;_>0;_--){if("break"===L(_))break}t.placement!==A&&(t.modifiersData[r]._skip=!0,t.placement=A,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Nt(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function qt(e){return[Ie,$e,He,We].some((function(t){return e[t]>=0}))}var zt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=$t(t,{elementContext:"reference"}),s=$t(t,{altBoundary:!0}),l=Nt(i,r),u=Nt(s,o,a),p=qt(l),f=qt(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:p,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":f})}};var Ut={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,i=Xe.reduce((function(e,n){return e[n]=function(e,t,n){var r=rt(e),o=[We,Ie].indexOf(r)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,i=a[0],s=a[1];return i=i||0,s=(s||0)*o,[We,$e].indexOf(r)>=0?{x:s,y:i}:{x:i,y:s}}(n,t.rects,a),e}),{}),s=i[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=i}};var Vt={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Ht({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}};var Kt={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,i=n.altAxis,s=void 0!==i&&i,l=n.boundary,u=n.rootBoundary,p=n.altBoundary,f=n.padding,c=n.tether,d=void 0===c||c,v=n.tetherOffset,g=void 0===v?0:v,m=$t(t,{boundary:l,rootBoundary:u,padding:f,altBoundary:p}),h=rt(t.placement),y=xt(t.placement),b=!y,w=mt(h),x=function(e){return"x"===e?"y":"x"}(w),O=t.modifiersData.popperOffsets,R=t.rects.reference,A=t.rects.popper,E="function"==typeof g?g(Object.assign({},t.rects,{placement:t.placement})):g,T="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),C=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,S={x:0,y:0};if(O){if(a){var k,M="y"===w?Ie:We,j="y"===w?He:$e,B="y"===w?"height":"width",P=O[w],F=P+m[M],L=P-m[j],_=d?-A[B]/2:0,D=y===ze?R[B]:A[B],I=y===ze?-A[B]:-R[B],H=t.elements.arrow,$=d&&H?lt(H):{width:0,height:0},W=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},N=W[M],q=W[j],z=ht(0,R[B],$[B]),U=b?R[B]/2-_-z-N-T.mainAxis:D-z-N-T.mainAxis,V=b?-R[B]/2+_+z+q+T.mainAxis:I+z+q+T.mainAxis,K=t.elements.arrow&&gt(t.elements.arrow),Z=K?"y"===w?K.clientTop||0:K.clientLeft||0:0,X=null!=(k=null==C?void 0:C[w])?k:0,Y=P+V-X,G=ht(d?at(F,P+U-X-Z):F,P,d?ot(L,Y):L);O[w]=G,S[w]=G-P}if(s){var J,Q="x"===w?Ie:We,ee="x"===w?He:$e,te=O[x],ne="y"===x?"height":"width",re=te+m[Q],oe=te-m[ee],ae=-1!==[Ie,We].indexOf(h),ie=null!=(J=null==C?void 0:C[x])?J:0,se=ae?re:te-R[ne]-A[ne]-ie+T.altAxis,le=ae?te+R[ne]+A[ne]-ie-T.altAxis:oe,ue=d&&ae?function(e,t,n){var r=ht(e,t,n);return r>n?n:r}(se,te,le):ht(d?se:re,te,d?le:oe);O[x]=ue,S[x]=ue-te}t.modifiersData[r]=S}},requiresIfExists:["offset"]};function Zt(e,t,n){void 0===n&&(n=!1);var r=et(t),o=et(t)&&function(e){var t=e.getBoundingClientRect(),n=it(t.width)/e.offsetWidth||1,r=it(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=ct(t),i=st(e,o),s={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&(("body"!==Ge(t)||Pt(a))&&(s=function(e){return e!==Je(e)&&et(e)?function(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}(e):jt(e)}(t)),et(t)?((l=st(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=Bt(a))),{x:i.left+s.scrollLeft-l.x,y:i.top+s.scrollTop-l.y,width:i.width,height:i.height}}function Xt(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}function Yt(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}var Gt={placement:"bottom",modifiers:[],strategy:"absolute"};function Jt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function Qt(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?Gt:o;return function(e,t,n){void 0===n&&(n=a);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},Gt,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},i=[],s=!1,l={state:o,setOptions:function(n){var s="function"==typeof n?n(o.options):n;u(),o.options=Object.assign({},a,o.options,s),o.scrollParents={reference:Qe(e)?Lt(e):e.contextElement?Lt(e.contextElement):[],popper:Lt(t)};var p=function(e){var t=Xt(e);return Ye.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}(function(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}([].concat(r,o.options.modifiers)));return o.orderedModifiers=p.filter((function(e){return e.enabled})),o.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,a=e.effect;if("function"==typeof a){var s=a({state:o,name:t,instance:l,options:r}),u=function(){};i.push(s||u)}})),l.update()},forceUpdate:function(){if(!s){var e=o.elements,t=e.reference,n=e.popper;if(Jt(t,n)){o.rects={reference:Zt(t,gt(n),"fixed"===o.options.strategy),popper:lt(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach((function(e){return o.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<o.orderedModifiers.length;r++)if(!0!==o.reset){var a=o.orderedModifiers[r],i=a.fn,u=a.options,p=void 0===u?{}:u,f=a.name;"function"==typeof i&&(o=i({state:o,options:p,name:f,instance:l})||o)}else o.reset=!1,r=-1}}},update:Yt((function(){return new Promise((function(e){l.forceUpdate(),e(o)}))})),destroy:function(){u(),s=!0}};if(!Jt(e,t))return l;function u(){i.forEach((function(e){return e()})),i=[]}return l.setOptions(n).then((function(e){!s&&n.onFirstUpdate&&n.onFirstUpdate(e)})),l}}Qt(),Qt({defaultModifiers:[Tt,Vt,At,nt]});var en=Qt({defaultModifiers:[Tt,Vt,At,nt,Ut,Wt,Kt,wt,zt]});const tn=r({boundariesPadding:{type:Number,default:0},fallbackPlacements:{type:b(Array),default:void 0},gpuAcceleration:{type:Boolean,default:!0},offset:{type:Number,default:12},placement:{type:String,values:Xe,default:"bottom"},popperOptions:{type:b(Object),default:()=>({})},strategy:{type:String,values:["fixed","absolute"],default:"absolute"}}),nn=r({...tn,id:String,style:{type:b([String,Array,Object])},className:{type:b([String,Array,Object])},effect:{type:b(String),default:"dark"},visible:Boolean,enterable:{type:Boolean,default:!0},pure:Boolean,focusOnShow:{type:Boolean,default:!1},trapping:{type:Boolean,default:!1},popperClass:{type:b([String,Array,Object])},popperStyle:{type:b([String,Array,Object])},referenceEl:{type:b(Object)},triggerTargetEl:{type:b(Object)},stopPopperMouseEvent:{type:Boolean,default:!0},virtualTriggering:Boolean,zIndex:Number,...D(["ariaLabel"])}),rn={mouseenter:e=>e instanceof MouseEvent,mouseleave:e=>e instanceof MouseEvent,focus:()=>!0,blur:()=>!0,close:()=>!0},on=(e,t=[])=>{const{placement:n,strategy:r,popperOptions:o}=e,a={placement:n,strategy:r,...o,modifiers:[...an(e),...t]};return function(e,t){t&&(e.modifiers=[...e.modifiers,...null!=t?t:[]])}(a,null==o?void 0:o.modifiers),a};function an(e){const{offset:t,gpuAcceleration:n,fallbackPlacements:r}=e;return[{name:"offset",options:{offset:[0,null!=t?t:12]}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5,fallbackPlacements:r}},{name:"computeStyles",options:{gpuAcceleration:n}}]}const sn=(e,t,n={})=>{const r={name:"updateState",enabled:!0,phase:"write",fn:({state:e})=>{const t=function(e){const t=Object.keys(e.elements),n=$(t.map((t=>[t,e.styles[t]||{}]))),r=$(t.map((t=>[t,e.attributes[t]])));return{styles:n,attributes:r}}(e);Object.assign(l.value,t)},requires:["computeStyles"]},o=s((()=>{const{onFirstUpdate:e,placement:t,strategy:o,modifiers:a}=h(n);return{onFirstUpdate:e,placement:t||"bottom",strategy:o||"absolute",modifiers:[...a||[],r,{name:"applyStyles",enabled:!1}]}})),a=H(),l=i({styles:{popper:{position:h(o).strategy,left:"0",top:"0"},arrow:{position:"absolute"}},attributes:{}}),u=()=>{a.value&&(a.value.destroy(),a.value=void 0)};return c(o,(e=>{const t=h(a);t&&t.setOptions(e)}),{deep:!0}),c([e,t],(([e,t])=>{u(),e&&t&&(a.value=en(e,t,h(o)))})),d((()=>{u()})),{state:s((()=>{var e;return{...(null==(e=h(a))?void 0:e.state)||{}}})),styles:s((()=>h(l).styles)),attributes:s((()=>h(l).attributes)),update:()=>{var e;return null==(e=h(a))?void 0:e.update()},forceUpdate:()=>{var e;return null==(e=h(a))?void 0:e.forceUpdate()},instanceRef:s((()=>h(a)))}};const ln=e=>{const{popperInstanceRef:t,contentRef:n,triggerRef:r,role:o}=f(Re,void 0),a=i(),l=i(),u=s((()=>({name:"eventListeners",enabled:!!e.visible}))),p=s((()=>{var e;const t=h(a),n=null!=(e=h(l))?e:0;return{name:"arrow",enabled:(r=t,!(void 0===r)),options:{element:t,padding:n}};var r})),d=s((()=>({onFirstUpdate:()=>{b()},...on(e,[h(p),h(u)])}))),v=s((()=>(e=>{if(I)return F(e)})(e.referenceEl)||h(r))),{attributes:g,state:m,styles:y,update:b,forceUpdate:w,instanceRef:x}=sn(v,n,d);return c(x,(e=>t.value=e)),S((()=>{c((()=>{var e;return null==(e=h(v))?void 0:e.getBoundingClientRect()}),(()=>{b()}))})),{attributes:g,arrowRef:a,contentRef:n,instanceRef:x,state:m,styles:y,role:o,forceUpdate:w,update:b}},un=a({name:"ElPopperContent"});var pn=o(a({...un,props:nn,emits:rn,setup(e,{expose:t,emit:n}){const r=e,{focusStartRef:o,trapped:a,onFocusAfterReleased:m,onFocusAfterTrapped:y,onFocusInTrap:b,onFocusoutPrevented:x,onReleaseRequested:O}=((e,t)=>{const n=i(!1),r=i();return{focusStartRef:r,trapped:n,onFocusAfterReleased:e=>{var n;"pointer"!==(null==(n=e.detail)?void 0:n.focusReason)&&(r.value="first",t("blur"))},onFocusAfterTrapped:()=>{t("focus")},onFocusInTrap:t=>{e.visible&&!n.value&&(t.target&&(r.value=t.target),n.value=!0)},onFocusoutPrevented:t=>{e.trapping||("pointer"===t.detail.focusReason&&t.preventDefault(),n.value=!1)},onReleaseRequested:()=>{n.value=!1,t("close")}}})(r,n),{attributes:R,arrowRef:A,contentRef:E,styles:T,instanceRef:M,role:j,update:F}=ln(r),{ariaModal:L,arrowStyle:D,contentAttrs:I,contentClass:H,contentStyle:$,updateZIndex:U}=((e,{attributes:t,styles:n,role:r})=>{const{nextZIndex:o}=W(),a=p("popper"),l=s((()=>h(t).popper)),u=i(N(e.zIndex)?e.zIndex:o()),f=s((()=>[a.b(),a.is("pure",e.pure),a.is(e.effect),e.popperClass])),c=s((()=>[{zIndex:h(u)},h(n).popper,e.popperStyle||{}]));return{ariaModal:s((()=>"dialog"===r.value?"false":void 0)),arrowStyle:s((()=>h(n).arrow||{})),contentAttrs:l,contentClass:f,contentStyle:c,contentZIndex:u,updateZIndex:()=>{u.value=N(e.zIndex)?e.zIndex:o()}}})(r,{styles:T,attributes:R,role:j}),V=f(q,void 0),K=i();let Z;u(Ae,{arrowStyle:D,arrowRef:A,arrowOffset:K}),V&&u(q,{...V,addInputId:w,removeInputId:w});const X=(e=!0)=>{F(),e&&U()},Y=()=>{X(!1),r.visible&&r.focusOnShow?a.value=!0:!1===r.visible&&(a.value=!1)};return S((()=>{c((()=>r.triggerTargetEl),((e,t)=>{null==Z||Z(),Z=void 0;const n=h(e||E.value),o=h(t||E.value);k(n)&&(Z=c([j,()=>r.ariaLabel,L,()=>r.id],(e=>{["role","aria-label","aria-modal","id"].forEach(((t,r)=>{_(e[r])?n.removeAttribute(t):n.setAttribute(t,e[r])}))}),{immediate:!0})),o!==n&&k(o)&&["role","aria-label","aria-modal","id"].forEach((e=>{o.removeAttribute(e)}))}),{immediate:!0}),c((()=>r.visible),Y,{immediate:!0})})),d((()=>{null==Z||Z(),Z=void 0})),t({popperContentRef:E,popperInstanceRef:M,updatePopper:X,contentStyle:$}),(e,t)=>(g(),v("div",P({ref_key:"contentRef",ref:E},h(I),{style:h($),class:h(H),tabindex:"-1",onMouseenter:t=>e.$emit("mouseenter",t),onMouseleave:t=>e.$emit("mouseleave",t)}),[C(h(z),{trapped:h(a),"trap-on-focus-in":!0,"focus-trap-el":h(E),"focus-start-el":h(o),onFocusAfterTrapped:h(y),onFocusAfterReleased:h(m),onFocusin:h(b),onFocusoutPrevented:h(x),onReleaseRequested:h(O)},{default:B((()=>[l(e.$slots,"default")])),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusin","onFocusoutPrevented","onReleaseRequested"])],16,["onMouseenter","onMouseleave"]))}}),[["__file","content.vue"]]);const fn=U(Ce),cn=Symbol("elTooltip");function dn(){let e;const t=()=>window.clearTimeout(e);return V((()=>t())),{registerTimeout:(n,r)=>{t(),e=window.setTimeout(n,r)},cancelTimeout:t}}const vn=r({showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0}}),gn=r({...vn,...nn,appendTo:{type:b([String,Object])},content:{type:String,default:""},rawContent:Boolean,persistent:Boolean,visible:{type:b(Boolean),default:null},transition:String,teleported:{type:Boolean,default:!0},disabled:Boolean,...D(["ariaLabel"])}),mn=r({...je,disabled:Boolean,trigger:{type:b([String,Array]),default:"hover"},triggerKeys:{type:b(Array),default:()=>[K.enter,K.numpadEnter,K.space]}}),hn=Z({type:b(Boolean),default:null}),yn=Z({type:b(Function)}),{useModelToggleProps:bn,useModelToggleEmits:wn,useModelToggle:xn}=(e=>{const t=`update:${e}`,n=`onUpdate:${e}`,r=[t];return{useModelToggle:({indicator:r,toggleReason:o,shouldHideWhenRouteChanges:a,shouldProceed:i,onShow:l,onHide:u})=>{const p=X(),{emit:f}=p,d=p.props,v=s((()=>Y(d[n]))),g=s((()=>null===d[e])),m=e=>{!0!==r.value&&(r.value=!0,o&&(o.value=e),Y(l)&&l(e))},h=e=>{!1!==r.value&&(r.value=!1,o&&(o.value=e),Y(u)&&u(e))},y=e=>{if(!0===d.disabled||Y(i)&&!i())return;const n=v.value&&I;n&&f(t,!0),!g.value&&n||m(e)},b=e=>{if(!0===d.disabled||!I)return;const n=v.value&&I;n&&f(t,!1),!g.value&&n||h(e)},w=e=>{G(e)&&(d.disabled&&e?v.value&&f(t,!1):r.value!==e&&(e?m():h()))};return c((()=>d[e]),w),a&&void 0!==p.appContext.config.globalProperties.$route&&c((()=>({...p.proxy.$route})),(()=>{a.value&&r.value&&b()})),S((()=>{w(d[e])})),{hide:b,show:y,toggle:()=>{r.value?b():y()},hasUpdateHandler:v}},useModelToggleProps:{[e]:hn,[n]:yn},useModelToggleEmits:r}})("visible"),On=r({...Ee,...bn,...gn,...mn,...Se,showArrow:{type:Boolean,default:!0}}),Rn=[...wn,"before-show","before-hide","show","hide","open","close"],An=(e,t,n)=>r=>{((e,t)=>J(e)?e.includes(t):e===t)(h(e),t)&&n(r)},En=(e,t,{checkForDefaultPrevented:n=!0}={})=>r=>{const o=null==e?void 0:e(r);if(!1===n||!o)return null==t?void 0:t(r)},Tn=a({name:"ElTooltipTrigger"});var Cn=o(a({...Tn,props:mn,setup(e,{expose:t}){const n=e,r=p("tooltip"),{controlled:o,id:a,open:s,onOpen:u,onClose:c,onToggle:d}=f(cn,void 0),v=i(null),m=()=>{if(h(o)||n.disabled)return!0},b=Q(n,"trigger"),w=En(m,An(b,"hover",u)),x=En(m,An(b,"hover",c)),O=En(m,An(b,"click",(e=>{0===e.button&&d(e)}))),R=En(m,An(b,"focus",u)),A=En(m,An(b,"focus",c)),E=En(m,An(b,"contextmenu",(e=>{e.preventDefault(),d(e)}))),T=En(m,(e=>{const{code:t}=e;n.triggerKeys.includes(t)&&(e.preventDefault(),d(e))}));return t({triggerRef:v}),(e,t)=>(g(),M(h(De),{id:h(a),"virtual-ref":e.virtualRef,open:h(s),"virtual-triggering":e.virtualTriggering,class:y(h(r).e("trigger")),onBlur:h(A),onClick:h(O),onContextmenu:h(E),onFocus:h(R),onMouseenter:h(w),onMouseleave:h(x),onKeydown:h(T)},{default:B((()=>[l(e.$slots,"default")])),_:3},8,["id","virtual-ref","open","virtual-triggering","class","onBlur","onClick","onContextmenu","onFocus","onMouseenter","onMouseleave","onKeydown"]))}}),[["__file","trigger.vue"]]);const Sn=U(o(a({__name:"teleport",props:r({to:{type:b([String,Object]),required:!0},disabled:Boolean}),setup:e=>(e,t)=>e.disabled?l(e.$slots,"default",{key:0}):(g(),M(ee,{key:1,to:e.to},[l(e.$slots,"default")],8,["to"]))}),[["__file","teleport.vue"]])),kn=()=>{const e=ne(),t=re(),n=s((()=>`${e.value}-popper-container-${t.prefix}`)),r=s((()=>`#${n.value}`));return{id:n,selector:r}},Mn=()=>{const{id:e,selector:t}=kn();return te((()=>{I&&(document.body.querySelector(t.value)||(e=>{const t=document.createElement("div");t.id=e,document.body.appendChild(t)})(e.value))})),{id:e,selector:t}},jn=a({name:"ElTooltipContent",inheritAttrs:!1});var Bn=o(a({...jn,props:gn,setup(e,{expose:t}){const n=e,{selector:r}=kn(),o=p("tooltip"),a=i();let u;const{controlled:v,id:m,open:y,trigger:b,onClose:w,onOpen:O,onShow:R,onHide:A,onBeforeShow:E,onBeforeHide:T}=f(cn,void 0),S=s((()=>n.transition||`${o.namespace.value}-fade-in-linear`)),k=s((()=>n.persistent));d((()=>{null==u||u()}));const F=s((()=>!!h(k)||h(y))),L=s((()=>!n.disabled&&h(y))),_=s((()=>n.appendTo||r.value)),D=s((()=>{var e;return null!=(e=n.style)?e:{}})),I=i(!0),H=()=>{A(),I.value=!0},$=()=>{if(h(v))return!0},W=En($,(()=>{n.enterable&&"hover"===h(b)&&O()})),N=En($,(()=>{"hover"===h(b)&&w()})),q=()=>{var e,t;null==(t=null==(e=a.value)?void 0:e.updatePopper)||t.call(e),null==E||E()},z=()=>{null==T||T()},U=()=>{R(),u=ie(s((()=>{var e;return null==(e=a.value)?void 0:e.popperContentRef})),(()=>{if(h(v))return;"hover"!==h(b)&&w()}))},V=()=>{n.virtualTriggering||w()};return c((()=>h(y)),(e=>{e?I.value=!1:null==u||u()}),{flush:"post"}),c((()=>n.content),(()=>{var e,t;null==(t=null==(e=a.value)?void 0:e.updatePopper)||t.call(e)})),t({contentRef:a}),(e,t)=>(g(),M(h(Sn),{disabled:!e.teleported,to:h(_)},{default:B((()=>[C(oe,{name:h(S),onAfterLeave:H,onBeforeEnter:q,onAfterEnter:U,onBeforeLeave:z},{default:B((()=>[h(F)?x((g(),M(h(pn),P({key:0,id:h(m),ref_key:"contentRef",ref:a},e.$attrs,{"aria-label":e.ariaLabel,"aria-hidden":I.value,"boundaries-padding":e.boundariesPadding,"fallback-placements":e.fallbackPlacements,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,placement:e.placement,"popper-options":e.popperOptions,strategy:e.strategy,effect:e.effect,enterable:e.enterable,pure:e.pure,"popper-class":e.popperClass,"popper-style":[e.popperStyle,h(D)],"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,visible:h(L),"z-index":e.zIndex,onMouseenter:h(W),onMouseleave:h(N),onBlur:V,onClose:h(w)}),{default:B((()=>[l(e.$slots,"default")])),_:3},16,["id","aria-label","aria-hidden","boundaries-padding","fallback-placements","gpu-acceleration","offset","placement","popper-options","strategy","effect","enterable","pure","popper-class","popper-style","reference-el","trigger-target-el","visible","z-index","onMouseenter","onMouseleave","onClose"])),[[ae,h(L)]]):j("v-if",!0)])),_:3},8,["name"])])),_:3},8,["disabled","to"]))}}),[["__file","content.vue"]]);const Pn=a({name:"ElTooltip"});const Fn=U(o(a({...Pn,props:On,emits:Rn,setup(e,{expose:t,emit:n}){const r=e;Mn();const o=se(),a=i(),p=i(),f=()=>{var e;const t=h(a);t&&(null==(e=t.popperInstanceRef)||e.update())},d=i(!1),m=i(),{show:y,hide:b,hasUpdateHandler:w}=xn({indicator:d,toggleReason:m}),{onOpen:x,onClose:O}=(({showAfter:e,hideAfter:t,autoClose:n,open:r,close:o})=>{const{registerTimeout:a}=dn(),{registerTimeout:i,cancelTimeout:s}=dn();return{onOpen:t=>{a((()=>{r(t);const e=h(n);N(e)&&e>0&&i((()=>{o(t)}),e)}),h(e))},onClose:e=>{s(),a((()=>{o(e)}),h(t))}}})({showAfter:Q(r,"showAfter"),hideAfter:Q(r,"hideAfter"),autoClose:Q(r,"autoClose"),open:y,close:b}),R=s((()=>G(r.visible)&&!w.value));u(cn,{controlled:R,id:o,open:le(d),trigger:Q(r,"trigger"),onOpen:e=>{x(e)},onClose:e=>{O(e)},onToggle:e=>{h(d)?O(e):x(e)},onShow:()=>{n("show",m.value)},onHide:()=>{n("hide",m.value)},onBeforeShow:()=>{n("before-show",m.value)},onBeforeHide:()=>{n("before-hide",m.value)},updatePopper:f}),c((()=>r.disabled),(e=>{e&&d.value&&(d.value=!1)}));return ue((()=>d.value&&b())),t({popperRef:a,contentRef:p,isFocusInsideContent:e=>{var t,n;const r=null==(n=null==(t=p.value)?void 0:t.contentRef)?void 0:n.popperContentRef,o=(null==e?void 0:e.relatedTarget)||document.activeElement;return r&&r.contains(o)},updatePopper:f,onOpen:x,onClose:O,hide:b}),(e,t)=>(g(),M(h(fn),{ref_key:"popperRef",ref:a,role:e.role},{default:B((()=>[C(Cn,{disabled:e.disabled,trigger:e.trigger,"trigger-keys":e.triggerKeys,"virtual-ref":e.virtualRef,"virtual-triggering":e.virtualTriggering},{default:B((()=>[e.$slots.default?l(e.$slots,"default",{key:0}):j("v-if",!0)])),_:3},8,["disabled","trigger","trigger-keys","virtual-ref","virtual-triggering"]),C(Bn,{ref_key:"contentRef",ref:p,"aria-label":e.ariaLabel,"boundaries-padding":e.boundariesPadding,content:e.content,disabled:e.disabled,effect:e.effect,enterable:e.enterable,"fallback-placements":e.fallbackPlacements,"hide-after":e.hideAfter,"gpu-acceleration":e.gpuAcceleration,offset:e.offset,persistent:e.persistent,"popper-class":e.popperClass,"popper-style":e.popperStyle,placement:e.placement,"popper-options":e.popperOptions,pure:e.pure,"raw-content":e.rawContent,"reference-el":e.referenceEl,"trigger-target-el":e.triggerTargetEl,"show-after":e.showAfter,strategy:e.strategy,teleported:e.teleported,transition:e.transition,"virtual-triggering":e.virtualTriggering,"z-index":e.zIndex,"append-to":e.appendTo},{default:B((()=>[l(e.$slots,"content",{},(()=>[e.rawContent?(g(),v("span",{key:0,innerHTML:e.content},null,8,["innerHTML"])):(g(),v("span",{key:1},pe(e.content),1))])),e.showArrow?(g(),M(h(Me),{key:0,"arrow-offset":e.arrowOffset},null,8,["arrow-offset"])):j("v-if",!0)])),_:3},8,["aria-label","boundaries-padding","content","disabled","effect","enterable","fallback-placements","hide-after","gpu-acceleration","offset","persistent","popper-class","popper-style","placement","popper-options","pure","raw-content","reference-el","trigger-target-el","show-after","strategy","teleported","transition","virtual-triggering","z-index","append-to"])])),_:3},8,["role"]))}}),[["__file","tooltip.vue"]])),Ln=new Map;if(I){let e;document.addEventListener("mousedown",(t=>e=t)),document.addEventListener("mouseup",(t=>{if(e){for(const n of Ln.values())for(const{documentHandler:r}of n)r(t,e);e=void 0}}))}function _n(e,t){let n=[];return J(t.arg)?n=t.arg:k(t.arg)&&n.push(t.arg),function(r,o){const a=t.instance.popperRef,i=r.target,s=null==o?void 0:o.target,l=!t||!t.instance,u=!i||!s,p=e.contains(i)||e.contains(s),f=e===i,c=n.length&&n.some((e=>null==e?void 0:e.contains(i)))||n.length&&n.includes(s),d=a&&(a.contains(i)||a.contains(s));l||u||p||f||c||d||t.value(r,o)}}const Dn={beforeMount(e,t){Ln.has(e)||Ln.set(e,[]),Ln.get(e).push({documentHandler:_n(e,t),bindingFn:t.value})},updated(e,t){Ln.has(e)||Ln.set(e,[]);const n=Ln.get(e),r=n.findIndex((e=>e.bindingFn===t.oldValue)),o={documentHandler:_n(e,t),bindingFn:t.value};r>=0?n.splice(r,1,o):n.push(o)},unmounted(e){Ln.delete(e)}};export{Dn as C,Fn as E,mn as a,Xe as b,Oe as d,gn as u};
