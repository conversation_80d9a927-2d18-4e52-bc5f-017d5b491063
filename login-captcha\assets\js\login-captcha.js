/**
 * 登录验证码JavaScript功能
 */

(function($) {
    'use strict';

    // 验证码对象
    var LoginCaptcha = {
        
        // 初始化
        init: function() {
            this.bindEvents();
            this.setupAccessibility();
            this.removeBorders();
        },

        // 绑定事件
        bindEvents: function() {
            // 刷新验证码点击事件
            $(document).on('click', '#refresh-captcha', function(e) {
                e.preventDefault();
                LoginCaptcha.refreshCaptcha();
            });

            // 验证码图片点击刷新
            $(document).on('click', '#captcha-image', function(e) {
                e.preventDefault();
                LoginCaptcha.refreshCaptcha();
            });

            // 验证码输入框事件
            $(document).on('input', '#login_captcha', function() {
                LoginCaptcha.clearError();
                LoginCaptcha.validateInput($(this));
            });

            // 表单提交前验证
            $('#loginform').on('submit', function(e) {
                if (!LoginCaptcha.validateCaptcha()) {
                    e.preventDefault();
                    return false;
                }
            });

            // 键盘快捷键支持
            $(document).on('keydown', function(e) {
                // F5 或 Ctrl+R 刷新验证码
                if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                    if ($('#login_captcha').is(':focus')) {
                        e.preventDefault();
                        LoginCaptcha.refreshCaptcha();
                    }
                }
            });
        },

        // 刷新验证码
        refreshCaptcha: function() {
            var $image = $('#captcha-image');
            var $refreshLink = $('#refresh-captcha');
            var $captchaContainer = $('.login-captcha-container');
            var $captchaField = $('.login-captcha-field');

            if ($image.length === 0) {
                return;
            }

            // 显示加载状态
            $captchaContainer.addClass('login-captcha-loading');
            $refreshLink.text('刷新中...');

            // 生成新的验证码ID
            var newCaptchaId = this.generateUUID();

            // 获取新的nonce
            var self = this;
            this.getNonce(function(nonce) {
                var newSrc = loginCaptcha.ajaxUrl + '?action=login_captcha_image&captcha_id=' + encodeURIComponent(newCaptchaId) + '&t=' + Date.now();

                // 如果有nonce，添加到URL中
                if (nonce) {
                    newSrc += '&_wpnonce=' + encodeURIComponent(nonce);
                    $('input[name="captcha_nonce"]').val(nonce);
                }

                // 预加载新图片
                var newImage = new Image();
                newImage.onload = function() {
                    // 更新图片源
                    $image.attr('src', newSrc);

                    // 更新隐藏字段
                    $('input[name="captcha_id"]').val(newCaptchaId);

                    // 清除输入框
                    $('#login_captcha').val('').focus();

                    // 移除加载状态
                    $captchaContainer.removeClass('login-captcha-loading');
                    $refreshLink.text(loginCaptcha.refreshText);

                    // 清除错误状态
                    LoginCaptcha.clearError();

                    // 触发刷新完成事件
                    $(document).trigger('loginCaptchaRefreshed');
                };

                newImage.onerror = function() {
                    // 加载失败处理
                    $captchaContainer.removeClass('login-captcha-loading');
                    $refreshLink.text(loginCaptcha.refreshText);

                    // 检查是否是频繁请求导致的失败
                    LoginCaptcha.handleImageError(newSrc);
                };

                newImage.src = newSrc;
            });
        },

        // 获取新的nonce
        getNonce: function(callback) {
            // 首先尝试使用现有的nonce
            var existingNonce = $('input[name="captcha_nonce"]').val();
            if (existingNonce) {
                callback(existingNonce);
                return;
            }

            // 如果没有现有nonce，通过AJAX获取新的
            $.ajax({
                url: loginCaptcha.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'login_captcha_nonce'
                },
                success: function(response) {
                    if (response.success && response.data.nonce) {
                        callback(response.data.nonce);
                    } else {
                        callback('');
                    }
                },
                error: function(xhr) {
                    // 如果是429错误（速率限制），显示友好提示
                    if (xhr.status === 429) {
                        LoginCaptcha.showError('频繁请求，请稍后重试');
                    }
                    callback('');
                }
            });
        },

        // 处理图片加载错误
        handleImageError: function(imageUrl) {
            // 通过AJAX检查具体的错误状态
            $.ajax({
                url: imageUrl,
                type: 'HEAD', // 只获取头信息
                success: function() {
                    // 如果HEAD请求成功，说明不是速率限制问题
                    LoginCaptcha.showError('验证码加载失败，请重试');
                },
                error: function(xhr) {
                    if (xhr.status === 429) {
                        // 429错误表示速率限制
                        LoginCaptcha.showError('频繁请求，请稍后重试');
                    } else if (xhr.status === 403) {
                        // 403错误表示权限问题
                        LoginCaptcha.showError('验证码已过期，请刷新页面');
                    } else {
                        // 其他错误
                        LoginCaptcha.showError('频繁请求，请稍后重试');
                    }
                }
            });
        },

        // 生成UUID
        generateUUID: function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0;
                var v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },

        // 验证输入
        validateInput: function($input) {
            var value = $input.val();
            var $field = $('.login-captcha-field');

            // 移除之前的状态
            $field.removeClass('error success');

            // 基本验证
            if (value.length === 0) {
                return;
            }

            // 检查字符是否有效
            var validChars = /^[A-Za-z0-9]+$/;
            if (!validChars.test(value)) {
                this.showError('请输入有效的验证码字符');
                $field.addClass('error');
                return false;
            }

            // 长度检查（假设验证码长度为4）
            if (value.length >= 4) {
                $field.addClass('success');
            }

            return true;
        },

        // 验证验证码
        validateCaptcha: function() {
            var $captchaInput = $('#login_captcha');
            var $captchaId = $('input[name="captcha_id"]');

            if ($captchaInput.length === 0) {
                return true; // 如果没有验证码字段，跳过验证
            }

            var captchaValue = $captchaInput.val().trim();
            var captchaId = $captchaId.val();

            // 检查是否为空
            if (captchaValue === '') {
                this.showError('请输入验证码');
                $captchaInput.focus();
                return false;
            }

            // 检查验证码ID
            if (captchaId === '') {
                this.showError('验证码已过期，请刷新');
                this.refreshCaptcha();
                return false;
            }

            return true;
        },

        // 显示错误
        showError: function(message) {
            var $field = $('.login-captcha-field');
            var $existingError = $field.find('.login-captcha-error');

            // 移除现有错误消息
            $existingError.remove();

            // 添加新错误消息
            var $error = $('<span class="login-captcha-error">' + message + '</span>');
            $field.append($error).addClass('error');

            // 自动隐藏错误消息
            setTimeout(function() {
                $error.fadeOut(function() {
                    $error.remove();
                });
            }, 5000);
        },

        // 清除错误
        clearError: function() {
            var $field = $('.login-captcha-field');
            $field.removeClass('error').find('.login-captcha-error').remove();
        },

        // 设置无障碍功能
        setupAccessibility: function() {
            var $captchaImage = $('#captcha-image');
            var $captchaInput = $('#login_captcha');
            var $refreshLink = $('#refresh-captcha');

            // 为图片添加无障碍属性
            $captchaImage.attr({
                'role': 'img',
                'aria-label': '验证码图片，点击可刷新',
                'tabindex': '0'
            });

            // 为输入框添加无障碍属性
            $captchaInput.attr({
                'aria-label': '请输入验证码',
                'aria-describedby': 'captcha-help',
                'autocomplete': 'off',
                'spellcheck': 'false'
            });

            // 添加帮助文本
            if ($('#captcha-help').length === 0) {
                var $help = $('<div id="captcha-help" class="login-captcha-help screen-reader-text">请输入图片中显示的验证码，不区分大小写。按F5可刷新验证码。</div>');
                $captchaInput.after($help);
            }

            // 为刷新链接添加无障碍属性
            $refreshLink.attr({
                'role': 'button',
                'aria-label': '刷新验证码图片',
                'tabindex': '0'
            });

            // 键盘导航支持
            $captchaImage.on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    LoginCaptcha.refreshCaptcha();
                }
            });

            $refreshLink.on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    LoginCaptcha.refreshCaptcha();
                }
            });
        },

        // 自动刷新验证码（可选功能）
        autoRefresh: function(interval) {
            if (interval && interval > 0) {
                setInterval(function() {
                    LoginCaptcha.refreshCaptcha();
                }, interval * 1000);
            }
        },

        // 获取验证码统计信息
        getStats: function() {
            return {
                refreshCount: this.refreshCount || 0,
                errorCount: this.errorCount || 0,
                lastRefresh: this.lastRefresh || null
            };
        },

        // 移除绿色边框和占位空间
        removeBorders: function() {
            var $captchaField = $('.login-captcha-field');
            var $captchaLabel = $('.login-captcha-field label');

            // 移除所有可能的边框样式和占位空间
            $captchaField.css({
                'border': 'none',
                'border-left': 'none',
                'border-left-color': 'transparent',
                'border-left-width': '0',
                'border-left-style': 'none',
                'box-shadow': 'none',
                'outline': 'none',
                'background-image': 'none',
                'padding': '0',
                'padding-left': '0',
                'padding-right': '0',
                'margin-left': '0',
                'margin-right': '0',
                'text-indent': '0'
            });

            $captchaLabel.css({
                'border': 'none',
                'border-left': 'none',
                'border-left-color': 'transparent',
                'border-left-width': '0',
                'border-left-style': 'none',
                'box-shadow': 'none',
                'outline': 'none',
                'background-image': 'none',
                'padding': '0',
                'padding-left': '0',
                'padding-right': '0',
                'margin-left': '0',
                'margin-right': '0',
                'text-indent': '0'
            });

            // 移除伪元素（通过添加CSS类）
            $captchaField.addClass('no-border-override');
            $captchaLabel.addClass('no-border-override');

            // 定期检查并移除可能被重新添加的样式
            setInterval(function() {
                $captchaField.css({
                    'border-left': 'none',
                    'padding-left': '0',
                    'margin-left': '0',
                    'text-indent': '0'
                });
                $captchaLabel.css({
                    'border-left': 'none',
                    'padding-left': '0',
                    'margin-left': '0',
                    'text-indent': '0'
                });
            }, 1000);
        }
    };

    // 页面加载完成后初始化
    $(document).ready(function() {
        LoginCaptcha.init();
        
        // 触发初始化完成事件
        $(document).trigger('loginCaptchaReady');
    });

    // 将对象暴露到全局作用域（用于调试和扩展）
    window.LoginCaptcha = LoginCaptcha;

})(jQuery);
