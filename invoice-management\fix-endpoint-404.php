<?php
/**
 * 修复发票管理插件端点404错误
 * 访问: /wp-content/plugins/invoice-management/fix-endpoint-404.php
 */

// 加载WordPress
require_once('../../../wp-config.php');

// 检查用户权限
if (!current_user_can('manage_options')) {
    wp_die('权限不足');
}

echo '<h1>🔧 修复发票管理端点404错误</h1>';

// 1. 检查当前重写规则
echo '<h2>1. 检查当前重写规则</h2>';
global $wp_rewrite;
$rules = get_option('rewrite_rules');

if (isset($rules['my-account/invoices/?$'])) {
    echo '<p style="color: green;">✅ invoices端点重写规则已存在</p>';
} else {
    echo '<p style="color: red;">❌ invoices端点重写规则不存在</p>';
}

// 显示相关的重写规则
echo '<h3>相关重写规则:</h3>';
echo '<ul>';
foreach ($rules as $pattern => $replacement) {
    if (strpos($pattern, 'my-account') !== false || strpos($pattern, 'invoices') !== false) {
        echo '<li><strong>' . esc_html($pattern) . '</strong> => ' . esc_html($replacement) . '</li>';
    }
}
echo '</ul>';

// 2. 检查WooCommerce端点
echo '<h2>2. 检查WooCommerce端点</h2>';
$wc_endpoints = WC()->query->get_query_vars();
if (isset($wc_endpoints['invoices'])) {
    echo '<p style="color: green;">✅ invoices端点已在WooCommerce中注册</p>';
    echo '<p>端点值: ' . esc_html($wc_endpoints['invoices']) . '</p>';
} else {
    echo '<p style="color: red;">❌ invoices端点未在WooCommerce中注册</p>';
}

// 3. 手动注册端点
echo '<h2>3. 手动注册端点</h2>';

// 添加端点
add_rewrite_endpoint('invoices', EP_ROOT | EP_PAGES);

// 添加到WooCommerce查询变量
add_filter('woocommerce_get_query_vars', function($vars) {
    $vars['invoices'] = 'invoices';
    return $vars;
});

echo '<p style="color: green;">✅ 端点已手动注册</p>';

// 4. 刷新重写规则
echo '<h2>4. 刷新重写规则</h2>';

// 强制刷新重写规则
flush_rewrite_rules(true);

echo '<p style="color: green;">✅ 重写规则已刷新</p>';

// 5. 验证修复结果
echo '<h2>5. 验证修复结果</h2>';

// 重新获取重写规则
$rules = get_option('rewrite_rules');
if (isset($rules['my-account/invoices/?$'])) {
    echo '<p style="color: green;">✅ invoices端点重写规则现在已存在</p>';
} else {
    echo '<p style="color: red;">❌ invoices端点重写规则仍然不存在</p>';
}

// 6. 测试端点URL
echo '<h2>6. 测试端点URL</h2>';

$my_account_url = wc_get_page_permalink('myaccount');
$invoices_url = wc_get_account_endpoint_url('invoices');

echo '<p><strong>我的账户页面:</strong> <a href="' . esc_url($my_account_url) . '" target="_blank">' . esc_html($my_account_url) . '</a></p>';
echo '<p><strong>发票页面:</strong> <a href="' . esc_url($invoices_url) . '" target="_blank">' . esc_html($invoices_url) . '</a></p>';

// 7. 检查插件钩子
echo '<h2>7. 检查插件钩子</h2>';

// 确保插件类已加载
if (!class_exists('Invoice_MyAccount')) {
    require_once('includes/class-invoice-myaccount.php');
}

// 检查关键钩子
$hooks_to_check = array(
    'woocommerce_account_menu_items' => 'add_menu_item',
    'woocommerce_get_query_vars' => 'add_query_vars',
    'woocommerce_account_invoices_endpoint' => 'invoices_content',
    'init' => 'add_endpoints'
);

foreach ($hooks_to_check as $hook => $method) {
    if (has_filter($hook)) {
        echo '<p style="color: green;">✅ ' . $hook . ' 钩子已注册</p>';
    } else {
        echo '<p style="color: red;">❌ ' . $hook . ' 钩子未注册</p>';
        
        // 尝试手动注册
        if (class_exists('Invoice_MyAccount')) {
            $myaccount = new Invoice_MyAccount();
            if (method_exists($myaccount, $method)) {
                if ($hook === 'init') {
                    add_action($hook, array($myaccount, $method));
                } else {
                    add_filter($hook, array($myaccount, $method), 10, 2);
                }
                echo '<p style="color: blue;">🔧 已手动注册 ' . $hook . ' 钩子</p>';
            }
        }
    }
}

// 8. 强制重新初始化插件
echo '<h2>8. 重新初始化插件</h2>';

if (function_exists('invoice_management')) {
    // 重新初始化插件
    $plugin = invoice_management();
    if (method_exists($plugin, 'init')) {
        remove_action('plugins_loaded', array($plugin, 'init'));
        $plugin->init();
        echo '<p style="color: green;">✅ 插件已重新初始化</p>';
    }
} else {
    echo '<p style="color: red;">❌ 插件函数不存在</p>';
}

// 9. 清除相关缓存
echo '<h2>9. 清除缓存</h2>';

// 清除WordPress缓存
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo '<p style="color: green;">✅ WordPress缓存已清除</p>';
}

// 清除WooCommerce缓存
if (function_exists('wc_delete_shop_order_transients')) {
    wc_delete_shop_order_transients();
    echo '<p style="color: green;">✅ WooCommerce缓存已清除</p>';
}

// 清除重写规则缓存
delete_option('rewrite_rules');
echo '<p style="color: green;">✅ 重写规则缓存已清除</p>';

// 10. 最终验证
echo '<h2>10. 最终验证</h2>';

// 再次刷新重写规则
flush_rewrite_rules(true);

// 检查最终状态
$rules = get_option('rewrite_rules');
if (isset($rules['my-account/invoices/?$'])) {
    echo '<p style="color: green;">✅ 修复成功！invoices端点现在应该可以正常工作</p>';
} else {
    echo '<p style="color: red;">❌ 修复失败，可能需要手动操作</p>';
}

echo '<h2>✅ 修复完成</h2>';
echo '<p><strong>请现在测试以下链接：</strong></p>';
echo '<ul>';
echo '<li><a href="' . esc_url($invoices_url) . '" target="_blank">发票页面</a></li>';
echo '<li><a href="' . esc_url($my_account_url) . '" target="_blank">我的账户页面</a></li>';
echo '</ul>';

echo '<h3>如果问题仍然存在：</h3>';
echo '<ol>';
echo '<li>停用并重新激活发票管理插件</li>';
echo '<li>在WordPress后台 设置 > 固定链接 中点击"保存更改"</li>';
echo '<li>检查.htaccess文件是否可写</li>';
echo '<li>联系系统管理员检查服务器配置</li>';
echo '</ol>';

echo '<p><a href="test-invoice.php">运行完整插件测试</a></p>';
?>
