import{l as S,q as d,m as u,e as b,f as T}from"./vendor.js";const B=()=>{const t=S().appContext.config.globalProperties,{$api:s,$cnf:o}=t;return{$api:s,$cnf:o,globalProperties:t}};u.defaults.withCredentials=!0;u.defaults.baseURL=void 0;const p=window.wb_base?window.wb_base:window.wbm_js_cnf?window.wbm_js_cnf:{ajax_url:"/wp-admin/admin-ajax.php",home_url:"/"},w=p.ajax_url,P=p.home_url,y={fetchData:(t={})=>_("GET",w+"?"+d.stringify(t)),getData:(t={})=>_("POST",w,d.stringify(t)),saveData:(t={})=>_("POST",w,d.stringify(t)),restGet:t=>_("GET",P+t),getJson:t=>_("GET",t)};function _(t,s,o=null,r=!1,a=null){if(t=="GET")return new Promise((e,c)=>{u.get(s).then(n=>{e(r?n:n.data)}).catch(n=>{c(n)})});if(t=="POST")return new Promise((e,c)=>{u.post(s,o,a).then(n=>{console.log(n,"POST - then - "+s),e(r?n:n.data)}).catch(n=>{c(n)})});if(t=="PUT")return new Promise((e,c)=>{u.put(s,o,a).then(n=>{e(r?n:n.data)}).catch(n=>{c(n)})});if(t=="DELETE")return new Promise((e,c)=>{u.delete(s,a).then(n=>{e(r?n:n.data)}).catch(n=>{c(n)})})}const I=async t=>{const s=(l,i=!1)=>{const f=sessionStorage.getItem(l);if(f)try{return i?JSON.parse(f):JSON.parse(f).data}catch{sessionStorage.removeItem(l)}else return!1},o=(l,i)=>{const f={ver:new Date().getTime(),data:i},m=JSON.stringify(f);sessionStorage.setItem(l,m)},{pd_code:r,assets_ver:a,locale:e}=t,c=`${a}_${e}`,n=r?"WB_I18N_DATA_"+r:"WBM_I18N",g=r?"WB_I18N_REV_"+r:"WBM_I18N_REV";if(s(g)==c)return s(n);{const l=typeof t.security<"u"?t.security:"",i=await y.getData({action:"wbp_api",op:"get_localize",_ajax_nonce:l});return i&&!i.code?(o(g,`${a}_${e}`),o(n,i.data),i.data):!1}},N=t=>{const s=new TextEncoder().encode(t);let o=[];for(let a=0;a<256;a++){let e=a;for(let c=0;c<8;c++)e=e&1?3988292384^e>>>1:e>>>1;o[a]=e}let r=-1;for(let a=0;a<s.length;a++)r=r>>>8^o[(r^s[a])&255];return(r^-1)>>>0},h=t=>N(t).toString(16).padStart(8,"0"),E=t=>{const s=b({}),o=async()=>{s.value=await I(t)},r=e=>{const c=h(e);return s.value[c]||`{${e}}`},a=(e,...c)=>{const n=h(e);return(s.value[n]||"").replace(/%s/g,()=>c.shift())};return T(o),{wb_i18n:s,wb_e:r,wb_sprintf:a}};export{E as a,B as u};
