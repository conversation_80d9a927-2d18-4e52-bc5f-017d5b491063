=== Simple Cloudflare Turnstile - CAPTCHA Alternative ===
Contributors: Elliot<PERSON>, RelyWP
Tags: cloudflare,turnstile,captcha,protect,spam
Donate link: https://www.paypal.com/donate/?hosted_button_id=RX28BBH7L5XDS
Requires at least: 4.7
Tested up to: 6.8
Stable Tag: 1.31.0
License: GPLv3 or later.
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Add Cloudflare Turnstile to WordPress, WooCommerce, Contact Forms & more. The user-friendly, privacy-preserving reCAPTCHA alternative. 100% free!

== Description ==

Easily add Cloudflare Turnstile to all your WordPress website forms to protect them from spam!

A user-friendly, privacy-preserving reCAPTCHA alternative.

## Supported Forms ##

You can currently enable Turnstile on the following forms:

**WordPress**

* Login Form
* Registration Form
* Password Reset Form
* Comments Form

**WooCommerce**

* Checkout
* Pay For Order
* Login Form
* Registration Form
* Password Reset Form

**Form Plugins**

* WPForms
* Fluent Forms
* Contact Form 7
* Gravity Forms
* Formidable Forms
* Forminator Forms
* Jetpack Forms
* Kadence Forms

**Other Integrations**

* Elementor Pro Forms
* Easy Digital Downloads Forms
* Paid Memberships Pro Forms
* Mailchimp for WordPress Forms
* MailPoet Forms
* BuddyPress Registration Form
* bbPress Create Topic & Reply Forms
* MemberPress Forms
* Ultimate Member Forms
* WP-Members Forms
* WP User Frontend Forms
* wpDiscuz Comments Form
* CheckoutWC & Flux Checkout

This plugin is also compatible with WordPress Multisite, and most two-factor authentication (2FA) plugins.

## Other Features ##

The plugin includes several other features and options:

* Theme: Select the default theme for Turnstile.
* Language: Set the default language for Turnstile.
* Appearance Mode: Choose if Turnstile is always displayed, or only when an interaction is required.
* Disable Submit Button: Disable the submit button on forms until the Turnstile challenge is completed.
* Custom Error Message: Set your own custom error message for failed submissions.
* Whitelist: Prevent Turnstile from showing for logged in users, or certain IP addresses (wildcards are not supported).

## Getting Started ##

It's super quick and easy to get started with Cloudflare Turnstile!

1. Simply generate a "site key" and "secret key" in your Cloudflare account, and add these in the plugin settings page.
2. Select which forms Turnstile should be added to and click save.
3. Finally, click the "TEST API RESPONSE" button to make sure the Turnstile API response is working OK.
4. A new Cloudflare Turnstile challenge will then be displayed on your selected forms to protect them from spam!

For more detailed instructions, please see our <a href="https://relywp.com/blog/how-to-add-cloudflare-turnstile-to-wordpress/?utm_source=plugin" target="_blank">setup guide</a>.

## What is Cloudflare Turnstile? ##

Cloudflare Turnstile delivers frustration-free, CAPTCHA-free web experiences to website visitors.

Turnstile stops abuse and confirms visitors are real without the data privacy concerns or awful UX that CAPTCHA thrusts on users.

Learn more here: <a href="https://www.cloudflare.com/en-gb/products/turnstile/" target="_blank">https://www.cloudflare.com/en-gb/products/turnstile/</a>

## Is it free to use? ##

Yes, this plugin is completely free with no paid version, and does not include any additional data tracking.

Cloudflare Turnstile is also a completely free service.

## Plugin Languages ##

Currently available in <a href="https://translate.wordpress.org/projects/wp-plugins/simple-cloudflare-turnstile/" target="_blank">12 languages</a>. Thank you to all the <a href="https://translate.wordpress.org/projects/wp-plugins/simple-cloudflare-turnstile/contributors/" target="_blank">contributers</a>! If you would like to help contribute translations, please <a href="https://translate.wordpress.org/projects/wp-plugins/simple-cloudflare-turnstile/" target="_blank">click here</a>.

## Other Information ##

* For help & suggestions, please <a href="https://wordpress.org/support/plugin/simple-cloudflare-turnstile/#new-topic-0" target="_blank">create a support topic</a>.
* Follow the developer <a href="https://twitter.com/ElliotSowersby" target="_blank">@ElliotSowersby</a> on Twitter.
* <a href="https://github.com/elliotvs/simple-cloudflare-turnstile" target="_blank">View on GitHub</a>

== Installation ==

1. Upload 'simple-cloudflare-turnstile' to the '/wp-content/plugins/' directory.
2. Activate the plugin through the 'Plugins' menu in WordPress.
3. Visit the plugin settings in WordPress admin menu at "Settings > Cloudflare Turnstile".
4. You will need to generate a "Site Key" and "Site Secret" in your Cloudflare account, then enter these in the settings page.
5. Select which forms you want to enable Turnstile on, then click the "Save Changes" button.
6. Finally, you will be required to complete a quick test of the widget by clicking "TEST API RESPONSE", to confirm it's working correctly.
7. A new Cloudflare Turnstile challenge will then be displayed on your selected forms to protect them from spam!

For more detailed instructions, please see our <a href="https://relywp.com/blog/how-to-add-cloudflare-turnstile-to-wordpress/?utm_source=plugin" target="_blank">setup guide</a>.

https://www.youtube.com/watch?v=Yn8X_GsTFnU

== Screenshots ==

1. Example Turnstile on the WP Login Page
2. Example Turnstile on the WP Register Page
3. Example Turnstile on the WP Comments Form
4. Example Turnstile on the WooCommerce My Account Page
5. Example Turnstile on the WooCommerce Checkout Page
6. Example Turnstile on a Contact Form
7. Example Settings Page

== Frequently Asked Questions ==

= Do you have a setup guide? =

Yes, you can view a setup guide for the plugin <a href="https://relywp.com/blog/how-to-add-cloudflare-turnstile-to-wordpress/?utm_source=plugin" target="_blank">here</a>.

= Will there be more integrations be added in the future? =

More integrations may be added in the future, based on user feedback. So if you have any suggestions please feel free to submit a <a href="https://wordpress.org/support/plugin/simple-cloudflare-turnstile/#new-topic-0">support topic</a>.

= I need help, can I get support? =

Please bare in mind, the <a href="https://simpleturnstile.com/">Simple Cloudflare Turnstile</a> plugin is 100% free, developed as a way to give back to the WordPress community.

You can post a support thread on the WordPress.org forums to get help from the community, and we will also keep an eye on this quite frequently, providing support and answers where possible. However, we can not guarantee an answer every single support ticket.

<a href="https://wordpress.org/support/plugin/simple-cloudflare-turnstile/#new-topic-0">Click here to create a support topic.</a>

The support forums are the only place you should submit a support ticket for 100% free support from us and the WordPress community.

= Is the plugin free? =

Yes, this plugin is completely free with no paid version, and does not include any additional data tracking.

Cloudflare Turnstile is also a completely free service.

= Is this better for Data Privacy & GDPR? =

Cloudflare mentions that they "never harvest data for ad retargeting" and Turnstile "never looks for cookies (like a login cookie), or uses cookies to collect or store information of any kind".

Check out <a href="https://blog.cloudflare.com/turnstile-private-captcha-alternative/#ux-isn-t-the-only-big-problem-with-captcha-so-is-privacy">this article</a> which explains more about how Turnstile has less data collection, more privacy, and same security.

View the Cloudflare <a href="https://www.cloudflare.com/en-gb/gdpr/introduction/">GDPR compliance</a> and <a href="https://www.cloudflare.com/en-gb/cloudflare-customer-dpa/">Data Processing Addendum</a> for more information how on they handle data.

= I see a 401 console error, is this a problem? =

You can safely ignore the error. It is requesting a “Private Access Token (PAT) Open external link” that your device or browser does not support yet. <a href="https://developers.cloudflare.com/turnstile/frequently-asked-questions/#i-am-seeing-a-401-error-in-your-console-during-a-turnstile-security-check-is-this-a-problem">Click here</a> for more information.

= The Turnstile Challenge is not appearing, what should I do? =

Firstly, make sure you have completed the <a href="https://relywp.com/blog/how-to-add-cloudflare-turnstile-to-wordpress/?utm_source=plugin" target="_blank">setup guide</a> correctly, and that you have completed the "TEST API RESPONSE" on the settings page.

If you are still having issues, please post a <a href="https://wordpress.org/support/plugin/simple-cloudflare-turnstile/#new-topic-0">support topic</a> on the WordPress.org forums.

= How can I report security bugs? =

You can report security bugs through the Patchstack Vulnerability Disclosure Program. The Patchstack team help validate, triage and handle any security vulnerabilities. [Report a security vulnerability.](https://patchstack.com/database/vdp/simple-cloudflare-turnstile)

== Changelog ===

= Version 1.31.0 - 27th May 2025 =
- New: Added support for the wp_login_form() function, so Turnstile can be added to the login form generated by this function.
- Tweak: Made a few design tweaks to the admin settings page.
- Fix: Change from gform_pre_submission to gform_validation hook for Gravity Forms integration. (<a href="https://github.com/ElliotSowersby/simple-cloudflare-turnstile/pull/40">#40</a>).
- Fix: Fixed "wp is not defined" error on WooCommerce checkout in some cases.

= Version 1.30.0 - 12th February 2025 =
- New: Added Kadence Forms integration.
- New: Added MailPoet integration.
- Fix: Fixed "Cannot read properties of null (reading 'dataset')" error with WooCommerce block checkout.
- Fix: Fixed an issue with Turnstile loading twice in some cases.
- Fix: Fixed an issue with the "Disable Submit Button" option not working.
- Fix: Gravity Forms integration rerender on validation. (<a href="https://github.com/ElliotSowersby/simple-cloudflare-turnstile/pull/38">#38</a>).
- Tested with WordPress 6.7.2
- Tested with WooCommerce 9.6.1

= Version 1.29.0 - 14th January 2025 =
- New: Added Jetpack Forms integration. (<a href="https://github.com/ElliotSowersby/simple-cloudflare-turnstile/pull/30">#30</a>).

= Version 1.28.1 - 13th January 2025 =
- Fix: Fixed a undefined array key "error_code" error showing in some cases.
- Fix: Fix CheckoutWC implementation. (<a href="https://github.com/ElliotSowersby/simple-cloudflare-turnstile/pull/33">#33</a>).
- Fix: Fix warning when cf-turnstile-response not present in Fluent Forms. (<a href="https://github.com/ElliotSowersby/simple-cloudflare-turnstile/pull/32">#32</a>).

= Version 1.28.0 - 17th December 2024 =
- New: Added Checkout block and Store API support. (Thanks [@senadir](https://github.com/senadir)) 
- Fix: Fixed an issue with Turnstile re-rendering when it does not need to in some cases, on WooCommerce checkout.
- Other: Tested with WooCommerce 9.4.3

= Version 1.27.1 - 10th November 2024 =
- New: Added a "Widget Size" option to the advanced settings section.
- Tweak: Moved the "Appearance Mode" option to the advanced settings section.
- Tweak: Added support for Elementor PRO Elements.
- Tweak: Added the new supported languages.
- Fix: Fixed a "WPCF7_TagGenerator::add()" error with the Contact Form 7 integration.
- Fix: Prevent Turnstile from showing on the order received page in some cases, such as with the CheckoutWC plugin.
- Other: Tested with WordPress 6.7.0
- Other: Tested with WooCommerce 9.3.3

= Version 1.27.0 - 7th August 2024 =
- New: Added a new option in the admin settings page to "enable debug logging of Turnstile form submission events". This will add a log event every time a form is submitted with Turnstile enabled, and display the log at the bottom of the settings page.
- Fix: Fixed issue with Turnstile not rendering on Elementor popup forms.
- Fix: Fixed issue with Turnstile not resetting properly on Contact Form 7 causing it to display twice on submission error.
- Fix: Fixed issue with Turnstile showing twice in some cases.

= Version 1.26.6 - 27th July 2024 =
- Fix: Fixed issue with Turnstile showing twice on Forminator forms in some cases.

= Version 1.26.5 - 26th July 2024 =
- Fix: Fixed issue with Turnstile showing twice on Elementor forms.

= Version 1.26.4 - 26th July 2024 =
- Fix: Tweak to help prevent some new rare cases where the Turnstile widget was being shown twice in some cases.
- Fix: Fixed an issue with the WooCommerce password reset form when using a hide admin login URL plugin like "WPS Hide Login".
- Other: Tested with WordPress 6.6.1

= Version 1.26.3 - 18th July 2024 =
- Tweak: Added a message to state that it is not currently compatible with WooCommerce "block-based" checkout.
- Fix: Fixed an issue with not being able to save the WP Forms integration settings if using the Pro version of WP Forms.
- Other: Tested with WordPress 6.6.0
- Other: Tested with WooCommerce 9.1.2

= Version 1.26.2 - 8th May 2024 =
- Fix: Fixed an issue with the new "ONLY enable for these Membership IDs" option with the MemberPress integration.

= Version 1.26.0 - 8th May 2024 =
- New: Added option to whitelist certain "User Agents" in the "Whitelist Settings" section to the settings page.
- Tweak: Made a few small changes to the admin settings page.
- Dev: Added a filter ("cfturnstile_widget_disable") to allow skipping Turnstile check and hiding the Turnstile widget programmatically.
- Code Fork: auutstudio/simple-cloudflare-turnstile-sometimes
-- New: Added an option to only show Turnstile on specific MemberPress registration forms, based on a declared list of Membership Product IDs.
-- Tweak: Clarify that the Whitelist IP option does not support wildcards.
-- Tweak: Clarify that WPForms has a redundant place in its own settings to configure Turnstile.

= Version 1.25.0 - 7th November 2023 =
- New: Added compatibility with the "Clean Login" plugin.
- New: Added an option to only show Turnstile on the default WordPress login and registration form, and not on any other similar forms added by plugins. This is enabled by default for new installs.
- New: Added a new "Extra Failure Message" option, in advanced settings, which will show a custom notice to the user, under the widget, if the Turnstile challenge fails.
- New: Added a new "Defer Scripts" option, in advanced settings, to decide whether the javascript files will be deferred or not. This is enabled by default.
- Tweak: Modified the code for WooCommerce login check.
- Tweak: Added some information about other form plugins that are not currently supported by our plugin, if they are installed.
- Tweak: Removed 100ms delay on Turnstile widget render.
- Tweak: Added an "Advanced Settings" tab to the settings page, and moved some settings into there.
- Tweak: A few small changes to the admin settings page.
- Tweak: The "Language" setting is now ordered alphabetically.
- Tweak: Improvements to the Blocksy integration.
- Fix: Fixed a compatibility issue with the EDD Auto Register plugin.
- Dev: Moved around some of the plugin code into different files.
- Dev: Added some filters for the WP Login and WP Register check functions.
- Other: Tested with WordPress 6.4.0

= Version 1.24.0 - 9th October 2023 =
- New: Added integration with the "Paid Memberships Pro" plugin.

= Version 1.23.3 - 4th October 2023 =
- Tweak: Added some additional sanitization and escaping to the plugin code, as an extra security precaution, and to meet WordPress coding standards better.
- Tweak: Added a class to the line break displayed below the Turnstile widget, so it can be hidden or edited with CSS if needed.
- Fix: Fixed issue with Turnstile not loading on WooCommerce checkout login form in some cases.

= Version 1.23.2 - 22nd September 2023 =
- Fix: Fixed a security vulnerability with the Gravity Forms integration.
- Tweak: Minified the inline scripts on Contact Form 7 and Gravity Forms integrations.
- Tweak: Small tweak to styles on wp-login.php page in some cases.
- Other: Tested with WordPress 6.3.1
- Other: Tested with WooCommerce 8.1.1

= Version 1.23.1 - 24th August 2023 =
- Fix: Fixed an issue with "Payment Methods to Skip" toggle on settings page.

= Version 1.23.0 - 24th August 2023 =
- New: Added "Whitelist Settings" section to the settings page, with options to whitelist logged in users, or certain IP addresses.
- New: Added support/integration for the CheckoutWC plugin.
- Tweak: Turnstile will now be disabled on all forms until the "Test Response" is successfully completed on the settings page. An admin error message will now show if the keys are set but the test is not complete.
- Tweak: If Turnstile returns error code "invalid-input-secret" at any point, it will automatically disable itself and requires a "Test Response" to be completed again in the settings page.
- Tweak: Modified the "Payment Methods to Skip" option (for WooCommerce) information to be easier to understand, and now displayed as checkboxes instead of a multi-select field.
- Tweak: Added a session to WooCommerce checkout, to ensure the Turnstile check is only run once during checkout.
- Tweak: The scripts loaded by the plugin are now automatically defered, to help improve performance.
- Fix: Fixed undefined array key "mode" error with Ultimate Member integration.
- Fix: Fixed issue with Turnstile not working on Contact Form 7 in some cases.
- Other: Tested with WordPress 6.3.0
- Other: Tested with WooCommerce 8.0.2

= Version 1.22.1 - 8th July 2023 =
- Tweak: Changed the hook used for validation on Turnstile on the comments form, to an earlier hook, to prevent potential conflicts with other plugins.
- Fix: Fixed an issue with the "Disable Submit Button" option on Forminator forms, when "Load for using AJAX" was enabled.
- Fix: Fixed an error with WooCommerce reset password form since last update.

= Version 1.22.0 - 6th July 2023 =
- Tweak: Updated Turnstile to always load explicitly (instead of implicitly) in all instances. This helps prevent certain potential conflict issues, for example with performance plugins.
- Fix: Fixed an issue with Forminator forms, where the Turnstile challenge stopped working if there was an error on the form after submitting once.
- Fix: Fixed a PHP error with WP Forms, since a newer version of the WP Forms plugin.

= Version 1.21.3 - 28th June 2023 =
- Fix: Fixed the "cfturnstile_uninstall_remove" option no longer saving since last update, and a PHP error when it's enabled and deleting the plugin.

= Version 1.21.2 - 28th June 2023 =
- Improvement: Small optimisation so that plugin options are no longer saved in the database for disabled integrations. Existing installs will need to re-save the plugin settings for this to apply.
- Tweak: Added compatibility fix for Cloudflare "Rocket Loader" feature, which was causing the Turnstile challenge to appear in some cases. May remove this in the future, if Cloudflare fix the issue on their end.
- Tweak: Remove "cfturnstile_tested" option on plugin uninstall.
- Tweak: Removed the "-2px" left margin on Turnstile widget, since it is no longer needed.
- Fix: Fixed an issue with WooCommerce login form.

= Version 1.21.1 - 23rd June 2023 =
- Tweak: The jQuery for AJAX comments forms support will now only load if needed, or the new option "AJAX comments form?" is enabled.
- Fix: Fixed issue with BBPress new topic button staying disabled with the "Disable Submit Button" option enabled.

= Version 1.21.0 - 21st June 2023 =
- New: Added integration with the "WP User Frontend" plugin.

= Version 1.20.4 - 6th June 2023 =
- Tweak: Admin scripts and styles will now only load on the plugin settings page.
- Fix: Fixed issue with Gravity Forms multi-page forms not being able to submit with Turnstile enabled.
- Fix: Fixed issue with Turnstile not working properly on Contact Form 7 forms when using the shortcode method (since 1.18.6).

= Version 1.20.3 - 1st June 2023 =
- Tweak: Small improvements and fixes for MemberPress integration.

= Version 1.20.2 - 28th May 2023 =
- Tweak: Made a few small syle changes to the settings page. Added setup guide link.
- Tweak: Changed structure of integration files, to be more organised.
- Tweak: Changed mt_rand() to wp_rand() in the plugin code.

= Version 1.20.1 - 27th May 2023 =
- Fix: Added extra sanitization to the new "Appearance Mode" option output.
- Fix: Fixed compatibility issue with "LiteSpeed Cache" plugin.

= Version 1.20.0 - 27th May 2023 =
- New: Added new "Appearance Mode" option. "Always" is the default option, however if "Interaction Only" is enabled, the Turnstile Widget is only displayed in cases where an interaction is required. This essentially makes it "invisible" for most valid users.
- Fix: Fixed compatibility issue with "SiteGround Optimizer" plugin.

= Version 1.19.0 - 26th May 2023 =
- New: Added integration with the "MemberPress" plugin.
- Tweak: Added skip for XMLRPC and REST API on WooCommerce login and register check.

= Version 1.18.6 - 26th May 2023 =
- Tweak: Added skip for REST API on WordPress login check.
- Tweak: Improved position and alignment of Turnstile on wpDiscuz comments form.
- Tweak: Removed ?onload=onloadTurnstileCallback from Turnstile script URL, which fixes a console error.
- Tweak: Reduced "setTimeout" delay in cfturnstile_force_render() function slightly to prevent console error.
- Tweak: Tweak to gravity forms code to prevent duplicate footer class.
- Fix: Fixed security issue with being able to bypass Turnstile on Contact Form 7.
- Other: Tested with WordPress 6.2.2

= Version 1.18.5 - 27th April 2023 =
- Fix: Fixed error on admin settings page for new installs, since last update.

= Version 1.18.4 - 27th April 2023 =
- Tweak: Made some tweaks to the admin settings page design.
- Tweak: Added check for Easy Digital Downloads PRO.
- Tweak: Turnstile will now work/be displayed on the Elementor login form.
- Tweak: Tweak to alignment of Turnstile on wpDiscuz comments form and added a "wpdiscuz-cfturnstile" class for applying any custom CSS/alignment tweaks if needed.
- Fix: Fixed occasional error with Contact Form 7 not re-rendering Turnstile on form submission error.
- Fix: Fixed issue with Gravity Forms when AJAX form was enabled, where if submitting with an error, Turnstile would not re-render.
- Fix: Fixed issue with Turnstile still not loading on some Forminator forms that had "Load form using AJAX" enabled.
- Fix: Fixed issue with Ultimate Member login form occasionally failing on first attempt with Turnstile enabled.
- Fix: Fixed WooCommerce integration settings sometimes not showing properly if Payment Method names were too long.

= Version 1.18.3 - 13th April 2023 =
- Fix: Fixed issue with Turnstile not loading on Forminator forms with "Load form using AJAX" enabled.
- Fix: Fixed broken settings link on plugins list since last update.

= Version 1.18.2 - 11th April 2023 =
- New: Added option to remove all the plugins options on uninstall.
- Tweak: Updated the admin settings page URL slug.
- Fix: Fixed issue with admins not being able to reply to comments on the /wp-admin/edit-comments.php page.
- Fix: Fixed an issue with wpDiscuz integration since 1.18.1 update.

= Version 1.18.1 - 10th April 2023 =
- Tweak: Update to make compatible with certain other login security plugins, or any other plugins that run the login "authenticate" multiple times.
- Tweak: Fixed issue with Turnstile widget showing on all Elementor forms (not just contact forms) and prevent it from showing twice.
- Tweak: Update to offer better compatibility with AJAX comment form plugins.

= Version 1.18.0 - 9th April 2023 =
- New: Added integration with the "Easy Digital Downloads" plugin.
- New: Added integration with the "WP-Members" plugin.
- New: Added compatibility with "Blocksy" theme login modal.
- New: Added option to skip Turnstile check for selected WooCommerce payment methods. Useful for Express Checkout options.
- Tweak: Improvements to code for displaying Turnstile on Elementor forms.
- Tweak: Added check to prevent Turnstile check when adding WooCommerce users in admin area.
- Tweak: Make tweak to ensure Turnstile challenge is checked for some 3rd-party plugins that load the login form.
- Tweak: Added "cfturnstile_cf7_button_types" filter for applying custom button types for the Contact Form 7 integration.
- Tweak: Made some tweaks to the admin settings page design.
- Tweak: Added code for declaring of compatibility with WooCommerce HPOS.
- Fix: Turnstile will now render properly on comment "reply" and "cancel reply" buttons being clicked.

= Version 1.17.3 - 10th March 2023 =
- Tweak: The "Language" setting options are now localized.
- Tweak: The "cfturnstile.css" file is now only loaded when required.
- Fix: Added a check to prevent PHP warning when there is an API error.

= Version 1.17.2 - 24th February 2023 =
- New: Added support for the new "data-language" attribute available with Turnstile. A default language can now be selected in the settings.
- Tweak: Added "data-action" attribute to Turnstile widget, which will allow you to see more detailed analytics in your Cloudflare dashboard.
- Tweak: Some improvements to code (added universal "cfturnstile_form_disable" function).
- Tweak: Added NULL check to force render script.
- Fix: Fixed "Uncaught TypeError" JS error when Turnstile enabled on comments form.
- Fix: Fixed potential console error due to unknown parameter sent to Cloudflare API.
- Fix: Fixed issue with multisite compatibility.

= Version 1.17.1 - 27th December 2022 =
- Tweak: Edited the filter used for WooCommerce login authentication, and fixed Turnstile challenge being checked twice when both WP Login and Woo Login enabled.

= Version 1.17.0 - 20th December 2022 =
- New: Added integration for the WooCommerce "Pay for Order" form.
- New: Added "Disabled Form IDs" option for "Forminator Forms" integration.
- Tweak: Added "After Form" option to elementor forms integration "Widget Location" setting. This will display the widget better on certain types of form layouts.
- Tweak: When submitting WPForms form, if there is an error, the Turnstile widget will now reset and re-verify.
- Tweak: On Elementor forms, wpDiscuz forms, and Forminator forms, Turnstile will now re-render on submission.
- Tweak: The code for the "Disable Submit Button" option now uses vanila javascript instead of jQuery, and the JS file will only be loaded if the option is enabled.
- Tweak: Turnstile on WordPress login will now work better with plugins that hide/change the admin login URL.
- Tweak: WordPress Login and Register will skip Turnstile check for XMLRPC requests.
- Tweak: Edited the filter used for WordPress login authentication.
- Fix: Fixed issue with Turnstile widget not showing in some cases with the Contact Form 7 integration "Enable on all CF7 Forms" option enabled, since CF7 version 5.7.
- Fix: Fixed "Disable Submit Button" not working with the new "Forminator" plugin integration.
- Fix: Fixed "One or more fields have an error." message sometimes showing on Contact Form 7 integration with Turnstile enabled.
- Fix: Fixed issue with the "bbPress" integration "alignment" option no longer working, since a recent update.

= Version 1.16.0 - 8th December 2022 =
- New: Added integration with the "Forminator" plugin.
- Tweak: Added a check in the scripts for showing Turnstile on Elementor, to prevent a possible console error.
- Fix: Fixed error with Turnstile widget not showing on Elementor forms with the "Before" option selected for "Widget Location".

= Version 1.15.4 - 30th November 2022 =
- Tweak: Implemented the new "data-retry-interval" attribute for Turnstile widget when displayed on Elementor forms.
- Fix: Turnstile widget now works properly with multiple Elementor forms on the same page, as long as each form has a unique "name".

= Version 1.15.3 - 27th November 2022 =
- Fix: Fixed Turnstile widget no longer rendering for some sites with certain optimisations enabled since 1.15.0 update.

= Version 1.15.2 - 26th November 2022 =
- Fix: Fixed "Call to undefined function is_plugin_active()" error showing on some sites since 1.15.0 update.

= Version 1.15.0 - 26th November 2022 =
- New: Added option to choose where exactly the Turnstile widget is displayed on WooCommerce checkout. Also updated code so adding it directly before the place order button now works.
- Tweak: Turnstile widget will now re-render on WooCommerce checkout, if any changes are made (checkout cart info reloads via js/ajax).
- Tweak: Minified the inline script for rendering Turnstile, and this now uses vanila javascript instead of jQuery.
- Tweak: Updated all forms to have completely unique Turnstile IDs, even if same form is displayed twice on same page (popups etc). This should prevent issues with Turnstile not loading properly on one of them.
- Tweak: Integrations should now work properly on WordPress multisite installations.
- Tweak: Implemented the new "data-retry-interval" attribute for Turnstile widget when displayed on WordPress comments.
- Other: Removed some redundant code for the "cfturnstile_scripts" option that was removed previously.

= Version 1.14.0 - 17th November 2022 =
- New: Added option to enable Turnstile on ALL forms created with Contact Form 7, instead of having to add it individually.
- Tweak: Implemented the new "data-retry-interval" attribute to speed up time it takes to retry on failure.
- Tweak: Turnstile will now work better/correctly with multiple forms displayed on the same page.
- Fix: Fixed error with Turnstile enabled on CF7 multi-step forms.
- Other: Tested with WordPress 6.1.1

= Version 1.13.2 - 11th November 2022 =
- Fix: Changed the code for wpDiscuz integration, so Turnstile loads properly for comment replies, and only attempt to enqueue scripts once.

= Version 1.13.1 - 4th November 2022 =
- Fix: Fixed Turnstile widget not showing on comments form for some sites that have certain optimisations enabled.

= Version 1.13.0 - 4th November 2022 =
- New: Added integration with "Ultimate Member" login, register, and password reset forms.
- Fix: Fixed 'Unknown parameter passed to api.js: "?ver=..."' console warning that was showing.

= Version 1.12.4 - 3rd November 2022 =
- Fix: Fixed a bug with widget showing twice for contact form 7 on some sites.

= Version 1.12.3 - 3rd November 2022 =
- Fix: Added check to see if jQuery is undefined, and fix error if so.

= Version 1.12.2 - 2nd November 2022 =
- Tweak: Removed "Where to load scripts?" option, since it will now accurately only load the scripts on pages that Turnstile is showing.
- Tweak: Tweaks to the WordPress comments validation code.
- Other: Tested with WordPress 6.1

= Version 1.12.1 - 30th October 2022 =
- Fix: Fixed a bug/issue on settings page for new installs (since last update).

= Version 1.12.0 - 30th October 2022 =
- New: Added "Disabled Form IDs" option for "Fluent Forms", "Gravity Forms", "WPForms", and "Formidable Forms" integrations.
- New: Added a "Widget Location" option to the "Formidable Forms" and "Elementor Forms" integrations.
- New: Added option to set your own custom error message, shown when the user submits the form with a failed Turnstile challenge.
- Tweak: A few small changes to the admin settings page.
- Fix: Fixed "PHP Deprecated" warning with Elementor integration enabled.

= Version 1.11.0 - 29th October 2022 =
- New: Added integration with "Formidable" forms. Simply enable it in the settings, and Turnstile will be added to all your forms.

= Version 1.10.0 - 28th October 2022 =
- New: Added integration with "Elementor" Pro forms. Simply enable it in the settings, and Turnstile will be added to all your forms.

= Version 1.9.0 - 28th October 2022 =
- New: Added integration with "wpDiscuz" plugin.

= Version 1.8.6 - 27th October 2022 =
- New: Added a "Widget Location" option to the "WPForms" and "Gravity Forms" integrations, to choose if the widget is shown before or after the button.
- Tweak: Updated alignment of Turnstile widget when displayed on frontend pages (moved 2px to left).

= Version 1.8.5 - 27th October 2022 =
- Tweak: It will now only try to re-render Turnstile widget explicitly (embedded JavaScript) if it can't currently find the Turnstile widget iFrame.

= Version 1.8.4 - 26th October 2022 =
- New: Added a fourth "Auto Detect + Custom Page IDs" option to the "Where to load scripts?" setting.
- Improvement: Updated the admin settings page to only show settings/dropdowns for integrations that are available (plugins activated and installed). A compact list of the other available integrations is still visible at the bottom.
- Dev: Updated some of the comments in the code to be more readable.

= Version 1.8.3 - 25th October 2022 =
- New: Added a third "Custom Page IDs" option to the "Where to load scripts?" setting. This lets you enter the specific page IDs that you want the scripts to load on.

= Version 1.8.2 - 24th October 2022 =
- New: Added option to select where the Turnstile script is loaded. Either "Auto Detect" or "All Pages".
- Tweak: "Auto Detect" will also load the required scripts on blog posts that include forms.

= Version 1.8.1 - 24th October 2022 =
- Fix: Fixed issue with admins not being able to reply to comments in the admin area, when Turnstile was enabled on the comments form.

= Version 1.8.0 - 24th October 2022 =
- New: Added integration with "Gravity Forms" plugin. Simply enable it in the settings, and Turnstile will be added to all your forms.
- Tweak: Added some code to ensure the Cloudflare widget is rendered when sites have certain optimisations enabled.
- Fix: Fixed error with WPForms emails still being sent if Turnstile fails.

= Version 1.7.0 - 22nd October 2022 =
- New: Added integration with "Fluent Forms" plugin. Simply enable it in the settings, and Turnstile will be added to all your forms.
- Tweak: Improved alignment of the Turnstile widget when displayed on Contact Form 7.
- Fix: Fixed a string that was missing localisation.

= Version 1.6.2 - 21st October 2022 =
- Fix: Fixed some strings that were missing localisation.
- Fix: Fixed "Cannot modify header information – headers already sent" error showing on some sites when activating plugins.

= Version 1.6.1 - 21st October 2022 =
- Tweak: Small tweaks to admin settings page styling.
- Fix: Fixed to the "Test API Response" step. It should now properly block Turnstile from loading on the login page until it's successfully tested (new activations only).

= Version 1.6.0 - 21st October 2022 =
- New: Added integration with "bbPress" create topic and reply forms.
- New: Added a new "Test API Response" step to the settings page, whenever the API keys are updated to make sure it's working. Turnstile will not work on your login forms until the test is successfully complete.
- Tweak: Changed the way the error message is shown for WordPress comments.
- Fix: Fixed issue with Turnstile verification not working correctly on checkout if "Create an account?" was selected.

= Version 1.5.1 - 20th October 2022 =
- Tweak: Removed the "Disable Submit Button" feature for the "WooCommerce Checkout" form button, to prevent issues with it sometimes not working.
- Fix: Fixed "Call to undefined function is_plugin_active()" error showing on some sites.
- Fix: Fixed styling/scripts not loading on admin page on first load.

= Version 1.5.0 - 20th October 2022 =
- New: Added integration with "WPForms" plugin. Simply enable it in the settings, and Turnstile will be added to all your forms.
- Tweak: Updated the design of admin settings page slightly.
- Fix: Fixed issue causing "Disable Submit Button" option to not work on some sites.

= Version 1.4.0 - 19th October 2022 =
- New: Added integration with "MC4WP: Mailchimp for WordPress". You can now add Turnstile to any MC4WP form. Just add the shortcode: [mc4wp-simple-turnstile]

= Version 1.3.0 - 18th October 2022 =
- New: Added integration with "BuddyPress" registration form.
- Other: Restructured some of the code.

= Version 1.2.2 - 18th October 2022 =
- Tweak: Upon submitting checkout form, if there is an error, it will now automatically reset Turnstile challenge token.

= Version 1.2.1 - 18th October 2022 =
- Tweak: Update so the the required scripts are only loaded on pages that need it.
- Tweak: Added an "Auto" option to the "Theme" setting.
- Tweak: Upon submitting contact form 7, it will now automatically reset Turnstile challenge token.
- Fix: Fixed some strings with wrong text domain, that could not be translated.
- Fix: Fixed settings link in plugins list not working.

= Version 1.2.0 - 17th October 2022 =
- New: Added integration with "Contact Form 7". You can now add Turnstile to any CF7 forms. Just add the shortcode: [cf7-simple-turnstile]

= Version 1.1.2 - 17th October 2022 =
- New: Added a "Disable Submit Button" option. When enabled, the submit button for all forms will be disabled until the Turnstile widget says "Success".
- Tweak: The turnstile script will now load correctly when using a custom wp-login URL.
- Tweak: Added a redirect to settings page on activation.
- Tweak: Hidden WooCommerce form settings if it is not installed or activated.

= Version 1.1.1 - 15th October 2022 =
- Fix: Fixed PHP error sometimes showing when WooCommerce is not installed.

= Version 1.1.0 - 15th October 2022 =
- New: Added option to enable Turnstile on WordPress comments form.

= Version 1.0.0 - 15th October 2022 =
- Plugin Released