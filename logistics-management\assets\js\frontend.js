/**
 * 物流管理前端JavaScript
 */

jQuery(document).ready(function($) {
    'use strict';

    // 查看物流详情按钮点击事件
    $(document).on('click', '.logistics-track-btn', function(e) {
        e.preventDefault();

        var $button = $(this);
        var trackingId = $button.data('tracking-id');
        var $detailsContainer = $('#tracking-details-' + trackingId);

        if (!trackingId) {
            alert(logistics_management_ajax.strings.error);
            return;
        }

        // 如果详情已经显示，则隐藏
        if ($detailsContainer.is(':visible')) {
            $detailsContainer.slideUp();
            $button.text('查询物流详情');
            return;
        }

        // 显示加载状态
        $button.prop('disabled', true).text(logistics_management_ajax.strings.loading);
        $detailsContainer.find('.tracking-timeline').html('<div class="loading-message">' + logistics_management_ajax.strings.loading + '</div>');
        $detailsContainer.slideDown();

        // 发送AJAX请求
        $.post(logistics_management_ajax.ajax_url, {
            action: 'get_tracking_details',
            tracking_id: trackingId,
            nonce: logistics_management_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                $detailsContainer.find('.tracking-timeline').html(response.data.html);
                $button.text('隐藏物流详情');

                // 根据是否是演示数据显示/隐藏演示提示
                var $demoNotice = $('#demo-notice-' + trackingId);
                if (response.data.is_demo) {
                    $demoNotice.show();
                } else {
                    $demoNotice.hide();
                }
            } else {
                $detailsContainer.find('.tracking-timeline').html('<div class="error-message">' + (response.data || logistics_management_ajax.strings.error) + '</div>');
                $button.text('重试查询');
            }
        })
        .fail(function() {
            $detailsContainer.find('.tracking-timeline').html('<div class="error-message">' + logistics_management_ajax.strings.error + '</div>');
            $button.text('重试查询');
        })
        .always(function() {
            $button.prop('disabled', false);
        });
    });

    // 刷新物流信息按钮点击事件
    $(document).on('click', '.logistics-refresh-btn', function(e) {
        e.preventDefault();

        var $button = $(this);
        var trackingId = $button.data('tracking-id');

        if (!trackingId) {
            alert(logistics_management_ajax.strings.error);
            return;
        }

        // 显示加载状态
        var originalText = $button.text();
        $button.prop('disabled', true).text(logistics_management_ajax.strings.loading);

        // 发送AJAX请求
        $.post(logistics_management_ajax.ajax_url, {
            action: 'refresh_tracking_info',
            tracking_id: trackingId,
            nonce: logistics_management_ajax.nonce
        })
        .done(function(response) {
            if (response.success) {
                // 显示成功消息
                showNotification(response.data.message, 'success');

                // 更新页面上的状态信息
                updateTrackingStatus(trackingId, response.data.status, response.data.last_update);

                // 如果详情已经展开，重新加载详情
                var $detailsContainer = $('#tracking-details-' + trackingId);
                if ($detailsContainer.is(':visible')) {
                    $('.logistics-track-btn[data-tracking-id="' + trackingId + '"]').click();
                }
            } else {
                showNotification(response.data || logistics_management_ajax.strings.error, 'error');
            }
        })
        .fail(function() {
            showNotification(logistics_management_ajax.strings.error, 'error');
        })
        .always(function() {
            $button.prop('disabled', false).text(originalText);
        });
    });

    // 复制快递单号功能
    $(document).on('click', '.tracking-number', function() {
        var trackingNumber = $(this).text();

        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(trackingNumber).then(function() {
                showNotification('快递单号已复制到剪贴板', 'success');
            }).catch(function() {
                fallbackCopyTextToClipboard(trackingNumber);
            });
        } else {
            fallbackCopyTextToClipboard(trackingNumber);
        }
    });

    // 显示通知消息
    function showNotification(message, type) {
        type = type || 'info';

        var $notification = $('<div class="logistics-notification logistics-notification-' + type + '">' + message + '</div>');

        $('body').append($notification);

        $notification.fadeIn().delay(3000).fadeOut(function() {
            $(this).remove();
        });
    }

    // 更新页面上的物流状态
    function updateTrackingStatus(trackingId, status, lastUpdate) {
        // 更新状态文本
        var $statusElements = $('.tracking-value[class*="status-"], .field-value[class*="status-"]').filter(function() {
            return $(this).closest('[data-tracking-id="' + trackingId + '"], #tracking-details-' + trackingId).length > 0;
        });

        $statusElements.each(function() {
            var $element = $(this);
            $element.removeClass(function(index, className) {
                return (className.match(/(^|\s)status-\S+/g) || []).join(' ');
            });
            $element.addClass('status-' + status);
        });

        // 更新最后更新时间
        var $timeElements = $('.tracking-value, .field-value').filter(function() {
            return $(this).text().indexOf('2024') !== -1 &&
                   $(this).closest('[data-tracking-id="' + trackingId + '"], #tracking-details-' + trackingId).length > 0;
        });

        if ($timeElements.length > 0) {
            var formattedTime = new Date(lastUpdate).toLocaleString('zh-CN');
            $timeElements.last().text(formattedTime);
        }
    }

    // 备用复制到剪贴板方法
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;

        // 避免滚动到底部
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                showNotification('快递单号已复制到剪贴板', 'success');
            } else {
                showNotification('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            showNotification('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    }

    // 添加工具提示
    $(document).on('mouseenter', '.tracking-number', function() {
        $(this).attr('title', '点击复制快递单号');
        $(this).css('cursor', 'pointer');
    });

    // 响应式处理
    function handleResponsive() {
        var windowWidth = $(window).width();

        if (windowWidth <= 768) {
            // 移动端优化
            $('.tracking-actions .button').addClass('button-small');
        } else {
            $('.tracking-actions .button').removeClass('button-small');
        }
    }

    // 初始化响应式处理
    handleResponsive();
    $(window).resize(handleResponsive);

    // 自动刷新功能（可选）
    var autoRefreshInterval = null;

    function startAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }

        // 每5分钟自动刷新一次物流信息
        autoRefreshInterval = setInterval(function() {
            $('.logistics-refresh-btn:visible').each(function() {
                var $button = $(this);
                if (!$button.prop('disabled')) {
                    $button.click();
                }
            });
        }, 5 * 60 * 1000); // 5分钟
    }

    // 页面可见性API - 当页面重新获得焦点时刷新
    if (typeof document.hidden !== "undefined") {
        document.addEventListener("visibilitychange", function() {
            if (!document.hidden) {
                // 页面重新可见时，刷新物流信息
                setTimeout(function() {
                    $('.logistics-refresh-btn:visible').first().click();
                }, 1000);
            }
        });
    }

    // 键盘快捷键支持
    $(document).keydown(function(e) {
        // Ctrl+R 或 F5 刷新物流信息
        if ((e.ctrlKey && e.keyCode === 82) || e.keyCode === 116) {
            var $refreshBtn = $('.logistics-refresh-btn:visible').first();
            if ($refreshBtn.length > 0) {
                e.preventDefault();
                $refreshBtn.click();
            }
        }
    });
});
