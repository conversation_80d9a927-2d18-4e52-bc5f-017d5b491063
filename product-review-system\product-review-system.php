<?php
/**
 * Plugin Name: WooCommerce 产品修改审核系统
 * Plugin URI: 
 * Description: 为WooCommerce产品修改提供两级审核流程：审核员审核 → 管理员审核 → 发布。集成Members插件权限系统。
 * Version: 1.0.0
 * Author: 
 * Author URI: 
 * Text Domain: product-review-system
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('PRS_VERSION', '1.0.0');
define('PRS_PLUGIN_FILE', __FILE__);
define('PRS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PRS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PRS_PLUGIN_BASENAME', plugin_basename(__FILE__));

// 安全配置常量
define('PRS_DEBUG', defined('WP_DEBUG') && WP_DEBUG);
define('PRS_MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('PRS_ALLOWED_FILE_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx');
define('PRS_SESSION_TIMEOUT', 3600); // 1小时

/**
 * 主插件类
 */
class Product_Review_System {

    /**
     * 单例实例
     */
    private static $instance = null;

    /**
     * 获取单例实例
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 构造函数
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
    }

    /**
     * 初始化插件
     */
    public function init() {
        // 检查依赖插件
        if (!$this->check_dependencies()) {
            return;
        }

        // 加载文本域
        load_plugin_textdomain('product-review-system', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // 包含必要的文件
        $this->includes();

        // 初始化钩子
        $this->init_hooks();

        // 检查数据库版本并升级
        $this->check_database_version();

        // 激活插件时创建数据库表
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * 检查依赖插件
     */
    private function check_dependencies() {
        $missing_plugins = array();

        // 检查WooCommerce
        if (!class_exists('WooCommerce')) {
            $missing_plugins[] = 'WooCommerce';
        }

        // 检查Members插件
        if (!function_exists('members_get_cap_groups')) {
            $missing_plugins[] = 'Members';
        }

        if (!empty($missing_plugins)) {
            add_action('admin_notices', function() use ($missing_plugins) {
                echo '<div class="notice notice-error"><p>';
                printf(
                    __('产品修改审核系统需要以下插件才能正常工作：%s', 'product-review-system'),
                    implode(', ', $missing_plugins)
                );
                echo '</p></div>';
            });
            return false;
        }

        return true;
    }

    /**
     * 包含必要的文件
     */
    private function includes() {
        // 首先加载安全相关类
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-security.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-security-config.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-security-middleware.php';

        // 加载其他核心类
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-menu-customizer.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-database.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-permissions.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-admin.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-product-handler.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-review-process.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-notifications.php';
        require_once PRS_PLUGIN_DIR . 'includes/class-prs-ajax.php';
    }

    /**
     * 初始化钩子
     */
    private function init_hooks() {
        // 首先初始化安全中间件
        PRS_Security_Middleware::init();

        // 初始化各个组件
        new PRS_Database();
        new PRS_Permissions();
        new PRS_Admin();
        new PRS_Product_Handler();
        new PRS_Review_Process();
        new PRS_Notifications();
        new PRS_Ajax();

        // 添加样式和脚本
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // 注册异步通知钩子
        add_action('prs_send_admin_notification', array($this, 'send_admin_notification'));
        add_action('prs_send_submitter_notification', array($this, 'send_submitter_notification'), 10, 2);

        // 修复WooCommerce文件名哈希问题 - 关闭文件名随机后缀
        add_action('init', array($this, 'fix_woocommerce_filename_hash'), 5);

        // 菜单定制器已在class-prs-menu-customizer.php中自动初始化
    }

    /**
     * 前端脚本和样式
     */
    public function enqueue_frontend_scripts() {
        // 前端暂时不需要特殊脚本
    }

    /**
     * 后端脚本和样式
     */
    public function enqueue_admin_scripts($hook) {
        // 只在相关页面加载
        $prs_pages = array(
            'toplevel_page_product-review-system',
            'product-review-system_page_prs-pending-reviews',
            'product-review-system_page_prs-review-history',
            'product-review-system_page_prs-settings',
            'product-review-system_page_prs-debug'
        );

        $is_prs_page = false;
        foreach ($prs_pages as $page) {
            if (strpos($hook, $page) !== false) {
                $is_prs_page = true;
                break;
            }
        }

        // 检查是否是产品相关页面
        $is_product_page = false;
        if ($hook === 'post.php' || $hook === 'post-new.php') {
            global $post;
            if ($post && $post->post_type === 'product') {
                $is_product_page = true;
            } elseif (isset($_GET['post']) && get_post_type($_GET['post']) === 'product') {
                $is_product_page = true;
            } elseif (isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
                $is_product_page = true;
            }
        } elseif ($hook === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
            // 产品列表页面
            $is_product_page = true;
        }

        if (!$is_prs_page && !$is_product_page) {
            return;
        }

        wp_enqueue_style(
            'prs-admin',
            PRS_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            PRS_VERSION
        );

        wp_enqueue_script(
            'prs-admin',
            PRS_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'wp-util'),
            PRS_VERSION,
            true
        );

        wp_localize_script('prs-admin', 'prs_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('prs_admin_nonce'),
            'strings' => array(
                'confirm_approve' => __('确定要通过这个审核吗？', 'product-review-system'),
                'confirm_reject' => __('确定要拒绝这个审核吗？', 'product-review-system'),
                'processing' => __('处理中...', 'product-review-system'),
                'success' => __('操作成功！', 'product-review-system'),
                'error' => __('操作失败，请重试。', 'product-review-system'),
            )
        ));
    }

    // Members菜单定制功能已移至 class-prs-menu-customizer.php

    /**
     * 修复WooCommerce文件名哈希问题
     */
    public function fix_woocommerce_filename_hash() {
        // 关闭WooCommerce为下载文件添加哈希的功能
        // 这会防止上传到媒体库的文件名出现随机编码后缀
        if (class_exists('WooCommerce')) {
            update_option('woocommerce_downloads_add_hash_to_filename', 'no');

            // 记录修复操作（仅在调试模式下）
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('PRS: Disabled WooCommerce filename hash to prevent random suffixes in media library uploads');
            }
        }
    }

    /**
     * 插件激活
     */
    public function activate() {
        // 创建数据库表
        PRS_Database::create_tables();

        // 注册权限
        PRS_Permissions::register_capabilities();

        // 刷新重写规则
        flush_rewrite_rules();
    }

    /**
     * 插件停用
     */
    public function deactivate() {
        // 刷新重写规则
        flush_rewrite_rules();
    }

    /**
     * 异步发送管理员通知
     */
    public function send_admin_notification($review_id) {
        try {
            PRS_Notifications::notify_admins($review_id);
            error_log('PRS: Admin notification sent successfully for review ID: ' . $review_id);
        } catch (Exception $e) {
            error_log('PRS: Failed to send admin notification: ' . $e->getMessage());
        }
    }

    /**
     * 异步发送提交者通知
     */
    public function send_submitter_notification($review_id, $result) {
        try {
            PRS_Notifications::notify_submitter($review_id, $result);
            error_log('PRS: Submitter notification sent successfully for review ID: ' . $review_id . ', result: ' . $result);
        } catch (Exception $e) {
            error_log('PRS: Failed to send submitter notification: ' . $e->getMessage());
        }
    }

    /**
     * 检查数据库版本并升级
     */
    private function check_database_version() {
        $current_version = get_option('prs_db_version', '0.0.0');
        $plugin_version = '1.0.0';

        if (version_compare($current_version, $plugin_version, '<')) {
            PRS_Database::upgrade_tables();
            update_option('prs_db_version', $plugin_version);
        }
    }
}

// 初始化插件
function product_review_system() {
    return Product_Review_System::get_instance();
}

// 启动插件
product_review_system();
