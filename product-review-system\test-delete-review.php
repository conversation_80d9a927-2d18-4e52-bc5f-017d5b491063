<?php
/**
 * 测试产品删除审核功能
 * 
 * 这个文件用于测试新增的产品删除审核功能
 * 使用方法：在浏览器中访问此文件来运行测试
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    // 如果不在WordPress环境中，尝试加载WordPress
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once($wp_load_path);
    } else {
        die('请在WordPress环境中运行此测试文件');
    }
}

// 检查用户权限
if (!current_user_can('manage_options')) {
    die('您没有权限运行此测试');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>产品删除审核功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>产品删除审核功能测试</h1>
    
    <?php
    echo '<div class="test-section">';
    echo '<h2>1. 数据库结构检查</h2>';
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'product_reviews';
    
    // 检查表是否存在
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    if ($table_exists) {
        echo '<p class="success">✓ 产品审核表存在</p>';
        
        // 检查operation_type字段是否存在
        $columns = $wpdb->get_results("DESCRIBE `$table_name`");
        $has_operation_type = false;
        foreach ($columns as $column) {
            if ($column->Field === 'operation_type') {
                $has_operation_type = true;
                break;
            }
        }
        
        if ($has_operation_type) {
            echo '<p class="success">✓ operation_type字段存在</p>';
        } else {
            echo '<p class="error">✗ operation_type字段不存在，请运行数据库升级</p>';
        }
    } else {
        echo '<p class="error">✗ 产品审核表不存在</p>';
    }
    echo '</div>';
    
    echo '<div class="test-section">';
    echo '<h2>2. 类和方法检查</h2>';
    
    // 检查必要的类是否存在
    $required_classes = array(
        'PRS_Database',
        'PRS_Product_Handler',
        'PRS_Review_Process',
        'PRS_Admin'
    );
    
    foreach ($required_classes as $class_name) {
        if (class_exists($class_name)) {
            echo '<p class="success">✓ ' . $class_name . ' 类存在</p>';
        } else {
            echo '<p class="error">✗ ' . $class_name . ' 类不存在</p>';
        }
    }
    
    // 检查新增的方法
    if (class_exists('PRS_Database')) {
        if (method_exists('PRS_Database', 'get_operation_type_options')) {
            echo '<p class="success">✓ PRS_Database::get_operation_type_options() 方法存在</p>';
        } else {
            echo '<p class="error">✗ PRS_Database::get_operation_type_options() 方法不存在</p>';
        }
    }
    
    if (class_exists('PRS_Product_Handler')) {
        if (method_exists('PRS_Product_Handler', 'intercept_product_delete')) {
            echo '<p class="success">✓ PRS_Product_Handler::intercept_product_delete() 方法存在</p>';
        } else {
            echo '<p class="error">✗ PRS_Product_Handler::intercept_product_delete() 方法不存在</p>';
        }
    }
    echo '</div>';
    
    echo '<div class="test-section">';
    echo '<h2>3. 钩子注册检查</h2>';
    
    // 检查删除拦截钩子是否注册
    if (has_filter('pre_trash_post')) {
        echo '<p class="success">✓ pre_trash_post 过滤器已注册</p>';
    } else {
        echo '<p class="warning">⚠ pre_trash_post 过滤器未注册（可能需要刷新页面）</p>';
    }
    echo '</div>';
    
    echo '<div class="test-section">';
    echo '<h2>4. 功能测试建议</h2>';
    echo '<p class="info">要完整测试删除审核功能，请按以下步骤操作：</p>';
    echo '<ol>';
    echo '<li>创建一个测试产品</li>';
    echo '<li><strong>使用任何账户（包括管理员）尝试删除产品</strong></li>';
    echo '<li>验证删除操作被拦截并创建审核记录</li>';
    echo '<li>检查删除审核记录是否正确创建</li>';
    echo '<li>使用审核员账户审核删除请求</li>';
    echo '<li>使用管理员账户最终确认删除</li>';
    echo '<li>验证产品是否被移到回收站</li>';
    echo '</ol>';
    echo '<p class="warning"><strong>注意：</strong> 现在所有用户删除产品都需要审核，包括管理员！</p>';
    echo '</div>';
    
    echo '<div class="test-section">';
    echo '<h2>5. 当前审核记录统计</h2>';
    
    if (class_exists('PRS_Database')) {
        $stats = PRS_Database::get_review_stats();
        if ($stats) {
            echo '<p>总审核记录: ' . $stats['total'] . '</p>';
            echo '<p>待审核: ' . $stats['pending_review'] . '</p>';
            echo '<p>待管理员审核: ' . $stats['pending_admin'] . '</p>';
            echo '<p>已通过: ' . $stats['approved'] . '</p>';
            echo '<p>已拒绝: ' . $stats['rejected'] . '</p>';
            
            // 检查是否有删除类型的审核记录
            $delete_reviews = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE operation_type = 'delete'");
            echo '<p class="info">删除审核记录: ' . $delete_reviews . '</p>';
        }
    }
    echo '</div>';
    ?>
    
    <div class="test-section">
        <h2>6. 快速操作</h2>
        <p><a href="<?php echo admin_url('admin.php?page=product-review-system'); ?>">查看产品审核系统</a></p>
        <p><a href="<?php echo admin_url('edit.php?post_type=product'); ?>">查看产品列表</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=prs-pending-reviews'); ?>">查看待审核列表</a></p>
    </div>
</body>
</html>
