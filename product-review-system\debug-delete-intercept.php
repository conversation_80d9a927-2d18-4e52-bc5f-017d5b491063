<?php
/**
 * 调试产品删除拦截功能
 * 
 * 这个文件用于调试产品删除拦截是否正常工作
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    // 如果不在WordPress环境中，尝试加载WordPress
    $wp_load_path = dirname(__FILE__) . '/../../../wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once($wp_load_path);
    } else {
        die('请在WordPress环境中运行此调试文件');
    }
}

// 检查用户权限
if (!current_user_can('manage_options')) {
    die('您没有权限运行此调试工具');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>产品删除拦截调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .code { background: #f5f5f5; padding: 10px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>产品删除拦截功能调试</h1>
    
    <?php
    echo '<div class="debug-section">';
    echo '<h2>1. 钩子注册检查</h2>';
    
    // 检查pre_trash_post过滤器
    global $wp_filter;
    if (isset($wp_filter['pre_trash_post'])) {
        echo '<p class="success">✓ pre_trash_post 过滤器已注册</p>';
        
        // 检查我们的回调函数是否注册
        $found_our_callback = false;
        foreach ($wp_filter['pre_trash_post']->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    get_class($callback['function'][0]) === 'PRS_Product_Handler' &&
                    $callback['function'][1] === 'intercept_product_delete') {
                    $found_our_callback = true;
                    echo '<p class="success">✓ PRS_Product_Handler::intercept_product_delete 回调已注册 (优先级: ' . $priority . ')</p>';
                    break 2;
                }
            }
        }
        
        if (!$found_our_callback) {
            echo '<p class="error">✗ PRS_Product_Handler::intercept_product_delete 回调未找到</p>';
        }
    } else {
        echo '<p class="error">✗ pre_trash_post 过滤器未注册</p>';
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>2. 类和方法检查</h2>';
    
    if (class_exists('PRS_Product_Handler')) {
        echo '<p class="success">✓ PRS_Product_Handler 类存在</p>';
        
        $handler = new PRS_Product_Handler();
        if (method_exists($handler, 'intercept_product_delete')) {
            echo '<p class="success">✓ intercept_product_delete 方法存在</p>';
        } else {
            echo '<p class="error">✗ intercept_product_delete 方法不存在</p>';
        }
        
        if (method_exists($handler, 'should_bypass_delete_review')) {
            echo '<p class="success">✓ should_bypass_delete_review 方法存在</p>';
        } else {
            echo '<p class="error">✗ should_bypass_delete_review 方法不存在</p>';
        }
        
        if (method_exists($handler, 'create_delete_review')) {
            echo '<p class="success">✓ create_delete_review 方法存在</p>';
        } else {
            echo '<p class="error">✗ create_delete_review 方法不存在</p>';
        }
    } else {
        echo '<p class="error">✗ PRS_Product_Handler 类不存在</p>';
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>3. 测试产品列表</h2>';
    
    // 获取一些测试产品
    $products = get_posts(array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'numberposts' => 5
    ));
    
    if (!empty($products)) {
        echo '<p class="info">找到 ' . count($products) . ' 个已发布的产品可用于测试：</p>';
        echo '<ul>';
        foreach ($products as $product) {
            $edit_url = admin_url('post.php?post=' . $product->ID . '&action=edit');
            echo '<li><a href="' . esc_url($edit_url) . '" target="_blank">' . esc_html($product->post_title) . '</a> (ID: ' . $product->ID . ')</li>';
        }
        echo '</ul>';
        echo '<p class="warning">⚠️ 请在产品编辑页面或产品列表页面尝试删除产品来测试拦截功能</p>';
    } else {
        echo '<p class="warning">⚠️ 没有找到已发布的产品，请先创建一些测试产品</p>';
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>4. 当前用户权限检查</h2>';
    
    $current_user = wp_get_current_user();
    echo '<p>当前用户: ' . esc_html($current_user->display_name) . ' (ID: ' . $current_user->ID . ')</p>';
    echo '<p>用户角色: ' . implode(', ', $current_user->roles) . '</p>';
    
    if (current_user_can('manage_options')) {
        echo '<p class="info">✓ 当前用户是管理员</p>';
    } else {
        echo '<p class="info">ℹ 当前用户不是管理员</p>';
    }
    
    if (current_user_can('delete_posts')) {
        echo '<p class="success">✓ 当前用户有删除文章权限</p>';
    } else {
        echo '<p class="error">✗ 当前用户没有删除文章权限</p>';
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>5. 调试建议</h2>';
    echo '<ol>';
    echo '<li><strong>测试删除拦截：</strong> 在产品列表页面点击"移到回收站"</li>';
    echo '<li><strong>检查浏览器控制台：</strong> 查看是否有JavaScript错误</li>';
    echo '<li><strong>检查网络请求：</strong> 在开发者工具中查看删除请求是否被发送</li>';
    echo '<li><strong>检查错误日志：</strong> 查看WordPress错误日志中是否有相关错误</li>';
    echo '<li><strong>检查数据库：</strong> 确认是否创建了删除审核记录</li>';
    echo '</ol>';
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>6. 手动测试代码</h2>';
    echo '<p>您可以在浏览器控制台中运行以下代码来测试删除拦截：</p>';
    echo '<div class="code">';
    echo 'console.log("Testing delete intercept...");<br>';
    echo '// 这将模拟删除操作<br>';
    echo 'jQuery(document).ready(function($) {<br>';
    echo '&nbsp;&nbsp;console.log("jQuery loaded, ready to test");<br>';
    echo '});';
    echo '</div>';
    echo '</div>';
    ?>
    
    <div class="debug-section">
        <h2>7. 快速链接</h2>
        <p><a href="<?php echo admin_url('edit.php?post_type=product'); ?>" target="_blank">产品列表页面</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=product-review-system'); ?>" target="_blank">产品审核系统</a></p>
        <p><a href="<?php echo admin_url('admin.php?page=prs-pending-reviews'); ?>" target="_blank">待审核列表</a></p>
    </div>
</body>
</html>
