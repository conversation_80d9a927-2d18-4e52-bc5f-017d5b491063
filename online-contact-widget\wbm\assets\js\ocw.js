import{c as v,a as w,b as y,r as E,o as g,d as b}from"./vendor.js";import{A as k}from"./fetch.js";const P=function(){const t=typeof document<"u"&&document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),R=function(s,t){return new URL(s,t).href},_={},A=function(t,o,a){let u=Promise.resolve();if(o&&o.length>0){const c=document.getElementsByTagName("link"),e=document.querySelector("meta[property=csp-nonce]"),p=(e==null?void 0:e.nonce)||(e==null?void 0:e.getAttribute("nonce"));u=Promise.allSettled(o.map(r=>{if(r=R(r,a),r in _)return;_[r]=!0;const i=r.endsWith(".css"),h=i?'[rel="stylesheet"]':"";if(!!a)for(let l=c.length-1;l>=0;l--){const f=c[l];if(f.href===r&&(!i||f.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${r}"]${h}`))return;const n=document.createElement("link");if(n.rel=i?"stylesheet":P,i||(n.as="script"),n.crossOrigin="",n.href=r,p&&n.setAttribute("nonce",p),document.head.appendChild(n),i)return new Promise((l,f)=>{n.addEventListener("load",l),n.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${r}`)))})}))}function d(c){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=c,window.dispatchEvent(e),!e.defaultPrevented)throw c}return u.then(c=>{for(const e of c||[])e.status==="rejected"&&d(e.reason);return t().catch(d)})},L=[{path:"",alias:"/ow-list",component:()=>A(()=>import("./ocw_list.js"),[],import.meta.url)}],S=v({history:w(),routes:L}),B=(s,t)=>{const o=s.__vccOpts||s;for(const[a,u]of t)o[a]=u;return o},$={};function C(s,t){const o=E("router-view");return g(),y(o,{ref:"route"},null,512)}const O=B($,[["render",C]]),m=b(O);m.use(S);m.mount("#wbm-ocw");m.config.globalProperties.$api=k;m.config.globalProperties.$cnf=window.wbm_js_cnf||{};
