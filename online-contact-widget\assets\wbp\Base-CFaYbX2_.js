import{_ as e,d as t,r as n,a as o,i as r,p as i,o as s,u as l,b as a,c,e as d,E as h,f as p,g as u,h as f,j as m,k as g,l as y,m as v,w,n as b,q as k,s as x,t as S,v as M,x as C,y as O,z as T,A,B as E,C as N,D,F as R,G as I,H as P,I as L,J as z,K as B,L as V,M as $,N as H,O as F,P as j,Q as _,R as q,S as W,T as K,U,V as J,W as G,X as Q,Y,Z as X,$ as Z,a0 as ee,a1 as te,a2 as ne,a3 as oe,a4 as re,a5 as ie,a6 as se}from"./wbs-Dtem2-xP.js";import{E as le,a as ae,b as ce}from"./el-switch-D8RcE-lN.js";import{E as de,a as he}from"./el-radio-CJI16kOl.js";import{S as pe}from"./Svg-23wbG-YI.js";import ue from"./Preview-cssRhrhC.js";/* empty css                  */import{u as fe,a as me,E as ge,C as ye}from"./index-CfGGe3vq.js";var ve=e(t({inheritAttrs:!1}),[["render",function(e,t,o,r,i,s){return n(e.$slots,"default")}],["__file","collection.vue"]]);var we=e(t({name:"ElCollectionItem",inheritAttrs:!1}),[["render",function(e,t,o,r,i,s){return n(e.$slots,"default")}],["__file","collection-item.vue"]]);const be=c({trigger:me.trigger,triggerKeys:{type:d(Array),default:()=>[h.enter,h.numpadEnter,h.space,h.down]},effect:{...fe.effect,default:"light"},type:{type:d(String)},placement:{type:d(String),default:"bottom"},popperOptions:{type:d(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:d([Number,String]),default:0},maxHeight:{type:d([Number,String]),default:""},popperClass:{type:String,default:""},disabled:Boolean,role:{type:String,default:"menu"},buttonProps:{type:d(Object)},teleported:fe.teleported});c({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:p}}),c({onKeydown:{type:d(Function)}}),(e=>{const t=`El${e}Collection`,n=`${t}Item`,c=Symbol(t),d=Symbol(n),h={...ve,name:t,setup(){const e=o(null),t=new Map;i(c,{itemMap:t,getItems:()=>{const n=l(e);if(!n)return[];const o=Array.from(n.querySelectorAll("[data-el-collection-item]"));return[...t.values()].sort(((e,t)=>o.indexOf(e.ref)-o.indexOf(t.ref)))},collectionRef:e})}},p={...we,name:n,setup(e,{attrs:t}){const n=o(null),h=r(c,void 0);i(d,{collectionItemRef:n}),s((()=>{const e=l(n);e&&h.itemMap.set(e,{ref:e,...t})})),a((()=>{const e=l(n);h.itemMap.delete(e)}))}}})("Dropdown");const ke=c({trigger:me.trigger,placement:be.placement,disabled:me.disabled,visible:fe.visible,transition:fe.transition,popperOptions:be.popperOptions,tabindex:be.tabindex,content:fe.content,popperStyle:fe.popperStyle,popperClass:fe.popperClass,enterable:{...fe.enterable,default:!0},effect:{...fe.effect,default:"light"},teleported:fe.teleported,title:String,width:{type:[String,Number],default:150},offset:{type:Number,default:void 0},showAfter:{type:Number,default:0},hideAfter:{type:Number,default:200},autoClose:{type:Number,default:0},showArrow:{type:Boolean,default:!0},persistent:{type:Boolean,default:!0},"onUpdate:visible":{type:Function}}),xe={"update:visible":e=>u(e),"before-enter":()=>!0,"before-leave":()=>!0,"after-enter":()=>!0,"after-leave":()=>!0},Se=t({name:"ElPopover"}),Me=t({...Se,props:ke,emits:xe,setup(e,{expose:t,emit:r}){const i=e,s=f((()=>i["onUpdate:visible"])),a=m("popover"),c=o(),d=f((()=>{var e;return null==(e=l(c))?void 0:e.popperRef})),h=f((()=>[{width:g(i.width)},i.popperStyle])),p=f((()=>[a.b(),i.popperClass,{[a.m("plain")]:!!i.content}])),u=f((()=>i.transition===`${a.namespace.value}-fade-in-linear`)),O=()=>{r("before-enter")},T=()=>{r("before-leave")},A=()=>{r("after-enter")},E=()=>{r("update:visible",!1),r("after-leave")};return t({popperRef:d,hide:()=>{var e;null==(e=c.value)||e.hide()}}),(e,t)=>(v(),y(l(ge),C({ref_key:"tooltipRef",ref:c},e.$attrs,{trigger:e.trigger,placement:e.placement,disabled:e.disabled,visible:e.visible,transition:e.transition,"popper-options":e.popperOptions,tabindex:e.tabindex,content:e.content,offset:e.offset,"show-after":e.showAfter,"hide-after":e.hideAfter,"auto-close":e.autoClose,"show-arrow":e.showArrow,"aria-label":e.title,effect:e.effect,enterable:e.enterable,"popper-class":l(p),"popper-style":l(h),teleported:e.teleported,persistent:e.persistent,"gpu-acceleration":l(u),"onUpdate:visible":l(s),onBeforeShow:O,onBeforeHide:T,onShow:A,onHide:E}),{content:w((()=>[e.title?(v(),k("div",{key:0,class:x(l(a).e("title")),role:"title"},S(e.title),3)):b("v-if",!0),n(e.$slots,"default",{},(()=>[M(S(e.content),1)]))])),default:w((()=>[e.$slots.reference?n(e.$slots,"reference",{key:0}):b("v-if",!0)])),_:3},16,["trigger","placement","disabled","visible","transition","popper-options","tabindex","content","offset","show-after","hide-after","auto-close","show-arrow","aria-label","effect","enterable","popper-class","popper-style","teleported","persistent","gpu-acceleration","onUpdate:visible"]))}});const Ce=(e,t)=>{const n=t.arg||t.value,o=null==n?void 0:n.popperRef;o&&(o.triggerRef=e)};const Oe=O(e(Me,[["__file","popover.vue"]]),{directive:T({mounted(e,t){Ce(e,t)},updated(e,t){Ce(e,t)}},"popover")});function Te(e){this.content=e}function Ae(e,t,n){for(let o=0;;o++){if(o==e.childCount||o==t.childCount)return e.childCount==t.childCount?null:n;let r=e.child(o),i=t.child(o);if(r!=i){if(!r.sameMarkup(i))return n;if(r.isText&&r.text!=i.text){for(let e=0;r.text[e]==i.text[e];e++)n++;return n}if(r.content.size||i.content.size){let e=Ae(r.content,i.content,n+1);if(null!=e)return e}n+=r.nodeSize}else n+=r.nodeSize}}function Ee(e,t,n,o){for(let r=e.childCount,i=t.childCount;;){if(0==r||0==i)return r==i?null:{a:n,b:o};let s=e.child(--r),l=t.child(--i),a=s.nodeSize;if(s!=l){if(!s.sameMarkup(l))return{a:n,b:o};if(s.isText&&s.text!=l.text){let e=0,t=Math.min(s.text.length,l.text.length);for(;e<t&&s.text[s.text.length-e-1]==l.text[l.text.length-e-1];)e++,n--,o--;return{a:n,b:o}}if(s.content.size||l.content.size){let e=Ee(s.content,l.content,n-1,o-1);if(e)return e}n-=a,o-=a}else n-=a,o-=a}}Te.prototype={constructor:Te,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return-1},get:function(e){var t=this.find(e);return-1==t?void 0:this.content[t+1]},update:function(e,t,n){var o=n&&n!=e?this.remove(n):this,r=o.find(e),i=o.content.slice();return-1==r?i.push(n||e,t):(i[r+1]=t,n&&(i[r]=n)),new Te(i)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new Te(n)},addToStart:function(e,t){return new Te([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new Te(n)},addBefore:function(e,t,n){var o=this.remove(t),r=o.content.slice(),i=o.find(e);return r.splice(-1==i?r.length:i,0,t,n),new Te(r)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=Te.from(e)).size?new Te(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=Te.from(e)).size?new Te(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=Te.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach((function(t,n){e[t]=n})),e},get size(){return this.content.length>>1}},Te.from=function(e){if(e instanceof Te)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new Te(t)};class Ne{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,o=0,r){for(let i=0,s=0;s<t;i++){let l=this.content[i],a=s+l.nodeSize;if(a>e&&!1!==n(l,o+s,r||null,i)&&l.content.size){let r=s+1;l.nodesBetween(Math.max(0,e-r),Math.min(l.content.size,t-r),n,o+r)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,o){let r="",i=!0;return this.nodesBetween(e,t,((s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?o?"function"==typeof o?o(s):o:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(i?i=!1:r+=n),r+=a}),0),r}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,o=this.content.slice(),r=0;for(t.isText&&t.sameMarkup(n)&&(o[o.length-1]=t.withText(t.text+n.text),r=1);r<e.content.length;r++)o.push(e.content[r]);return new Ne(o,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],o=0;if(t>e)for(let r=0,i=0;i<t;r++){let s=this.content[r],l=i+s.nodeSize;l>e&&((i<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-i),Math.min(s.text.length,t-i)):s.cut(Math.max(0,e-i-1),Math.min(s.content.size,t-i-1))),n.push(s),o+=s.nodeSize),i=l}return new Ne(n,o)}cutByIndex(e,t){return e==t?Ne.empty:0==e&&t==this.content.length?this:new Ne(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let o=this.content.slice(),r=this.size+t.nodeSize-n.nodeSize;return o[e]=t,new Ne(o,r)}addToStart(e){return new Ne([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new Ne(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let o=this.content[t];e(o,n,t),n+=o.nodeSize}}findDiffStart(e,t=0){return Ae(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return Ee(this,e,t,n)}findIndex(e,t=-1){if(0==e)return Re(0,e);if(e==this.size)return Re(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,o=0;;n++){let r=o+this.child(n).nodeSize;if(r>=e)return r==e||t>0?Re(n+1,r):Re(n,o);o=r}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map((e=>e.toJSON())):null}static fromJSON(e,t){if(!t)return Ne.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new Ne(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return Ne.empty;let t,n=0;for(let o=0;o<e.length;o++){let r=e[o];n+=r.nodeSize,o&&r.isText&&e[o-1].sameMarkup(r)?(t||(t=e.slice(0,o)),t[t.length-1]=r.withText(t[t.length-1].text+r.text)):t&&t.push(r)}return new Ne(t||e,n)}static from(e){if(!e)return Ne.empty;if(e instanceof Ne)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new Ne([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}Ne.empty=new Ne([],0);const De={index:0,offset:0};function Re(e,t){return De.index=e,De.offset=t,De}function Ie(e,t){if(e===t)return!0;if(!e||"object"!=typeof e||!t||"object"!=typeof t)return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!Ie(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!Ie(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}let Pe=class e{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let o=0;o<e.length;o++){let r=e[o];if(this.eq(r))return e;if(this.type.excludes(r.type))t||(t=e.slice(0,o));else{if(r.type.excludes(this.type))return e;!n&&r.type.rank>this.type.rank&&(t||(t=e.slice(0,o)),t.push(this),n=!0),t&&t.push(r)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Ie(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);let o=n.create(t.attrs);return n.checkAttrs(o.attrs),o}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return e.none;if(t instanceof e)return[t];let n=t.slice();return n.sort(((e,t)=>e.type.rank-t.type.rank)),n}};Pe.none=[];class Le extends Error{}class ze{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=Ve(this.content,e+this.openStart,t);return n&&new ze(n,this.openStart,this.openEnd)}removeBetween(e,t){return new ze(Be(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return ze.empty;let n=t.openStart||0,o=t.openEnd||0;if("number"!=typeof n||"number"!=typeof o)throw new RangeError("Invalid input for Slice.fromJSON");return new ze(Ne.fromJSON(e,t.content),n,o)}static maxOpen(e,t=!0){let n=0,o=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let r=e.lastChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.lastChild)o++;return new ze(e,n,o)}}function Be(e,t,n){let{index:o,offset:r}=e.findIndex(t),i=e.maybeChild(o),{index:s,offset:l}=e.findIndex(n);if(r==t||i.isText){if(l!=n&&!e.child(s).isText)throw new RangeError("Removing non-flat range");return e.cut(0,t).append(e.cut(n))}if(o!=s)throw new RangeError("Removing non-flat range");return e.replaceChild(o,i.copy(Be(i.content,t-r-1,n-r-1)))}function Ve(e,t,n,o){let{index:r,offset:i}=e.findIndex(t),s=e.maybeChild(r);if(i==t||s.isText)return e.cut(0,t).append(n).append(e.cut(t));let l=Ve(s.content,t-i-1,n);return l&&e.replaceChild(r,s.copy(l))}function $e(e,t,n){if(n.openStart>e.depth)throw new Le("Inserted content deeper than insertion position");if(e.depth-n.openStart!=t.depth-n.openEnd)throw new Le("Inconsistent open depths");return He(e,t,n,0)}function He(e,t,n,o){let r=e.index(o),i=e.node(o);if(r==t.index(o)&&o<e.depth-n.openStart){let s=He(e,t,n,o+1);return i.copy(i.content.replaceChild(r,s))}if(n.content.size){if(n.openStart||n.openEnd||e.depth!=o||t.depth!=o){let{start:r,end:s}=function(e,t){let n=t.depth-e.openStart,o=t.node(n).copy(e.content);for(let r=n-1;r>=0;r--)o=t.node(r).copy(Ne.from(o));return{start:o.resolveNoCache(e.openStart+n),end:o.resolveNoCache(o.content.size-e.openEnd-n)}}(n,e);return We(i,Ke(e,r,s,t,o))}{let o=e.parent,r=o.content;return We(o,r.cut(0,e.parentOffset).append(n.content).append(r.cut(t.parentOffset)))}}return We(i,Ue(e,t,o))}function Fe(e,t){if(!t.type.compatibleContent(e.type))throw new Le("Cannot join "+t.type.name+" onto "+e.type.name)}function je(e,t,n){let o=e.node(n);return Fe(o,t.node(n)),o}function _e(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function qe(e,t,n,o){let r=(t||e).node(n),i=0,s=t?t.index(n):r.childCount;e&&(i=e.index(n),e.depth>n?i++:e.textOffset&&(_e(e.nodeAfter,o),i++));for(let l=i;l<s;l++)_e(r.child(l),o);t&&t.depth==n&&t.textOffset&&_e(t.nodeBefore,o)}function We(e,t){return e.type.checkContent(t),e.copy(t)}function Ke(e,t,n,o,r){let i=e.depth>r&&je(e,t,r+1),s=o.depth>r&&je(n,o,r+1),l=[];return qe(null,e,r,l),i&&s&&t.index(r)==n.index(r)?(Fe(i,s),_e(We(i,Ke(e,t,n,o,r+1)),l)):(i&&_e(We(i,Ue(e,t,r+1)),l),qe(t,n,r,l),s&&_e(We(s,Ue(n,o,r+1)),l)),qe(o,null,r,l),new Ne(l)}function Ue(e,t,n){let o=[];if(qe(null,e,n,o),e.depth>n){_e(We(je(e,t,n+1),Ue(e,t,n+1)),o)}return qe(t,null,n,o),new Ne(o)}ze.empty=new ze(Ne.empty,0,0);class Je{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],o=e.child(t);return n?e.child(t).cut(n):o}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],o=0==t?0:this.path[3*t-1]+1;for(let r=0;r<e;r++)o+=n.child(r).nodeSize;return o}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return Pe.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),o=e.maybeChild(t);if(!n){let e=n;n=o,o=e}let r=n.marks;for(var i=0;i<r.length;i++)!1!==r[i].type.spec.inclusive||o&&r[i].isInSet(o.marks)||(r=r[i--].removeFromSet(r));return r}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,o=e.parent.maybeChild(e.index());for(var r=0;r<n.length;r++)!1!==n[r].type.spec.inclusive||o&&n[r].isInSet(o.marks)||(n=n[r--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new Xe(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],o=0,r=t;for(let i=e;;){let{index:e,offset:t}=i.content.findIndex(r),s=r-t;if(n.push(i,e,o+t),!s)break;if(i=i.child(e),i.isText)break;r=s-1,o+=t+1}return new Je(t,n,r)}static resolveCached(e,t){let n=Ye.get(e);if(n)for(let r=0;r<n.elts.length;r++){let e=n.elts[r];if(e.pos==t)return e}else Ye.set(e,n=new Ge);let o=n.elts[n.i]=Je.resolve(e,t);return n.i=(n.i+1)%Qe,o}}class Ge{constructor(){this.elts=[],this.i=0}}const Qe=12,Ye=new WeakMap;class Xe{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const Ze=Object.create(null);let et=class e{constructor(e,t,n,o=Pe.none){this.type=e,this.attrs=t,this.marks=o,this.content=n||Ne.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,o=0){this.content.nodesBetween(e,t,n,o,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,o){return this.content.textBetween(e,t,n,o)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&Ie(this.attrs,t||e.defaultAttrs||Ze)&&Pe.sameSet(this.marks,n||Pe.none)}copy(t=null){return t==this.content?this:new e(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new e(this.type,this.attrs,this.content,t)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return ze.empty;let o=this.resolve(e),r=this.resolve(t),i=n?0:o.sharedDepth(t),s=o.start(i),l=o.node(i).content.cut(o.pos-s,r.pos-s);return new ze(l,o.depth-i,r.depth-i)}replace(e,t,n){return $e(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:o}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(o==e||t.isText)return t;e-=o+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let o=this.content.child(t-1);return{node:o,index:t-1,offset:n-o.nodeSize}}resolve(e){return Je.resolveCached(this,e)}resolveNoCache(e){return Je.resolve(this,e)}rangeHasMark(e,t,n){let o=!1;return t>e&&this.nodesBetween(e,t,(e=>(n.isInSet(e.marks)&&(o=!0),!o))),o}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),nt(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=Ne.empty,o=0,r=n.childCount){let i=this.contentMatchAt(e).matchFragment(n,o,r),s=i&&i.matchFragment(this.content,t);if(!s||!s.validEnd)return!1;for(let l=o;l<r;l++)if(!this.type.allowsMarks(n.child(l).marks))return!1;return!0}canReplaceWith(e,t,n,o){if(o&&!this.type.allowsMarks(o))return!1;let r=this.contentMatchAt(e).matchType(n),i=r&&r.matchFragment(this.content,t);return!!i&&i.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=Pe.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!Pe.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map((e=>e.type.name))}`);this.content.forEach((e=>e.check()))}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map((e=>e.toJSON()))),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let o=Ne.fromJSON(e,t.content),r=e.nodeType(t.type).create(t.attrs,o,n);return r.type.checkAttrs(r.attrs),r}};et.prototype.text=void 0;class tt extends et{constructor(e,t,n,o){if(super(e,t,null,o),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):nt(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new tt(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new tt(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function nt(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class ot{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new rt(e,t);if(null==n.next)return ot.empty;let o=it(n);n.next&&n.err("Unexpected trailing text");let r=function(e){let t=Object.create(null);return n(ht(e,0));function n(o){let r=[];o.forEach((t=>{e[t].forEach((({term:t,to:n})=>{if(!t)return;let o;for(let e=0;e<r.length;e++)r[e][0]==t&&(o=r[e][1]);ht(e,n).forEach((e=>{o||r.push([t,o=[]]),-1==o.indexOf(e)&&o.push(e)}))}))}));let i=t[o.join(",")]=new ot(o.indexOf(e.length-1)>-1);for(let e=0;e<r.length;e++){let o=r[e][1].sort(dt);i.next.push({type:r[e][0],next:t[o.join(",")]||n(o)})}return i}}(function(e){let t=[[]];return r(i(e,0),n()),t;function n(){return t.push([])-1}function o(e,n,o){let r={term:o,to:n};return t[e].push(r),r}function r(e,t){e.forEach((e=>e.to=t))}function i(e,t){if("choice"==e.type)return e.exprs.reduce(((e,n)=>e.concat(i(n,t))),[]);if("seq"!=e.type){if("star"==e.type){let s=n();return o(t,s),r(i(e.expr,s),s),[o(s)]}if("plus"==e.type){let s=n();return r(i(e.expr,t),s),r(i(e.expr,s),s),[o(s)]}if("opt"==e.type)return[o(t)].concat(i(e.expr,t));if("range"==e.type){let s=t;for(let t=0;t<e.min;t++){let t=n();r(i(e.expr,s),t),s=t}if(-1==e.max)r(i(e.expr,s),s);else for(let t=e.min;t<e.max;t++){let t=n();o(s,t),r(i(e.expr,s),t),s=t}return[o(s)]}if("name"==e.type)return[o(t,void 0,e.value)];throw new Error("Unknown expr type")}for(let o=0;;o++){let s=i(e.exprs[o],t);if(o==e.exprs.length-1)return s;r(s,t=n())}}}(o));return function(e,t){for(let n=0,o=[e];n<o.length;n++){let e=o[n],r=!e.validEnd,i=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];i.push(n.name),!r||n.isText||n.hasRequiredAttrs()||(r=!1),-1==o.indexOf(s)&&o.push(s)}r&&t.err("Only non-generatable nodes ("+i.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(r,n),r}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let o=this;for(let r=t;o&&r<n;r++)o=o.matchType(e.child(r).type);return o}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let o=[this];return function r(i,s){let l=i.matchFragment(e,n);if(l&&(!t||l.validEnd))return Ne.from(s.map((e=>e.createAndFill())));for(let e=0;e<i.next.length;e++){let{type:t,next:n}=i.next[e];if(!t.isText&&!t.hasRequiredAttrs()&&-1==o.indexOf(n)){o.push(n);let e=r(n,s.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let o=n.shift(),r=o.match;if(r.matchType(e)){let e=[];for(let t=o;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<r.next.length;e++){let{type:i,next:s}=r.next[e];i.isLeaf||i.hasRequiredAttrs()||i.name in t||o.type&&!s.validEnd||(n.push({match:i.contentMatch,type:i,via:o}),t[i.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let o=0;o<n.next.length;o++)-1==e.indexOf(n.next[o].next)&&t(n.next[o].next)}(this),e.map(((t,n)=>{let o=n+(t.validEnd?"*":" ")+" ";for(let r=0;r<t.next.length;r++)o+=(r?", ":"")+t.next[r].type.name+"->"+e.indexOf(t.next[r].next);return o})).join("\n")}}ot.empty=new ot(!0);class rt{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function it(e){let t=[];do{t.push(st(e))}while(e.eat("|"));return 1==t.length?t[0]:{type:"choice",exprs:t}}function st(e){let t=[];do{t.push(lt(e))}while(e.next&&")"!=e.next&&"|"!=e.next);return 1==t.length?t[0]:{type:"seq",exprs:t}}function lt(e){let t=function(e){if(e.eat("(")){let t=it(e);return e.eat(")")||e.err("Missing closing paren"),t}if(!/\W/.test(e.next)){let t=function(e,t){let n=e.nodeTypes,o=n[t];if(o)return[o];let r=[];for(let i in n){let e=n[i];e.isInGroup(t)&&r.push(e)}0==r.length&&e.err("No node type or group '"+t+"' found");return r}(e,e.next).map((t=>(null==e.inline?e.inline=t.isInline:e.inline!=t.isInline&&e.err("Mixing inline and block content"),{type:"name",value:t})));return e.pos++,1==t.length?t[0]:{type:"choice",exprs:t}}e.err("Unexpected token '"+e.next+"'")}(e);for(;;)if(e.eat("+"))t={type:"plus",expr:t};else if(e.eat("*"))t={type:"star",expr:t};else if(e.eat("?"))t={type:"opt",expr:t};else{if(!e.eat("{"))break;t=ct(e,t)}return t}function at(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function ct(e,t){let n=at(e),o=n;return e.eat(",")&&(o="}"!=e.next?at(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:o,expr:t}}function dt(e,t){return t-e}function ht(e,t){let n=[];return function t(o){let r=e[o];if(1==r.length&&!r[0].term)return t(r[0].to);n.push(o);for(let e=0;e<r.length;e++){let{term:o,to:i}=r[e];o||-1!=n.indexOf(i)||t(i)}}(t),n.sort(dt)}function pt(e){let t=Object.create(null);for(let n in e){let o=e[n];if(!o.hasDefault)return null;t[n]=o.default}return t}function ut(e,t){let n=Object.create(null);for(let o in e){let r=t&&t[o];if(void 0===r){let t=e[o];if(!t.hasDefault)throw new RangeError("No value supplied for attribute "+o);r=t.default}n[o]=r}return n}function ft(e,t,n,o){for(let r in t)if(!(r in e))throw new RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let r in e){let n=e[r];n.validate&&n.validate(t[r])}}function mt(e,t){let n=Object.create(null);if(t)for(let o in t)n[o]=new yt(e,o,t[o]);return n}let gt=class e{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=mt(e,n.attrs),this.defaultAttrs=pt(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==ot.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:ut(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new et(this,this.computeAttrs(e),Ne.from(t),Pe.setFrom(n))}createChecked(e=null,t,n){return t=Ne.from(t),this.checkContent(t),new et(this,this.computeAttrs(e),t,Pe.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=Ne.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let o=this.contentMatch.matchFragment(t),r=o&&o.fillBefore(Ne.empty,!0);return r?new et(this,e,t.append(r),Pe.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){ft(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(null==this.markSet)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:Pe.none:e}static compile(t,n){let o=Object.create(null);t.forEach(((t,r)=>o[t]=new e(t,n,r)));let r=n.spec.topNode||"doc";if(!o[r])throw new RangeError("Schema is missing its top node type ('"+r+"')");if(!o.text)throw new RangeError("Every schema needs a 'text' type");for(let e in o.text.attrs)throw new RangeError("The text node type should not have attributes");return o}};class yt{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let o=n.split("|");return n=>{let r=null===n?"null":typeof n;if(o.indexOf(r)<0)throw new RangeError(`Expected value of type ${o} for attribute ${t} on type ${e}, got ${r}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class vt{constructor(e,t,n,o){this.name=e,this.rank=t,this.schema=n,this.spec=o,this.attrs=mt(e,o.attrs),this.excluded=null;let r=pt(this.attrs);this.instance=r?new Pe(this,r):null}create(e=null){return!e&&this.instance?this.instance:new Pe(this,ut(this.attrs,e))}static compile(e,t){let n=Object.create(null),o=0;return e.forEach(((e,r)=>n[e]=new vt(e,o++,t,r))),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){ft(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class wt{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let o in e)t[o]=e[o];t.nodes=Te.from(e.nodes),t.marks=Te.from(e.marks||{}),this.nodes=gt.compile(this.spec.nodes,this),this.marks=vt.compile(this.spec.marks,this);let n=Object.create(null);for(let o in this.nodes){if(o in this.marks)throw new RangeError(o+" can not be both a node and a mark");let e=this.nodes[o],t=e.spec.content||"",r=e.spec.marks;if(e.contentMatch=n[t]||(n[t]=ot.parse(t,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==r?null:r?bt(this,r.split(" ")):""!=r&&e.inlineContent?null:[]}for(let o in this.marks){let e=this.marks[o],t=e.spec.excludes;e.excluded=null==t?[e]:""==t?[]:bt(this,t.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,o){if("string"==typeof e)e=this.nodeType(e);else{if(!(e instanceof gt))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,o)}text(e,t){let n=this.nodes.text;return new tt(n,n.defaultAttrs,e,Pe.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return et.fromJSON(this,e)}markFromJSON(e){return Pe.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function bt(e,t){let n=[];for(let o=0;o<t.length;o++){let r=t[o],i=e.marks[r],s=i;if(i)n.push(i);else for(let t in e.marks){let o=e.marks[t];("_"==r||o.spec.group&&o.spec.group.split(" ").indexOf(r)>-1)&&n.push(s=o)}if(!s)throw new SyntaxError("Unknown mark type: '"+t[o]+"'")}return n}class kt{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach((e=>{if(function(e){return null!=e.tag}(e))this.tags.push(e);else if(function(e){return null!=e.style}(e)){let t=/[^=]*/.exec(e.style)[0];n.indexOf(t)<0&&n.push(t),this.styles.push(e)}})),this.normalizeLists=!this.tags.some((t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)}))}parse(e,t={}){let n=new Tt(this,t,!1);return n.addAll(e,Pe.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new Tt(this,t,!0);return n.addAll(e,Pe.none,t.from,t.to),ze.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){let n=this.tags[o];if(At(e,n.tag)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,o){for(let r=o?this.styles.indexOf(o)+1:0;r<this.styles.length;r++){let o=this.styles[r],i=o.style;if(!(0!=i.indexOf(e)||o.context&&!n.matchesContext(o.context)||i.length>e.length&&(61!=i.charCodeAt(e.length)||i.slice(e.length+1)!=t))){if(o.getAttrs){let e=o.getAttrs(t);if(!1===e)continue;o.attrs=e||void 0}return o}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,o=0;for(;o<t.length;o++){let e=t[o];if((null==e.priority?50:e.priority)<n)break}t.splice(o,0,e)}for(let o in e.marks){let t=e.marks[o].spec.parseDOM;t&&t.forEach((e=>{n(e=Et(e)),e.mark||e.ignore||e.clearMark||(e.mark=o)}))}for(let o in e.nodes){let t=e.nodes[o].spec.parseDOM;t&&t.forEach((e=>{n(e=Et(e)),e.node||e.ignore||e.mark||(e.node=o)}))}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new kt(e,kt.schemaRules(e)))}}const xt={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},St={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},Mt={ol:!0,ul:!0};function Ct(e,t,n){return null!=t?(t?1:0)|("full"===t?2:0):e&&"pre"==e.whitespace?3:-5&n}class Ot{constructor(e,t,n,o,r,i){this.type=e,this.attrs=t,this.marks=n,this.solid=o,this.options=i,this.content=[],this.activeMarks=Pe.none,this.match=r||(4&i?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(Ne.from(e));if(!t){let t,n=this.type.contentMatch;return(t=n.findWrapping(e.type))?(this.match=n,t):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e,t=this.content[this.content.length-1];if(t&&t.isText&&(e=/[ \t\r\n\u000c]+$/.exec(t.text))){let n=t;t.text.length==e[0].length?this.content.pop():this.content[this.content.length-1]=n.withText(n.text.slice(0,n.text.length-e[0].length))}}let t=Ne.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(Ne.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!xt.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class Tt{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let o,r=t.topNode,i=Ct(null,t.preserveWhitespace,0)|(n?4:0);o=r?new Ot(r.type,r.attrs,Pe.none,!0,t.topMatch||r.type.contentMatch,i):new Ot(n?null:e.schema.topNodeType,null,Pe.none,!0,null,i),this.nodes=[o],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,o=this.top,r=2&o.options?"full":this.localPreserveWS||(1&o.options)>0;if("full"===r||o.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(r)n="full"!==r?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=o.content[o.content.length-1],r=e.previousSibling;(!t||r&&"BR"==r.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let o=this.localPreserveWS,r=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let i,s=e.nodeName.toLowerCase();Mt.hasOwnProperty(s)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&Mt.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(i=this.parser.matchTag(e,this,n));e:if(l?l.ignore:St.hasOwnProperty(s))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,o=this.needsBlock;if(xt.hasOwnProperty(s))r.content.length&&r.content[0].isInline&&this.open&&(this.open--,r=this.top),n=!0,r.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let i=l&&l.skip?t:this.readStyles(e,t);i&&this.addAll(e,i),n&&this.sync(r),this.needsBlock=o}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?i:void 0)}this.localPreserveWS=o}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let o=0;o<this.parser.matchedStyles.length;o++){let e=this.parser.matchedStyles[o],r=n.getPropertyValue(e);if(r)for(let n;;){let o=this.parser.matchStyle(e,r,this,n);if(!o)break;if(o.ignore)return null;if(t=o.clearMark?t.filter((e=>!o.clearMark(e))):t.concat(this.parser.schema.marks[o.mark].create(o.attrs)),!1!==o.consuming)break;n=o}}return t}addElementByRule(e,t,n,o){let r,i;if(t.node)if(i=this.parser.schema.nodes[t.node],i.isLeaf)this.insertNode(i.create(t.attrs),n)||this.leafFallback(e,n);else{let e=this.enter(i,t.attrs||null,n,t.preserveWhitespace);e&&(r=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(i&&i.isLeaf)this.findInside(e);else if(o)this.addElement(e,n,o);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach((e=>this.insertNode(e,n)));else{let o=e;"string"==typeof t.contentElement?o=e.querySelector(t.contentElement):"function"==typeof t.contentElement?o=t.contentElement(e):t.contentElement&&(o=t.contentElement),this.findAround(e,o,!0),this.addAll(o,n),this.findAround(e,o,!1)}r&&this.sync(s)&&this.open--}addAll(e,t,n,o){let r=n||0;for(let i=n?e.childNodes[n]:e.firstChild,s=null==o?null:e.childNodes[o];i!=s;i=i.nextSibling,++r)this.findAtPoint(e,r),this.addDOM(i,t);this.findAtPoint(e,r)}findPlace(e,t){let n,o;for(let r=this.open;r>=0;r--){let t=this.nodes[r],i=t.findWrapping(e);if(i&&(!n||n.length>i.length)&&(n=i,o=t,!i.length))break;if(t.solid)break}if(!n)return null;this.sync(o);for(let r=0;r<n.length;r++)t=this.enterInner(n[r],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let n=this.findPlace(e,t);if(n){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let o=Pe.none;for(let r of n.concat(e.marks))(t.type?t.type.allowsMarkType(r.type):Nt(r.type,e.type))&&(o=r.addToSet(o));return t.content.push(e.mark(o)),!0}return!1}enter(e,t,n,o){let r=this.findPlace(e.create(t),n);return r&&(r=this.enterInner(e,t,n,!0,o)),r}enterInner(e,t,n,o=!1,r){this.closeExtra();let i=this.top;i.match=i.match&&i.match.matchType(e);let s=Ct(e,r,i.options);4&i.options&&0==i.content.length&&(s|=4);let l=Pe.none;return n=n.filter((t=>!(i.type?i.type.allowsMarkType(t.type):Nt(t.type,e))||(l=t.addToSet(l),!1))),this.nodes.push(new Ot(e,t,l,o,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!(!this.isOpen&&!this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=1)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let o=0;o<this.find.length;o++)if(null==this.find[o].pos&&1==e.nodeType&&e.contains(this.find[o].node)){t.compareDocumentPosition(this.find[o].node)&(n?2:4)&&(this.find[o].pos=this.currentPos)}}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,o=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),r=-(n?n.depth+1:0)+(o?0:1),i=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=r;s--)if(i(e-1,s))return!0;return!1}{let e=s>0||0==s&&o?this.nodes[s].type:n&&s>=r?n.node(s-r).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return i(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function At(e,t){return(e.matches||e.msMatchesSelector||e.webkitMatchesSelector||e.mozMatchesSelector).call(e,t)}function Et(e){let t={};for(let n in e)t[n]=e[n];return t}function Nt(e,t){let n=t.schema.nodes;for(let o in n){let r=n[o];if(!r.allowsMarkType(e))continue;let i=[],s=e=>{i.push(e);for(let n=0;n<e.edgeCount;n++){let{type:o,next:r}=e.edge(n);if(o==t)return!0;if(i.indexOf(r)<0&&s(r))return!0}};if(s(r.contentMatch))return!0}}class Dt{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=It(t).createDocumentFragment());let o=n,r=[];return e.forEach((e=>{if(r.length||e.marks.length){let n=0,i=0;for(;n<r.length&&i<e.marks.length;){let t=e.marks[i];if(this.marks[t.type.name]){if(!t.eq(r[n][0])||!1===t.type.spec.spanning)break;n++,i++}else i++}for(;n<r.length;)o=r.pop()[1];for(;i<e.marks.length;){let n=e.marks[i++],s=this.serializeMark(n,e.isInline,t);s&&(r.push([n,o]),o.appendChild(s.dom),o=s.contentDOM||s.dom)}}o.appendChild(this.serializeNodeInner(e,t))})),n}serializeNodeInner(e,t){let{dom:n,contentDOM:o}=zt(It(t),this.nodes[e.type.name](e),null,e.attrs);if(o){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,o)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let o=e.marks.length-1;o>=0;o--){let r=this.serializeMark(e.marks[o],e.isInline,t);r&&((r.contentDOM||r.dom).appendChild(n),n=r.dom)}return n}serializeMark(e,t,n={}){let o=this.marks[e.type.name];return o&&zt(It(n),o(e,t),null,e.attrs)}static renderSpec(e,t,n=null,o){return zt(e,t,n,o)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new Dt(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Rt(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return Rt(e.marks)}}function Rt(e){let t={};for(let n in e){let o=e[n].spec.toDOM;o&&(t[n]=o)}return t}function It(e){return e.document||window.document}const Pt=new WeakMap;function Lt(e){let t=Pt.get(e);return void 0===t&&Pt.set(e,t=function(e){let t=null;function n(e){if(e&&"object"==typeof e)if(Array.isArray(e))if("string"==typeof e[0])t||(t=[]),t.push(e);else for(let t=0;t<e.length;t++)n(e[t]);else for(let t in e)n(e[t])}return n(e),t}(e)),t}function zt(e,t,n,o){if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let r,i=t[0];if("string"!=typeof i)throw new RangeError("Invalid array passed to renderSpec");if(o&&(r=Lt(o))&&r.indexOf(t)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s,l=i.indexOf(" ");l>0&&(n=i.slice(0,l),i=i.slice(l+1));let a=n?e.createElementNS(n,i):e.createElement(i),c=t[1],d=1;if(c&&"object"==typeof c&&null==c.nodeType&&!Array.isArray(c)){d=2;for(let e in c)if(null!=c[e]){let t=e.indexOf(" ");t>0?a.setAttributeNS(e.slice(0,t),e.slice(t+1),c[e]):a.setAttribute(e,c[e])}}for(let h=d;h<t.length;h++){let r=t[h];if(0===r){if(h<t.length-1||h>d)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}{let{dom:t,contentDOM:i}=zt(e,r,n,o);if(a.appendChild(t),i){if(s)throw new RangeError("Multiple content holes");s=i}}}return{dom:a,contentDOM:s}}const Bt=Math.pow(2,16);function Vt(e){return 65535&e}class $t{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class Ht{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&Ht.empty)return Ht.empty}recover(e){let t=0,n=Vt(e);if(!this.inverted)for(let o=0;o<n;o++)t+=this.ranges[3*o+2]-this.ranges[3*o+1];return this.ranges[3*n]+t+function(e){return(e-(65535&e))/Bt}(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let o=0,r=this.inverted?2:1,i=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let l=this.ranges[s]-(this.inverted?o:0);if(l>e)break;let a=this.ranges[s+r],c=this.ranges[s+i],d=l+a;if(e<=d){let r=l+o+((a?e==l?-1:e==d?1:t:t)<0?0:c);if(n)return r;let i=e==(t<0?l:d)?null:s/3+(e-l)*Bt,h=e==l?2:e==d?1:4;return(t<0?e!=l:e!=d)&&(h|=8),new $t(r,h,i)}o+=c-a}return n?e+o:new $t(e+o,0,null)}touches(e,t){let n=0,o=Vt(t),r=this.inverted?2:1,i=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let t=this.ranges[s]-(this.inverted?n:0);if(t>e)break;let l=this.ranges[s+r];if(e<=t+l&&s==3*o)return!0;n+=this.ranges[s+i]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let o=0,r=0;o<this.ranges.length;o+=3){let i=this.ranges[o],s=i-(this.inverted?r:0),l=i+(this.inverted?0:r),a=this.ranges[o+t],c=this.ranges[o+n];e(s,s+a,l,l+c),r+=c-a}}invert(){return new Ht(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?Ht.empty:new Ht(e<0?[0,-e,0]:[0,0,e])}}Ht.empty=new Ht([]);class Ft{constructor(e,t,n=0,o=(e?e.length:0)){this.mirror=t,this.from=n,this.to=o,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new Ft(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let o=e.getMirror(t);this.appendMap(e._maps[t],null!=o&&o<t?n+o:void 0)}}getMirror(e){if(this.mirror)for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let o=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=o&&o>t?n-o-1:void 0)}}invert(){let e=new Ft;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let o=0;for(let r=this.from;r<this.to;r++){let n=this._maps[r].mapResult(e,t);if(null!=n.recover){let t=this.getMirror(r);if(null!=t&&t>r&&t<this.to){r=t,e=this._maps[t].recover(n.recover);continue}}o|=n.delInfo,e=n.pos}return n?e:new $t(e,o,null)}}const jt=Object.create(null);class _t{getMap(){return Ht.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=jt[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in jt)throw new RangeError("Duplicate use of step JSON ID "+e);return jt[e]=t,t.prototype.jsonID=e,t}}class qt{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new qt(e,null)}static fail(e){return new qt(null,e)}static fromReplace(e,t,n,o){try{return qt.ok(e.replace(t,n,o))}catch(r){if(r instanceof Le)return qt.fail(r.message);throw r}}}function Wt(e,t,n){let o=[];for(let r=0;r<e.childCount;r++){let i=e.child(r);i.content.size&&(i=i.copy(Wt(i.content,t,i))),i.isInline&&(i=t(i,n,r)),o.push(i)}return Ne.fromArray(o)}class Kt extends _t{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),o=n.node(n.sharedDepth(this.to)),r=new ze(Wt(t.content,((e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e),o),t.openStart,t.openEnd);return qt.fromReplace(e,this.from,this.to,r)}invert(){return new Ut(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Kt(t.pos,n.pos,this.mark)}merge(e){return e instanceof Kt&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Kt(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new Kt(t.from,t.to,e.markFromJSON(t.mark))}}_t.jsonID("addMark",Kt);class Ut extends _t{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new ze(Wt(t.content,(e=>e.mark(this.mark.removeFromSet(e.marks))),e),t.openStart,t.openEnd);return qt.fromReplace(e,this.from,this.to,n)}invert(){return new Kt(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Ut(t.pos,n.pos,this.mark)}merge(e){return e instanceof Ut&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Ut(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new Ut(t.from,t.to,e.markFromJSON(t.mark))}}_t.jsonID("removeMark",Ut);class Jt extends _t{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return qt.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return qt.fromReplace(e,this.pos,this.pos+1,new ze(Ne.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new Jt(this.pos,t.marks[n]);return new Jt(this.pos,this.mark)}}return new Gt(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Jt(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new Jt(t.pos,e.markFromJSON(t.mark))}}_t.jsonID("addNodeMark",Jt);class Gt extends _t{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return qt.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return qt.fromReplace(e,this.pos,this.pos+1,new ze(Ne.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new Jt(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Gt(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new Gt(t.pos,e.markFromJSON(t.mark))}}_t.jsonID("removeNodeMark",Gt);class Qt extends _t{constructor(e,t,n,o=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=o}apply(e){return this.structure&&Xt(e,this.from,this.to)?qt.fail("Structure replace would overwrite content"):qt.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new Ht([this.from,this.to-this.from,this.slice.size])}invert(e){return new Qt(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new Qt(t.pos,Math.max(t.pos,n.pos),this.slice)}merge(e){if(!(e instanceof Qt)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?ze.empty:new ze(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new Qt(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?ze.empty:new ze(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new Qt(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new Qt(t.from,t.to,ze.fromJSON(e,t.slice),!!t.structure)}}_t.jsonID("replace",Qt);class Yt extends _t{constructor(e,t,n,o,r,i,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=o,this.slice=r,this.insert=i,this.structure=s}apply(e){if(this.structure&&(Xt(e,this.from,this.gapFrom)||Xt(e,this.gapTo,this.to)))return qt.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return qt.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?qt.fromReplace(e,this.from,this.to,n):qt.fail("Content does not fit in gap")}getMap(){return new Ht([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new Yt(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),o=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),r=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||o<t.pos||r>n.pos?null:new Yt(t.pos,n.pos,o,r,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new Yt(t.from,t.to,t.gapFrom,t.gapTo,ze.fromJSON(e,t.slice),t.insert,!!t.structure)}}function Xt(e,t,n){let o=e.resolve(t),r=n-t,i=o.depth;for(;r>0&&i>0&&o.indexAfter(i)==o.node(i).childCount;)i--,r--;if(r>0){let e=o.node(i).maybeChild(o.indexAfter(i));for(;r>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,r--}}return!1}function Zt(e,t,n,o=n.contentMatch,r=!0){let i=e.doc.nodeAt(t),s=[],l=t+1;for(let a=0;a<i.childCount;a++){let t=i.child(a),c=l+t.nodeSize,d=o.matchType(t.type);if(d){o=d;for(let o=0;o<t.marks.length;o++)n.allowsMarkType(t.marks[o].type)||e.step(new Ut(l,c,t.marks[o]));if(r&&t.isText&&"pre"!=n.whitespace){let e,o,r=/\r?\n|\r/g;for(;e=r.exec(t.text);)o||(o=new ze(Ne.from(n.schema.text(" ",n.allowedMarks(t.marks))),0,0)),s.push(new Qt(l+e.index,l+e.index+e[0].length,o))}}else s.push(new Qt(l,c,ze.empty));l=c}if(!o.validEnd){let t=o.fillBefore(Ne.empty,!0);e.replace(l,l,new ze(t,0,0))}for(let a=s.length-1;a>=0;a--)e.step(s[a])}function en(e,t,n){return(0==t||e.canReplace(t,e.childCount))&&(n==e.childCount||e.canReplace(0,n))}function tn(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let o=e.$from.node(n),r=e.$from.index(n),i=e.$to.indexAfter(n);if(n<e.depth&&o.canReplace(r,i,t))return n;if(0==n||o.type.spec.isolating||!en(o,r,i))break}return null}function nn(e,t,n=null,o=e){let r=function(e,t){let{parent:n,startIndex:o,endIndex:r}=e,i=n.contentMatchAt(o).findWrapping(t);if(!i)return null;let s=i.length?i[0]:t;return n.canReplaceWith(o,r,s)?i:null}(e,t),i=r&&function(e,t){let{parent:n,startIndex:o,endIndex:r}=e,i=n.child(o),s=t.contentMatch.findWrapping(i.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let a=o;l&&a<r;a++)l=l.matchType(n.child(a).type);return l&&l.validEnd?s:null}(o,t);return i?r.map(on).concat({type:t,attrs:n}).concat(i.map(on)):null}function on(e){return{type:e,attrs:null}}function rn(e,t,n,o){t.forEach(((r,i)=>{if(r.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(r.text);){let r=e.mapping.slice(o).map(n+1+i+s.index);e.replaceWith(r,r+1,t.type.schema.linebreakReplacement.create())}}}))}function sn(e,t,n,o){t.forEach(((r,i)=>{if(r.type==r.type.schema.linebreakReplacement){let r=e.mapping.slice(o).map(n+1+i);e.replaceWith(r,r+1,t.type.schema.text("\n"))}}))}function ln(e,t,n=1,o){let r=e.resolve(t),i=r.depth-n,s=o&&o[o.length-1]||r.parent;if(i<0||r.parent.type.spec.isolating||!r.parent.canReplace(r.index(),r.parent.childCount)||!s.type.validContent(r.parent.content.cutByIndex(r.index(),r.parent.childCount)))return!1;for(let c=r.depth-1,d=n-2;c>i;c--,d--){let e=r.node(c),t=r.index(c);if(e.type.spec.isolating)return!1;let n=e.content.cutByIndex(t,e.childCount),i=o&&o[d+1];i&&(n=n.replaceChild(0,i.type.create(i.attrs)));let s=o&&o[d]||e;if(!e.canReplace(t+1,e.childCount)||!s.type.validContent(n))return!1}let l=r.indexAfter(i),a=o&&o[0];return r.node(i).canReplaceWith(l,l,a?a.type:r.node(i+1).type)}function an(e,t){let n=e.resolve(t),o=n.index();return cn(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(o,o+1)}function cn(e,t){return!(!e||!t||e.isLeaf||!function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:o}=e.type.schema;for(let r=0;r<t.childCount;r++){let i=t.child(r),s=i.type==o?e.type.schema.nodes.text:i.type;if(n=n.matchType(s),!n)return!1;if(!e.type.allowsMarks(i.marks))return!1}return n.validEnd}(e,t))}function dn(e,t,n=-1){let o=e.resolve(t);for(let r=o.depth;;r--){let e,i,s=o.index(r);if(r==o.depth?(e=o.nodeBefore,i=o.nodeAfter):n>0?(e=o.node(r+1),s++,i=o.node(r).maybeChild(s)):(e=o.node(r).maybeChild(s-1),i=o.node(r+1)),e&&!e.isTextblock&&cn(e,i)&&o.node(r).canReplace(s,s+1))return t;if(0==r)break;t=n<0?o.before(r):o.after(r)}}function hn(e,t,n){let o=e.resolve(t);if(!n.content.size)return t;let r=n.content;for(let i=0;i<n.openStart;i++)r=r.firstChild.content;for(let i=1;i<=(0==n.openStart&&n.size?2:1);i++)for(let e=o.depth;e>=0;e--){let t=e==o.depth?0:o.pos<=(o.start(e+1)+o.end(e+1))/2?-1:1,n=o.index(e)+(t>0?1:0),s=o.node(e),l=!1;if(1==i)l=s.canReplace(n,n,r);else{let e=s.contentMatchAt(n).findWrapping(r.firstChild.type);l=e&&s.canReplaceWith(n,n,e[0])}if(l)return 0==t?o.pos:t<0?o.before(e+1):o.after(e+1)}return null}function pn(e,t,n=t,o=ze.empty){if(t==n&&!o.size)return null;let r=e.resolve(t),i=e.resolve(n);return un(r,i,o)?new Qt(t,n,o):new fn(r,i,o).fit()}function un(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}_t.jsonID("replaceAround",Yt);class fn{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=Ne.empty;for(let o=0;o<=e.depth;o++){let t=e.node(o);this.frontier.push({type:t.type,match:t.contentMatchAt(e.indexAfter(o))})}for(let o=e.depth;o>0;o--)this.placed=Ne.from(e.node(o).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,o=this.close(e<0?this.$to:n.doc.resolve(e));if(!o)return null;let r=this.placed,i=n.depth,s=o.depth;for(;i&&s&&1==r.childCount;)r=r.firstChild.content,i--,s--;let l=new ze(r,i,s);return e>-1?new Yt(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new Qt(n.pos,o.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,o=this.unplaced.openEnd;n<e;n++){let r=t.firstChild;if(t.childCount>1&&(o=0),r.type.spec.isolating&&o<=n){e=n;break}t=r.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,o=null;n?(o=yn(this.unplaced.content,n-1).firstChild,e=o.content):e=this.unplaced.content;let r=e.firstChild;for(let i=this.depth;i>=0;i--){let e,{type:s,match:l}=this.frontier[i],a=null;if(1==t&&(r?l.matchType(r.type)||(a=l.fillBefore(Ne.from(r),!1)):o&&s.compatibleContent(o.type)))return{sliceDepth:n,frontierDepth:i,parent:o,inject:a};if(2==t&&r&&(e=l.findWrapping(r.type)))return{sliceDepth:n,frontierDepth:i,parent:o,wrap:e};if(o&&l.matchType(o.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,o=yn(e,t);return!(!o.childCount||o.firstChild.isLeaf)&&(this.unplaced=new ze(e,t+1,Math.max(n,o.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,o=yn(e,t);if(o.childCount<=1&&t>0){let r=e.size-t<=t+o.size;this.unplaced=new ze(mn(e,t-1,1),t-1,r?t-1:n)}else this.unplaced=new ze(mn(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:o,wrap:r}){for(;this.depth>t;)this.closeFrontierNode();if(r)for(let f=0;f<r.length;f++)this.openFrontierNode(r[f]);let i=this.unplaced,s=n?n.content:i.content,l=i.openStart-e,a=0,c=[],{match:d,type:h}=this.frontier[t];if(o){for(let e=0;e<o.childCount;e++)c.push(o.child(e));d=d.matchFragment(o)}let p=s.size+e-(i.content.size-i.openEnd);for(;a<s.childCount;){let e=s.child(a),t=d.matchType(e.type);if(!t)break;a++,(a>1||0==l||e.content.size)&&(d=t,c.push(vn(e.mark(h.allowedMarks(e.marks)),1==a?l:0,a==s.childCount?p:-1)))}let u=a==s.childCount;u||(p=-1),this.placed=gn(this.placed,t,Ne.from(c)),this.frontier[t].match=d,u&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let f=0,m=s;f<p;f++){let e=m.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),m=e.content}this.unplaced=u?0==e?ze.empty:new ze(mn(i.content,e-1,1),e-1,p<0?i.openEnd:e-1):new ze(mn(i.content,e,a),i.openStart,i.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e,t=this.frontier[this.depth];if(!t.type.isTextblock||!wn(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return-1;let{depth:n}=this.$to,o=this.$to.after(n);for(;n>1&&o==this.$to.end(--n);)++o;return o}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:o}=this.frontier[t],r=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),i=wn(e,t,o,n,r);if(i){for(let n=t-1;n>=0;n--){let{match:t,type:o}=this.frontier[n],r=wn(e,n,o,t,!0);if(!r||r.childCount)continue e}return{depth:t,fit:i,move:r?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=gn(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),o=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,o)}return e}openFrontierNode(e,t=null,n){let o=this.frontier[this.depth];o.match=o.match.matchType(e),this.placed=gn(this.placed,this.depth,Ne.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(Ne.empty,!0);e.childCount&&(this.placed=gn(this.placed,this.frontier.length,e))}}function mn(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(mn(e.firstChild.content,t-1,n)))}function gn(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(gn(e.lastChild.content,t-1,n)))}function yn(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function vn(e,t,n){if(t<=0)return e;let o=e.content;return t>1&&(o=o.replaceChild(0,vn(o.firstChild,t-1,1==o.childCount?n-1:0))),t>0&&(o=e.type.contentMatch.fillBefore(o).append(o),n<=0&&(o=o.append(e.type.contentMatch.matchFragment(o).fillBefore(Ne.empty,!0)))),e.copy(o)}function wn(e,t,n,o,r){let i=e.node(t),s=r?e.indexAfter(t):e.index(t);if(s==i.childCount&&!n.compatibleContent(i.type))return null;let l=o.fillBefore(i.content,!0,s);return l&&!function(e,t,n){for(let o=n;o<t.childCount;o++)if(!e.allowsMarks(t.child(o).marks))return!0;return!1}(n,i.content,s)?l:null}function bn(e,t,n,o,r){if(t<n){let r=e.firstChild;e=e.replaceChild(0,r.copy(bn(r.content,t+1,n,o,r)))}if(t>o){let t=r.contentMatchAt(0),n=t.fillBefore(e).append(e);e=n.append(t.matchFragment(n).fillBefore(Ne.empty,!0))}return e}function kn(e,t){let n=[];for(let o=Math.min(e.depth,t.depth);o>=0;o--){let r=e.start(o);if(r<e.pos-(e.depth-o)||t.end(o)>t.pos+(t.depth-o)||e.node(o).type.spec.isolating||t.node(o).type.spec.isolating)break;(r==t.start(o)||o==e.depth&&o==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&o&&t.start(o-1)==r-1)&&n.push(o)}return n}class xn extends _t{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return qt.fail("No node at attribute step's position");let n=Object.create(null);for(let r in t.attrs)n[r]=t.attrs[r];n[this.attr]=this.value;let o=t.type.create(n,null,t.marks);return qt.fromReplace(e,this.pos,this.pos+1,new ze(Ne.from(o),0,t.isLeaf?0:1))}getMap(){return Ht.empty}invert(e){return new xn(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new xn(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new xn(t.pos,t.attr,t.value)}}_t.jsonID("attr",xn);class Sn extends _t{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let o in e.attrs)t[o]=e.attrs[o];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return qt.ok(n)}getMap(){return Ht.empty}invert(e){return new Sn(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Sn(t.attr,t.value)}}_t.jsonID("docAttr",Sn);let Mn=class extends Error{};Mn=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n},(Mn.prototype=Object.create(Error.prototype)).constructor=Mn,Mn.prototype.name="TransformError";class Cn{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Ft}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new Mn(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=ze.empty){let o=pn(this.doc,e,t,n);return o&&this.step(o),this}replaceWith(e,t,n){return this.replace(e,t,new ze(Ne.from(n),0,0))}delete(e,t){return this.replace(e,t,ze.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function(e,t,n,o){if(!o.size)return e.deleteRange(t,n);let r=e.doc.resolve(t),i=e.doc.resolve(n);if(un(r,i,o))return e.step(new Qt(t,n,o));let s=kn(r,e.doc.resolve(n));0==s[s.length-1]&&s.pop();let l=-(r.depth+1);s.unshift(l);for(let u=r.depth,f=r.pos-1;u>0;u--,f--){let e=r.node(u).type.spec;if(e.defining||e.definingAsContext||e.isolating)break;s.indexOf(u)>-1?l=u:r.before(u)==f&&s.splice(1,0,-u)}let a=s.indexOf(l),c=[],d=o.openStart;for(let u=o.content,f=0;;f++){let e=u.firstChild;if(c.push(e),f==o.openStart)break;u=e.content}for(let u=d-1;u>=0;u--){let e=c[u],t=(h=e.type).spec.defining||h.spec.definingForContent;if(t&&!e.sameMarkup(r.node(Math.abs(l)-1)))d=u;else if(t||!e.type.isTextblock)break}var h;for(let u=o.openStart;u>=0;u--){let t=(u+d+1)%(o.openStart+1),l=c[t];if(l)for(let c=0;c<s.length;c++){let d=s[(c+a)%s.length],h=!0;d<0&&(h=!1,d=-d);let p=r.node(d-1),u=r.index(d-1);if(p.canReplaceWith(u,u,l.type,l.marks))return e.replace(r.before(d),h?i.after(d):n,new ze(bn(o.content,0,o.openStart,t),t,o.openEnd))}}let p=e.steps.length;for(let u=s.length-1;u>=0&&(e.replace(t,n,o),!(e.steps.length>p));u--){let e=s[u];e<0||(t=r.before(e),n=i.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function(e,t,n,o){if(!o.isInline&&t==n&&e.doc.resolve(t).parent.content.size){let r=function(e,t,n){let o=e.resolve(t);if(o.parent.canReplaceWith(o.index(),o.index(),n))return t;if(0==o.parentOffset)for(let r=o.depth-1;r>=0;r--){let e=o.index(r);if(o.node(r).canReplaceWith(e,e,n))return o.before(r+1);if(e>0)return null}if(o.parentOffset==o.parent.content.size)for(let r=o.depth-1;r>=0;r--){let e=o.indexAfter(r);if(o.node(r).canReplaceWith(e,e,n))return o.after(r+1);if(e<o.node(r).childCount)return null}return null}(e.doc,t,o.type);null!=r&&(t=n=r)}e.replaceRange(t,n,new ze(Ne.from(o),0,0))}(this,e,t,n),this}deleteRange(e,t){return function(e,t,n){let o=e.doc.resolve(t),r=e.doc.resolve(n),i=kn(o,r);for(let s=0;s<i.length;s++){let t=i[s],n=s==i.length-1;if(n&&0==t||o.node(t).type.contentMatch.validEnd)return e.delete(o.start(t),r.end(t));if(t>0&&(n||o.node(t-1).canReplace(o.index(t-1),r.indexAfter(t-1))))return e.delete(o.before(t),r.after(t))}for(let s=1;s<=o.depth&&s<=r.depth;s++)if(t-o.start(s)==o.depth-s&&n>o.end(s)&&r.end(s)-n!=r.depth-s&&o.start(s-1)==r.start(s-1)&&o.node(s-1).canReplace(o.index(s-1),r.index(s-1)))return e.delete(o.before(s),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return function(e,t,n){let{$from:o,$to:r,depth:i}=t,s=o.before(i+1),l=r.after(i+1),a=s,c=l,d=Ne.empty,h=0;for(let f=i,m=!1;f>n;f--)m||o.index(f)>0?(m=!0,d=Ne.from(o.node(f).copy(d)),h++):a--;let p=Ne.empty,u=0;for(let f=i,m=!1;f>n;f--)m||r.after(f+1)<r.end(f)?(m=!0,p=Ne.from(r.node(f).copy(p)),u++):c++;e.step(new Yt(a,c,s,l,new ze(d.append(p),h,u),d.size-h,!0))}(this,e,t),this}join(e,t=1){return function(e,t,n){let o=null,{linebreakReplacement:r}=e.doc.type.schema,i=e.doc.resolve(t-n),s=i.node().type;if(r&&s.inlineContent){let e="pre"==s.whitespace,t=!!s.contentMatch.matchType(r);e&&!t?o=!1:!e&&t&&(o=!0)}let l=e.steps.length;if(!1===o){let o=e.doc.resolve(t+n);sn(e,o.node(),o.before(),l)}s.inlineContent&&Zt(e,t+n-1,s,i.node().contentMatchAt(i.index()),null==o);let a=e.mapping.slice(l),c=a.map(t-n);if(e.step(new Qt(c,a.map(t+n,-1),ze.empty,!0)),!0===o){let t=e.doc.resolve(c);rn(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return function(e,t,n){let o=Ne.empty;for(let s=n.length-1;s>=0;s--){if(o.size){let e=n[s].type.contentMatch.matchFragment(o);if(!e||!e.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}o=Ne.from(n[s].type.create(n[s].attrs,o))}let r=t.start,i=t.end;e.step(new Yt(r,i,r,i,new ze(o,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,o=null){return function(e,t,n,o,r){if(!o.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let i=e.steps.length;e.doc.nodesBetween(t,n,((t,n)=>{let s="function"==typeof r?r(t):r;if(t.isTextblock&&!t.hasMarkup(o,s)&&function(e,t,n){let o=e.resolve(t),r=o.index();return o.parent.canReplaceWith(r,r+1,n)}(e.doc,e.mapping.slice(i).map(n),o)){let r=null;if(o.schema.linebreakReplacement){let e="pre"==o.whitespace,t=!!o.contentMatch.matchType(o.schema.linebreakReplacement);e&&!t?r=!1:!e&&t&&(r=!0)}!1===r&&sn(e,t,n,i),Zt(e,e.mapping.slice(i).map(n,1),o,void 0,null===r);let l=e.mapping.slice(i),a=l.map(n,1),c=l.map(n+t.nodeSize,1);return e.step(new Yt(a,c,a+1,c-1,new ze(Ne.from(o.create(s,null,t.marks)),0,0),1,!0)),!0===r&&rn(e,t,n,i),!1}}))}(this,e,t,n,o),this}setNodeMarkup(e,t,n=null,o){return function(e,t,n,o,r){let i=e.doc.nodeAt(t);if(!i)throw new RangeError("No node at given position");n||(n=i.type);let s=n.create(o,null,r||i.marks);if(i.isLeaf)return e.replaceWith(t,t+i.nodeSize,s);if(!n.validContent(i.content))throw new RangeError("Invalid content for node type "+n.name);e.step(new Yt(t,t+i.nodeSize,t+1,t+i.nodeSize-1,new ze(Ne.from(s),0,0),1,!0))}(this,e,t,n,o),this}setNodeAttribute(e,t,n){return this.step(new xn(e,t,n)),this}setDocAttribute(e,t){return this.step(new Sn(e,t)),this}addNodeMark(e,t){return this.step(new Jt(e,t)),this}removeNodeMark(e,t){if(!(t instanceof Pe)){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(!(t=t.isInSet(n.marks)))return this}return this.step(new Gt(e,t)),this}split(e,t=1,n){return function(e,t,n=1,o){let r=e.doc.resolve(t),i=Ne.empty,s=Ne.empty;for(let l=r.depth,a=r.depth-n,c=n-1;l>a;l--,c--){i=Ne.from(r.node(l).copy(i));let e=o&&o[c];s=Ne.from(e?e.type.create(e.attrs,s):r.node(l).copy(s))}e.step(new Qt(t,t,new ze(i.append(s),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){return function(e,t,n,o){let r,i,s=[],l=[];e.doc.nodesBetween(t,n,((e,a,c)=>{if(!e.isInline)return;let d=e.marks;if(!o.isInSet(d)&&c.type.allowsMarkType(o.type)){let c=Math.max(a,t),h=Math.min(a+e.nodeSize,n),p=o.addToSet(d);for(let e=0;e<d.length;e++)d[e].isInSet(p)||(r&&r.to==c&&r.mark.eq(d[e])?r.to=h:s.push(r=new Ut(c,h,d[e])));i&&i.to==c?i.to=h:l.push(i=new Kt(c,h,o))}})),s.forEach((t=>e.step(t))),l.forEach((t=>e.step(t)))}(this,e,t,n),this}removeMark(e,t,n){return function(e,t,n,o){let r=[],i=0;e.doc.nodesBetween(t,n,((e,s)=>{if(!e.isInline)return;i++;let l=null;if(o instanceof vt){let t,n=e.marks;for(;t=o.isInSet(n);)(l||(l=[])).push(t),n=t.removeFromSet(n)}else o?o.isInSet(e.marks)&&(l=[o]):l=e.marks;if(l&&l.length){let o=Math.min(s+e.nodeSize,n);for(let e=0;e<l.length;e++){let n,a=l[e];for(let e=0;e<r.length;e++){let t=r[e];t.step==i-1&&a.eq(r[e].style)&&(n=t)}n?(n.to=o,n.step=i):r.push({style:a,from:Math.max(s,t),to:o,step:i})}}})),r.forEach((t=>e.step(new Ut(t.from,t.to,t.style))))}(this,e,t,n),this}clearIncompatible(e,t,n){return Zt(this,e,t,n),this}}const On=Object.create(null);class Tn{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new An(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=ze.empty){let n=t.content.lastChild,o=null;for(let s=0;s<t.openEnd;s++)o=n,n=n.lastChild;let r=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:l,$to:a}=i[s],c=e.mapping.slice(r);e.replaceRange(c.map(l.pos),c.map(a.pos),s?ze.empty:t),0==s&&Vn(e,r,(n?n.isInline:o&&o.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,o=this.ranges;for(let r=0;r<o.length;r++){let{$from:i,$to:s}=o[r],l=e.mapping.slice(n),a=l.map(i.pos),c=l.map(s.pos);r?e.deleteRange(a,c):(e.replaceRangeWith(a,c,t),Vn(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let o=e.parent.inlineContent?new Dn(e):Bn(e.node(0),e.parent,e.pos,e.index(),t,n);if(o)return o;for(let r=e.depth-1;r>=0;r--){let o=t<0?Bn(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):Bn(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(o)return o}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new Ln(e.node(0))}static atStart(e){return Bn(e,e,0,0,1)||new Ln(e)}static atEnd(e){return Bn(e,e,e.content.size,e.childCount,-1)||new Ln(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=On[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in On)throw new RangeError("Duplicate use of selection JSON ID "+e);return On[e]=t,t.prototype.jsonID=e,t}getBookmark(){return Dn.between(this.$anchor,this.$head).getBookmark()}}Tn.prototype.visible=!0;class An{constructor(e,t){this.$from=e,this.$to=t}}let En=!1;function Nn(e){En||e.parent.inlineContent||(En=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class Dn extends Tn{constructor(e,t=e){Nn(e),Nn(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return Tn.near(n);let o=e.resolve(t.map(this.anchor));return new Dn(o.parent.inlineContent?o:n,n)}replace(e,t=ze.empty){if(super.replace(e,t),t==ze.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof Dn&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Rn(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new Dn(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let o=e.resolve(t);return new this(o,n==t?o:e.resolve(n))}static between(e,t,n){let o=e.pos-t.pos;if(n&&!o||(n=o>=0?1:-1),!t.parent.inlineContent){let e=Tn.findFrom(t,n,!0)||Tn.findFrom(t,-n,!0);if(!e)return Tn.near(t,n);t=e.$head}return e.parent.inlineContent||(0==o||(e=(Tn.findFrom(e,-n,!0)||Tn.findFrom(e,n,!0)).$anchor).pos<t.pos!=o<0)&&(e=t),new Dn(e,t)}}Tn.jsonID("text",Dn);class Rn{constructor(e,t){this.anchor=e,this.head=t}map(e){return new Rn(e.map(this.anchor),e.map(this.head))}resolve(e){return Dn.between(e.resolve(this.anchor),e.resolve(this.head))}}class In extends Tn{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:o}=t.mapResult(this.anchor),r=e.resolve(o);return n?Tn.near(r):new In(r)}content(){return new ze(Ne.from(this.node),0,0)}eq(e){return e instanceof In&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new Pn(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new In(e.resolve(t.anchor))}static create(e,t){return new In(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}In.prototype.visible=!1,Tn.jsonID("node",In);class Pn{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new Rn(n,n):new Pn(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&In.isSelectable(n)?new In(t):Tn.near(t)}}class Ln extends Tn{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=ze.empty){if(t==ze.empty){e.delete(0,e.doc.content.size);let t=Tn.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new Ln(e)}map(e){return new Ln(e)}eq(e){return e instanceof Ln}getBookmark(){return zn}}Tn.jsonID("all",Ln);const zn={map(){return this},resolve:e=>new Ln(e)};function Bn(e,t,n,o,r,i=!1){if(t.inlineContent)return Dn.create(e,n);for(let s=o-(r>0?0:1);r>0?s<t.childCount:s>=0;s+=r){let o=t.child(s);if(o.isAtom){if(!i&&In.isSelectable(o))return In.create(e,n-(r<0?o.nodeSize:0))}else{let t=Bn(e,o,n+r,r<0?o.childCount:0,r,i);if(t)return t}n+=o.nodeSize*r}return null}function Vn(e,t,n){let o=e.steps.length-1;if(o<t)return;let r,i=e.steps[o];(i instanceof Qt||i instanceof Yt)&&(e.mapping.maps[o].forEach(((e,t,n,o)=>{null==r&&(r=o)})),e.setSelection(Tn.near(e.doc.resolve(r),n)))}class $n extends Cn{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return Pe.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||Pe.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let o=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(o.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let r=this.storedMarks;if(!r){let e=this.doc.resolve(t);r=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,o.text(e,r)),this.selection.empty||this.setSelection(Tn.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function Hn(e,t){return t&&e?e.bind(t):e}class Fn{constructor(e,t,n){this.name=e,this.init=Hn(t.init,n),this.apply=Hn(t.apply,n)}}const jn=[new Fn("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new Fn("selection",{init:(e,t)=>e.selection||Tn.atStart(t.doc),apply:e=>e.selection}),new Fn("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,o)=>o.selection.$cursor?e.storedMarks:null}),new Fn("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class _n{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=jn.slice(),t&&t.forEach((e=>{if(this.pluginsByKey[e.key])throw new RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new Fn(e.key,e.spec.state,e))}))}}class qn{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),o=null;for(;;){let r=!1;for(let i=0;i<this.config.plugins.length;i++){let s=this.config.plugins[i];if(s.spec.appendTransaction){let l=o?o[i].n:0,a=o?o[i].state:this,c=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(c&&n.filterTransaction(c,i)){if(c.setMeta("appendedTransaction",e),!o){o=[];for(let e=0;e<this.config.plugins.length;e++)o.push(e<i?{state:n,n:t.length}:{state:this,n:0})}t.push(c),n=n.applyInner(c),r=!0}o&&(o[i]={state:n,n:t.length})}}if(!r)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new qn(this.config),n=this.config.fields;for(let o=0;o<n.length;o++){let r=n[o];t[r.name]=r.apply(e,this[r.name],this,t)}return t}get tr(){return new $n(this)}static create(e){let t=new _n(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new qn(t);for(let o=0;o<t.fields.length;o++)n[t.fields[o].name]=t.fields[o].init(e,n);return n}reconfigure(e){let t=new _n(this.schema,e.plugins),n=t.fields,o=new qn(t);for(let r=0;r<n.length;r++){let t=n[r].name;o[t]=this.hasOwnProperty(t)?this[t]:n[r].init(e,o)}return o}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map((e=>e.toJSON()))),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let o=e[n],r=o.spec.state;r&&r.toJSON&&(t[n]=r.toJSON.call(o,this[o.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let o=new _n(e.schema,e.plugins),r=new qn(o);return o.fields.forEach((o=>{if("doc"==o.name)r.doc=et.fromJSON(e.schema,t.doc);else if("selection"==o.name)r.selection=Tn.fromJSON(r.doc,t.selection);else if("storedMarks"==o.name)t.storedMarks&&(r.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let i in n){let s=n[i],l=s.spec.state;if(s.key==o.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,i))return void(r[o.name]=l.fromJSON.call(s,e,t[i],r))}r[o.name]=o.init(e,r)}})),r}}function Wn(e,t,n){for(let o in e){let r=e[o];r instanceof Function?r=r.bind(t):"handleDOMEvents"==o&&(r=Wn(r,t,{})),n[o]=r}return n}class Kn{constructor(e){this.spec=e,this.props={},e.props&&Wn(e.props,this,this.props),this.key=e.key?e.key.key:Jn("plugin")}getState(e){return e[this.key]}}const Un=Object.create(null);function Jn(e){return e in Un?e+"$"+ ++Un[e]:(Un[e]=0,e+"$")}class Gn{constructor(e="key"){this.key=Jn(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const Qn=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},Yn=function(e){let t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t};let Xn=null;const Zn=function(e,t,n){let o=Xn||(Xn=document.createRange());return o.setEnd(e,null==n?e.nodeValue.length:n),o.setStart(e,t||0),o},eo=function(e,t,n,o){return n&&(no(e,t,n,o,-1)||no(e,t,n,o,1))},to=/^(img|br|input|textarea|hr)$/i;function no(e,t,n,o,r){for(;;){if(e==n&&t==o)return!0;if(t==(r<0?0:oo(e))){let n=e.parentNode;if(!n||1!=n.nodeType||ro(e)||to.test(e.nodeName)||"false"==e.contentEditable)return!1;t=Qn(e)+(r<0?0:1),e=n}else{if(1!=e.nodeType)return!1;if("false"==(e=e.childNodes[t+(r<0?-1:0)]).contentEditable)return!1;t=r<0?oo(e):0}}}function oo(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function ro(e){let t;for(let n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}const io=function(e){return e.focusNode&&eo(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)};function so(e,t){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}const lo="undefined"!=typeof navigator?navigator:null,ao="undefined"!=typeof document?document:null,co=lo&&lo.userAgent||"",ho=/Edge\/(\d+)/.exec(co),po=/MSIE \d/.exec(co),uo=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(co),fo=!!(po||uo||ho),mo=po?document.documentMode:uo?+uo[1]:ho?+ho[1]:0,go=!fo&&/gecko\/(\d+)/i.test(co);go&&(/Firefox\/(\d+)/.exec(co)||[0,0])[1];const yo=!fo&&/Chrome\/(\d+)/.exec(co),vo=!!yo,wo=yo?+yo[1]:0,bo=!fo&&!!lo&&/Apple Computer/.test(lo.vendor),ko=bo&&(/Mobile\/\w+/.test(co)||!!lo&&lo.maxTouchPoints>2),xo=ko||!!lo&&/Mac/.test(lo.platform),So=!!lo&&/Win/.test(lo.platform),Mo=/Android \d/.test(co),Co=!!ao&&"webkitFontSmoothing"in ao.documentElement.style,Oo=Co?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function To(e){let t=e.defaultView&&e.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}function Ao(e,t){return"number"==typeof e?e:e[t]}function Eo(e){let t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,o=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*o}}function No(e,t,n){let o=e.someProp("scrollThreshold")||0,r=e.someProp("scrollMargin")||5,i=e.dom.ownerDocument;for(let s=n||e.dom;s;){if(1!=s.nodeType){s=Yn(s);continue}let e=s,n=e==i.body,l=n?To(i):Eo(e),a=0,c=0;if(t.top<l.top+Ao(o,"top")?c=-(l.top-t.top+Ao(r,"top")):t.bottom>l.bottom-Ao(o,"bottom")&&(c=t.bottom-t.top>l.bottom-l.top?t.top+Ao(r,"top")-l.top:t.bottom-l.bottom+Ao(r,"bottom")),t.left<l.left+Ao(o,"left")?a=-(l.left-t.left+Ao(r,"left")):t.right>l.right-Ao(o,"right")&&(a=t.right-l.right+Ao(r,"right")),a||c)if(n)i.defaultView.scrollBy(a,c);else{let n=e.scrollLeft,o=e.scrollTop;c&&(e.scrollTop+=c),a&&(e.scrollLeft+=a);let r=e.scrollLeft-n,i=e.scrollTop-o;t={left:t.left-r,top:t.top-i,right:t.right-r,bottom:t.bottom-i}}let d=n?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(d))break;s="absolute"==d?s.offsetParent:Yn(s)}}function Do(e){let t=[],n=e.ownerDocument;for(let o=e;o&&(t.push({dom:o,top:o.scrollTop,left:o.scrollLeft}),e!=n);o=Yn(o));return t}function Ro(e,t){for(let n=0;n<e.length;n++){let{dom:o,top:r,left:i}=e[n];o.scrollTop!=r+t&&(o.scrollTop=r+t),o.scrollLeft!=i&&(o.scrollLeft=i)}}let Io=null;function Po(e,t){let n,o,r,i,s=2e8,l=0,a=t.top,c=t.top;for(let d=e.firstChild,h=0;d;d=d.nextSibling,h++){let e;if(1==d.nodeType)e=d.getClientRects();else{if(3!=d.nodeType)continue;e=Zn(d).getClientRects()}for(let p=0;p<e.length;p++){let u=e[p];if(u.top<=a&&u.bottom>=c){a=Math.max(u.bottom,a),c=Math.min(u.top,c);let e=u.left>t.left?u.left-t.left:u.right<t.left?t.left-u.right:0;if(e<s){n=d,s=e,o=e&&3==n.nodeType?{left:u.right<t.left?u.right:u.left,top:t.top}:t,1==d.nodeType&&e&&(l=h+(t.left>=(u.left+u.right)/2?1:0));continue}}else u.top>t.top&&!r&&u.left<=t.left&&u.right>=t.left&&(r=d,i={left:Math.max(u.left,Math.min(u.right,t.left)),top:u.top});!n&&(t.left>=u.right&&t.top>=u.top||t.left>=u.left&&t.top>=u.bottom)&&(l=h+1)}}return!n&&r&&(n=r,o=i,s=0),n&&3==n.nodeType?function(e,t){let n=e.nodeValue.length,o=document.createRange();for(let r=0;r<n;r++){o.setEnd(e,r+1),o.setStart(e,r);let n=$o(o,1);if(n.top!=n.bottom&&Lo(t,n))return{node:e,offset:r+(t.left>=(n.left+n.right)/2?1:0)}}return{node:e,offset:0}}(n,o):!n||s&&1==n.nodeType?{node:e,offset:l}:Po(n,o)}function Lo(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function zo(e,t,n){let o=e.childNodes.length;if(o&&n.top<n.bottom)for(let r=Math.max(0,Math.min(o-1,Math.floor(o*(t.top-n.top)/(n.bottom-n.top))-2)),i=r;;){let n=e.childNodes[i];if(1==n.nodeType){let e=n.getClientRects();for(let o=0;o<e.length;o++){let r=e[o];if(Lo(t,r))return zo(n,t,r)}}if((i=(i+1)%o)==r)break}return e}function Bo(e,t){let n,o=e.dom.ownerDocument,r=0,i=function(e,t,n){if(e.caretPositionFromPoint)try{let o=e.caretPositionFromPoint(t,n);if(o)return{node:o.offsetNode,offset:Math.min(oo(o.offsetNode),o.offset)}}catch(o){}if(e.caretRangeFromPoint){let o=e.caretRangeFromPoint(t,n);if(o)return{node:o.startContainer,offset:Math.min(oo(o.startContainer),o.startOffset)}}}(o,t.left,t.top);i&&({node:n,offset:r}=i);let s,l=(e.root.elementFromPoint?e.root:o).elementFromPoint(t.left,t.top);if(!l||!e.dom.contains(1!=l.nodeType?l.parentNode:l)){let n=e.dom.getBoundingClientRect();if(!Lo(t,n))return null;if(l=zo(e.dom,t,n),!l)return null}if(bo)for(let c=l;n&&c;c=Yn(c))c.draggable&&(n=void 0);if(l=function(e,t){let n=e.parentNode;return n&&/^li$/i.test(n.nodeName)&&t.left<e.getBoundingClientRect().left?n:e}(l,t),n){if(go&&1==n.nodeType&&(r=Math.min(r,n.childNodes.length),r<n.childNodes.length)){let e,o=n.childNodes[r];"IMG"==o.nodeName&&(e=o.getBoundingClientRect()).right<=t.left&&e.bottom>t.top&&r++}let o;Co&&r&&1==n.nodeType&&1==(o=n.childNodes[r-1]).nodeType&&"false"==o.contentEditable&&o.getBoundingClientRect().top>=t.top&&r--,n==e.dom&&r==n.childNodes.length-1&&1==n.lastChild.nodeType&&t.top>n.lastChild.getBoundingClientRect().bottom?s=e.state.doc.content.size:0!=r&&1==n.nodeType&&"BR"==n.childNodes[r-1].nodeName||(s=function(e,t,n,o){let r=-1;for(let i=t,s=!1;i!=e.dom;){let t,n=e.docView.nearestDesc(i,!0);if(!n)return null;if(1==n.dom.nodeType&&(n.node.isBlock&&n.parent||!n.contentDOM)&&((t=n.dom.getBoundingClientRect()).width||t.height)&&(n.node.isBlock&&n.parent&&(!s&&t.left>o.left||t.top>o.top?r=n.posBefore:(!s&&t.right<o.left||t.bottom<o.top)&&(r=n.posAfter),s=!0),!n.contentDOM&&r<0&&!n.node.isText))return(n.node.isBlock?o.top<(t.top+t.bottom)/2:o.left<(t.left+t.right)/2)?n.posBefore:n.posAfter;i=n.dom.parentNode}return r>-1?r:e.docView.posFromDOM(t,n,-1)}(e,n,r,t))}null==s&&(s=function(e,t,n){let{node:o,offset:r}=Po(t,n),i=-1;if(1==o.nodeType&&!o.firstChild){let e=o.getBoundingClientRect();i=e.left!=e.right&&n.left>(e.left+e.right)/2?1:-1}return e.docView.posFromDOM(o,r,i)}(e,l,t));let a=e.docView.nearestDesc(l,!0);return{pos:s,inside:a?a.posAtStart-a.border:-1}}function Vo(e){return e.top<e.bottom||e.left<e.right}function $o(e,t){let n=e.getClientRects();if(n.length){let e=n[t<0?0:n.length-1];if(Vo(e))return e}return Array.prototype.find.call(n,Vo)||e.getBoundingClientRect()}const Ho=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Fo(e,t,n){let{node:o,offset:r,atom:i}=e.docView.domFromPos(t,n<0?-1:1),s=Co||go;if(3==o.nodeType){if(!s||!Ho.test(o.nodeValue)&&(n<0?r:r!=o.nodeValue.length)){let e=r,t=r,i=n<0?1:-1;return n<0&&!r?(t++,i=-1):n>=0&&r==o.nodeValue.length?(e--,i=1):n<0?e--:t++,jo($o(Zn(o,e,t),i),i<0)}{let e=$o(Zn(o,r,r),n);if(go&&r&&/\s/.test(o.nodeValue[r-1])&&r<o.nodeValue.length){let t=$o(Zn(o,r-1,r-1),-1);if(t.top==e.top){let n=$o(Zn(o,r,r+1),-1);if(n.top!=e.top)return jo(n,n.left<t.left)}}return e}}if(!e.state.doc.resolve(t-(i||0)).parent.inlineContent){if(null==i&&r&&(n<0||r==oo(o))){let e=o.childNodes[r-1];if(1==e.nodeType)return _o(e.getBoundingClientRect(),!1)}if(null==i&&r<oo(o)){let e=o.childNodes[r];if(1==e.nodeType)return _o(e.getBoundingClientRect(),!0)}return _o(o.getBoundingClientRect(),n>=0)}if(null==i&&r&&(n<0||r==oo(o))){let e=o.childNodes[r-1],t=3==e.nodeType?Zn(e,oo(e)-(s?0:1)):1!=e.nodeType||"BR"==e.nodeName&&e.nextSibling?null:e;if(t)return jo($o(t,1),!1)}if(null==i&&r<oo(o)){let e=o.childNodes[r];for(;e.pmViewDesc&&e.pmViewDesc.ignoreForCoords;)e=e.nextSibling;let t=e?3==e.nodeType?Zn(e,0,s?0:1):1==e.nodeType?e:null:null;if(t)return jo($o(t,-1),!0)}return jo($o(3==o.nodeType?Zn(o):o,-n),n>=0)}function jo(e,t){if(0==e.width)return e;let n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function _o(e,t){if(0==e.height)return e;let n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function qo(e,t,n){let o=e.state,r=e.root.activeElement;o!=t&&e.updateState(t),r!=e.dom&&e.focus();try{return n()}finally{o!=t&&e.updateState(o),r!=e.dom&&r&&r.focus()}}const Wo=/[\u0590-\u08ac]/;let Ko=null,Uo=null,Jo=!1;function Go(e,t,n){return Ko==t&&Uo==n?Jo:(Ko=t,Uo=n,Jo="up"==n||"down"==n?function(e,t,n){let o=t.selection,r="up"==n?o.$from:o.$to;return qo(e,t,(()=>{let{node:t}=e.docView.domFromPos(r.pos,"up"==n?-1:1);for(;;){let n=e.docView.nearestDesc(t,!0);if(!n)break;if(n.node.isBlock){t=n.contentDOM||n.dom;break}t=n.dom.parentNode}let o=Fo(e,r.pos,1);for(let e=t.firstChild;e;e=e.nextSibling){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=Zn(e,0,e.nodeValue.length).getClientRects()}for(let e=0;e<t.length;e++){let r=t[e];if(r.bottom>r.top+1&&("up"==n?o.top-r.top>2*(r.bottom-o.top):r.bottom-o.bottom>2*(o.bottom-r.top)))return!1}}return!0}))}(e,t,n):function(e,t,n){let{$head:o}=t.selection;if(!o.parent.isTextblock)return!1;let r=o.parentOffset,i=!r,s=r==o.parent.content.size,l=e.domSelection();return l?Wo.test(o.parent.textContent)&&l.modify?qo(e,t,(()=>{let{focusNode:t,focusOffset:r,anchorNode:i,anchorOffset:s}=e.domSelectionRange(),a=l.caretBidiLevel;l.modify("move",n,"character");let c=o.depth?e.docView.domAfterPos(o.before()):e.dom,{focusNode:d,focusOffset:h}=e.domSelectionRange(),p=d&&!c.contains(1==d.nodeType?d:d.parentNode)||t==d&&r==h;try{l.collapse(i,s),t&&(t!=i||r!=s)&&l.extend&&l.extend(t,r)}catch(u){}return null!=a&&(l.caretBidiLevel=a),p})):"left"==n||"backward"==n?i:s:o.pos==o.start()||o.pos==o.end()}(e,t,n))}class Qo{constructor(e,t,n,o){this.parent=e,this.children=t,this.dom=n,this.contentDOM=o,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let o=this.children[t];if(o==e)return n;n+=o.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode)){if(n<0){let n,o;if(e==this.contentDOM)n=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.previousSibling}for(;n&&(!(o=n.pmViewDesc)||o.parent!=this);)n=n.previousSibling;return n?this.posBeforeChild(o)+o.size:this.posAtStart}{let n,o;if(e==this.contentDOM)n=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.nextSibling}for(;n&&(!(o=n.pmViewDesc)||o.parent!=this);)n=n.nextSibling;return n?this.posBeforeChild(o):this.posAtEnd}}let o;if(e==this.dom&&this.contentDOM)o=t>Qn(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))o=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let t=e;;t=t.parentNode){if(t==this.dom){o=!1;break}if(t.previousSibling)break}if(null==o&&t==e.childNodes.length)for(let t=e;;t=t.parentNode){if(t==this.dom){o=!0;break}if(t.nextSibling)break}}return(null==o?n>0:o)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,o=e;o;o=o.parentNode){let r,i=this.getDesc(o);if(i&&(!t||i.node)){if(!n||!(r=i.nodeDOM)||(1==r.nodeType?r.contains(1==e.nodeType?e:e.parentNode):r==e))return i;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let o=e;o;o=o.parentNode){let r=this.getDesc(o);if(r)return r.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let o=this.children[t],r=n+o.size;if(n==e&&r!=n){for(;!o.border&&o.children.length;)for(let e=0;e<o.children.length;e++){let t=o.children[e];if(t.size){o=t;break}}return o}if(e<r)return o.descAt(e-n-o.border);n=r}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,o=0;for(let r=0;n<this.children.length;n++){let t=this.children[n],i=r+t.size;if(i>e||t instanceof or){o=e-r;break}r=i}if(o)return this.children[n].domFromPos(o-this.children[n].border,t);for(let r;n&&!(r=this.children[n-1]).size&&r instanceof Yo&&r.side>=0;n--);if(t<=0){let e,o=!0;for(;e=n?this.children[n-1]:null,e&&e.dom.parentNode!=this.contentDOM;n--,o=!1);return e&&t&&o&&!e.border&&!e.domAtom?e.domFromPos(e.size,t):{node:this.contentDOM,offset:e?Qn(e.dom)+1:0}}{let e,o=!0;for(;e=n<this.children.length?this.children[n]:null,e&&e.dom.parentNode!=this.contentDOM;n++,o=!1);return e&&o&&!e.border&&!e.domAtom?e.domFromPos(0,t):{node:this.contentDOM,offset:e?Qn(e.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let o=-1,r=-1;for(let i=n,s=0;;s++){let n=this.children[s],l=i+n.size;if(-1==o&&e<=l){let r=i+n.border;if(e>=r&&t<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(e,t,r);e=i;for(let t=s;t>0;t--){let n=this.children[t-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){o=Qn(n.dom)+1;break}e-=n.size}-1==o&&(o=0)}if(o>-1&&(l>t||s==this.children.length-1)){t=l;for(let e=s+1;e<this.children.length;e++){let n=this.children[e];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){r=Qn(n.dom);break}t+=n.size}-1==r&&(r=this.contentDOM.childNodes.length);break}i=l}return{node:this.contentDOM,from:e,to:t,fromOffset:o,toOffset:r}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,o=!1){let r=Math.min(e,t),i=Math.max(e,t);for(let u=0,f=0;u<this.children.length;u++){let s=this.children[u],l=f+s.size;if(r>f&&i<l)return s.setSelection(e-f-s.border,t-f-s.border,n,o);f=l}let s=this.domFromPos(e,e?-1:1),l=t==e?s:this.domFromPos(t,t?-1:1),a=n.root.getSelection(),c=n.domSelectionRange(),d=!1;if((go||bo)&&e==t){let{node:e,offset:t}=s;if(3==e.nodeType){if(d=!(!t||"\n"!=e.nodeValue[t-1]),d&&t==e.nodeValue.length)for(let n,o=e;o;o=o.parentNode){if(n=o.nextSibling){"BR"==n.nodeName&&(s=l={node:n.parentNode,offset:Qn(n)+1});break}let e=o.pmViewDesc;if(e&&e.node&&e.node.isBlock)break}}else{let n=e.childNodes[t-1];d=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(go&&c.focusNode&&c.focusNode!=l.node&&1==c.focusNode.nodeType){let e=c.focusNode.childNodes[c.focusOffset];e&&"false"==e.contentEditable&&(o=!0)}if(!(o||d&&bo)&&eo(s.node,s.offset,c.anchorNode,c.anchorOffset)&&eo(l.node,l.offset,c.focusNode,c.focusOffset))return;let h=!1;if((a.extend||e==t)&&!d){a.collapse(s.node,s.offset);try{e!=t&&a.extend(l.node,l.offset),h=!0}catch(p){}}if(!h){if(e>t){let e=s;s=l,l=e}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(s.node,s.offset),a.removeAllRanges(),a.addRange(n)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,o=0;o<this.children.length;o++){let r=this.children[o],i=n+r.size;if(n==i?e<=i&&t>=n:e<i&&t>n){let o=n+r.border,s=i-r.border;if(e>=o&&t<=s)return this.dirty=e==n||t==i?2:1,void(e!=o||t!=s||!r.contentLost&&r.dom.parentNode==this.contentDOM?r.markDirty(e-o,t-o):r.dirty=3);r.dirty=r.dom!=r.contentDOM||r.dom.parentNode!=this.contentDOM||r.children.length?3:2}n=i}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class Yo extends Qo{constructor(e,t,n,o){let r,i=t.type.toDOM;if("function"==typeof i&&(i=i(n,(()=>r?r.parent?r.parent.posBeforeChild(r):void 0:o))),!t.type.spec.raw){if(1!=i.nodeType){let e=document.createElement("span");e.appendChild(i),i=e}i.contentEditable="false",i.classList.add("ProseMirror-widget")}super(e,[],i,null),this.widget=t,this.widget=t,r=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class Xo extends Qo{constructor(e,t,n,o){super(e,[],t,null),this.textDOM=n,this.text=o}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class Zo extends Qo{constructor(e,t,n,o,r){super(e,[],n,o),this.mark=t,this.spec=r}static create(e,t,n,o){let r=o.nodeViews[t.type.name],i=r&&r(t,o,n);return i&&i.dom||(i=Dt.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new Zo(e,t,i.dom,i.contentDOM||i.dom,i)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let e=this.parent;for(;!e.node;)e=e.parent;e.dirty<this.dirty&&(e.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let o=Zo.create(this.parent,this.mark,!0,n),r=this.children,i=this.size;t<i&&(r=gr(r,t,i,n)),e>0&&(r=gr(r,0,e,n));for(let s=0;s<r.length;s++)r[s].parent=o;return o.children=r,o}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class er extends Qo{constructor(e,t,n,o,r,i,s,l,a){super(e,[],r,i),this.node=t,this.outerDeco=n,this.innerDeco=o,this.nodeDOM=s}static create(e,t,n,o,r,i){let s,l=r.nodeViews[t.type.name],a=l&&l(t,r,(()=>s?s.parent?s.parent.posBeforeChild(s):void 0:i),n,o),c=a&&a.dom,d=a&&a.contentDOM;if(t.isText)if(c){if(3!=c.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else c=document.createTextNode(t.text);else if(!c){let e=Dt.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs);({dom:c,contentDOM:d}=e)}d||t.isText||"BR"==c.nodeName||(c.hasAttribute("contenteditable")||(c.contentEditable="false"),t.type.spec.draggable&&(c.draggable=!0));let h=c;return c=hr(c,n,t),a?s=new rr(e,t,n,o,c,d||null,h,a,r,i+1):t.isText?new nr(e,t,n,o,c,h,r):new er(e,t,n,o,c,d||null,h,r,i+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>Ne.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&pr(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,o=t,r=e.composing?this.localCompositionInfo(e,t):null,i=r&&r.pos>-1?r:null,s=r&&r.pos<0,l=new fr(this,i&&i.node,e);!function(e,t,n,o){let r=t.locals(e),i=0;if(0==r.length){for(let n=0;n<e.childCount;n++){let s=e.child(n);o(s,r,t.forChild(i,s),n),i+=s.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let d,h,p,u;for(;s<r.length&&r[s].to==i;){let e=r[s++];e.widget&&(d?(h||(h=[d])).push(e):d=e)}if(d)if(h){h.sort(mr);for(let e=0;e<h.length;e++)n(h[e],c,!!a)}else n(d,c,!!a);if(a)u=-1,p=a,a=null;else{if(!(c<e.childCount))break;u=c,p=e.child(c++)}for(let e=0;e<l.length;e++)l[e].to<=i&&l.splice(e--,1);for(;s<r.length&&r[s].from<=i&&r[s].to>i;)l.push(r[s++]);let f=i+p.nodeSize;if(p.isText){let e=f;s<r.length&&r[s].from<e&&(e=r[s].from);for(let t=0;t<l.length;t++)l[t].to<e&&(e=l[t].to);e<f&&(a=p.cut(e-i),p=p.cut(0,e-i),f=e,u=-1)}else for(;s<r.length&&r[s].to<f;)s++;o(p,p.isInline&&!p.isLeaf?l.filter((e=>!e.inline)):l.slice(),t.forChild(i,p),u),i=f}}(this.node,this.innerDeco,((t,r,i)=>{t.spec.marks?l.syncToMarks(t.spec.marks,n,e):t.type.side>=0&&!i&&l.syncToMarks(r==this.node.childCount?Pe.none:this.node.child(r).marks,n,e),l.placeWidget(t,e,o)}),((t,i,a,c)=>{let d;l.syncToMarks(t.marks,n,e),l.findNodeMatch(t,i,a,c)||s&&e.state.selection.from>o&&e.state.selection.to<o+t.nodeSize&&(d=l.findIndexWithChild(r.node))>-1&&l.updateNodeAt(t,i,a,d,e)||l.updateNextNode(t,i,a,e,c,o)||l.addNode(t,i,a,e,o),o+=t.nodeSize})),l.syncToMarks([],n,e),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||2==this.dirty)&&(i&&this.protectLocalComposition(e,i),ir(this.contentDOM,this.children,e),ko&&function(e){if("UL"==e.nodeName||"OL"==e.nodeName){let t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:o}=e.state.selection;if(!(e.state.selection instanceof Dn)||n<t||o>t+this.node.content.size)return null;let r=e.input.compositionNode;if(!r||!this.dom.contains(r.parentNode))return null;if(this.node.inlineContent){let e=r.nodeValue,i=function(e,t,n,o){for(let r=0,i=0;r<e.childCount&&i<=o;){let s=e.child(r++),l=i;if(i+=s.nodeSize,!s.isText)continue;let a=s.text;for(;r<e.childCount;){let t=e.child(r++);if(i+=t.nodeSize,!t.isText)break;a+=t.text}if(i>=n){if(i>=o&&a.slice(o-t.length-l,o-l)==t)return o-t.length;let e=l<o?a.lastIndexOf(t,o-l-1):-1;if(e>=0&&e+t.length+l>=n)return l+e;if(n==o&&a.length>=o+t.length-l&&a.slice(o-l,o-l+t.length)==t)return o}}return-1}(this.node.content,e,n-t,o-t);return i<0?null:{node:r,pos:i,text:e}}return{node:r,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:o}){if(this.getDesc(t))return;let r=t;for(;r.parentNode!=this.contentDOM;r=r.parentNode){for(;r.previousSibling;)r.parentNode.removeChild(r.previousSibling);for(;r.nextSibling;)r.parentNode.removeChild(r.nextSibling);r.pmViewDesc&&(r.pmViewDesc=void 0)}let i=new Xo(this,r,t,o);e.input.compositionNodes.push(i),this.children=gr(this.children,n,n+o.length,e,i)}update(e,t,n,o){return!(3==this.dirty||!e.sameMarkup(this.node))&&(this.updateInner(e,t,n,o),!0)}updateInner(e,t,n,o){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(o,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(pr(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=cr(this.dom,this.nodeDOM,ar(this.outerDeco,this.node,t),ar(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function tr(e,t,n,o,r){hr(o,t,e);let i=new er(void 0,e,t,n,o,o,o,r,0);return i.contentDOM&&i.updateChildren(r,0),i}class nr extends er{constructor(e,t,n,o,r,i,s){super(e,t,n,o,r,null,i,s,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,o){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!e.sameMarkup(this.node))&&(this.updateOuterDeco(t),0==this.dirty&&e.text==this.node.text||e.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=e.text,o.trackWrites==this.nodeDOM&&(o.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let o=this.node.cut(e,t),r=document.createTextNode(o.text);return new nr(this.parent,o,this.outerDeco,this.innerDeco,r,r,n)}markDirty(e,t){super.markDirty(e,t),this.dom==this.nodeDOM||0!=e&&t!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class or extends Qo{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class rr extends er{constructor(e,t,n,o,r,i,s,l,a,c){super(e,t,n,o,r,i,s,a,c),this.spec=l}update(e,t,n,o){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let r=this.spec.update(e,t,n);return r&&this.updateInner(e,t,n,o),r}return!(!this.contentDOM&&!e.isLeaf)&&super.update(e,t,n,o)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,o){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,o)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function ir(e,t,n){let o=e.firstChild,r=!1;for(let i=0;i<t.length;i++){let s=t[i],l=s.dom;if(l.parentNode==e){for(;l!=o;)o=ur(o),r=!0;o=o.nextSibling}else r=!0,e.insertBefore(l,o);if(s instanceof Zo){let t=o?o.previousSibling:e.lastChild;ir(s.contentDOM,s.children,n),o=t?t.nextSibling:e.firstChild}}for(;o;)o=ur(o),r=!0;r&&n.trackWrites==e&&(n.trackWrites=null)}const sr=function(e){e&&(this.nodeName=e)};sr.prototype=Object.create(null);const lr=[new sr];function ar(e,t,n){if(0==e.length)return lr;let o=n?lr[0]:new sr,r=[o];for(let i=0;i<e.length;i++){let s=e[i].type.attrs;if(s){s.nodeName&&r.push(o=new sr(s.nodeName));for(let e in s){let i=s[e];null!=i&&(n&&1==r.length&&r.push(o=new sr(t.isInline?"span":"div")),"class"==e?o.class=(o.class?o.class+" ":"")+i:"style"==e?o.style=(o.style?o.style+";":"")+i:"nodeName"!=e&&(o[e]=i))}}}return r}function cr(e,t,n,o){if(n==lr&&o==lr)return t;let r=t;for(let i=0;i<o.length;i++){let t=o[i],s=n[i];if(i){let n;s&&s.nodeName==t.nodeName&&r!=e&&(n=r.parentNode)&&n.nodeName.toLowerCase()==t.nodeName||(n=document.createElement(t.nodeName),n.pmIsDeco=!0,n.appendChild(r),s=lr[0]),r=n}dr(r,s||lr[0],t)}return r}function dr(e,t,n){for(let o in t)"class"==o||"style"==o||"nodeName"==o||o in n||e.removeAttribute(o);for(let o in n)"class"!=o&&"style"!=o&&"nodeName"!=o&&n[o]!=t[o]&&e.setAttribute(o,n[o]);if(t.class!=n.class){let o=t.class?t.class.split(" ").filter(Boolean):[],r=n.class?n.class.split(" ").filter(Boolean):[];for(let t=0;t<o.length;t++)-1==r.indexOf(o[t])&&e.classList.remove(o[t]);for(let t=0;t<r.length;t++)-1==o.indexOf(r[t])&&e.classList.add(r[t]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style){let n,o=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=o.exec(t.style);)e.style.removeProperty(n[1])}n.style&&(e.style.cssText+=n.style)}}function hr(e,t,n){return cr(e,e,lr,ar(t,n,1!=e.nodeType))}function pr(e,t){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function ur(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}class fr{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(e,t){let n=t,o=n.children.length,r=e.childCount,i=new Map,s=[];e:for(;r>0;){let l;for(;;)if(o){let e=n.children[o-1];if(!(e instanceof Zo)){l=e,o--;break}n=e,o=e.children.length}else{if(n==t)break e;o=n.parent.children.indexOf(n),n=n.parent}let a=l.node;if(a){if(a!=e.child(r-1))break;--r,i.set(l,r),s.push(l)}}return{index:r,matched:i,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let o=0,r=this.stack.length>>1,i=Math.min(r,e.length);for(;o<i&&(o==r-1?this.top:this.stack[o+1<<1]).matchesMark(e[o])&&!1!==e[o].type.spec.spanning;)o++;for(;o<r;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),r--;for(;r<e.length;){this.stack.push(this.top,this.index+1);let o=-1;for(let t=this.index;t<Math.min(this.index+3,this.top.children.length);t++){let n=this.top.children[t];if(n.matchesMark(e[r])&&!this.isLocked(n.dom)){o=t;break}}if(o>-1)o>this.index&&(this.changed=!0,this.destroyBetween(this.index,o)),this.top=this.top.children[this.index];else{let o=Zo.create(this.top,e[r],t,n);this.top.children.splice(this.index,0,o),this.top=o,this.changed=!0}this.index=0,r++}}findNodeMatch(e,t,n,o){let r,i=-1;if(o>=this.preMatch.index&&(r=this.preMatch.matches[o-this.preMatch.index]).parent==this.top&&r.matchesNode(e,t,n))i=this.top.children.indexOf(r,this.index);else for(let s=this.index,l=Math.min(this.top.children.length,s+5);s<l;s++){let o=this.top.children[s];if(o.matchesNode(e,t,n)&&!this.preMatch.matched.has(o)){i=s;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,t,n,o,r){let i=this.top.children[o];return 3==i.dirty&&i.dom==i.contentDOM&&(i.dirty=2),!!i.update(e,t,n,r)&&(this.destroyBetween(this.index,o),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let t=e.pmViewDesc;if(t)for(let e=this.index;e<this.top.children.length;e++)if(this.top.children[e]==t)return e;return-1}e=t}}updateNextNode(e,t,n,o,r,i){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof er){let a=this.preMatch.matched.get(l);if(null!=a&&a!=r)return!1;let c,d=l.dom,h=this.isLocked(d)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&3!=l.dirty&&pr(t,l.outerDeco));if(!h&&l.update(e,t,n,o))return this.destroyBetween(this.index,s),l.dom!=d&&(this.changed=!0),this.index++,!0;if(!h&&(c=this.recreateWrapper(l,e,t,n,o,i)))return this.destroyBetween(this.index,s),this.top.children[this.index]=c,c.contentDOM&&(c.dirty=2,c.updateChildren(o,i+1),c.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,o,r,i){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!pr(n,e.outerDeco)||!o.eq(e.innerDeco))return null;let s=er.create(this.top,t,n,o,r,i);if(s.contentDOM){s.children=e.children,e.children=[];for(let e of s.children)e.parent=s}return e.destroy(),s}addNode(e,t,n,o,r){let i=er.create(this.top,e,t,n,o,r);i.contentDOM&&i.updateChildren(o,r+1),this.top.children.splice(this.index++,0,i),this.changed=!0}placeWidget(e,t,n){let o=this.index<this.top.children.length?this.top.children[this.index]:null;if(!o||!o.matchesWidget(e)||e!=o.widget&&o.widget.type.toDOM.parentNode){let o=new Yo(this.top,e,t,n);this.top.children.splice(this.index++,0,o),this.changed=!0}else this.index++}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof Zo;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof nr)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((bo||vo)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let o=new or(this.top,[],n,null);t!=this.top?t.children.push(o):t.children.splice(this.index++,0,o),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function mr(e,t){return e.type.side-t.type.side}function gr(e,t,n,o,r){let i=[];for(let s=0,l=0;s<e.length;s++){let a=e[s],c=l,d=l+=a.size;c>=n||d<=t?i.push(a):(c<t&&i.push(a.slice(0,t-c,o)),r&&(i.push(r),r=void 0),d>n&&i.push(a.slice(n-c,a.size,o)))}return i}function yr(e,t=null){let n=e.domSelectionRange(),o=e.state.doc;if(!n.focusNode)return null;let r=e.docView.nearestDesc(n.focusNode),i=r&&0==r.size,s=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;let l,a,c=o.resolve(s);if(io(n)){for(l=s;r&&!r.node;)r=r.parent;let e=r.node;if(r&&e.isAtom&&In.isSelectable(e)&&r.parent&&(!e.isInline||!function(e,t,n){for(let o=0==t,r=t==oo(e);o||r;){if(e==n)return!0;let t=Qn(e);if(!(e=e.parentNode))return!1;o=o&&0==t,r=r&&t==oo(e)}}(n.focusNode,n.focusOffset,r.dom))){let e=r.posBefore;a=new In(s==e?c:o.resolve(e))}}else{if(n instanceof e.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let t=s,r=s;for(let o=0;o<n.rangeCount;o++){let i=n.getRangeAt(o);t=Math.min(t,e.docView.posFromDOM(i.startContainer,i.startOffset,1)),r=Math.max(r,e.docView.posFromDOM(i.endContainer,i.endOffset,-1))}if(t<0)return null;[l,s]=r==e.state.selection.anchor?[r,t]:[t,r],c=o.resolve(s)}else l=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(l<0)return null}let d=o.resolve(l);if(!a){a=Or(e,d,c,"pointer"==t||e.state.selection.head<c.pos&&!i?1:-1)}return a}function vr(e){return e.editable?e.hasFocus():Ar(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function wr(e,t=!1){let n=e.state.selection;if(Mr(e,n),vr(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&vo){let t=e.domSelectionRange(),n=e.domObserver.currentSelection;if(t.anchorNode&&n.anchorNode&&eo(t.anchorNode,t.anchorOffset,n.anchorNode,n.anchorOffset))return e.input.mouseDown.delayedSelectionSync=!0,void e.domObserver.setCurSelection()}if(e.domObserver.disconnectSelection(),e.cursorWrapper)!function(e){let t=e.domSelection(),n=document.createRange();if(!t)return;let o=e.cursorWrapper.dom,r="IMG"==o.nodeName;r?n.setStart(o.parentNode,Qn(o)+1):n.setStart(o,0);n.collapse(!0),t.removeAllRanges(),t.addRange(n),!r&&!e.state.selection.visible&&fo&&mo<=11&&(o.disabled=!0,o.disabled=!1)}(e);else{let o,r,{anchor:i,head:s}=n;!br||n instanceof Dn||(n.$from.parent.inlineContent||(o=kr(e,n.from)),n.empty||n.$from.parent.inlineContent||(r=kr(e,n.to))),e.docView.setSelection(i,s,e,t),br&&(o&&Sr(o),r&&Sr(r)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(e){let t=e.dom.ownerDocument;t.removeEventListener("selectionchange",e.input.hideSelectionGuard);let n=e.domSelectionRange(),o=n.anchorNode,r=n.anchorOffset;t.addEventListener("selectionchange",e.input.hideSelectionGuard=()=>{n.anchorNode==o&&n.anchorOffset==r||(t.removeEventListener("selectionchange",e.input.hideSelectionGuard),setTimeout((()=>{vr(e)&&!e.state.selection.visible||e.dom.classList.remove("ProseMirror-hideselection")}),20))})}(e))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}const br=bo||vo&&wo<63;function kr(e,t){let{node:n,offset:o}=e.docView.domFromPos(t,0),r=o<n.childNodes.length?n.childNodes[o]:null,i=o?n.childNodes[o-1]:null;if(bo&&r&&"false"==r.contentEditable)return xr(r);if(!(r&&"false"!=r.contentEditable||i&&"false"!=i.contentEditable)){if(r)return xr(r);if(i)return xr(i)}}function xr(e){return e.contentEditable="true",bo&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function Sr(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function Mr(e,t){if(t instanceof In){let n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(Cr(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else Cr(e)}function Cr(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function Or(e,t,n,o){return e.someProp("createSelectionBetween",(o=>o(e,t,n)))||Dn.between(t,n,o)}function Tr(e){return!(e.editable&&!e.hasFocus())&&Ar(e)}function Ar(e){let t=e.domSelectionRange();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(n){return!1}}function Er(e,t){let{$anchor:n,$head:o}=e.selection,r=t>0?n.max(o):n.min(o),i=r.parent.inlineContent?r.depth?e.doc.resolve(t>0?r.after():r.before()):null:r;return i&&Tn.findFrom(i,t)}function Nr(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function Dr(e,t,n){let o=e.state.selection;if(!(o instanceof Dn)){if(o instanceof In&&o.node.isInline)return Nr(e,new Dn(t>0?o.$to:o.$from));{let n=Er(e.state,t);return!!n&&Nr(e,n)}}if(n.indexOf("s")>-1){let{$head:n}=o,r=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter;if(!r||r.isText||!r.isLeaf)return!1;let i=e.state.doc.resolve(n.pos+r.nodeSize*(t<0?-1:1));return Nr(e,new Dn(o.$anchor,i))}if(!o.empty)return!1;if(e.endOfTextblock(t>0?"forward":"backward")){let n=Er(e.state,t);return!!(n&&n instanceof In)&&Nr(e,n)}if(!(xo&&n.indexOf("m")>-1)){let n,r=o.$head,i=r.textOffset?null:t<0?r.nodeBefore:r.nodeAfter;if(!i||i.isText)return!1;let s=t<0?r.pos-i.nodeSize:r.pos;return!!(i.isAtom||(n=e.docView.descAt(s))&&!n.contentDOM)&&(In.isSelectable(i)?Nr(e,new In(t<0?e.state.doc.resolve(r.pos-i.nodeSize):r)):!!Co&&Nr(e,new Dn(e.state.doc.resolve(t<0?s:s+i.nodeSize))))}}function Rr(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function Ir(e,t){let n=e.pmViewDesc;return n&&0==n.size&&(t<0||e.nextSibling||"BR"!=e.nodeName)}function Pr(e,t){return t<0?function(e){let t=e.domSelectionRange(),n=t.focusNode,o=t.focusOffset;if(!n)return;let r,i,s=!1;go&&1==n.nodeType&&o<Rr(n)&&Ir(n.childNodes[o],-1)&&(s=!0);for(;;)if(o>0){if(1!=n.nodeType)break;{let e=n.childNodes[o-1];if(Ir(e,-1))r=n,i=--o;else{if(3!=e.nodeType)break;n=e,o=n.nodeValue.length}}}else{if(Lr(n))break;{let t=n.previousSibling;for(;t&&Ir(t,-1);)r=n.parentNode,i=Qn(t),t=t.previousSibling;if(t)n=t,o=Rr(n);else{if(n=n.parentNode,n==e.dom)break;o=0}}}s?zr(e,n,o):r&&zr(e,r,i)}(e):function(e){let t=e.domSelectionRange(),n=t.focusNode,o=t.focusOffset;if(!n)return;let r,i,s=Rr(n);for(;;)if(o<s){if(1!=n.nodeType)break;if(!Ir(n.childNodes[o],1))break;r=n,i=++o}else{if(Lr(n))break;{let t=n.nextSibling;for(;t&&Ir(t,1);)r=t.parentNode,i=Qn(t)+1,t=t.nextSibling;if(t)n=t,o=0,s=Rr(n);else{if(n=n.parentNode,n==e.dom)break;o=s=0}}}r&&zr(e,r,i)}(e)}function Lr(e){let t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function zr(e,t,n){if(3!=t.nodeType){let e,o;(o=function(e,t){for(;e&&t==e.childNodes.length&&!ro(e);)t=Qn(e)+1,e=e.parentNode;for(;e&&t<e.childNodes.length;){let n=e.childNodes[t];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=n,t=0}}(t,n))?(t=o,n=0):(e=function(e,t){for(;e&&!t&&!ro(e);)t=Qn(e),e=e.parentNode;for(;e&&t;){let n=e.childNodes[t-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=(e=n).childNodes.length}}(t,n))&&(t=e,n=e.nodeValue.length)}let o=e.domSelection();if(!o)return;if(io(o)){let e=document.createRange();e.setEnd(t,n),e.setStart(t,n),o.removeAllRanges(),o.addRange(e)}else o.extend&&o.extend(t,n);e.domObserver.setCurSelection();let{state:r}=e;setTimeout((()=>{e.state==r&&wr(e)}),50)}function Br(e,t){let n=e.state.doc.resolve(t);if(!vo&&!So&&n.parent.inlineContent){let o=e.coordsAtPos(t);if(t>n.start()){let n=e.coordsAtPos(t-1),r=(n.top+n.bottom)/2;if(r>o.top&&r<o.bottom&&Math.abs(n.left-o.left)>1)return n.left<o.left?"ltr":"rtl"}if(t<n.end()){let n=e.coordsAtPos(t+1),r=(n.top+n.bottom)/2;if(r>o.top&&r<o.bottom&&Math.abs(n.left-o.left)>1)return n.left>o.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(e.dom).direction?"rtl":"ltr"}function Vr(e,t,n){let o=e.state.selection;if(o instanceof Dn&&!o.empty||n.indexOf("s")>-1)return!1;if(xo&&n.indexOf("m")>-1)return!1;let{$from:r,$to:i}=o;if(!r.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){let n=Er(e.state,t);if(n&&n instanceof In)return Nr(e,n)}if(!r.parent.inlineContent){let n=t<0?r:i,s=o instanceof Ln?Tn.near(n,t):Tn.findFrom(n,t);return!!s&&Nr(e,s)}return!1}function $r(e,t){if(!(e.state.selection instanceof Dn))return!0;let{$head:n,$anchor:o,empty:r}=e.state.selection;if(!n.sameParent(o))return!0;if(!r)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;let i=!n.textOffset&&(t<0?n.nodeBefore:n.nodeAfter);if(i&&!i.isText){let o=e.state.tr;return t<0?o.delete(n.pos-i.nodeSize,n.pos):o.delete(n.pos,n.pos+i.nodeSize),e.dispatch(o),!0}return!1}function Hr(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function Fr(e,t){let n=t.keyCode,o=function(e){let t="";return e.ctrlKey&&(t+="c"),e.metaKey&&(t+="m"),e.altKey&&(t+="a"),e.shiftKey&&(t+="s"),t}(t);if(8==n||xo&&72==n&&"c"==o)return $r(e,-1)||Pr(e,-1);if(46==n&&!t.shiftKey||xo&&68==n&&"c"==o)return $r(e,1)||Pr(e,1);if(13==n||27==n)return!0;if(37==n||xo&&66==n&&"c"==o){let t=37==n?"ltr"==Br(e,e.state.selection.from)?-1:1:-1;return Dr(e,t,o)||Pr(e,t)}if(39==n||xo&&70==n&&"c"==o){let t=39==n?"ltr"==Br(e,e.state.selection.from)?1:-1:1;return Dr(e,t,o)||Pr(e,t)}return 38==n||xo&&80==n&&"c"==o?Vr(e,-1,o)||Pr(e,-1):40==n||xo&&78==n&&"c"==o?function(e){if(!bo||e.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:n}=e.domSelectionRange();if(t&&1==t.nodeType&&0==n&&t.firstChild&&"false"==t.firstChild.contentEditable){let n=t.firstChild;Hr(e,n,"true"),setTimeout((()=>Hr(e,n,"false")),20)}return!1}(e)||Vr(e,1,o)||Pr(e,1):o==(xo?"m":"c")&&(66==n||73==n||89==n||90==n)}function jr(e,t){e.someProp("transformCopied",(n=>{t=n(t,e)}));let n=[],{content:o,openStart:r,openEnd:i}=t;for(;r>1&&i>1&&1==o.childCount&&1==o.firstChild.childCount;){r--,i--;let e=o.firstChild;n.push(e.type.name,e.attrs!=e.type.defaultAttrs?e.attrs:null),o=e.content}let s=e.someProp("clipboardSerializer")||Dt.fromSchema(e.state.schema),l=Xr(),a=l.createElement("div");a.appendChild(s.serializeFragment(o,{document:l}));let c,d=a.firstChild,h=0;for(;d&&1==d.nodeType&&(c=Qr[d.nodeName.toLowerCase()]);){for(let e=c.length-1;e>=0;e--){let t=l.createElement(c[e]);for(;a.firstChild;)t.appendChild(a.firstChild);a.appendChild(t),h++}d=a.firstChild}return d&&1==d.nodeType&&d.setAttribute("data-pm-slice",`${r} ${i}${h?` -${h}`:""} ${JSON.stringify(n)}`),{dom:a,text:e.someProp("clipboardTextSerializer",(n=>n(t,e)))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function _r(e,t,n,o,r){let i,s,l=r.parent.type.spec.code;if(!n&&!t)return null;let a=t&&(o||l||!n);if(a){if(e.someProp("transformPastedText",(n=>{t=n(t,l||o,e)})),l)return t?new ze(Ne.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0):ze.empty;let n=e.someProp("clipboardTextParser",(n=>n(t,r,o,e)));if(n)s=n;else{let n=r.marks(),{schema:o}=e.state,s=Dt.fromSchema(o);i=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach((e=>{let t=i.appendChild(document.createElement("p"));e&&t.appendChild(s.serializeNode(o.text(e,n)))}))}}else e.someProp("transformPastedHTML",(t=>{n=t(n,e)})),i=function(e){let t=/^(\s*<meta [^>]*>)*/.exec(e);t&&(e=e.slice(t[0].length));let n,o=Xr().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(e);(n=r&&Qr[r[1].toLowerCase()])&&(e=n.map((e=>"<"+e+">")).join("")+e+n.map((e=>"</"+e+">")).reverse().join(""));if(o.innerHTML=function(e){let t=window.trustedTypes;if(!t)return e;Zr||(Zr=t.createPolicy("ProseMirrorClipboard",{createHTML:e=>e}));return Zr.createHTML(e)}(e),n)for(let i=0;i<n.length;i++)o=o.querySelector(n[i])||o;return o}(n),Co&&function(e){let t=e.querySelectorAll(vo?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<t.length;n++){let o=t[n];1==o.childNodes.length&&" "==o.textContent&&o.parentNode&&o.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),o)}}(i);let c=i&&i.querySelector("[data-pm-slice]"),d=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(d&&d[3])for(let h=+d[3];h>0;h--){let e=i.firstChild;for(;e&&1!=e.nodeType;)e=e.nextSibling;if(!e)break;i=e}if(!s){let t=e.someProp("clipboardParser")||e.someProp("domParser")||kt.fromSchema(e.state.schema);s=t.parseSlice(i,{preserveWhitespace:!(!a&&!d),context:r,ruleFromNode:e=>"BR"!=e.nodeName||e.nextSibling||!e.parentNode||qr.test(e.parentNode.nodeName)?null:{ignore:!0}})}if(d)s=function(e,t){if(!e.size)return e;let n,o=e.content.firstChild.type.schema;try{n=JSON.parse(t)}catch(l){return e}let{content:r,openStart:i,openEnd:s}=e;for(let a=n.length-2;a>=0;a-=2){let e=o.nodes[n[a]];if(!e||e.hasRequiredAttrs())break;r=Ne.from(e.create(n[a+1],r)),i++,s++}return new ze(r,i,s)}(Gr(s,+d[1],+d[2]),d[4]);else if(s=ze.maxOpen(function(e,t){if(e.childCount<2)return e;for(let n=t.depth;n>=0;n--){let o,r=t.node(n).contentMatchAt(t.index(n)),i=[];if(e.forEach((e=>{if(!i)return;let t,n=r.findWrapping(e.type);if(!n)return i=null;if(t=i.length&&o.length&&Kr(n,o,e,i[i.length-1],0))i[i.length-1]=t;else{i.length&&(i[i.length-1]=Ur(i[i.length-1],o.length));let t=Wr(e,n);i.push(t),r=r.matchType(t.type),o=n}})),i)return Ne.from(i)}return e}(s.content,r),!0),s.openStart||s.openEnd){let e=0,t=0;for(let n=s.content.firstChild;e<s.openStart&&!n.type.spec.isolating;e++,n=n.firstChild);for(let n=s.content.lastChild;t<s.openEnd&&!n.type.spec.isolating;t++,n=n.lastChild);s=Gr(s,e,t)}return e.someProp("transformPasted",(t=>{s=t(s,e)})),s}const qr=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Wr(e,t,n=0){for(let o=t.length-1;o>=n;o--)e=t[o].create(null,Ne.from(e));return e}function Kr(e,t,n,o,r){if(r<e.length&&r<t.length&&e[r]==t[r]){let i=Kr(e,t,n,o.lastChild,r+1);if(i)return o.copy(o.content.replaceChild(o.childCount-1,i));if(o.contentMatchAt(o.childCount).matchType(r==e.length-1?n.type:e[r+1]))return o.copy(o.content.append(Ne.from(Wr(n,e,r+1))))}}function Ur(e,t){if(0==t)return e;let n=e.content.replaceChild(e.childCount-1,Ur(e.lastChild,t-1)),o=e.contentMatchAt(e.childCount).fillBefore(Ne.empty,!0);return e.copy(n.append(o))}function Jr(e,t,n,o,r,i){let s=t<0?e.firstChild:e.lastChild,l=s.content;return e.childCount>1&&(i=0),r<o-1&&(l=Jr(l,t,n,o,r+1,i)),r>=n&&(l=t<0?s.contentMatchAt(0).fillBefore(l,i<=r).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(Ne.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,s.copy(l))}function Gr(e,t,n){return t<e.openStart&&(e=new ze(Jr(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new ze(Jr(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}const Qr={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let Yr=null;function Xr(){return Yr||(Yr=document.implementation.createHTMLDocument("title"))}let Zr=null;const ei={},ti={},ni={touchstart:!0,touchmove:!0};class oi{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function ri(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function ii(e){e.someProp("handleDOMEvents",(t=>{for(let n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=t=>si(e,t))}))}function si(e,t){return e.someProp("handleDOMEvents",(n=>{let o=n[t.type];return!!o&&(o(e,t)||t.defaultPrevented)}))}function li(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}function ai(e){return{left:e.clientX,top:e.clientY}}function ci(e,t,n,o,r){if(-1==o)return!1;let i=e.state.doc.resolve(o);for(let s=i.depth+1;s>0;s--)if(e.someProp(t,(t=>s>i.depth?t(e,n,i.nodeAfter,i.before(s),r,!0):t(e,n,i.node(s),i.before(s),r,!1))))return!0;return!1}function di(e,t,n){if(e.focused||e.focus(),e.state.selection.eq(t))return;let o=e.state.tr.setSelection(t);o.setMeta("pointer",!0),e.dispatch(o)}function hi(e,t,n,o,r){return ci(e,"handleClickOn",t,n,o)||e.someProp("handleClick",(n=>n(e,t,o)))||(r?function(e,t){if(-1==t)return!1;let n,o,r=e.state.selection;r instanceof In&&(n=r.node);let i=e.state.doc.resolve(t);for(let s=i.depth+1;s>0;s--){let e=s>i.depth?i.nodeAfter:i.node(s);if(In.isSelectable(e)){o=n&&r.$from.depth>0&&s>=r.$from.depth&&i.before(r.$from.depth+1)==r.$from.pos?i.before(r.$from.depth):i.before(s);break}}return null!=o&&(di(e,In.create(e.state.doc,o)),!0)}(e,n):function(e,t){if(-1==t)return!1;let n=e.state.doc.resolve(t),o=n.nodeAfter;return!!(o&&o.isAtom&&In.isSelectable(o))&&(di(e,new In(n)),!0)}(e,n))}function pi(e,t,n,o){return ci(e,"handleDoubleClickOn",t,n,o)||e.someProp("handleDoubleClick",(n=>n(e,t,o)))}function ui(e,t,n,o){return ci(e,"handleTripleClickOn",t,n,o)||e.someProp("handleTripleClick",(n=>n(e,t,o)))||function(e,t,n){if(0!=n.button)return!1;let o=e.state.doc;if(-1==t)return!!o.inlineContent&&(di(e,Dn.create(o,0,o.content.size)),!0);let r=o.resolve(t);for(let i=r.depth+1;i>0;i--){let t=i>r.depth?r.nodeAfter:r.node(i),n=r.before(i);if(t.inlineContent)di(e,Dn.create(o,n+1,n+1+t.content.size));else{if(!In.isSelectable(t))continue;di(e,In.create(o,n))}return!0}}(e,n,o)}function fi(e){return xi(e)}ti.keydown=(e,t)=>{let n=t;if(e.input.shiftKey=16==n.keyCode||n.shiftKey,!yi(e,n)&&(e.input.lastKeyCode=n.keyCode,e.input.lastKeyCodeTime=Date.now(),!Mo||!vo||13!=n.keyCode))if(229!=n.keyCode&&e.domObserver.forceFlush(),!ko||13!=n.keyCode||n.ctrlKey||n.altKey||n.metaKey)e.someProp("handleKeyDown",(t=>t(e,n)))||Fr(e,n)?n.preventDefault():ri(e,"key");else{let t=Date.now();e.input.lastIOSEnter=t,e.input.lastIOSEnterFallbackTimeout=setTimeout((()=>{e.input.lastIOSEnter==t&&(e.someProp("handleKeyDown",(t=>t(e,so(13,"Enter")))),e.input.lastIOSEnter=0)}),200)}},ti.keyup=(e,t)=>{16==t.keyCode&&(e.input.shiftKey=!1)},ti.keypress=(e,t)=>{let n=t;if(yi(e,n)||!n.charCode||n.ctrlKey&&!n.altKey||xo&&n.metaKey)return;if(e.someProp("handleKeyPress",(t=>t(e,n))))return void n.preventDefault();let o=e.state.selection;if(!(o instanceof Dn&&o.$from.sameParent(o.$to))){let t=String.fromCharCode(n.charCode);/[\r\n]/.test(t)||e.someProp("handleTextInput",(n=>n(e,o.$from.pos,o.$to.pos,t)))||e.dispatch(e.state.tr.insertText(t).scrollIntoView()),n.preventDefault()}};const mi=xo?"metaKey":"ctrlKey";ei.mousedown=(e,t)=>{let n=t;e.input.shiftKey=n.shiftKey;let o=fi(e),r=Date.now(),i="singleClick";r-e.input.lastClick.time<500&&function(e,t){let n=t.x-e.clientX,o=t.y-e.clientY;return n*n+o*o<100}(n,e.input.lastClick)&&!n[mi]&&("singleClick"==e.input.lastClick.type?i="doubleClick":"doubleClick"==e.input.lastClick.type&&(i="tripleClick")),e.input.lastClick={time:r,x:n.clientX,y:n.clientY,type:i};let s=e.posAtCoords(ai(n));s&&("singleClick"==i?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new gi(e,s,n,!!o)):("doubleClick"==i?pi:ui)(e,s.pos,s.inside,n)?n.preventDefault():ri(e,"pointer"))};class gi{constructor(e,t,n,o){let r,i;if(this.view=e,this.pos=t,this.event=n,this.flushed=o,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[mi],this.allowDefault=n.shiftKey,t.inside>-1)r=e.state.doc.nodeAt(t.inside),i=t.inside;else{let n=e.state.doc.resolve(t.pos);r=n.parent,i=n.depth?n.before():0}const s=o?null:n.target,l=s?e.docView.nearestDesc(s,!0):null;this.target=l&&1==l.dom.nodeType?l.dom:null;let{selection:a}=e.state;(0==n.button&&r.type.spec.draggable&&!1!==r.type.spec.selectable||a instanceof In&&a.from<=i&&a.to>i)&&(this.mightDrag={node:r,pos:i,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!go||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),ri(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((()=>wr(this.view))),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(ai(e))),this.updateAllowDefault(e),this.allowDefault||!t?ri(this.view,"pointer"):hi(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():0==e.button&&(this.flushed||bo&&this.mightDrag&&!this.mightDrag.node.isAtom||vo&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(di(this.view,Tn.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):ri(this.view,"pointer")}move(e){this.updateAllowDefault(e),ri(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function yi(e,t){return!!e.composing||!!(bo&&Math.abs(t.timeStamp-e.input.compositionEndedAt)<500)&&(e.input.compositionEndedAt=-2e8,!0)}ei.touchstart=e=>{e.input.lastTouch=Date.now(),fi(e),ri(e,"pointer")},ei.touchmove=e=>{e.input.lastTouch=Date.now(),ri(e,"pointer")},ei.contextmenu=e=>fi(e);const vi=Mo?5e3:-1;function wi(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout((()=>xi(e)),t))}function bi(e){for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=function(){let e=document.createEvent("Event");return e.initEvent("event",!0,!0),e.timeStamp}());e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function ki(e){let t=e.domSelectionRange();if(!t.focusNode)return null;let n=function(e,t){for(;;){if(3==e.nodeType&&t)return e;if(1==e.nodeType&&t>0){if("false"==e.contentEditable)return null;t=oo(e=e.childNodes[t-1])}else{if(!e.parentNode||ro(e))return null;t=Qn(e),e=e.parentNode}}}(t.focusNode,t.focusOffset),o=function(e,t){for(;;){if(3==e.nodeType&&t<e.nodeValue.length)return e;if(1==e.nodeType&&t<e.childNodes.length){if("false"==e.contentEditable)return null;e=e.childNodes[t],t=0}else{if(!e.parentNode||ro(e))return null;t=Qn(e)+1,e=e.parentNode}}}(t.focusNode,t.focusOffset);if(n&&o&&n!=o){let t=o.pmViewDesc,r=e.domObserver.lastChangedTextNode;if(n==r||o==r)return r;if(!t||!t.isText(o.nodeValue))return o;if(e.input.compositionNode==o){let e=n.pmViewDesc;if(e&&e.isText(n.nodeValue))return o}}return n||o}function xi(e,t=!1){if(!(Mo&&e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),bi(e),t||e.docView&&e.docView.dirty){let n=yr(e);return n&&!n.eq(e.state.selection)?e.dispatch(e.state.tr.setSelection(n)):!e.markCursor&&!t||e.state.selection.empty?e.updateState(e.state):e.dispatch(e.state.tr.deleteSelection()),!0}return!1}}ti.compositionstart=ti.compositionupdate=e=>{if(!e.composing){e.domObserver.flush();let{state:t}=e,n=t.selection.$to;if(t.selection instanceof Dn&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some((e=>!1===e.type.spec.inclusive))))e.markCursor=e.state.storedMarks||n.marks(),xi(e,!0),e.markCursor=null;else if(xi(e,!t.selection.empty),go&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let t=e.domSelectionRange();for(let n=t.focusNode,o=t.focusOffset;n&&1==n.nodeType&&0!=o;){let t=o<0?n.lastChild:n.childNodes[o-1];if(!t)break;if(3==t.nodeType){let n=e.domSelection();n&&n.collapse(t,t.nodeValue.length);break}n=t,o=-1}}e.input.composing=!0}wi(e,vi)},ti.compositionend=(e,t)=>{e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,e.input.compositionPendingChanges=e.domObserver.pendingRecords().length?e.input.compositionID:0,e.input.compositionNode=null,e.input.compositionPendingChanges&&Promise.resolve().then((()=>e.domObserver.flush())),e.input.compositionID++,wi(e,20))};const Si=fo&&mo<15||ko&&Oo<604;function Mi(e,t,n,o,r){let i=_r(e,t,n,o,e.state.selection.$from);if(e.someProp("handlePaste",(t=>t(e,r,i||ze.empty))))return!0;if(!i)return!1;let s=function(e){return 0==e.openStart&&0==e.openEnd&&1==e.content.childCount?e.content.firstChild:null}(i),l=s?e.state.tr.replaceSelectionWith(s,o):e.state.tr.replaceSelection(i);return e.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function Ci(e){let t=e.getData("text/plain")||e.getData("Text");if(t)return t;let n=e.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}ei.copy=ti.cut=(e,t)=>{let n=t,o=e.state.selection,r="cut"==n.type;if(o.empty)return;let i=Si?null:n.clipboardData,s=o.content(),{dom:l,text:a}=jr(e,s);i?(n.preventDefault(),i.clearData(),i.setData("text/html",l.innerHTML),i.setData("text/plain",a)):function(e,t){if(!e.dom.parentNode)return;let n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";let o=getSelection(),r=document.createRange();r.selectNodeContents(t),e.dom.blur(),o.removeAllRanges(),o.addRange(r),setTimeout((()=>{n.parentNode&&n.parentNode.removeChild(n),e.focus()}),50)}(e,l),r&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},ti.paste=(e,t)=>{let n=t;if(e.composing&&!Mo)return;let o=Si?null:n.clipboardData,r=e.input.shiftKey&&45!=e.input.lastKeyCode;o&&Mi(e,Ci(o),o.getData("text/html"),r,n)?n.preventDefault():function(e,t){if(!e.dom.parentNode)return;let n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,o=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(o.contentEditable="true"),o.style.cssText="position: fixed; left: -10000px; top: 10px",o.focus();let r=e.input.shiftKey&&45!=e.input.lastKeyCode;setTimeout((()=>{e.focus(),o.parentNode&&o.parentNode.removeChild(o),n?Mi(e,o.value,null,r,t):Mi(e,o.textContent,o.innerHTML,r,t)}),50)}(e,n)};class Oi{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const Ti=xo?"altKey":"ctrlKey";function Ai(e,t){let n=e.someProp("dragCopies",(e=>!e(t)));return null!=n?n:!t[Ti]}ei.dragstart=(e,t)=>{let n=t,o=e.input.mouseDown;if(o&&o.done(),!n.dataTransfer)return;let r,i=e.state.selection,s=i.empty?null:e.posAtCoords(ai(n));if(s&&s.pos>=i.from&&s.pos<=(i instanceof In?i.to-1:i.to));else if(o&&o.mightDrag)r=In.create(e.state.doc,o.mightDrag.pos);else if(n.target&&1==n.target.nodeType){let t=e.docView.nearestDesc(n.target,!0);t&&t.node.type.spec.draggable&&t!=e.docView&&(r=In.create(e.state.doc,t.posBefore))}let l=(r||e.state.selection).content(),{dom:a,text:c,slice:d}=jr(e,l);(!n.dataTransfer.files.length||!vo||wo>120)&&n.dataTransfer.clearData(),n.dataTransfer.setData(Si?"Text":"text/html",a.innerHTML),n.dataTransfer.effectAllowed="copyMove",Si||n.dataTransfer.setData("text/plain",c),e.dragging=new Oi(d,Ai(e,n),r)},ei.dragend=e=>{let t=e.dragging;window.setTimeout((()=>{e.dragging==t&&(e.dragging=null)}),50)},ti.dragover=ti.dragenter=(e,t)=>t.preventDefault(),ti.drop=(e,t)=>{let n=t,o=e.dragging;if(e.dragging=null,!n.dataTransfer)return;let r=e.posAtCoords(ai(n));if(!r)return;let i=e.state.doc.resolve(r.pos),s=o&&o.slice;s?e.someProp("transformPasted",(t=>{s=t(s,e)})):s=_r(e,Ci(n.dataTransfer),Si?null:n.dataTransfer.getData("text/html"),!1,i);let l=!(!o||!Ai(e,n));if(e.someProp("handleDrop",(t=>t(e,n,s||ze.empty,l))))return void n.preventDefault();if(!s)return;n.preventDefault();let a=s?hn(e.state.doc,i.pos,s):i.pos;null==a&&(a=i.pos);let c=e.state.tr;if(l){let{node:e}=o;e?e.replace(c):c.deleteSelection()}let d=c.mapping.map(a),h=0==s.openStart&&0==s.openEnd&&1==s.content.childCount,p=c.doc;if(h?c.replaceRangeWith(d,d,s.content.firstChild):c.replaceRange(d,d,s),c.doc.eq(p))return;let u=c.doc.resolve(d);if(h&&In.isSelectable(s.content.firstChild)&&u.nodeAfter&&u.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new In(u));else{let t=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach(((e,n,o,r)=>t=r)),c.setSelection(Or(e,u,c.doc.resolve(t)))}e.focus(),e.dispatch(c.setMeta("uiEvent","drop"))},ei.focus=e=>{e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout((()=>{e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelectionRange())&&wr(e)}),20))},ei.blur=(e,t)=>{let n=t;e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),n.relatedTarget&&e.dom.contains(n.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},ei.beforeinput=(e,t)=>{if(vo&&Mo&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();let{domChangeCount:t}=e.input;setTimeout((()=>{if(e.input.domChangeCount!=t)return;if(e.dom.blur(),e.focus(),e.someProp("handleKeyDown",(t=>t(e,so(8,"Backspace")))))return;let{$cursor:n}=e.state.selection;n&&n.pos>0&&e.dispatch(e.state.tr.delete(n.pos-1,n.pos).scrollIntoView())}),50)}};for(let os in ti)ei[os]=ti[os];function Ei(e,t){if(e==t)return!0;for(let n in e)if(e[n]!==t[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}class Ni{constructor(e,t){this.toDOM=e,this.spec=t||Li,this.side=this.spec.side||0}map(e,t,n,o){let{pos:r,deleted:i}=e.mapResult(t.from+o,this.side<0?-1:1);return i?null:new Ii(r-n,r-n,this)}valid(){return!0}eq(e){return this==e||e instanceof Ni&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&Ei(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Di{constructor(e,t){this.attrs=e,this.spec=t||Li}map(e,t,n,o){let r=e.map(t.from+o,this.spec.inclusiveStart?-1:1)-n,i=e.map(t.to+o,this.spec.inclusiveEnd?1:-1)-n;return r>=i?null:new Ii(r,i,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Di&&Ei(this.attrs,e.attrs)&&Ei(this.spec,e.spec)}static is(e){return e.type instanceof Di}destroy(){}}class Ri{constructor(e,t){this.attrs=e,this.spec=t||Li}map(e,t,n,o){let r=e.mapResult(t.from+o,1);if(r.deleted)return null;let i=e.mapResult(t.to+o,-1);return i.deleted||i.pos<=r.pos?null:new Ii(r.pos-n,i.pos-n,this)}valid(e,t){let n,{index:o,offset:r}=e.content.findIndex(t.from);return r==t.from&&!(n=e.child(o)).isText&&r+n.nodeSize==t.to}eq(e){return this==e||e instanceof Ri&&Ei(this.attrs,e.attrs)&&Ei(this.spec,e.spec)}destroy(){}}class Ii{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new Ii(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new Ii(e,e,new Ni(t,n))}static inline(e,t,n,o){return new Ii(e,t,new Di(n,o))}static node(e,t,n,o){return new Ii(e,t,new Ri(n,o))}get spec(){return this.type.spec}get inline(){return this.type instanceof Di}get widget(){return this.type instanceof Ni}}const Pi=[],Li={};class zi{constructor(e,t){this.local=e.length?e:Pi,this.children=t.length?t:Pi}static create(e,t){return t.length?ji(t,e,0,Li):Bi}find(e,t,n){let o=[];return this.findInner(null==e?0:e,null==t?1e9:t,o,0,n),o}findInner(e,t,n,o,r){for(let i=0;i<this.local.length;i++){let s=this.local[i];s.from<=t&&s.to>=e&&(!r||r(s.spec))&&n.push(s.copy(s.from+o,s.to+o))}for(let i=0;i<this.children.length;i+=3)if(this.children[i]<t&&this.children[i+1]>e){let s=this.children[i]+1;this.children[i+2].findInner(e-s,t-s,n,o+s,r)}}map(e,t,n){return this==Bi||0==e.maps.length?this:this.mapInner(e,t,0,0,n||Li)}mapInner(e,t,n,o,r){let i;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(e,n,o);l&&l.type.valid(t,l)?(i||(i=[])).push(l):r.onRemove&&r.onRemove(this.local[s].spec)}return this.children.length?function(e,t,n,o,r,i,s){let l=e.slice();for(let c=0,d=i;c<n.maps.length;c++){let e=0;n.maps[c].forEach(((t,n,o,r)=>{let i=r-o-(n-t);for(let s=0;s<l.length;s+=3){let o=l[s+1];if(o<0||t>o+d-e)continue;let r=l[s]+d-e;n>=r?l[s+1]=t<=r?-2:-1:t>=d&&i&&(l[s]+=i,l[s+1]+=i)}e+=i})),d=n.maps[c].map(d,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(-2==l[c+1]){a=!0,l[c+1]=-1;continue}let t=n.map(e[c]+i),d=t-r;if(d<0||d>=o.content.size){a=!0;continue}let h=n.map(e[c+1]+i,-1)-r,{index:p,offset:u}=o.content.findIndex(d),f=o.maybeChild(p);if(f&&u==d&&u+f.nodeSize==h){let o=l[c+2].mapInner(n,f,t+1,e[c]+i+1,s);o!=Bi?(l[c]=d,l[c+1]=h,l[c+2]=o):(l[c+1]=-2,a=!0)}else a=!0}if(a){let a=function(e,t,n,o,r,i,s){function l(e,t){for(let i=0;i<e.local.length;i++){let l=e.local[i].map(o,r,t);l?n.push(l):s.onRemove&&s.onRemove(e.local[i].spec)}for(let n=0;n<e.children.length;n+=3)l(e.children[n+2],e.children[n]+t+1)}for(let a=0;a<e.length;a+=3)-1==e[a+1]&&l(e[a+2],t[a]+i+1);return n}(l,e,t,n,r,i,s),c=ji(a,o,0,s);t=c.local;for(let e=0;e<l.length;e+=3)l[e+1]<0&&(l.splice(e,3),e-=3);for(let e=0,t=0;e<c.children.length;e+=3){let n=c.children[e];for(;t<l.length&&l[t]<n;)t+=3;l.splice(t,0,c.children[e],c.children[e+1],c.children[e+2])}}return new zi(t.sort(_i),l)}(this.children,i||[],e,t,n,o,r):i?new zi(i.sort(_i),Pi):Bi}add(e,t){return t.length?this==Bi?zi.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let o,r=0;e.forEach(((e,i)=>{let s,l=i+n;if(s=Hi(t,e,l)){for(o||(o=this.children.slice());r<o.length&&o[r]<i;)r+=3;o[r]==i?o[r+2]=o[r+2].addInner(e,s,l+1):o.splice(r,0,i,i+e.nodeSize,ji(s,e,l+1,Li)),r+=3}}));let i=$i(r?Fi(t):t,-n);for(let s=0;s<i.length;s++)i[s].type.valid(e,i[s])||i.splice(s--,1);return new zi(i.length?this.local.concat(i).sort(_i):this.local,o||this.children)}remove(e){return 0==e.length||this==Bi?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,o=this.local;for(let r=0;r<n.length;r+=3){let o,i=n[r]+t,s=n[r+1]+t;for(let t,n=0;n<e.length;n++)(t=e[n])&&t.from>i&&t.to<s&&(e[n]=null,(o||(o=[])).push(t));if(!o)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(o,i+1);l!=Bi?n[r+2]=l:(n.splice(r,3),r-=3)}if(o.length)for(let r,i=0;i<e.length;i++)if(r=e[i])for(let e=0;e<o.length;e++)o[e].eq(r,t)&&(o==this.local&&(o=this.local.slice()),o.splice(e--,1));return n==this.children&&o==this.local?this:o.length||n.length?new zi(o,n):Bi}forChild(e,t){if(this==Bi)return this;if(t.isLeaf)return zi.empty;let n,o;for(let s=0;s<this.children.length;s+=3)if(this.children[s]>=e){this.children[s]==e&&(n=this.children[s+2]);break}let r=e+1,i=r+t.content.size;for(let s=0;s<this.local.length;s++){let e=this.local[s];if(e.from<i&&e.to>r&&e.type instanceof Di){let t=Math.max(r,e.from)-r,n=Math.min(i,e.to)-r;t<n&&(o||(o=[])).push(e.copy(t,n))}}if(o){let e=new zi(o.sort(_i),Pi);return n?new Vi([e,n]):e}return n||Bi}eq(e){if(this==e)return!0;if(!(e instanceof zi)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return qi(this.localsInner(e))}localsInner(e){if(this==Bi)return Pi;if(e.inlineContent||!this.local.some(Di.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof Di||t.push(this.local[n]);return t}forEachSet(e){e(this)}}zi.empty=new zi([],[]),zi.removeOverlap=qi;const Bi=zi.empty;class Vi{constructor(e){this.members=e}map(e,t){const n=this.members.map((n=>n.map(e,t,Li)));return Vi.from(n)}forChild(e,t){if(t.isLeaf)return zi.empty;let n=[];for(let o=0;o<this.members.length;o++){let r=this.members[o].forChild(e,t);r!=Bi&&(r instanceof Vi?n=n.concat(r.members):n.push(r))}return Vi.from(n)}eq(e){if(!(e instanceof Vi)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let o=0;o<this.members.length;o++){let r=this.members[o].localsInner(e);if(r.length)if(t){n&&(t=t.slice(),n=!1);for(let e=0;e<r.length;e++)t.push(r[e])}else t=r}return t?qi(n?t:t.sort(_i)):Pi}static from(e){switch(e.length){case 0:return Bi;case 1:return e[0];default:return new Vi(e.every((e=>e instanceof zi))?e:e.reduce(((e,t)=>e.concat(t instanceof zi?t:t.members)),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function $i(e,t){if(!t||!e.length)return e;let n=[];for(let o=0;o<e.length;o++){let r=e[o];n.push(new Ii(r.from+t,r.to+t,r.type))}return n}function Hi(e,t,n){if(t.isLeaf)return null;let o=n+t.nodeSize,r=null;for(let i,s=0;s<e.length;s++)(i=e[s])&&i.from>n&&i.to<o&&((r||(r=[])).push(i),e[s]=null);return r}function Fi(e){let t=[];for(let n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function ji(e,t,n,o){let r=[],i=!1;t.forEach(((t,s)=>{let l=Hi(e,t,s+n);if(l){i=!0;let e=ji(l,t,n+s+1,o);e!=Bi&&r.push(s,s+t.nodeSize,e)}}));let s=$i(i?Fi(e):e,-n).sort(_i);for(let l=0;l<s.length;l++)s[l].type.valid(t,s[l])||(o.onRemove&&o.onRemove(s[l].spec),s.splice(l--,1));return s.length||r.length?new zi(s,r):Bi}function _i(e,t){return e.from-t.from||e.to-t.to}function qi(e){let t=e;for(let n=0;n<t.length-1;n++){let o=t[n];if(o.from!=o.to)for(let r=n+1;r<t.length;r++){let i=t[r];if(i.from!=o.from){i.from<o.to&&(t==e&&(t=e.slice()),t[n]=o.copy(o.from,i.from),Wi(t,r,o.copy(i.from,o.to)));break}i.to!=o.to&&(t==e&&(t=e.slice()),t[r]=i.copy(i.from,o.to),Wi(t,r+1,i.copy(o.to,i.to)))}}return t}function Wi(e,t,n){for(;t<e.length&&_i(n,e[t])>0;)t++;e.splice(t,0,n)}function Ki(e){let t=[];return e.someProp("decorations",(n=>{let o=n(e.state);o&&o!=Bi&&t.push(o)})),e.cursorWrapper&&t.push(zi.create(e.state.doc,[e.cursorWrapper.deco])),Vi.from(t)}const Ui={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Ji=fo&&mo<=11;class Gi{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class Qi{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Gi,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver((e=>{for(let t=0;t<e.length;t++)this.queue.push(e[t]);fo&&mo<=11&&e.some((e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length))?this.flushSoon():this.flush()})),Ji&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((()=>{this.flushingSoon=-1,this.flush()}),20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Ui)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout((()=>this.flush()),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout((()=>this.suppressingSelectionUpdates=!1),50)}onSelectionChange(){if(Tr(this.view)){if(this.suppressingSelectionUpdates)return wr(this.view);if(fo&&mo<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&eo(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t,n=new Set;for(let r=e.focusNode;r;r=Yn(r))n.add(r);for(let r=e.anchorNode;r;r=Yn(r))if(n.has(r)){t=r;break}let o=t&&this.view.docView.nearestDesc(t);return o&&o.ignoreMutation({type:"selection",target:3==t.nodeType?t.parentNode:t})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Tr(e)&&!this.ignoreSelectionChange(n),r=-1,i=-1,s=!1,l=[];if(e.editable)for(let c=0;c<t.length;c++){let e=this.registerMutation(t[c],l);e&&(r=r<0?e.from:Math.min(e.from,r),i=i<0?e.to:Math.max(e.to,i),e.typeOver&&(s=!0))}if(go&&l.length){let t=l.filter((e=>"BR"==e.nodeName));if(2==t.length){let[e,n]=t;e.parentNode&&e.parentNode.parentNode==n.parentNode?n.remove():e.remove()}else{let{focusNode:n}=this.currentSelection;for(let o of t){let t=o.parentNode;!t||"LI"!=t.nodeName||n&&es(e,n)==t||o.remove()}}}let a=null;r<0&&o&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&io(n)&&(a=yr(e))&&a.eq(Tn.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,wr(e),this.currentSelection.set(n),e.scrollToSelection()):(r>-1||o)&&(r>-1&&(e.docView.markDirty(r,i),function(e){if(Yi.has(e))return;if(Yi.set(e,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace)){if(e.requiresGeckoHackNode=go,Xi)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Xi=!0}}(e)),this.handleDOMChange(r,i,s,l),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||wr(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style")))return null;if(!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let n=0;n<e.addedNodes.length;n++){let o=e.addedNodes[n];t.push(o),3==o.nodeType&&(this.lastChangedTextNode=o)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let o=e.previousSibling,r=e.nextSibling;if(fo&&mo<=11&&e.addedNodes.length)for(let t=0;t<e.addedNodes.length;t++){let{previousSibling:n,nextSibling:i}=e.addedNodes[t];(!n||Array.prototype.indexOf.call(e.addedNodes,n)<0)&&(o=n),(!i||Array.prototype.indexOf.call(e.addedNodes,i)<0)&&(r=i)}let i=o&&o.parentNode==e.target?Qn(o)+1:0,s=n.localPosFromDOM(e.target,i,-1),l=r&&r.parentNode==e.target?Qn(r):e.target.childNodes.length;return{from:s,to:n.localPosFromDOM(e.target,l,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let Yi=new WeakMap,Xi=!1;function Zi(e,t){let n=t.startContainer,o=t.startOffset,r=t.endContainer,i=t.endOffset,s=e.domAtPos(e.state.selection.anchor);return eo(s.node,s.offset,r,i)&&([n,o,r,i]=[r,i,n,o]),{anchorNode:n,anchorOffset:o,focusNode:r,focusOffset:i}}function es(e,t){for(let n=t.parentNode;n&&n!=e.dom;n=n.parentNode){let t=e.docView.nearestDesc(n,!0);if(t&&t.node.isBlock)return n}return null}function ts(e){let t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(bo&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}if(e.parentNode.lastChild==e||bo&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}const ns=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function rs(e,t,n,o,r){let i=e.input.compositionPendingChanges||(e.composing?e.input.compositionID:0);if(e.input.compositionPendingChanges=0,t<0){let t=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,n=yr(e,t);if(n&&!e.state.selection.eq(n)){if(vo&&Mo&&13===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime&&e.someProp("handleKeyDown",(t=>t(e,so(13,"Enter")))))return;let o=e.state.tr.setSelection(n);"pointer"==t?o.setMeta("pointer",!0):"key"==t&&o.scrollIntoView(),i&&o.setMeta("composition",i),e.dispatch(o)}return}let s=e.state.doc.resolve(t),l=s.sharedDepth(n);t=s.before(l+1),n=e.state.doc.resolve(n).after(l+1);let a,c,d=e.state.selection,h=function(e,t,n){let o,{node:r,fromOffset:i,toOffset:s,from:l,to:a}=e.docView.parseRange(t,n),c=e.domSelectionRange(),d=c.anchorNode;if(d&&e.dom.contains(1==d.nodeType?d:d.parentNode)&&(o=[{node:d,offset:c.anchorOffset}],io(c)||o.push({node:c.focusNode,offset:c.focusOffset})),vo&&8===e.input.lastKeyCode)for(let g=s;g>i;g--){let e=r.childNodes[g-1],t=e.pmViewDesc;if("BR"==e.nodeName&&!t){s=g;break}if(!t||t.size)break}let h=e.state.doc,p=e.someProp("domParser")||kt.fromSchema(e.state.schema),u=h.resolve(l),f=null,m=p.parse(r,{topNode:u.parent,topMatch:u.parent.contentMatchAt(u.index()),topOpen:!0,from:i,to:s,preserveWhitespace:"pre"!=u.parent.type.whitespace||"full",findPositions:o,ruleFromNode:ts,context:u});if(o&&null!=o[0].pos){let e=o[0].pos,t=o[1]&&o[1].pos;null==t&&(t=e),f={anchor:e+l,head:t+l}}return{doc:m,sel:f,from:l,to:a}}(e,t,n),p=e.state.doc,u=p.slice(h.from,h.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(a=e.state.selection.to,c="end"):(a=e.state.selection.from,c="start"),e.input.lastKeyCode=null;let f=function(e,t,n,o,r){let i=e.findDiffStart(t,n);if(null==i)return null;let{a:s,b:l}=e.findDiffEnd(t,n+e.size,n+t.size);if("end"==r){o-=s+Math.max(0,i-Math.min(s,l))-i}if(s<i&&e.size<t.size){let e=o<=i&&o>=s?i-o:0;i-=e,i&&i<t.size&&ls(t.textBetween(i-1,i+1))&&(i+=e?1:-1),l=i+(l-s),s=i}else if(l<i){let t=o<=i&&o>=l?i-o:0;i-=t,i&&i<e.size&&ls(e.textBetween(i-1,i+1))&&(i+=t?1:-1),s=i+(s-l),l=i}return{start:i,endA:s,endB:l}}(u.content,h.doc.content,h.from,a,c);if(f&&e.input.domChangeCount++,(ko&&e.input.lastIOSEnter>Date.now()-225||Mo)&&r.some((e=>1==e.nodeType&&!ns.test(e.nodeName)))&&(!f||f.endA>=f.endB)&&e.someProp("handleKeyDown",(t=>t(e,so(13,"Enter")))))return void(e.input.lastIOSEnter=0);if(!f){if(!(o&&d instanceof Dn&&!d.empty&&d.$head.sameParent(d.$anchor))||e.composing||h.sel&&h.sel.anchor!=h.sel.head){if(h.sel){let t=is(e,e.state.doc,h.sel);if(t&&!t.eq(e.state.selection)){let n=e.state.tr.setSelection(t);i&&n.setMeta("composition",i),e.dispatch(n)}}return}f={start:d.from,endA:d.to,endB:d.to}}e.state.selection.from<e.state.selection.to&&f.start==f.endB&&e.state.selection instanceof Dn&&(f.start>e.state.selection.from&&f.start<=e.state.selection.from+2&&e.state.selection.from>=h.from?f.start=e.state.selection.from:f.endA<e.state.selection.to&&f.endA>=e.state.selection.to-2&&e.state.selection.to<=h.to&&(f.endB+=e.state.selection.to-f.endA,f.endA=e.state.selection.to)),fo&&mo<=11&&f.endB==f.start+1&&f.endA==f.start&&f.start>h.from&&"  "==h.doc.textBetween(f.start-h.from-1,f.start-h.from+1)&&(f.start--,f.endA--,f.endB--);let m,g=h.doc.resolveNoCache(f.start-h.from),y=h.doc.resolveNoCache(f.endB-h.from),v=p.resolve(f.start),w=g.sameParent(y)&&g.parent.inlineContent&&v.end()>=f.endA;if((ko&&e.input.lastIOSEnter>Date.now()-225&&(!w||r.some((e=>"DIV"==e.nodeName||"P"==e.nodeName)))||!w&&g.pos<h.doc.content.size&&(!g.sameParent(y)||!g.parent.inlineContent)&&(m=Tn.findFrom(h.doc.resolve(g.pos+1),1,!0))&&m.head>g.pos)&&e.someProp("handleKeyDown",(t=>t(e,so(13,"Enter")))))return void(e.input.lastIOSEnter=0);if(e.state.selection.anchor>f.start&&function(e,t,n,o,r){if(n-t<=r.pos-o.pos||ss(o,!0,!1)<r.pos)return!1;let i=e.resolve(t);if(!o.parent.isTextblock){let e=i.nodeAfter;return null!=e&&n==t+e.nodeSize}if(i.parentOffset<i.parent.content.size||!i.parent.isTextblock)return!1;let s=e.resolve(ss(i,!0,!0));return!(!s.parent.isTextblock||s.pos>n||ss(s,!0,!1)<n)&&o.parent.content.cut(o.parentOffset).eq(s.parent.content)}(p,f.start,f.endA,g,y)&&e.someProp("handleKeyDown",(t=>t(e,so(8,"Backspace")))))return void(Mo&&vo&&e.domObserver.suppressSelectionUpdates());vo&&f.endB==f.start&&(e.input.lastChromeDelete=Date.now()),Mo&&!w&&g.start()!=y.start()&&0==y.parentOffset&&g.depth==y.depth&&h.sel&&h.sel.anchor==h.sel.head&&h.sel.head==f.endA&&(f.endB-=2,y=h.doc.resolveNoCache(f.endB-h.from),setTimeout((()=>{e.someProp("handleKeyDown",(function(t){return t(e,so(13,"Enter"))}))}),20));let b,k,x,S=f.start,M=f.endA;if(w)if(g.pos==y.pos)fo&&mo<=11&&0==g.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout((()=>wr(e)),20)),b=e.state.tr.delete(S,M),k=p.resolve(f.start).marksAcross(p.resolve(f.endA));else if(f.endA==f.endB&&(x=function(e,t){let n,o,r,i=e.firstChild.marks,s=t.firstChild.marks,l=i,a=s;for(let d=0;d<s.length;d++)l=s[d].removeFromSet(l);for(let d=0;d<i.length;d++)a=i[d].removeFromSet(a);if(1==l.length&&0==a.length)o=l[0],n="add",r=e=>e.mark(o.addToSet(e.marks));else{if(0!=l.length||1!=a.length)return null;o=a[0],n="remove",r=e=>e.mark(o.removeFromSet(e.marks))}let c=[];for(let d=0;d<t.childCount;d++)c.push(r(t.child(d)));if(Ne.from(c).eq(e))return{mark:o,type:n}}(g.parent.content.cut(g.parentOffset,y.parentOffset),v.parent.content.cut(v.parentOffset,f.endA-v.start()))))b=e.state.tr,"add"==x.type?b.addMark(S,M,x.mark):b.removeMark(S,M,x.mark);else if(g.parent.child(g.index()).isText&&g.index()==y.index()-(y.textOffset?0:1)){let t=g.parent.textBetween(g.parentOffset,y.parentOffset);if(e.someProp("handleTextInput",(n=>n(e,S,M,t))))return;b=e.state.tr.insertText(t,S,M)}if(b||(b=e.state.tr.replace(S,M,h.doc.slice(f.start-h.from,f.endB-h.from))),h.sel){let t=is(e,b.doc,h.sel);t&&!(vo&&e.composing&&t.empty&&(f.start!=f.endB||e.input.lastChromeDelete<Date.now()-100)&&(t.head==S||t.head==b.mapping.map(M)-1)||fo&&t.empty&&t.head==S)&&b.setSelection(t)}k&&b.ensureMarks(k),i&&b.setMeta("composition",i),e.dispatch(b.scrollIntoView())}function is(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:Or(e,t.resolve(n.anchor),t.resolve(n.head))}function ss(e,t,n){let o=e.depth,r=t?e.end():e.pos;for(;o>0&&(t||e.indexAfter(o)==e.node(o).childCount);)o--,r++,t=!1;if(n){let t=e.node(o).maybeChild(e.indexAfter(o));for(;t&&!t.isLeaf;)t=t.firstChild,r++}return r}function ls(e){if(2!=e.length)return!1;let t=e.charCodeAt(0),n=e.charCodeAt(1);return t>=56320&&t<=57343&&n>=55296&&n<=56319}class as{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new oi,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(us),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=hs(this),ds(this),this.nodeViews=ps(this),this.docView=tr(this.state.doc,cs(this),Ki(this),this.dom,this),this.domObserver=new Qi(this,((e,t,n,o)=>rs(this,e,t,n,o))),this.domObserver.start(),function(e){for(let t in ei){let n=ei[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=t=>{!li(e,t)||si(e,t)||!e.editable&&t.type in ti||n(e,t)},ni[t]?{passive:!0}:void 0)}bo&&e.dom.addEventListener("input",(()=>null)),ii(e)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&ii(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(us),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let o=this.state,r=!1,i=!1;e.storedMarks&&this.composing&&(bi(this),i=!0),this.state=e;let s=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(s||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let e=ps(this);(function(e,t){let n=0,o=0;for(let r in e){if(e[r]!=t[r])return!0;n++}for(let r in t)o++;return n!=o})(e,this.nodeViews)&&(this.nodeViews=e,r=!0)}(s||t.handleDOMEvents!=this._props.handleDOMEvents)&&ii(this),this.editable=hs(this),ds(this);let l=Ki(this),a=cs(this),c=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",d=r||!this.docView.matchesNode(e.doc,a,l);!d&&e.selection.eq(o.selection)||(i=!0);let h="preserve"==c&&i&&null==this.dom.style.overflowAnchor&&function(e){let t,n,o=e.dom.getBoundingClientRect(),r=Math.max(0,o.top);for(let i=(o.left+o.right)/2,s=r+1;s<Math.min(innerHeight,o.bottom);s+=5){let o=e.root.elementFromPoint(i,s);if(!o||o==e.dom||!e.dom.contains(o))continue;let l=o.getBoundingClientRect();if(l.top>=r-20){t=o,n=l.top;break}}return{refDOM:t,refTop:n,stack:Do(e.dom)}}(this);if(i){this.domObserver.stop();let t=d&&(fo||vo)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&function(e,t){let n=Math.min(e.$anchor.sharedDepth(e.head),t.$anchor.sharedDepth(t.head));return e.$anchor.start(n)!=t.$anchor.start(n)}(o.selection,e.selection);if(d){let n=vo?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=ki(this)),!r&&this.docView.update(e.doc,a,l,this)||(this.docView.updateOuterDeco(a),this.docView.destroy(),this.docView=tr(e.doc,a,l,this.dom,this)),n&&!this.trackWrites&&(t=!0)}t||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(e){let t=e.docView.domFromPos(e.state.selection.anchor,0),n=e.domSelectionRange();return eo(t.node,t.offset,n.anchorNode,n.anchorOffset)}(this))?wr(this,t):(Mr(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),"reset"==c?this.dom.scrollTop=0:"to selection"==c?this.scrollToSelection():h&&function({refDOM:e,refTop:t,stack:n}){let o=e?e.getBoundingClientRect().top:0;Ro(n,0==o?0:o-t)}(h)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode))if(this.someProp("handleScrollToSelection",(e=>e(this))));else if(this.state.selection instanceof In){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&No(this,t.getBoundingClientRect(),e)}else No(this,this.coordsAtPos(this.state.selection.head,1),e);else;}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let t=this.directPlugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let t=this.state.plugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,o=-1;if(this.state.doc.nodeAt(n.from)==n.node)o=n.from;else{let e=n.from+(this.state.doc.content.size-t.doc.content.size);(e>0&&this.state.doc.nodeAt(e))==n.node&&(o=e)}this.dragging=new Oi(e.slice,e.move,o<0?void 0:In.create(this.state.doc,o))}someProp(e,t){let n,o=this._props&&this._props[e];if(null!=o&&(n=t?t(o):o))return n;for(let i=0;i<this.directPlugins.length;i++){let o=this.directPlugins[i].props[e];if(null!=o&&(n=t?t(o):o))return n}let r=this.state.plugins;if(r)for(let i=0;i<r.length;i++){let o=r[i].props[e];if(null!=o&&(n=t?t(o):o))return n}}hasFocus(){if(fo){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(Io)return e.focus(Io);let t=Do(e);e.focus(null==Io?{get preventScroll(){return Io={preventScroll:!0},!0}}:void 0),Io||(Io=!1,Ro(t,0))}(this.dom),wr(this),this.domObserver.start()}get root(){let e=this._root;if(null==e)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return e||document}updateRoot(){this._root=null}posAtCoords(e){return Bo(this,e)}coordsAtPos(e,t=1){return Fo(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let o=this.docView.posFromDOM(e,t,n);if(null==o)throw new RangeError("DOM position not inside the editor");return o}endOfTextblock(e,t){return Go(this,t||this.state,e)}pasteHTML(e,t){return Mi(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Mi(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return jr(this,e)}destroy(){this.docView&&(!function(e){e.domObserver.stop();for(let t in e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],Ki(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Xn=null)}get isDestroyed(){return null==this.docView}dispatchEvent(e){return function(e,t){si(e,t)||!ei[t.type]||!e.editable&&t.type in ti||ei[t.type](e,t)}(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return e?bo&&11===this.root.nodeType&&function(e){let t=e.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}(this.dom.ownerDocument)==this.dom&&function(e,t){if(t.getComposedRanges){let n=t.getComposedRanges(e.root)[0];if(n)return Zi(e,n)}let n;function o(e){e.preventDefault(),e.stopImmediatePropagation(),n=e.getTargetRanges()[0]}return e.dom.addEventListener("beforeinput",o,!0),document.execCommand("indent"),e.dom.removeEventListener("beforeinput",o,!0),n?Zi(e,n):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function cs(e){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),e.someProp("attributes",(n=>{if("function"==typeof n&&(n=n(e.state)),n)for(let e in n)"class"==e?t.class+=" "+n[e]:"style"==e?t.style=(t.style?t.style+";":"")+n[e]:t[e]||"contenteditable"==e||"nodeName"==e||(t[e]=String(n[e]))})),t.translate||(t.translate="no"),[Ii.node(0,e.state.doc.content.size,t)]}function ds(e){if(e.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:Ii.widget(e.state.selection.from,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function hs(e){return!e.someProp("editable",(t=>!1===t(e.state)))}function ps(e){let t=Object.create(null);function n(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function us(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var fs={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},ms={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},gs="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),ys="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),vs=0;vs<10;vs++)fs[48+vs]=fs[96+vs]=String(vs);for(vs=1;vs<=24;vs++)fs[vs+111]="F"+vs;for(vs=65;vs<=90;vs++)fs[vs]=String.fromCharCode(vs+32),ms[vs]=String.fromCharCode(vs);for(var ws in fs)ms.hasOwnProperty(ws)||(ms[ws]=fs[ws]);const bs="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function ks(e){let t,n,o,r,i=e.split(/-(?!$)/),s=i[i.length-1];"Space"==s&&(s=" ");for(let l=0;l<i.length-1;l++){let e=i[l];if(/^(cmd|meta|m)$/i.test(e))r=!0;else if(/^a(lt)?$/i.test(e))t=!0;else if(/^(c|ctrl|control)$/i.test(e))n=!0;else if(/^s(hift)?$/i.test(e))o=!0;else{if(!/^mod$/i.test(e))throw new Error("Unrecognized modifier name: "+e);bs?r=!0:n=!0}}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),r&&(s="Meta-"+s),o&&(s="Shift-"+s),s}function xs(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function Ss(e){let t=function(e){let t=Object.create(null);for(let n in e)t[ks(n)]=e[n];return t}(e);return function(e,n){let o,r=function(e){var t=!(gs&&e.metaKey&&e.shiftKey&&!e.ctrlKey&&!e.altKey||ys&&e.shiftKey&&e.key&&1==e.key.length||"Unidentified"==e.key)&&e.key||(e.shiftKey?ms:fs)[e.keyCode]||e.key||"Unidentified";return"Esc"==t&&(t="Escape"),"Del"==t&&(t="Delete"),"Left"==t&&(t="ArrowLeft"),"Up"==t&&(t="ArrowUp"),"Right"==t&&(t="ArrowRight"),"Down"==t&&(t="ArrowDown"),t}(n),i=t[xs(r,n)];if(i&&i(e.state,e.dispatch,e))return!0;if(1==r.length&&" "!=r){if(n.shiftKey){let o=t[xs(r,n,!1)];if(o&&o(e.state,e.dispatch,e))return!0}if((n.shiftKey||n.altKey||n.metaKey||r.charCodeAt(0)>127)&&(o=fs[n.keyCode])&&o!=r){let r=t[xs(o,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}const Ms=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function Cs(e,t){let{$cursor:n}=e.selection;return!n||(t?!t.endOfTextblock("backward",e):n.parentOffset>0)?null:n}const Os=(e,t,n)=>{let o=Cs(e,n);if(!o)return!1;let r=Ns(o);if(!r){let n=o.blockRange(),r=n&&tn(n);return null!=r&&(t&&t(e.tr.lift(n,r).scrollIntoView()),!0)}let i=r.nodeBefore;if(Hs(e,r,t,-1))return!0;if(0==o.parent.content.size&&(As(i,"end")||In.isSelectable(i)))for(let s=o.depth;;s--){let n=pn(e.doc,o.before(s),o.after(s),ze.empty);if(n&&n.slice.size<n.to-n.from){if(t){let o=e.tr.step(n);o.setSelection(As(i,"end")?Tn.findFrom(o.doc.resolve(o.mapping.map(r.pos,-1)),-1):In.create(o.doc,r.pos-i.nodeSize)),t(o.scrollIntoView())}return!0}if(1==s||o.node(s-1).childCount>1)break}return!(!i.isAtom||r.depth!=o.depth-1)&&(t&&t(e.tr.delete(r.pos-i.nodeSize,r.pos).scrollIntoView()),!0)};function Ts(e,t,n){let o=t.nodeBefore,r=t.pos-1;for(;!o.isTextblock;r--){if(o.type.spec.isolating)return!1;let e=o.lastChild;if(!e)return!1;o=e}let i=t.nodeAfter,s=t.pos+1;for(;!i.isTextblock;s++){if(i.type.spec.isolating)return!1;let e=i.firstChild;if(!e)return!1;i=e}let l=pn(e.doc,r,s,ze.empty);if(!l||l.from!=r||l instanceof Qt&&l.slice.size>=s-r)return!1;if(n){let t=e.tr.step(l);t.setSelection(Dn.create(t.doc,r)),n(t.scrollIntoView())}return!0}function As(e,t,n=!1){for(let o=e;o;o="start"==t?o.firstChild:o.lastChild){if(o.isTextblock)return!0;if(n&&1!=o.childCount)return!1}return!1}const Es=(e,t,n)=>{let{$head:o,empty:r}=e.selection,i=o;if(!r)return!1;if(o.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):o.parentOffset>0)return!1;i=Ns(o)}let s=i&&i.nodeBefore;return!(!s||!In.isSelectable(s))&&(t&&t(e.tr.setSelection(In.create(e.doc,i.pos-s.nodeSize)).scrollIntoView()),!0)};function Ns(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function Ds(e,t){let{$cursor:n}=e.selection;return!n||(t?!t.endOfTextblock("forward",e):n.parentOffset<n.parent.content.size)?null:n}const Rs=(e,t,n)=>{let o=Ds(e,n);if(!o)return!1;let r=Ps(o);if(!r)return!1;let i=r.nodeAfter;if(Hs(e,r,t,1))return!0;if(0==o.parent.content.size&&(As(i,"start")||In.isSelectable(i))){let n=pn(e.doc,o.before(),o.after(),ze.empty);if(n&&n.slice.size<n.to-n.from){if(t){let o=e.tr.step(n);o.setSelection(As(i,"start")?Tn.findFrom(o.doc.resolve(o.mapping.map(r.pos)),1):In.create(o.doc,o.mapping.map(r.pos))),t(o.scrollIntoView())}return!0}}return!(!i.isAtom||r.depth!=o.depth-1)&&(t&&t(e.tr.delete(r.pos,r.pos+i.nodeSize).scrollIntoView()),!0)},Is=(e,t,n)=>{let{$head:o,empty:r}=e.selection,i=o;if(!r)return!1;if(o.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):o.parentOffset<o.parent.content.size)return!1;i=Ps(o)}let s=i&&i.nodeAfter;return!(!s||!In.isSelectable(s))&&(t&&t(e.tr.setSelection(In.create(e.doc,i.pos)).scrollIntoView()),!0)};function Ps(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}const Ls=(e,t)=>{let{$head:n,$anchor:o}=e.selection;return!(!n.parent.type.spec.code||!n.sameParent(o))&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function zs(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}const Bs=(e,t)=>{let n=e.selection,{$from:o,$to:r}=n;if(n instanceof Ln||o.parent.inlineContent||r.parent.inlineContent)return!1;let i=zs(r.parent.contentMatchAt(r.indexAfter()));if(!i||!i.isTextblock)return!1;if(t){let n=(!o.parentOffset&&r.index()<r.parent.childCount?o:r).pos,s=e.tr.insert(n,i.createAndFill());s.setSelection(Dn.create(s.doc,n+1)),t(s.scrollIntoView())}return!0},Vs=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let o=n.before();if(ln(e.doc,o))return t&&t(e.tr.split(o).scrollIntoView()),!0}let o=n.blockRange(),r=o&&tn(o);return null!=r&&(t&&t(e.tr.lift(o,r).scrollIntoView()),!0)};const $s=(e,t)=>{let{$from:n,$to:o}=e.selection;if(e.selection instanceof In&&e.selection.node.isBlock)return!(!n.parentOffset||!ln(e.doc,n.pos)||(t&&t(e.tr.split(n.pos).scrollIntoView()),0));if(!n.depth)return!1;let r,i,s=[],l=!1,a=!1;for(let p=n.depth;;p--){if(n.node(p).isBlock){l=n.end(p)==n.pos+(n.depth-p),a=n.start(p)==n.pos-(n.depth-p),i=zs(n.node(p-1).contentMatchAt(n.indexAfter(p-1))),s.unshift(l&&i?{type:i}:null),r=p;break}if(1==p)return!1;s.unshift(null)}let c=e.tr;(e.selection instanceof Dn||e.selection instanceof Ln)&&c.deleteSelection();let d=c.mapping.map(n.pos),h=ln(c.doc,d,s.length,s);if(h||(s[0]=i?{type:i}:null,h=ln(c.doc,d,s.length,s)),!h)return!1;if(c.split(d,s.length,s),!l&&a&&n.node(r).type!=i){let e=c.mapping.map(n.before(r)),t=c.doc.resolve(e);i&&n.node(r-1).canReplaceWith(t.index(),t.index()+1,i)&&c.setNodeMarkup(c.mapping.map(n.before(r)),i)}return t&&t(c.scrollIntoView()),!0};function Hs(e,t,n,o){let r,i,s=t.nodeBefore,l=t.nodeAfter,a=s.type.spec.isolating||l.type.spec.isolating;if(!a&&function(e,t,n){let o=t.nodeBefore,r=t.nodeAfter,i=t.index();return!(!(o&&r&&o.type.compatibleContent(r.type))||(!o.content.size&&t.parent.canReplace(i-1,i)?(n&&n(e.tr.delete(t.pos-o.nodeSize,t.pos).scrollIntoView()),0):!t.parent.canReplace(i,i+1)||!r.isTextblock&&!an(e.doc,t.pos)||(n&&n(e.tr.join(t.pos).scrollIntoView()),0)))}(e,t,n))return!0;let c=!a&&t.parent.canReplace(t.index(),t.index()+1);if(c&&(r=(i=s.contentMatchAt(s.childCount)).findWrapping(l.type))&&i.matchType(r[0]||l.type).validEnd){if(n){let o=t.pos+l.nodeSize,i=Ne.empty;for(let e=r.length-1;e>=0;e--)i=Ne.from(r[e].create(null,i));i=Ne.from(s.copy(i));let a=e.tr.step(new Yt(t.pos-1,o,t.pos,o,new ze(i,1,0),r.length,!0)),c=a.doc.resolve(o+2*r.length);c.nodeAfter&&c.nodeAfter.type==s.type&&an(a.doc,c.pos)&&a.join(c.pos),n(a.scrollIntoView())}return!0}let d=l.type.spec.isolating||o>0&&a?null:Tn.findFrom(t,1),h=d&&d.$from.blockRange(d.$to),p=h&&tn(h);if(null!=p&&p>=t.depth)return n&&n(e.tr.lift(h,p).scrollIntoView()),!0;if(c&&As(l,"start",!0)&&As(s,"end")){let o=s,r=[];for(;r.push(o),!o.isTextblock;)o=o.lastChild;let i=l,a=1;for(;!i.isTextblock;i=i.firstChild)a++;if(o.canReplace(o.childCount,o.childCount,i.content)){if(n){let o=Ne.empty;for(let e=r.length-1;e>=0;e--)o=Ne.from(r[e].copy(o));n(e.tr.step(new Yt(t.pos-r.length,t.pos+l.nodeSize,t.pos+a,t.pos+l.nodeSize-a,new ze(o,r.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function Fs(e){return function(t,n){let o=t.selection,r=e<0?o.$from:o.$to,i=r.depth;for(;r.node(i).isInline;){if(!i)return!1;i--}return!!r.node(i).isTextblock&&(n&&n(t.tr.setSelection(Dn.create(t.doc,e<0?r.start(i):r.end(i)))),!0)}}const js=Fs(-1),_s=Fs(1);function qs(e,t=null){return function(n,o){let r=!1;for(let i=0;i<n.selection.ranges.length&&!r;i++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[i];n.doc.nodesBetween(o,s,((o,i)=>{if(r)return!1;if(o.isTextblock&&!o.hasMarkup(e,t))if(o.type==e)r=!0;else{let t=n.doc.resolve(i),o=t.index();r=t.parent.canReplaceWith(o,o+1,e)}}))}if(!r)return!1;if(o){let r=n.tr;for(let o=0;o<n.selection.ranges.length;o++){let{$from:{pos:i},$to:{pos:s}}=n.selection.ranges[o];r.setBlockType(i,s,e,t)}o(r.scrollIntoView())}return!0}}function Ws(...e){return function(t,n,o){for(let r=0;r<e.length;r++)if(e[r](t,n,o))return!0;return!1}}function Ks(e,t=null){return function(n,o){let{$from:r,$to:i}=n.selection,s=r.blockRange(i);if(!s)return!1;let l=o?n.tr:null;return!!function(e,t,n,o=null){let r=!1,i=t,s=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=s.resolve(t.start-2);i=new Xe(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new Xe(t.$from,s.resolve(t.$to.end(t.depth)),t.depth)),r=!0}let l=nn(i,n,o,t);if(!l)return!1;e&&function(e,t,n,o,r){let i=Ne.empty;for(let d=n.length-1;d>=0;d--)i=Ne.from(n[d].type.create(n[d].attrs,i));e.step(new Yt(t.start-(o?2:0),t.end,t.start,t.end,new ze(i,0,0),n.length,!0));let s=0;for(let d=0;d<n.length;d++)n[d].type==r&&(s=d+1);let l=n.length-s,a=t.start+n.length-(o?2:0),c=t.parent;for(let d=t.startIndex,h=t.endIndex,p=!0;d<h;d++,p=!1)!p&&ln(e.doc,a,l)&&(e.split(a,l),a+=2*l),a+=c.child(d).nodeSize}(e,t,l,r,n);return!0}(l,s,e,t)&&(o&&o(l.scrollIntoView()),!0)}}function Us(e){return function(t,n){let{$from:o,$to:r}=t.selection,i=o.blockRange(r,(t=>t.childCount>0&&t.firstChild.type==e));return!!i&&(!n||(o.node(i.depth-1).type==e?function(e,t,n,o){let r=e.tr,i=o.end,s=o.$to.end(o.depth);i<s&&(r.step(new Yt(i-1,s,i,s,new ze(Ne.from(n.create(null,o.parent.copy())),1,0),1,!0)),o=new Xe(r.doc.resolve(o.$from.pos),r.doc.resolve(s),o.depth));const l=tn(o);if(null==l)return!1;r.lift(o,l);let a=r.doc.resolve(r.mapping.map(i,-1)-1);an(r.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&r.join(a.pos);return t(r.scrollIntoView()),!0}(t,n,e,i):function(e,t,n){let o=e.tr,r=n.parent;for(let u=n.end,f=n.endIndex-1,m=n.startIndex;f>m;f--)u-=r.child(f).nodeSize,o.delete(u-1,u+1);let i=o.doc.resolve(n.start),s=i.nodeAfter;if(o.mapping.map(n.end)!=n.start+i.nodeAfter.nodeSize)return!1;let l=0==n.startIndex,a=n.endIndex==r.childCount,c=i.node(-1),d=i.index(-1);if(!c.canReplace(d+(l?0:1),d+1,s.content.append(a?Ne.empty:Ne.from(r))))return!1;let h=i.pos,p=h+s.nodeSize;return o.step(new Yt(h-(l?1:0),p+(a?1:0),h+1,p-1,new ze((l?Ne.empty:Ne.from(r.copy(Ne.empty))).append(a?Ne.empty:Ne.from(r.copy(Ne.empty))),l?0:1,a?0:1),l?0:1)),t(o.scrollIntoView()),!0}(t,n,i)))}}function Js(e){const{state:t,transaction:n}=e;let{selection:o}=n,{doc:r}=n,{storedMarks:i}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return i},get selection(){return o},get doc(){return r},get tr(){return o=n.selection,r=n.doc,i=n.storedMarks,n}}}Ws(Ms,Os,Es),Ws(Ms,Rs,Is),Ws(Ls,Bs,Vs,$s),"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();class Gs{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:n}=this,{view:o}=t,{tr:r}=n,i=this.buildProps(r);return Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(...e)=>{const n=t(...e)(i);return r.getMeta("preventDispatch")||this.hasCustomState||o.dispatch(r),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:n,editor:o,state:r}=this,{view:i}=o,s=[],l=!!e,a=e||r.tr,c={...Object.fromEntries(Object.entries(n).map((([e,n])=>[e,(...e)=>{const o=this.buildProps(a,t),r=n(...e)(o);return s.push(r),c}]))),run:()=>(l||!t||a.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(a),s.every((e=>!0===e)))};return c}createCan(e){const{rawCommands:t,state:n}=this,o=!1,r=e||n.tr,i=this.buildProps(r,o);return{...Object.fromEntries(Object.entries(t).map((([e,t])=>[e,(...e)=>t(...e)({...i,dispatch:void 0})]))),chain:()=>this.createChain(r,o)}}buildProps(e,t=!0){const{rawCommands:n,editor:o,state:r}=this,{view:i}=o,s={tr:e,editor:o,view:i,state:Js({state:r,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map((([e,t])=>[e,(...e)=>t(...e)(s)])))}};return s}}class Qs{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach((e=>e.apply(this,t))),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter((e=>e!==t)):delete this.callbacks[e]),this}once(e,t){const n=(...o)=>{this.off(e,n),t.apply(this,o)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function Ys(e,t,n){if(void 0===e.config[t]&&e.parent)return Ys(e.parent,t,n);if("function"==typeof e.config[t]){return e.config[t].bind({...n,parent:e.parent?Ys(e.parent,t,n):null})}return e.config[t]}function Xs(e){return{baseExtensions:e.filter((e=>"extension"===e.type)),nodeExtensions:e.filter((e=>"node"===e.type)),markExtensions:e.filter((e=>"mark"===e.type))}}function Zs(e){const t=[],{nodeExtensions:n,markExtensions:o}=Xs(e),r=[...n,...o],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach((e=>{const n=Ys(e,"addGlobalAttributes",{name:e.name,options:e.options,storage:e.storage,extensions:r});if(!n)return;n().forEach((e=>{e.types.forEach((n=>{Object.entries(e.attributes).forEach((([e,o])=>{t.push({type:n,name:e,attribute:{...i,...o}})}))}))}))})),r.forEach((e=>{const n={name:e.name,options:e.options,storage:e.storage},o=Ys(e,"addAttributes",n);if(!o)return;const r=o();Object.entries(r).forEach((([n,o])=>{const r={...i,...o};"function"==typeof(null==r?void 0:r.default)&&(r.default=r.default()),(null==r?void 0:r.isRequired)&&void 0===(null==r?void 0:r.default)&&delete r.default,t.push({type:e.name,name:n,attribute:r})}))})),t}function el(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function tl(...e){return e.filter((e=>!!e)).reduce(((e,t)=>{const n={...e};return Object.entries(t).forEach((([e,t])=>{if(n[e])if("class"===e){const o=t?String(t).split(" "):[],r=n[e]?n[e].split(" "):[],i=o.filter((e=>!r.includes(e)));n[e]=[...r,...i].join(" ")}else if("style"===e){const o=t?t.split(";").map((e=>e.trim())).filter(Boolean):[],r=n[e]?n[e].split(";").map((e=>e.trim())).filter(Boolean):[],i=new Map;r.forEach((e=>{const[t,n]=e.split(":").map((e=>e.trim()));i.set(t,n)})),o.forEach((e=>{const[t,n]=e.split(":").map((e=>e.trim()));i.set(t,n)})),n[e]=Array.from(i.entries()).map((([e,t])=>`${e}: ${t}`)).join("; ")}else n[e]=t;else n[e]=t})),n}),{})}function nl(e,t){return t.filter((t=>t.type===e.type.name)).filter((e=>e.attribute.rendered)).map((t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]})).reduce(((e,t)=>tl(e,t)),{})}function ol(e){return"function"==typeof e}function rl(e,t=void 0,...n){return ol(e)?t?e.bind(t)(...n):e(...n):e}function il(e,t){return"style"in e?e:{...e,getAttrs:n=>{const o=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===o)return!1;const r=t.reduce(((e,t)=>{const o=t.attribute.parseHTML?t.attribute.parseHTML(n):function(e){return"string"!=typeof e?e:e.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(e):"true"===e||"false"!==e&&e}(n.getAttribute(t.name));return null==o?e:{...e,[t.name]:o}}),{});return{...o,...r}}}}function sl(e){return Object.fromEntries(Object.entries(e).filter((([e,t])=>("attrs"!==e||!function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t)))}function ll(e,t){return t.nodes[e]||t.marks[e]||null}function al(e,t){return Array.isArray(t)?t.some((t=>("string"==typeof t?t:t.name)===e.name)):t}function cl(e,t){const n=Dt.fromSchema(t).serializeFragment(e),o=document.implementation.createHTMLDocument().createElement("div");return o.appendChild(n),o.innerHTML}function dl(e){return"[object RegExp]"===Object.prototype.toString.call(e)}class hl{constructor(e){this.find=e.find,this.handler=e.handler}}function pl(e){var t;const{editor:n,from:o,to:r,text:i,rules:s,plugin:l}=e,{view:a}=n;if(a.composing)return!1;const c=a.state.doc.resolve(o);if(c.parent.type.spec.code||(null===(t=c.nodeBefore||c.nodeAfter)||void 0===t?void 0:t.marks.find((e=>e.type.spec.code))))return!1;let d=!1;const h=((e,t=500)=>{let n="";const o=e.parentOffset;return e.parent.nodesBetween(Math.max(0,o-t),o,((e,t,r,i)=>{var s,l;const a=(null===(l=(s=e.type.spec).toText)||void 0===l?void 0:l.call(s,{node:e,pos:t,parent:r,index:i}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?a:a.slice(0,Math.max(0,o-t))})),n})(c)+i;return s.forEach((e=>{if(d)return;const t=((e,t)=>{if(dl(t))return t.exec(e);const n=t(e);if(!n)return null;const o=[n.text];return o.index=n.index,o.input=e,o.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),o.push(n.replaceWith)),o})(h,e.find);if(!t)return;const s=a.state.tr,c=Js({state:a.state,transaction:s}),p={from:o-(t[0].length-i.length),to:r},{commands:u,chain:f,can:m}=new Gs({editor:n,state:c});null!==e.handler({state:c,range:p,match:t,commands:u,chain:f,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:o,to:r,text:i}),a.dispatch(s),d=!0)})),d}function ul(e){const{editor:t,rules:n}=e,o=new Kn({state:{init:()=>null,apply(e,r,i){const s=e.getMeta(o);if(s)return s;const l=e.getMeta("applyInputRules");return!!l&&setTimeout((()=>{let{text:e}=l;"string"==typeof e||(e=cl(Ne.from(e),i.schema));const{from:r}=l,s=r+e.length;pl({editor:t,from:r,to:s,text:e,rules:n,plugin:o})})),e.selectionSet||e.docChanged?null:r}},props:{handleTextInput:(e,r,i,s)=>pl({editor:t,from:r,to:i,text:s,rules:n,plugin:o}),handleDOMEvents:{compositionend:e=>(setTimeout((()=>{const{$cursor:r}=e.state.selection;r&&pl({editor:t,from:r.pos,to:r.pos,text:"",rules:n,plugin:o})})),!1)},handleKeyDown(e,r){if("Enter"!==r.key)return!1;const{$cursor:i}=e.state.selection;return!!i&&pl({editor:t,from:i.pos,to:i.pos,text:"\n",rules:n,plugin:o})}},isInputRules:!0});return o}function fl(e){return"Object"===function(e){return Object.prototype.toString.call(e).slice(8,-1)}(e)&&(e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype)}function ml(e,t){const n={...e};return fl(e)&&fl(t)&&Object.keys(t).forEach((o=>{fl(t[o])&&fl(e[o])?n[o]=ml(e[o],t[o]):n[o]=t[o]})),n}class gl{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=rl(Ys(this,"addOptions",{name:this.name}))),this.storage=rl(Ys(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new gl(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>ml(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new gl(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=rl(Ys(t,"addOptions",{name:t.name})),t.storage=rl(Ys(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,o=e.state.selection.$from;if(o.pos===o.end()){const r=o.marks();if(!!!r.find((e=>(null==e?void 0:e.type.name)===t.name)))return!1;const i=r.find((e=>(null==e?void 0:e.type.name)===t.name));return i&&n.removeStoredMark(i),n.insertText(" ",o.pos),e.view.dispatch(n),!0}return!1}}class yl{constructor(e){this.find=e.find,this.handler=e.handler}}function vl(e){const{editor:t,state:n,from:o,to:r,rule:i,pasteEvent:s,dropEvent:l}=e,{commands:a,chain:c,can:d}=new Gs({editor:t,state:n}),h=[];n.doc.nodesBetween(o,r,((e,t)=>{if(!e.isTextblock||e.type.spec.code)return;const p=Math.max(o,t),u=Math.min(r,t+e.content.size);((e,t,n)=>{if(dl(t))return[...e.matchAll(t)];const o=t(e,n);return o?o.map((t=>{const n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n})):[]})(e.textBetween(p-t,u-t,void 0,"￼"),i.find,s).forEach((e=>{if(void 0===e.index)return;const t=p+e.index+1,o=t+e[0].length,r={from:n.tr.mapping.map(t),to:n.tr.mapping.map(o)},u=i.handler({state:n,range:r,match:e,commands:a,chain:c,can:d,pasteEvent:s,dropEvent:l});h.push(u)}))}));return h.every((e=>null!==e))}let wl=null;function bl(e){const{editor:t,rules:n}=e;let o,r=null,i=!1,s=!1,l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{o="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{o=null}const a=({state:e,from:n,to:r,rule:i,pasteEvt:s})=>{const a=e.tr,c=Js({state:e,transaction:a});if(vl({editor:t,state:c,from:Math.max(n-1,0),to:r.b-1,rule:i,pasteEvent:s,dropEvent:o})&&a.steps.length){try{o="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{o=null}return l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,a}};return n.map((e=>new Kn({view(e){const n=n=>{var o;r=(null===(o=e.dom.parentElement)||void 0===o?void 0:o.contains(n.target))?e.dom.parentElement:null,r&&(wl=t)},o=()=>{wl&&(wl=null)};return window.addEventListener("dragstart",n),window.addEventListener("dragend",o),{destroy(){window.removeEventListener("dragstart",n),window.removeEventListener("dragend",o)}}},props:{handleDOMEvents:{drop:(e,t)=>{if(s=r===e.dom.parentElement,o=t,!s){const e=wl;e&&setTimeout((()=>{const t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})}),10)}return!1},paste:(e,t)=>{var n;const o=null===(n=t.clipboardData)||void 0===n?void 0:n.getData("text/html");return l=t,i=!!(null==o?void 0:o.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,o)=>{const r=t[0],c="paste"===r.getMeta("uiEvent")&&!i,d="drop"===r.getMeta("uiEvent")&&!s,h=r.getMeta("applyPasteRules"),p=!!h;if(!c&&!d&&!p)return;if(p){let{text:t}=h;"string"==typeof t||(t=cl(Ne.from(t),o.schema));const{from:n}=h,r=n+t.length,i=(e=>{var t;const n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(t=n.clipboardData)||void 0===t||t.setData("text/html",e),n})(t);return a({rule:e,state:o,from:n,to:{b:r},pasteEvt:i})}const u=n.doc.content.findDiffStart(o.doc.content),f=n.doc.content.findDiffEnd(o.doc.content);return"number"==typeof u&&f&&u!==f.b?a({rule:e,state:o,from:u,to:f,pasteEvt:l}):void 0}})))}class kl{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=kl.resolve(e),this.schema=function(e,t){var n;const o=Zs(e),{nodeExtensions:r,markExtensions:i}=Xs(e),s=null===(n=r.find((e=>Ys(e,"topNode"))))||void 0===n?void 0:n.name,l=Object.fromEntries(r.map((n=>{const r=o.filter((e=>e.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:t},s=sl({...e.reduce(((e,t)=>{const o=Ys(t,"extendNodeSchema",i);return{...e,...o?o(n):{}}}),{}),content:rl(Ys(n,"content",i)),marks:rl(Ys(n,"marks",i)),group:rl(Ys(n,"group",i)),inline:rl(Ys(n,"inline",i)),atom:rl(Ys(n,"atom",i)),selectable:rl(Ys(n,"selectable",i)),draggable:rl(Ys(n,"draggable",i)),code:rl(Ys(n,"code",i)),whitespace:rl(Ys(n,"whitespace",i)),linebreakReplacement:rl(Ys(n,"linebreakReplacement",i)),defining:rl(Ys(n,"defining",i)),isolating:rl(Ys(n,"isolating",i)),attrs:Object.fromEntries(r.map((e=>{var t;return[e.name,{default:null===(t=null==e?void 0:e.attribute)||void 0===t?void 0:t.default}]})))}),l=rl(Ys(n,"parseHTML",i));l&&(s.parseDOM=l.map((e=>il(e,r))));const a=Ys(n,"renderHTML",i);a&&(s.toDOM=e=>a({node:e,HTMLAttributes:nl(e,r)}));const c=Ys(n,"renderText",i);return c&&(s.toText=c),[n.name,s]}))),a=Object.fromEntries(i.map((n=>{const r=o.filter((e=>e.type===n.name)),i={name:n.name,options:n.options,storage:n.storage,editor:t},s=sl({...e.reduce(((e,t)=>{const o=Ys(t,"extendMarkSchema",i);return{...e,...o?o(n):{}}}),{}),inclusive:rl(Ys(n,"inclusive",i)),excludes:rl(Ys(n,"excludes",i)),group:rl(Ys(n,"group",i)),spanning:rl(Ys(n,"spanning",i)),code:rl(Ys(n,"code",i)),attrs:Object.fromEntries(r.map((e=>{var t;return[e.name,{default:null===(t=null==e?void 0:e.attribute)||void 0===t?void 0:t.default}]})))}),l=rl(Ys(n,"parseHTML",i));l&&(s.parseDOM=l.map((e=>il(e,r))));const a=Ys(n,"renderHTML",i);return a&&(s.toDOM=e=>a({mark:e,HTMLAttributes:nl(e,r)})),[n.name,s]})));return new wt({topNode:s,nodes:l,marks:a})}(this.extensions,t),this.setupExtensions()}static resolve(e){const t=kl.sort(kl.flatten(e)),n=function(e){const t=e.filter(((t,n)=>e.indexOf(t)!==n));return Array.from(new Set(t))}(t.map((e=>e.name)));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map((e=>`'${e}'`)).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map((e=>{const t=Ys(e,"addExtensions",{name:e.name,options:e.options,storage:e.storage});return t?[e,...this.flatten(t())]:e})).flat(10)}static sort(e){return e.sort(((e,t)=>{const n=Ys(e,"priority")||100,o=Ys(t,"priority")||100;return n>o?-1:n<o?1:0}))}get commands(){return this.extensions.reduce(((e,t)=>{const n=Ys(t,"addCommands",{name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:ll(t.name,this.schema)});return n?{...e,...n()}:e}),{})}get plugins(){const{editor:e}=this,t=kl.sort([...this.extensions].reverse()),n=[],o=[],r=t.map((t=>{const r={name:t.name,options:t.options,storage:t.storage,editor:e,type:ll(t.name,this.schema)},i=[],s=Ys(t,"addKeyboardShortcuts",r);let l={};if("mark"===t.type&&Ys(t,"exitable",r)&&(l.ArrowRight=()=>gl.handleExit({editor:e,mark:t})),s){const t=Object.fromEntries(Object.entries(s()).map((([t,n])=>[t,()=>n({editor:e})])));l={...l,...t}}const a=new Kn({props:{handleKeyDown:Ss(l)}});i.push(a);const c=Ys(t,"addInputRules",r);al(t,e.options.enableInputRules)&&c&&n.push(...c());const d=Ys(t,"addPasteRules",r);al(t,e.options.enablePasteRules)&&d&&o.push(...d());const h=Ys(t,"addProseMirrorPlugins",r);if(h){const e=h();i.push(...e)}return i})).flat();return[ul({editor:e,rules:n}),...bl({editor:e,rules:o}),...r]}get attributes(){return Zs(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=Xs(this.extensions);return Object.fromEntries(t.filter((e=>!!Ys(e,"addNodeView"))).map((t=>{const n=this.attributes.filter((e=>e.type===t.name)),o={name:t.name,options:t.options,storage:t.storage,editor:e,type:el(t.name,this.schema)},r=Ys(t,"addNodeView",o);if(!r)return[];return[t.name,(o,i,s,l,a)=>{const c=nl(o,n);return r()({node:o,view:i,getPos:s,decorations:l,innerDecorations:a,editor:e,extension:t,HTMLAttributes:c})}]})))}setupExtensions(){this.extensions.forEach((e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:ll(e.name,this.schema)};if("mark"===e.type){(null===(t=rl(Ys(e,"keepOnSplit",n)))||void 0===t||t)&&this.splittableMarks.push(e.name)}const o=Ys(e,"onBeforeCreate",n),r=Ys(e,"onCreate",n),i=Ys(e,"onUpdate",n),s=Ys(e,"onSelectionUpdate",n),l=Ys(e,"onTransaction",n),a=Ys(e,"onFocus",n),c=Ys(e,"onBlur",n),d=Ys(e,"onDestroy",n);o&&this.editor.on("beforeCreate",o),r&&this.editor.on("create",r),i&&this.editor.on("update",i),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),c&&this.editor.on("blur",c),d&&this.editor.on("destroy",d)}))}}class xl{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=rl(Ys(this,"addOptions",{name:this.name}))),this.storage=rl(Ys(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new xl(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>ml(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new xl({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=rl(Ys(t,"addOptions",{name:t.name})),t.storage=rl(Ys(t,"addStorage",{name:t.name,options:t.options})),t}}function Sl(e,t,n){const{from:o,to:r}=t,{blockSeparator:i="\n\n",textSerializers:s={}}=n||{};let l="";return e.nodesBetween(o,r,((e,n,a,c)=>{var d;e.isBlock&&n>o&&(l+=i);const h=null==s?void 0:s[e.type.name];if(h)return a&&(l+=h({node:e,pos:n,parent:a,index:c,range:t})),!1;e.isText&&(l+=null===(d=null==e?void 0:e.text)||void 0===d?void 0:d.slice(Math.max(o,n)-n,r-n))})),l}function Ml(e){return Object.fromEntries(Object.entries(e.nodes).filter((([,e])=>e.spec.toText)).map((([e,t])=>[e,t.spec.toText])))}const Cl=xl.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new Kn({key:new Gn("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:e}=this,{state:t,schema:n}=e,{doc:o,selection:r}=t,{ranges:i}=r,s=Math.min(...i.map((e=>e.$from.pos))),l=Math.max(...i.map((e=>e.$to.pos))),a=Ml(n);return Sl(o,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function Ol(e,t,n={strict:!0}){const o=Object.keys(t);return!o.length||o.every((o=>n.strict?t[o]===e[o]:dl(t[o])?t[o].test(e[o]):t[o]===e[o]))}function Tl(e,t,n={}){return e.find((e=>e.type===t&&Ol(Object.fromEntries(Object.keys(n).map((t=>[t,e.attrs[t]]))),n)))}function Al(e,t,n={}){return!!Tl(e,t,n)}function El(e,t,n){var o;if(!e||!t)return;let r=e.parent.childAfter(e.parentOffset);if(r.node&&r.node.marks.some((e=>e.type===t))||(r=e.parent.childBefore(e.parentOffset)),!r.node||!r.node.marks.some((e=>e.type===t)))return;n=n||(null===(o=r.node.marks[0])||void 0===o?void 0:o.attrs);if(!Tl([...r.node.marks],t,n))return;let i=r.index,s=e.start()+r.offset,l=i+1,a=s+r.node.nodeSize;for(;i>0&&Al([...e.parent.child(i-1).marks],t,n);)i-=1,s-=e.parent.child(i).nodeSize;for(;l<e.parent.childCount&&Al([...e.parent.child(l).marks],t,n);)a+=e.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function Nl(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function Dl(e){return e instanceof Dn}function Rl(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function Il(e,t=null){if(!t)return null;const n=Tn.atStart(e),o=Tn.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return o;const r=n.from,i=o.to;return"all"===t?Dn.create(e,Rl(0,r,i),Rl(e.content.size,r,i)):Dn.create(e,Rl(t,r,i),Rl(t,r,i))}function Pl(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Ll=e=>{const t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){const o=t[n];3===o.nodeType&&o.nodeValue&&/^(\n\s\s|\n)$/.test(o.nodeValue)?e.removeChild(o):1===o.nodeType&&Ll(o)}return e};function zl(e){const t=`<body>${e}</body>`,n=(new window.DOMParser).parseFromString(t,"text/html").body;return Ll(n)}function Bl(e,t,n){if(e instanceof et||e instanceof Ne)return e;n={slice:!0,parseOptions:{},...n};const o="string"==typeof e;if("object"==typeof e&&null!==e)try{if(Array.isArray(e)&&e.length>0)return Ne.fromArray(e.map((e=>t.nodeFromJSON(e))));const o=t.nodeFromJSON(e);return n.errorOnInvalidContent&&o.check(),o}catch(r){if(n.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",r),Bl("",t,n)}if(o){if(n.errorOnInvalidContent){let o=!1,r="";const i=new wt({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(o=!0,r="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?kt.fromSchema(i).parseSlice(zl(e),n.parseOptions):kt.fromSchema(i).parse(zl(e),n.parseOptions),n.errorOnInvalidContent&&o)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${r}`)})}const o=kt.fromSchema(t);return n.slice?o.parseSlice(zl(e),n.parseOptions).content:o.parse(zl(e),n.parseOptions)}return Bl("",t,n)}function Vl(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function $l(e,t,n={}){const{from:o,to:r,empty:i}=e.selection,s=t?el(t,e.schema):null,l=[];e.doc.nodesBetween(o,r,((e,t)=>{if(e.isText)return;const n=Math.max(o,t),i=Math.min(r,t+e.nodeSize);l.push({node:e,from:n,to:i})}));const a=r-o,c=l.filter((e=>!s||s.name===e.node.type.name)).filter((e=>Ol(e.node.attrs,n,{strict:!1})));if(i)return!!c.length;return c.reduce(((e,t)=>e+t.to-t.from),0)>=a}function Hl(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function Fl(e,t){const n="string"==typeof t?[t]:t;return Object.keys(e).reduce(((t,o)=>(n.includes(o)||(t[o]=e[o]),t)),{})}function jl(e,t,n={},o={}){return Bl(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:o.errorOnInvalidContent})}function _l(e,t){const n=Nl(t,e.schema),{from:o,to:r,empty:i}=e.selection,s=[];i?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(o,r,(e=>{s.push(...e.marks)}));const l=s.find((e=>e.type.name===n.name));return l?{...l.attrs}:{}}function ql(e){return t=>function(e,t){for(let n=e.depth;n>0;n-=1){const o=e.node(n);if(t(o))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:o}}}(t.$from,e)}function Wl(e,t){const n=Hl("string"==typeof t?t:t.name,e.schema);return"node"===n?function(e,t){const n=el(t,e.schema),{from:o,to:r}=e.selection,i=[];e.doc.nodesBetween(o,r,(e=>{i.push(e)}));const s=i.reverse().find((e=>e.type.name===n.name));return s?{...s.attrs}:{}}(e,t):"mark"===n?_l(e,t):{}}function Kl(e){const t=function(e,t=JSON.stringify){const n={};return e.filter((e=>{const o=t(e);return!Object.prototype.hasOwnProperty.call(n,o)&&(n[o]=!0)}))}(e);return 1===t.length?t:t.filter(((e,n)=>{const o=t.filter(((e,t)=>t!==n));return!o.some((t=>e.oldRange.from>=t.oldRange.from&&e.oldRange.to<=t.oldRange.to&&e.newRange.from>=t.newRange.from&&e.newRange.to<=t.newRange.to))}))}function Ul(e,t,n){const o=[];return e===t?n.resolve(e).marks().forEach((t=>{const r=El(n.resolve(e),t.type);r&&o.push({mark:t,...r})})):n.nodesBetween(e,t,((e,t)=>{e&&void 0!==(null==e?void 0:e.nodeSize)&&o.push(...e.marks.map((n=>({from:t,to:t+e.nodeSize,mark:n}))))})),o}function Jl(e,t,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const o=e.find((e=>e.type===t&&e.name===n));return!!o&&o.attribute.keepOnSplit})))}function Gl(e,t,n={}){const{empty:o,ranges:r}=e.selection,i=t?Nl(t,e.schema):null;if(o)return!!(e.storedMarks||e.selection.$from.marks()).filter((e=>!i||i.name===e.type.name)).find((e=>Ol(e.attrs,n,{strict:!1})));let s=0;const l=[];if(r.forEach((({$from:t,$to:n})=>{const o=t.pos,r=n.pos;e.doc.nodesBetween(o,r,((e,t)=>{if(!e.isText&&!e.marks.length)return;const n=Math.max(o,t),i=Math.min(r,t+e.nodeSize);s+=i-n,l.push(...e.marks.map((e=>({mark:e,from:n,to:i}))))}))})),0===s)return!1;const a=l.filter((e=>!i||i.name===e.mark.type.name)).filter((e=>Ol(e.mark.attrs,n,{strict:!1}))).reduce(((e,t)=>e+t.to-t.from),0),c=l.filter((e=>!i||e.mark.type!==i&&e.mark.type.excludes(i))).reduce(((e,t)=>e+t.to-t.from),0);return(a>0?a+c:a)>=s}function Ql(e,t){const{nodeExtensions:n}=Xs(t),o=n.find((t=>t.name===e));if(!o)return!1;const r=rl(Ys(o,"group",{name:o.name,options:o.options,storage:o.storage}));return"string"==typeof r&&r.split(" ").includes("list")}function Yl(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var o;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!==(o=e.text)&&void 0!==o?o:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let o=!0;return e.content.forEach((e=>{!1!==o&&(Yl(e,{ignoreWhitespace:n,checkChildren:t})||(o=!1))})),o}return!1}function Xl(e,t){const n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){const o=n.filter((e=>null==t?void 0:t.includes(e.type.name)));e.tr.ensureMarks(o)}}const Zl=(e,t)=>{const n=ql((e=>e.type===t))(e.selection);if(!n)return!0;const o=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===o)return!0;const r=e.doc.nodeAt(o);return n.node.type!==(null==r?void 0:r.type)||!an(e.doc,n.pos)||(e.join(n.pos),!0)},ea=(e,t)=>{const n=ql((e=>e.type===t))(e.selection);if(!n)return!0;const o=e.doc.resolve(n.start).after(n.depth);if(void 0===o)return!0;const r=e.doc.nodeAt(o);return n.node.type!==(null==r?void 0:r.type)||!an(e.doc,o)||(e.join(o),!0)};var ta=Object.freeze({__proto__:null,blur:()=>({editor:e,view:t})=>(requestAnimationFrame((()=>{var n;e.isDestroyed||(t.dom.blur(),null===(n=null===window||void 0===window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())})),!0),clearContent:(e=!1)=>({commands:t})=>t.setContent("",e),clearNodes:()=>({state:e,tr:t,dispatch:n})=>{const{selection:o}=t,{ranges:r}=o;return!n||(r.forEach((({$from:n,$to:o})=>{e.doc.nodesBetween(n.pos,o.pos,((e,n)=>{if(e.type.isText)return;const{doc:o,mapping:r}=t,i=o.resolve(r.map(n)),s=o.resolve(r.map(n+e.nodeSize)),l=i.blockRange(s);if(!l)return;const a=tn(l);if(e.type.isTextblock){const{defaultType:e}=i.parent.contentMatchAt(i.index());t.setNodeMarkup(l.start,e)}(a||0===a)&&t.lift(l,a)}))})),!0)},command:e=>t=>e(t),createParagraphNear:()=>({state:e,dispatch:t})=>Bs(e,t),cut:(e,t)=>({editor:n,tr:o})=>{const{state:r}=n,i=r.doc.slice(e.from,e.to);o.deleteRange(e.from,e.to);const s=o.mapping.map(t);return o.insert(s,i.content),o.setSelection(new Dn(o.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:e,dispatch:t})=>{const{selection:n}=e,o=n.$anchor.node();if(o.content.size>0)return!1;const r=e.selection.$anchor;for(let i=r.depth;i>0;i-=1){if(r.node(i).type===o.type){if(t){const t=r.before(i),n=r.after(i);e.delete(t,n).scrollIntoView()}return!0}}return!1},deleteNode:e=>({tr:t,state:n,dispatch:o})=>{const r=el(e,n.schema),i=t.selection.$anchor;for(let e=i.depth;e>0;e-=1){if(i.node(e).type===r){if(o){const n=i.before(e),o=i.after(e);t.delete(n,o).scrollIntoView()}return!0}}return!1},deleteRange:e=>({tr:t,dispatch:n})=>{const{from:o,to:r}=e;return n&&t.delete(o,r),!0},deleteSelection:()=>({state:e,dispatch:t})=>Ms(e,t),enter:()=>({commands:e})=>e.keyboardShortcut("Enter"),exitCode:()=>({state:e,dispatch:t})=>((e,t)=>{let{$head:n,$anchor:o}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(o))return!1;let r=n.node(-1),i=n.indexAfter(-1),s=zs(r.contentMatchAt(i));if(!s||!r.canReplaceWith(i,i,s))return!1;if(t){let o=n.after(),r=e.tr.replaceWith(o,o,s.createAndFill());r.setSelection(Tn.near(r.doc.resolve(o),1)),t(r.scrollIntoView())}return!0})(e,t),extendMarkRange:(e,t={})=>({tr:n,state:o,dispatch:r})=>{const i=Nl(e,o.schema),{doc:s,selection:l}=n,{$from:a,from:c,to:d}=l;if(r){const e=El(a,i,t);if(e&&e.from<=c&&e.to>=d){const t=Dn.create(s,e.from,e.to);n.setSelection(t)}}return!0},first:e=>t=>{const n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1},focus:(e=null,t={})=>({editor:n,view:o,tr:r,dispatch:i})=>{t={scrollIntoView:!0,...t};const s=()=>{(Pl()||"Android"===navigator.platform||/android/i.test(navigator.userAgent))&&o.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(o.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())}))};if(o.hasFocus()&&null===e||!1===e)return!0;if(i&&null===e&&!Dl(n.state.selection))return s(),!0;const l=Il(r.doc,e)||n.state.selection,a=n.state.selection.eq(l);return i&&(a||r.setSelection(l),a&&r.storedMarks&&r.setStoredMarks(r.storedMarks),s()),!0},forEach:(e,t)=>n=>e.every(((e,o)=>t(e,{...n,index:o}))),insertContent:(e,t)=>({tr:n,commands:o})=>o.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),insertContentAt:(e,t,n)=>({tr:o,dispatch:r,editor:i})=>{var s;if(r){let r;n={parseOptions:i.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};try{r=Bl(t,i.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions},errorOnInvalidContent:null!==(s=n.errorOnInvalidContent)&&void 0!==s?s:i.options.enableContentCheck})}catch(l){return i.emit("contentError",{editor:i,error:l,disableCollaboration:()=>{i.storage.collaboration&&(i.storage.collaboration.isDisabled=!0)}}),!1}let{from:a,to:c}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},d=!0,h=!0;if(("type"in r?[r]:r).forEach((e=>{e.check(),d=!!d&&(e.isText&&0===e.marks.length),h=!!h&&e.isBlock})),a===c&&h){const{parent:e}=o.doc.resolve(a);e.isTextblock&&!e.type.spec.code&&!e.childCount&&(a-=1,c+=1)}let p;if(d){if(Array.isArray(t))p=t.map((e=>e.text||"")).join("");else if(t instanceof Ne){let e="";t.forEach((t=>{t.text&&(e+=t.text)})),p=e}else p="object"==typeof t&&t&&t.text?t.text:t;o.insertText(p,a,c)}else p=r,o.replaceWith(a,c,p);n.updateSelection&&function(e,t,n){const o=e.steps.length-1;if(o<t)return;const r=e.steps[o];if(!(r instanceof Qt||r instanceof Yt))return;const i=e.mapping.maps[o];let s=0;i.forEach(((e,t,n,o)=>{0===s&&(s=o)})),e.setSelection(Tn.near(e.doc.resolve(s),n))}(o,o.steps.length-1,-1),n.applyInputRules&&o.setMeta("applyInputRules",{from:a,text:p}),n.applyPasteRules&&o.setMeta("applyPasteRules",{from:a,text:p})}return!0},joinBackward:()=>({state:e,dispatch:t})=>Os(e,t),joinDown:()=>({state:e,dispatch:t})=>((e,t)=>{let n,o=e.selection;if(o instanceof In){if(o.node.isTextblock||!an(e.doc,o.to))return!1;n=o.to}else if(n=dn(e.doc,o.to,1),null==n)return!1;return t&&t(e.tr.join(n).scrollIntoView()),!0})(e,t),joinForward:()=>({state:e,dispatch:t})=>Rs(e,t),joinItemBackward:()=>({state:e,dispatch:t,tr:n})=>{try{const o=dn(e.doc,e.selection.$from.pos,-1);return null!=o&&(n.join(o,2),t&&t(n),!0)}catch{return!1}},joinItemForward:()=>({state:e,dispatch:t,tr:n})=>{try{const o=dn(e.doc,e.selection.$from.pos,1);return null!=o&&(n.join(o,2),t&&t(n),!0)}catch{return!1}},joinTextblockBackward:()=>({state:e,dispatch:t})=>((e,t,n)=>{let o=Cs(e,n);if(!o)return!1;let r=Ns(o);return!!r&&Ts(e,r,t)})(e,t),joinTextblockForward:()=>({state:e,dispatch:t})=>((e,t,n)=>{let o=Ds(e,n);if(!o)return!1;let r=Ps(o);return!!r&&Ts(e,r,t)})(e,t),joinUp:()=>({state:e,dispatch:t})=>((e,t)=>{let n,o=e.selection,r=o instanceof In;if(r){if(o.node.isTextblock||!an(e.doc,o.from))return!1;n=o.from}else if(n=dn(e.doc,o.from,-1),null==n)return!1;if(t){let o=e.tr.join(n);r&&o.setSelection(In.create(o.doc,n-e.doc.resolve(n).nodeBefore.nodeSize)),t(o.scrollIntoView())}return!0})(e,t),keyboardShortcut:e=>({editor:t,view:n,tr:o,dispatch:r})=>{const i=function(e){const t=e.split(/-(?!$)/);let n,o,r,i,s=t[t.length-1];"Space"===s&&(s=" ");for(let l=0;l<t.length-1;l+=1){const e=t[l];if(/^(cmd|meta|m)$/i.test(e))i=!0;else if(/^a(lt)?$/i.test(e))n=!0;else if(/^(c|ctrl|control)$/i.test(e))o=!0;else if(/^s(hift)?$/i.test(e))r=!0;else{if(!/^mod$/i.test(e))throw new Error(`Unrecognized modifier name: ${e}`);Pl()||Vl()?i=!0:o=!0}}return n&&(s=`Alt-${s}`),o&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),r&&(s=`Shift-${s}`),s}(e).split(/-(?!$)/),s=i.find((e=>!["Alt","Ctrl","Meta","Shift"].includes(e))),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction((()=>{n.someProp("handleKeyDown",(e=>e(n,l)))}));return null==a||a.steps.forEach((e=>{const t=e.map(o.mapping);t&&r&&o.maybeStep(t)})),!0},lift:(e,t={})=>({state:n,dispatch:o})=>!!$l(n,el(e,n.schema),t)&&((e,t)=>{let{$from:n,$to:o}=e.selection,r=n.blockRange(o),i=r&&tn(r);return null!=i&&(t&&t(e.tr.lift(r,i).scrollIntoView()),!0)})(n,o),liftEmptyBlock:()=>({state:e,dispatch:t})=>Vs(e,t),liftListItem:e=>({state:t,dispatch:n})=>Us(el(e,t.schema))(t,n),newlineInCode:()=>({state:e,dispatch:t})=>Ls(e,t),resetAttributes:(e,t)=>({tr:n,state:o,dispatch:r})=>{let i=null,s=null;const l=Hl("string"==typeof e?e:e.name,o.schema);return!!l&&("node"===l&&(i=el(e,o.schema)),"mark"===l&&(s=Nl(e,o.schema)),r&&n.selection.ranges.forEach((e=>{o.doc.nodesBetween(e.$from.pos,e.$to.pos,((e,o)=>{i&&i===e.type&&n.setNodeMarkup(o,void 0,Fl(e.attrs,t)),s&&e.marks.length&&e.marks.forEach((r=>{s===r.type&&n.addMark(o,o+e.nodeSize,s.create(Fl(r.attrs,t)))}))}))})),!0)},scrollIntoView:()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),selectAll:()=>({tr:e,dispatch:t})=>{if(t){const t=new Ln(e.doc);e.setSelection(t)}return!0},selectNodeBackward:()=>({state:e,dispatch:t})=>Es(e,t),selectNodeForward:()=>({state:e,dispatch:t})=>Is(e,t),selectParentNode:()=>({state:e,dispatch:t})=>((e,t)=>{let n,{$from:o,to:r}=e.selection,i=o.sharedDepth(r);return 0!=i&&(n=o.before(i),t&&t(e.tr.setSelection(In.create(e.doc,n))),!0)})(e,t),selectTextblockEnd:()=>({state:e,dispatch:t})=>_s(e,t),selectTextblockStart:()=>({state:e,dispatch:t})=>js(e,t),setContent:(e,t=!1,n={},o={})=>({editor:r,tr:i,dispatch:s,commands:l})=>{var a,c;const{doc:d}=i;if("full"!==n.preserveWhitespace){const l=jl(e,r.schema,n,{errorOnInvalidContent:null!==(a=o.errorOnInvalidContent)&&void 0!==a?a:r.options.enableContentCheck});return s&&i.replaceWith(0,d.content.size,l).setMeta("preventUpdate",!t),!0}return s&&i.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:d.content.size},e,{parseOptions:n,errorOnInvalidContent:null!==(c=o.errorOnInvalidContent)&&void 0!==c?c:r.options.enableContentCheck})},setMark:(e,t={})=>({tr:n,state:o,dispatch:r})=>{const{selection:i}=n,{empty:s,ranges:l}=i,a=Nl(e,o.schema);if(r)if(s){const e=_l(o,a);n.addStoredMark(a.create({...e,...t}))}else l.forEach((e=>{const r=e.$from.pos,i=e.$to.pos;o.doc.nodesBetween(r,i,((e,o)=>{const s=Math.max(o,r),l=Math.min(o+e.nodeSize,i);e.marks.find((e=>e.type===a))?e.marks.forEach((e=>{a===e.type&&n.addMark(s,l,a.create({...e.attrs,...t}))})):n.addMark(s,l,a.create(t))}))}));return function(e,t,n){var o;const{selection:r}=t;let i=null;if(Dl(r)&&(i=r.$cursor),i){const t=null!==(o=e.storedMarks)&&void 0!==o?o:i.marks();return!!n.isInSet(t)||!t.some((e=>e.type.excludes(n)))}const{ranges:s}=r;return s.some((({$from:t,$to:o})=>{let r=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,o.pos,((e,t,o)=>{if(r)return!1;if(e.isInline){const t=!o||o.type.allowsMarkType(n),i=!!n.isInSet(e.marks)||!e.marks.some((e=>e.type.excludes(n)));r=t&&i}return!r})),r}))}(o,n,a)},setMeta:(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),setNode:(e,t={})=>({state:n,dispatch:o,chain:r})=>{const i=el(e,n.schema);let s;return n.selection.$anchor.sameParent(n.selection.$head)&&(s=n.selection.$anchor.parent.attrs),i.isTextblock?r().command((({commands:e})=>!!qs(i,{...s,...t})(n)||e.clearNodes())).command((({state:e})=>qs(i,{...s,...t})(e,o))).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:e=>({tr:t,dispatch:n})=>{if(n){const{doc:n}=t,o=Rl(e,0,n.content.size),r=In.create(n,o);t.setSelection(r)}return!0},setTextSelection:e=>({tr:t,dispatch:n})=>{if(n){const{doc:n}=t,{from:o,to:r}="number"==typeof e?{from:e,to:e}:e,i=Dn.atStart(n).from,s=Dn.atEnd(n).to,l=Rl(o,i,s),a=Rl(r,i,s),c=Dn.create(n,l,a);t.setSelection(c)}return!0},sinkListItem:e=>({state:t,dispatch:n})=>{const o=el(e,t.schema);return(r=o,function(e,t){let{$from:n,$to:o}=e.selection,i=n.blockRange(o,(e=>e.childCount>0&&e.firstChild.type==r));if(!i)return!1;let s=i.startIndex;if(0==s)return!1;let l=i.parent,a=l.child(s-1);if(a.type!=r)return!1;if(t){let n=a.lastChild&&a.lastChild.type==l.type,o=Ne.from(n?r.create():null),s=new ze(Ne.from(r.create(null,Ne.from(l.type.create(null,o)))),n?3:1,0),c=i.start,d=i.end;t(e.tr.step(new Yt(c-(n?3:1),d,c,d,s,1,!0)).scrollIntoView())}return!0})(t,n);var r},splitBlock:({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:o,editor:r})=>{const{selection:i,doc:s}=t,{$from:l,$to:a}=i,c=Jl(r.extensionManager.attributes,l.node().type.name,l.node().attrs);if(i instanceof In&&i.node.isBlock)return!(!l.parentOffset||!ln(s,l.pos))&&(o&&(e&&Xl(n,r.extensionManager.splittableMarks),t.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const d=a.parentOffset===a.parent.content.size,h=0===l.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){const{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1)));let p=d&&h?[{type:h,attrs:c}]:void 0,u=ln(t.doc,t.mapping.map(l.pos),1,p);if(p||u||!ln(t.doc,t.mapping.map(l.pos),1,h?[{type:h}]:void 0)||(u=!0,p=h?[{type:h,attrs:c}]:void 0),o){if(u&&(i instanceof Dn&&t.deleteSelection(),t.split(t.mapping.map(l.pos),1,p),h&&!d&&!l.parentOffset&&l.parent.type!==h)){const e=t.mapping.map(l.before()),n=t.doc.resolve(e);l.node(-1).canReplaceWith(n.index(),n.index()+1,h)&&t.setNodeMarkup(t.mapping.map(l.before()),h)}e&&Xl(n,r.extensionManager.splittableMarks),t.scrollIntoView()}return u},splitListItem:(e,t={})=>({tr:n,state:o,dispatch:r,editor:i})=>{var s;const l=el(e,o.schema),{$from:a,$to:c}=o.selection,d=o.selection.node;if(d&&d.isBlock||a.depth<2||!a.sameParent(c))return!1;const h=a.node(-1);if(h.type!==l)return!1;const p=i.extensionManager.attributes;if(0===a.parent.content.size&&a.node(-1).childCount===a.indexAfter(-1)){if(2===a.depth||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(r){let e=Ne.empty;const o=a.index(-1)?1:a.index(-2)?2:3;for(let t=a.depth-o;t>=a.depth-3;t-=1)e=Ne.from(a.node(t).copy(e));const r=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,i={...Jl(p,a.node().type.name,a.node().attrs),...t},c=(null===(s=l.contentMatch.defaultType)||void 0===s?void 0:s.createAndFill(i))||void 0;e=e.append(Ne.from(l.createAndFill(null,c)||void 0));const d=a.before(a.depth-(o-1));n.replace(d,a.after(-r),new ze(e,4-o,0));let h=-1;n.doc.nodesBetween(d,n.doc.content.size,((e,t)=>{if(h>-1)return!1;e.isTextblock&&0===e.content.size&&(h=t+1)})),h>-1&&n.setSelection(Dn.near(n.doc.resolve(h))),n.scrollIntoView()}return!0}const u=c.pos===a.end()?h.contentMatchAt(0).defaultType:null,f={...Jl(p,h.type.name,h.attrs),...t},m={...Jl(p,a.node().type.name,a.node().attrs),...t};n.delete(a.pos,c.pos);const g=u?[{type:l,attrs:f},{type:u,attrs:m}]:[{type:l,attrs:f}];if(!ln(n.doc,a.pos,2))return!1;if(r){const{selection:e,storedMarks:t}=o,{splittableMarks:s}=i.extensionManager,l=t||e.$to.parentOffset&&e.$from.marks();if(n.split(a.pos,2,g).scrollIntoView(),!l||!r)return!0;const c=l.filter((e=>s.includes(e.type.name)));n.ensureMarks(c)}return!0},toggleList:(e,t,n,o={})=>({editor:r,tr:i,state:s,dispatch:l,chain:a,commands:c,can:d})=>{const{extensions:h,splittableMarks:p}=r.extensionManager,u=el(e,s.schema),f=el(t,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:v}=m,w=y.blockRange(v),b=g||m.$to.parentOffset&&m.$from.marks();if(!w)return!1;const k=ql((e=>Ql(e.type.name,h)))(m);if(w.depth>=1&&k&&w.depth-k.depth<=1){if(k.node.type===u)return c.liftListItem(f);if(Ql(k.node.type.name,h)&&u.validContent(k.node.content)&&l)return a().command((()=>(i.setNodeMarkup(k.pos,u),!0))).command((()=>Zl(i,u))).command((()=>ea(i,u))).run()}return n&&b&&l?a().command((()=>{const e=d().wrapInList(u,o),t=b.filter((e=>p.includes(e.type.name)));return i.ensureMarks(t),!!e||c.clearNodes()})).wrapInList(u,o).command((()=>Zl(i,u))).command((()=>ea(i,u))).run():a().command((()=>!!d().wrapInList(u,o)||c.clearNodes())).wrapInList(u,o).command((()=>Zl(i,u))).command((()=>ea(i,u))).run()},toggleMark:(e,t={},n={})=>({state:o,commands:r})=>{const{extendEmptyMarkRange:i=!1}=n,s=Nl(e,o.schema);return Gl(o,s,t)?r.unsetMark(s,{extendEmptyMarkRange:i}):r.setMark(s,t)},toggleNode:(e,t,n={})=>({state:o,commands:r})=>{const i=el(e,o.schema),s=el(t,o.schema),l=$l(o,i,n);let a;return o.selection.$anchor.sameParent(o.selection.$head)&&(a=o.selection.$anchor.parent.attrs),l?r.setNode(s,a):r.setNode(i,{...a,...n})},toggleWrap:(e,t={})=>({state:n,commands:o})=>{const r=el(e,n.schema);return $l(n,r,t)?o.lift(r):o.wrapIn(r,t)},undoInputRule:()=>({state:e,dispatch:t})=>{const n=e.plugins;for(let o=0;o<n.length;o+=1){const r=n[o];let i;if(r.spec.isInputRules&&(i=r.getState(e))){if(t){const t=e.tr,n=i.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(i.text){const n=t.doc.resolve(i.from).marks();t.replaceWith(i.from,i.to,e.schema.text(i.text,n))}else t.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:e,dispatch:t})=>{const{selection:n}=e,{empty:o,ranges:r}=n;return o||t&&r.forEach((t=>{e.removeMark(t.$from.pos,t.$to.pos)})),!0},unsetMark:(e,t={})=>({tr:n,state:o,dispatch:r})=>{var i;const{extendEmptyMarkRange:s=!1}=t,{selection:l}=n,a=Nl(e,o.schema),{$from:c,empty:d,ranges:h}=l;if(!r)return!0;if(d&&s){let{from:e,to:t}=l;const o=null===(i=c.marks().find((e=>e.type===a)))||void 0===i?void 0:i.attrs,r=El(c,a,o);r&&(e=r.from,t=r.to),n.removeMark(e,t,a)}else h.forEach((e=>{n.removeMark(e.$from.pos,e.$to.pos,a)}));return n.removeStoredMark(a),!0},updateAttributes:(e,t={})=>({tr:n,state:o,dispatch:r})=>{let i=null,s=null;const l=Hl("string"==typeof e?e:e.name,o.schema);return!!l&&("node"===l&&(i=el(e,o.schema)),"mark"===l&&(s=Nl(e,o.schema)),r&&n.selection.ranges.forEach((e=>{const r=e.$from.pos,l=e.$to.pos;let a,c,d,h;n.selection.empty?o.doc.nodesBetween(r,l,((e,t)=>{i&&i===e.type&&(d=Math.max(t,r),h=Math.min(t+e.nodeSize,l),a=t,c=e)})):o.doc.nodesBetween(r,l,((e,o)=>{o<r&&i&&i===e.type&&(d=Math.max(o,r),h=Math.min(o+e.nodeSize,l),a=o,c=e),o>=r&&o<=l&&(i&&i===e.type&&n.setNodeMarkup(o,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach((i=>{if(s===i.type){const a=Math.max(o,r),c=Math.min(o+e.nodeSize,l);n.addMark(a,c,s.create({...i.attrs,...t}))}})))})),c&&(void 0!==a&&n.setNodeMarkup(a,void 0,{...c.attrs,...t}),s&&c.marks.length&&c.marks.forEach((e=>{s===e.type&&n.addMark(d,h,s.create({...e.attrs,...t}))})))})),!0)},wrapIn:(e,t={})=>({state:n,dispatch:o})=>function(e,t=null){return function(n,o){let{$from:r,$to:i}=n.selection,s=r.blockRange(i),l=s&&nn(s,e,t);return!!l&&(o&&o(n.tr.wrap(s,l).scrollIntoView()),!0)}}(el(e,n.schema),t)(n,o),wrapInList:(e,t={})=>({state:n,dispatch:o})=>Ks(el(e,n.schema),t)(n,o)});const na=xl.create({name:"commands",addCommands:()=>({...ta})}),oa=xl.create({name:"drop",addProseMirrorPlugins(){return[new Kn({key:new Gn("tiptapDrop"),props:{handleDrop:(e,t,n,o)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:o})}}})]}}),ra=xl.create({name:"editable",addProseMirrorPlugins(){return[new Kn({key:new Gn("editable"),props:{editable:()=>this.editor.options.editable}})]}}),ia=new Gn("focusEvents"),sa=xl.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:e}=this;return[new Kn({key:ia,props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;const o=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(o),!1},blur:(t,n)=>{e.isFocused=!1;const o=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(o),!1}}}})]}}),la=xl.create({name:"keymap",addKeyboardShortcuts(){const e=()=>this.editor.commands.first((({commands:e})=>[()=>e.undoInputRule(),()=>e.command((({tr:t})=>{const{selection:n,doc:o}=t,{empty:r,$anchor:i}=n,{pos:s,parent:l}=i,a=i.parent.isTextblock&&s>0?t.doc.resolve(s-1):i,c=a.parent.type.spec.isolating,d=i.pos-i.parentOffset,h=c&&1===a.parent.childCount?d===i.pos:Tn.atStart(o).from===s;return!(!r||!l.type.isTextblock||l.textContent.length||!h||h&&"paragraph"===i.parent.type.name)&&e.clearNodes()})),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()])),t=()=>this.editor.commands.first((({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},o={...n},r={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Pl()||Vl()?r:o},addProseMirrorPlugins(){return[new Kn({key:new Gn("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some((e=>e.getMeta("composition"))))return;const o=e.some((e=>e.docChanged))&&!t.doc.eq(n.doc),r=e.some((e=>e.getMeta("preventClearDocument")));if(!o||r)return;const{empty:i,from:s,to:l}=t.selection,a=Tn.atStart(t.doc).from,c=Tn.atEnd(t.doc).to;if(i||!(s===a&&l===c))return;if(!Yl(n.doc))return;const d=n.tr,h=Js({state:n,transaction:d}),{commands:p}=new Gs({editor:this.editor,state:h});return p.clearNodes(),d.steps.length?d:void 0}})]}}),aa=xl.create({name:"paste",addProseMirrorPlugins(){return[new Kn({key:new Gn("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),ca=xl.create({name:"tabindex",addProseMirrorPlugins(){return[new Kn({key:new Gn("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class da{get name(){return this.node.type.name}constructor(e,t,n=!1,o=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=o}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!==(e=this.actualDepth)&&void 0!==e?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new da(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new da(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new da(e,this.editor)}get children(){const e=[];return this.node.content.forEach(((t,n)=>{const o=t.isBlock&&!t.isTextblock,r=t.isAtom&&!t.isText,i=this.pos+n+(r?0:1),s=this.resolvedPos.doc.resolve(i);if(!o&&s.depth<=this.depth)return;const l=new da(s,this.editor,o,o?t:null);o&&(l.actualDepth=this.depth+1),e.push(new da(s,this.editor,o,o?t:null))})),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,o=this.parent;for(;o&&!n;){if(o.node.type.name===e)if(Object.keys(t).length>0){const e=o.node.attrs,n=Object.keys(t);for(let o=0;o<n.length;o+=1){const r=n[o];if(e[r]!==t[r])break}}else n=o;o=o.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let o=[];if(!this.children||0===this.children.length)return o;const r=Object.keys(t);return this.children.forEach((i=>{if(!(n&&o.length>0)){if(i.node.type.name===e){r.every((e=>t[e]===i.node.attrs[e]))&&o.push(i)}n&&o.length>0||(o=o.concat(i.querySelectorAll(e,t,n)))}})),o}setAttribute(e){const{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}let ha=class extends Qs{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",(({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n))),this.on("paste",(({event:e,slice:t})=>this.options.onPaste(e,t))),window.setTimeout((()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)}),0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t){const n=document.querySelector("style[data-tiptap-style]");if(null!==n)return n;const o=document.createElement("style");return t&&o.setAttribute("nonce",t),o.setAttribute("data-tiptap-style",""),o.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(o),o}('.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: "liga" 0; /* the above doesn\'t seem to work in Edge */\n}\n\n.ProseMirror [contenteditable="false"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable="false"] [contenteditable="true"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}',this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=ol(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],o=this.state.reconfigure({plugins:n});return this.view.updateState(o),o}unregisterPlugin(e){if(this.isDestroyed)return;const t=this.state.plugins;let n=t;if([].concat(e).forEach((e=>{const o="string"==typeof e?`${e}$`:e.key;n=t.filter((e=>!e.key.startsWith(o)))})),t.length===n.length)return;const o=this.state.reconfigure({plugins:n});return this.view.updateState(o),o}createExtensionManager(){var e,t;const n=[...this.options.enableCoreExtensions?[ra,Cl.configure({blockSeparator:null===(t=null===(e=this.options.coreExtensionOptions)||void 0===e?void 0:e.clipboardTextSerializer)||void 0===t?void 0:t.blockSeparator}),na,sa,la,ca,oa,aa].filter((e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name])):[],...this.options.extensions].filter((e=>["extension","node","mark"].includes(null==e?void 0:e.type)));this.extensionManager=new kl(n,this)}createCommandManager(){this.commandManager=new Gs({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=jl(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(r){if(!(r instanceof Error&&["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(r.message)))throw r;this.emit("contentError",{editor:this,error:r,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter((e=>"collaboration"!==e.name)),this.createExtensionManager()}}),t=jl(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const n=Il(t,this.options.autofocus);this.view=new as(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null===(e=this.options.editorProps)||void 0===e?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:qn.create({doc:t,selection:n||void 0})});const o=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(o),this.createNodeViews(),this.prependClass();this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void e.steps.forEach((e=>{var t;return null===(t=this.capturedTransaction)||void 0===t?void 0:t.step(e)})):void(this.capturedTransaction=e);const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const o=e.getMeta("focus"),r=e.getMeta("blur");o&&this.emit("focus",{editor:this,event:o.event,transaction:e}),r&&this.emit("blur",{editor:this,event:r.event,transaction:e}),e.docChanged&&!e.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return Wl(this.state,e)}isActive(e,t){const n="string"==typeof e?e:null,o="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return $l(e,null,n)||Gl(e,null,n);const o=Hl(t,e.schema);return"node"===o?$l(e,t,n):"mark"===o&&Gl(e,t,n)}(this.state,n,o)}getJSON(){return this.state.doc.toJSON()}getHTML(){return cl(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){return Sl(e,{from:0,to:e.content.size},t)}(this.state.doc,{blockSeparator:t,textSerializers:{...Ml(this.schema),...n}})}get isEmpty(){return Yl(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(null===(e=this.view)||void 0===e?void 0:e.docView)}$node(e,t){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new da(t,this)}get $doc(){return this.$pos(0)}};function pa(e){return new hl({find:e.find,handler:({state:t,range:n,match:o})=>{const r=rl(e.getAttributes,void 0,o);if(!1===r||null===r)return null;const{tr:i}=t,s=o[o.length-1],l=o[0];if(s){const o=l.search(/\S/),a=n.from+l.indexOf(s),c=a+s.length;if(Ul(n.from,n.to,t.doc).filter((t=>t.mark.type.excluded.find((n=>n===e.type&&n!==t.mark.type)))).filter((e=>e.to>a)).length)return null;c<n.to&&i.delete(c,n.to),a>n.from&&i.delete(n.from+o,a);const d=n.from+o+s.length;i.addMark(n.from+o,d,e.type.create(r||{})),i.removeStoredMark(e.type)}}})}function ua(e){return new hl({find:e.find,handler:({state:t,range:n,match:o})=>{const r=t.doc.resolve(n.from),i=rl(e.getAttributes,void 0,o)||{};if(!r.node(-1).canReplaceWith(r.index(-1),r.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,i)}})}function fa(e){return new hl({find:e.find,handler:({state:t,range:n,match:o,chain:r})=>{const i=rl(e.getAttributes,void 0,o)||{},s=t.tr.delete(n.from,n.to),l=s.doc.resolve(n.from).blockRange(),a=l&&nn(l,e.type,i);if(!a)return null;if(s.wrap(l,a),e.keepMarks&&e.editor){const{selection:n,storedMarks:o}=t,{splittableMarks:r}=e.editor.extensionManager,i=o||n.$to.parentOffset&&n.$from.marks();if(i){const e=i.filter((e=>r.includes(e.type.name)));s.ensureMarks(e)}}if(e.keepAttributes){const t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";r().updateAttributes(t,i).run()}const c=s.doc.resolve(n.from-1).nodeBefore;c&&c.type===e.type&&an(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(o,c))&&s.join(n.from-1)}})}class ma{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=rl(Ys(this,"addOptions",{name:this.name}))),this.storage=rl(Ys(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new ma(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>ml(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new ma(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=rl(Ys(t,"addOptions",{name:t.name})),t.storage=rl(Ys(t,"addStorage",{name:t.name,options:t.options})),t}}function ga(e){return new yl({find:e.find,handler:({state:t,range:n,match:o,pasteEvent:r})=>{const i=rl(e.getAttributes,void 0,o,r);if(!1===i||null===i)return null;const{tr:s}=t,l=o[o.length-1],a=o[0];let c=n.to;if(l){const o=a.search(/\S/),r=n.from+a.indexOf(l),d=r+l.length;if(Ul(n.from,n.to,t.doc).filter((t=>t.mark.type.excluded.find((n=>n===e.type&&n!==t.mark.type)))).filter((e=>e.to>r)).length)return null;d<n.to&&s.delete(d,n.to),r>n.from&&s.delete(n.from+o,r),c=n.from+o+l.length,s.addMark(n.from+o,c,e.type.create(i||{})),s.removeStoredMark(e.type)}}})}function ya(e){return P(((t,n)=>({get:()=>(t(),e),set(t){e=t,requestAnimationFrame((()=>{requestAnimationFrame((()=>{n()}))}))}})))}class va extends ha{constructor(e={}){return super(e),this.contentComponent=null,this.appContext=null,this.reactiveState=ya(this.view.state),this.reactiveExtensionStorage=ya(this.extensionStorage),this.on("beforeTransaction",(({nextState:e})=>{this.reactiveState.value=e,this.reactiveExtensionStorage.value=this.extensionStorage})),I(this)}get state(){return this.reactiveState?this.reactiveState.value:this.view.state}get storage(){return this.reactiveExtensionStorage?this.reactiveExtensionStorage.value:super.storage}registerPlugin(e,t){const n=super.registerPlugin(e,t);return this.reactiveState&&(this.reactiveState.value=n),n}unregisterPlugin(e){const t=super.unregisterPlugin(e);return this.reactiveState&&t&&(this.reactiveState.value=t),t}}const wa=t({name:"EditorContent",props:{editor:{default:null,type:Object}},setup(e){const t=o(),n=R();return N((()=>{const o=e.editor;o&&o.options.element&&t.value&&D((()=>{if(!t.value||!o.options.element.firstChild)return;const e=l(t.value);t.value.append(...o.options.element.childNodes),o.contentComponent=n.ctx._,n&&(o.appContext={...n.appContext,provides:n.provides}),o.setOptions({element:e}),o.createNodeViews()}))})),a((()=>{const t=e.editor;t&&(t.contentComponent=null,t.appContext=null)})),{rootEl:t}},render(){return E("div",{ref:e=>{this.rootEl=e}})}}),ba=gl.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("underline")&&{}}],renderHTML({HTMLAttributes:e}){return["u",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setUnderline:()=>({commands:e})=>e.setMark(this.name),toggleUnderline:()=>({commands:e})=>e.toggleMark(this.name),unsetUnderline:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),ka=/^\s*>\s$/,xa=ma.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[fa({find:ka,type:this.type})]}}),Sa=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,Ma=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,Ca=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,Oa=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,Ta=gl.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[pa({find:Sa,type:this.type}),pa({find:Ca,type:this.type})]},addPasteRules(){return[ga({find:Ma,type:this.type}),ga({find:Oa,type:this.type})]}}),Aa="textStyle",Ea=/^\s*([-+*])\s$/,Na=ma.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(Aa)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=fa({find:Ea,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=fa({find:Ea,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(Aa),editor:this.editor})),[e]}}),Da=/(^|[^`])`([^`]+)`(?!`)/,Ra=/(^|[^`])`([^`]+)`(?!`)/g,Ia=gl.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[pa({find:Da,type:this.type})]},addPasteRules(){return[ga({find:Ra,type:this.type})]}}),Pa=/^```([a-z]+)?[\s\n]$/,La=/^~~~([a-z]+)?[\s\n]$/,za=ma.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;const{languageClassPrefix:n}=this.options,o=[...(null===(t=e.firstElementChild)||void 0===t?void 0:t.classList)||[]].filter((e=>e.startsWith(n))).map((e=>e.replace(n,"")))[0];return o||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",tl(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!(!e||t.parent.type.name!==this.name)&&(!(!n&&t.parent.textContent.length)&&this.editor.commands.clearNodes())},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:t}=e,{selection:n}=t,{$from:o,empty:r}=n;if(!r||o.parent.type!==this.type)return!1;const i=o.parentOffset===o.parent.nodeSize-2,s=o.parent.textContent.endsWith("\n\n");return!(!i||!s)&&e.chain().command((({tr:e})=>(e.delete(o.pos-2,o.pos),!0))).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;const{state:t}=e,{selection:n,doc:o}=t,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;if(!(r.parentOffset===r.parent.nodeSize-2))return!1;const s=r.after();if(void 0===s)return!1;return o.nodeAt(s)?e.commands.command((({tr:e})=>(e.setSelection(Tn.near(o.resolve(s))),!0))):e.commands.exitCode()}}},addInputRules(){return[ua({find:Pa,type:this.type,getAttributes:e=>({language:e[1]})}),ua({find:La,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new Kn({key:new Gn("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData)return!1;if(this.editor.isActive(this.type.name))return!1;const n=t.clipboardData.getData("text/plain"),o=t.clipboardData.getData("vscode-editor-data"),r=o?JSON.parse(o):void 0,i=null==r?void 0:r.mode;if(!n||!i)return!1;const{tr:s,schema:l}=e.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:i},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(Dn.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}}),Ba=ma.create({name:"doc",topNode:!0,content:"block+"});function Va(e={}){return new Kn({view:t=>new $a(t,e)})}class $a{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!==(n=t.width)&&void 0!==n?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map((t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}}))}destroy(){this.handlers.forEach((({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t)))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t=this.editorView.state.doc.resolve(this.cursorPos),n=!t.parent.inlineContent;if(n){let n=t.nodeBefore,o=t.nodeAfter;if(n||o){let t=this.editorView.nodeDOM(this.cursorPos-(n?n.nodeSize:0));if(t){let r=t.getBoundingClientRect(),i=n?r.bottom:r.top;n&&o&&(i=(i+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),e={left:r.left,right:r.right,top:i-this.width/2,bottom:i+this.width/2}}}}if(!e){let t=this.editorView.coordsAtPos(this.cursorPos);e={left:t.left-this.width/2,right:t.left+this.width/2,top:t.top,bottom:t.bottom}}let o,r,i=this.editorView.dom.offsetParent;if(this.element||(this.element=i.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",n),this.element.classList.toggle("prosemirror-dropcursor-inline",!n),!i||i==document.body&&"static"==getComputedStyle(i).position)o=-pageXOffset,r=-pageYOffset;else{let e=i.getBoundingClientRect();o=e.left-i.scrollLeft,r=e.top-i.scrollTop}this.element.style.left=e.left-o+"px",this.element.style.top=e.top-r+"px",this.element.style.width=e.right-e.left+"px",this.element.style.height=e.bottom-e.top+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout((()=>this.setCursor(null)),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),o=n&&n.type.spec.disableDropCursor,r="function"==typeof o?o(this.editorView,t,e):o;if(t&&!r){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=hn(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){e.target!=this.editorView.dom&&this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}const Ha=xl.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[Va(this.options)]}});class Fa extends Tn{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return Fa.valid(n)?new Fa(n):Tn.near(n)}content(){return ze.empty}eq(e){return e instanceof Fa&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw new RangeError("Invalid input for GapCursor.fromJSON");return new Fa(e.resolve(t.pos))}getBookmark(){return new ja(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),o=e.node(t);if(0!=n)for(let e=o.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}else if(o.type.spec.isolating)return!0}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),o=e.node(t);if(n!=o.childCount)for(let e=o.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}else if(o.type.spec.isolating)return!0}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let o=t.contentMatchAt(e.index()).defaultType;return o&&o.isTextblock}static findGapCursorFrom(e,t,n=!1){e:for(;;){if(!n&&Fa.valid(e))return e;let o=e.pos,r=null;for(let n=e.depth;;n--){let i=e.node(n);if(t>0?e.indexAfter(n)<i.childCount:e.index(n)>0){r=i.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;o+=t;let s=e.doc.resolve(o);if(Fa.valid(s))return s}for(;;){let i=t>0?r.firstChild:r.lastChild;if(!i){if(r.isAtom&&!r.isText&&!In.isSelectable(r)){e=e.doc.resolve(o+r.nodeSize*t),n=!1;continue e}break}r=i,o+=t;let s=e.doc.resolve(o);if(Fa.valid(s))return s}return null}}}Fa.prototype.visible=!1,Fa.findFrom=Fa.findGapCursorFrom,Tn.jsonID("gapcursor",Fa);class ja{constructor(e){this.pos=e}map(e){return new ja(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return Fa.valid(t)?new Fa(t):Tn.near(t)}}const _a=Ss({ArrowLeft:qa("horiz",-1),ArrowRight:qa("horiz",1),ArrowUp:qa("vert",-1),ArrowDown:qa("vert",1)});function qa(e,t){const n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,o,r){let i=e.selection,s=t>0?i.$to:i.$from,l=i.empty;if(i instanceof Dn){if(!r.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=Fa.findGapCursorFrom(s,t,l);return!!a&&(o&&o(e.tr.setSelection(new Fa(a))),!0)}}function Wa(e,t,n){if(!e||!e.editable)return!1;let o=e.state.doc.resolve(t);if(!Fa.valid(o))return!1;let r=e.posAtCoords({left:n.clientX,top:n.clientY});return!(r&&r.inside>-1&&In.isSelectable(e.state.doc.nodeAt(r.inside)))&&(e.dispatch(e.state.tr.setSelection(new Fa(o))),!0)}function Ka(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof Fa))return!1;let{$from:n}=e.state.selection,o=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!o)return!1;let r=Ne.empty;for(let s=o.length-1;s>=0;s--)r=Ne.from(o[s].createAndFill(null,r));let i=e.state.tr.replace(n.pos,n.pos,new ze(r,0,0));return i.setSelection(Dn.near(i.doc.resolve(n.pos+1))),e.dispatch(i),!1}function Ua(e){if(!(e.selection instanceof Fa))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",zi.create(e.doc,[Ii.widget(e.selection.head,t,{key:"gapcursor"})])}const Ja=xl.create({name:"gapCursor",addProseMirrorPlugins:()=>[new Kn({props:{decorations:Ua,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&Fa.valid(n)?new Fa(n):null,handleClick:Wa,handleKeyDown:_a,handleDOMEvents:{beforeinput:Ka}}})],extendNodeSchema(e){var t;return{allowGapCursor:null!==(t=rl(Ys(e,"allowGapCursor",{name:e.name,options:e.options,storage:e.storage})))&&void 0!==t?t:null}}}),Ga=ma.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",tl(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:o})=>e.first([()=>e.exitCode(),()=>e.command((()=>{const{selection:e,storedMarks:r}=n;if(e.$from.parent.type.spec.isolating)return!1;const{keepMarks:i}=this.options,{splittableMarks:s}=o.extensionManager,l=r||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command((({tr:e,dispatch:t})=>{if(t&&l&&i){const t=l.filter((e=>s.includes(e.type.name)));e.ensureMarks(t)}return!0})).run()}))])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),Qa=ma.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map((e=>({tag:`h${e}`,attrs:{level:e}})))},renderHTML({node:e,HTMLAttributes:t}){return[`h${this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0]}`,tl(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce(((e,t)=>({...e,[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})})),{})},addInputRules(){return this.options.levels.map((e=>ua({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}})))}});var Ya=200,Xa=function(){};Xa.prototype.append=function(e){return e.length?(e=Xa.from(e),!this.length&&e||e.length<Ya&&this.leafAppend(e)||this.length<Ya&&e.leafPrepend(this)||this.appendInner(e)):this},Xa.prototype.prepend=function(e){return e.length?Xa.from(e).append(this):this},Xa.prototype.appendInner=function(e){return new ec(this,e)},Xa.prototype.slice=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t?Xa.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},Xa.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)},Xa.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},Xa.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var o=[];return this.forEach((function(t,n){return o.push(e(t,n))}),t,n),o},Xa.from=function(e){return e instanceof Xa?e:e&&e.length?new Za(e):Xa.empty};var Za=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,o){for(var r=t;r<n;r++)if(!1===e(this.values[r],o+r))return!1},t.prototype.forEachInvertedInner=function(e,t,n,o){for(var r=t-1;r>=n;r--)if(!1===e(this.values[r],o+r))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=Ya)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=Ya)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(Xa);Xa.empty=new Za([]);var ec=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,o){var r=this.left.length;return!(t<r&&!1===this.left.forEachInner(e,t,Math.min(n,r),o))&&(!(n>r&&!1===this.right.forEachInner(e,Math.max(t-r,0),Math.min(this.length,n)-r,o+r))&&void 0)},t.prototype.forEachInvertedInner=function(e,t,n,o){var r=this.left.length;return!(t>r&&!1===this.right.forEachInvertedInner(e,t-r,Math.max(n,r)-r,o+r))&&(!(n<r&&!1===this.left.forEachInvertedInner(e,Math.min(t,r),n,o))&&void 0)},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(Xa);class tc{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(0==this.eventCount)return null;let n,o,r=this.items.length;for(;;r--){if(this.items.get(r-1).selection){--r;break}}t&&(n=this.remapping(r,this.items.length),o=n.maps.length);let i,s,l=e.tr,a=[],c=[];return this.items.forEach(((e,t)=>{if(!e.step)return n||(n=this.remapping(r,t+1),o=n.maps.length),o--,void c.push(e);if(n){c.push(new nc(e.map));let t,r=e.step.map(n.slice(o));r&&l.maybeStep(r).doc&&(t=l.mapping.maps[l.mapping.maps.length-1],a.push(new nc(t,void 0,void 0,a.length+c.length))),o--,t&&n.appendMap(t,o)}else l.maybeStep(e.step);return e.selection?(i=n?e.selection.map(n.slice(o)):e.selection,s=new tc(this.items.slice(0,r).append(c.reverse().concat(a)),this.eventCount-1),!1):void 0}),this.items.length,0),{remaining:s,transform:l,selection:i}}addTransform(e,t,n,o){let r=[],i=this.eventCount,s=this.items,l=!o&&s.length?s.get(s.length-1):null;for(let c=0;c<e.steps.length;c++){let n,a=e.steps[c].invert(e.docs[c]),d=new nc(e.mapping.maps[c],a,t);(n=l&&l.merge(d))&&(d=n,c?r.pop():s=s.slice(0,s.length-1)),r.push(d),t&&(i++,t=void 0),o||(l=d)}let a=i-n.depth;return a>rc&&(s=function(e,t){let n;return e.forEach(((e,o)=>{if(e.selection&&0==t--)return n=o,!1})),e.slice(n)}(s,a),i-=a),new tc(s.append(r),i)}remapping(e,t){let n=new Ft;return this.items.forEach(((t,o)=>{let r=null!=t.mirrorOffset&&o-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,r)}),e,t),n}addMaps(e){return 0==this.eventCount?this:new tc(this.items.append(e.map((e=>new nc(e)))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],o=Math.max(0,this.items.length-t),r=e.mapping,i=e.steps.length,s=this.eventCount;this.items.forEach((e=>{e.selection&&s--}),o);let l=t;this.items.forEach((t=>{let o=r.getMirror(--l);if(null==o)return;i=Math.min(i,o);let a=r.maps[o];if(t.step){let i=e.steps[o].invert(e.docs[o]),c=t.selection&&t.selection.map(r.slice(l+1,o));c&&s++,n.push(new nc(a,i,c))}else n.push(new nc(a))}),o);let a=[];for(let h=t;h<i;h++)a.push(new nc(r.maps[h]));let c=this.items.slice(0,o).append(a).append(n),d=new tc(c,s);return d.emptyItemCount()>500&&(d=d.compress(this.items.length-n.length)),d}emptyItemCount(){let e=0;return this.items.forEach((t=>{t.step||e++})),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,o=[],r=0;return this.items.forEach(((i,s)=>{if(s>=e)o.push(i),i.selection&&r++;else if(i.step){let e=i.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=i.selection&&i.selection.map(t.slice(n));l&&r++;let a,c=new nc(s.invert(),e,l),d=o.length-1;(a=o.length&&o[d].merge(c))?o[d]=a:o.push(c)}}else i.map&&n--}),this.items.length,0),new tc(Xa.from(o.reverse()),r)}}tc.empty=new tc(Xa.empty,0);class nc{constructor(e,t,n,o){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=o}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new nc(t.getMap().invert(),t,this.selection)}}}class oc{constructor(e,t,n,o,r){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=o,this.prevComposition=r}}const rc=20;function ic(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach(((e,n,o,r)=>t.push(o,r)));return t}function sc(e,t){if(!e)return null;let n=[];for(let o=0;o<e.length;o+=2){let r=t.map(e[o],1),i=t.map(e[o+1],-1);r<=i&&n.push(r,i)}return n}let lc=!1,ac=null;function cc(e){let t=e.plugins;if(ac!=t){lc=!1,ac=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){lc=!0;break}}return lc}const dc=new Gn("history"),hc=new Gn("closeHistory");function pc(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new Kn({key:dc,state:{init:()=>new oc(tc.empty,tc.empty,null,0,-1),apply:(t,n,o)=>function(e,t,n,o){let r,i=n.getMeta(dc);if(i)return i.historyState;n.getMeta(hc)&&(e=new oc(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(dc))return s.getMeta(dc).redo?new oc(e.done.addTransform(n,void 0,o,cc(t)),e.undone,ic(n.mapping.maps),e.prevTime,e.prevComposition):new oc(e.done,e.undone.addTransform(n,void 0,o,cc(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))return(r=n.getMeta("rebased"))?new oc(e.done.rebased(n,r),e.undone.rebased(n,r),sc(e.prevRanges,n.mapping),e.prevTime,e.prevComposition):new oc(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),sc(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let r=n.getMeta("composition"),i=0==e.prevTime||!s&&e.prevComposition!=r&&(e.prevTime<(n.time||0)-o.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach(((e,o)=>{for(let r=0;r<t.length;r+=2)e<=t[r+1]&&o>=t[r]&&(n=!0)})),n}(n,e.prevRanges)),l=s?sc(e.prevRanges,n.mapping):ic(n.mapping.maps);return new oc(e.done.addTransform(n,i?t.selection.getBookmark():void 0,o,cc(t)),tc.empty,l,n.time,null==r?e.prevComposition:r)}}(n,o,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,o="historyUndo"==n?fc:"historyRedo"==n?mc:null;return!!o&&(t.preventDefault(),o(e.state,e.dispatch))}}}})}function uc(e,t){return(n,o)=>{let r=dc.getState(n);if(!r||0==(e?r.undone:r.done).eventCount)return!1;if(o){let i=function(e,t,n){let o=cc(t),r=dc.get(t).spec.config,i=(n?e.undone:e.done).popEvent(t,o);if(!i)return null;let s=i.selection.resolve(i.transform.doc),l=(n?e.done:e.undone).addTransform(i.transform,t.selection.getBookmark(),r,o),a=new oc(n?l:i.remaining,n?i.remaining:l,null,0,-1);return i.transform.setSelection(s).setMeta(dc,{redo:n,historyState:a})}(r,n,e);i&&o(t?i.scrollIntoView():i)}return!0}}const fc=uc(!1,!0),mc=uc(!0,!0),gc=xl.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>fc(e,t),redo:()=>({state:e,dispatch:t})=>mc(e,t)}),addProseMirrorPlugins(){return[pc(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),yc=ma.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",tl(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{const{selection:n}=t,{$from:o,$to:r}=n,i=e();return 0===o.parentOffset?i.insertContentAt({from:Math.max(o.pos-1,0),to:r.pos},{type:this.name}):n instanceof In?i.insertContentAt(r.pos,{type:this.name}):i.insertContent({type:this.name}),i.command((({tr:e,dispatch:t})=>{var n;if(t){const{$to:t}=e.selection,o=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(Dn.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(In.create(e.doc,t.pos)):e.setSelection(Dn.create(e.doc,t.pos));else{const r=null===(n=t.parent.type.contentMatch.defaultType)||void 0===n?void 0:n.create();r&&(e.insert(o,r),e.setSelection(Dn.create(e.doc,o+1)))}e.scrollIntoView()}return!0})).run()}}},addInputRules(){return[(e={find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type},new hl({find:e.find,handler:({state:t,range:n,match:o})=>{const r=rl(e.getAttributes,void 0,o)||{},{tr:i}=t,s=n.from;let l=n.to;const a=e.type.create(r);if(o[1]){let e=s+o[0].lastIndexOf(o[1]);e>l?e=l:l=e+o[1].length;const t=o[0][o[0].length-1];i.insertText(t,s+o[0].length-1),i.replaceWith(e,l,a)}else if(o[0]){const t=e.type.isInline?s:s-1;i.insert(t,e.type.create(r)).delete(i.mapping.map(s),i.mapping.map(l))}i.scrollIntoView()}}))];var e}}),vc=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,wc=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,bc=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,kc=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,xc=gl.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[pa({find:vc,type:this.type}),pa({find:bc,type:this.type})]},addPasteRules(){return[ga({find:wc,type:this.type}),ga({find:kc,type:this.type})]}}),Sc=ma.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",tl(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Mc="textStyle",Cc=/^(\d+)\.\s$/,Oc=ma.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){const{start:t,...n}=e;return 1===t?["ol",tl(this.options.HTMLAttributes,n),0]:["ol",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(Mc)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=fa({find:Cc,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=fa({find:Cc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(Mc)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),Tc=ma.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Ac=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,Ec=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,Nc=gl.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",tl(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[pa({find:Ac,type:this.type})]},addPasteRules(){return[ga({find:Ec,type:this.type})]}}),Dc=ma.create({name:"text",group:"inline"}),Rc=xl.create({name:"starterKit",addExtensions(){const e=[];return!1!==this.options.bold&&e.push(Ta.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(xa.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(Na.configure(this.options.bulletList)),!1!==this.options.code&&e.push(Ia.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(za.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(Ba.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(Ha.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(Ja.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(Ga.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(Qa.configure(this.options.heading)),!1!==this.options.history&&e.push(gc.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(yc.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(xc.configure(this.options.italic)),!1!==this.options.listItem&&e.push(Sc.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(Oc.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(Tc.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(Nc.configure(this.options.strike)),!1!==this.options.text&&e.push(Dc.configure(this.options.text)),e}}),Ic=(e,t)=>{for(const n in t)e[n]=t[n];return e},Pc="numeric",Lc="ascii",zc="alpha",Bc="asciinumeric",Vc="alphanumeric",$c="domain",Hc="emoji",Fc="scheme",jc="slashscheme",_c="whitespace";function qc(e,t){return e in t||(t[e]=[]),t[e]}function Wc(e,t,n){t[Pc]&&(t[Bc]=!0,t[Vc]=!0),t[Lc]&&(t[Bc]=!0,t[zc]=!0),t[Bc]&&(t[Vc]=!0),t[zc]&&(t[Vc]=!0),t[Vc]&&(t[$c]=!0),t[Hc]&&(t[$c]=!0);for(const o in t){const t=qc(o,n);t.indexOf(e)<0&&t.push(e)}}function Kc(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}Kc.groups={},Kc.prototype={accepts(){return!!this.t},go(e){const t=this,n=t.j[e];if(n)return n;for(let o=0;o<t.jr.length;o++){const n=t.jr[o][0],r=t.jr[o][1];if(r&&n.test(e))return r}return t.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,o){for(let r=0;r<e.length;r++)this.tt(e[r],t,n,o)},tr(e,t,n,o){let r;return o=o||Kc.groups,t&&t.j?r=t:(r=new Kc(t),n&&o&&Wc(t,n,o)),this.jr.push([e,r]),r},ts(e,t,n,o){let r=this;const i=e.length;if(!i)return r;for(let s=0;s<i-1;s++)r=r.tt(e[s]);return r.tt(e[i-1],t,n,o)},tt(e,t,n,o){o=o||Kc.groups;const r=this;if(t&&t.j)return r.j[e]=t,t;const i=t;let s,l=r.go(e);if(l?(s=new Kc,Ic(s.j,l.j),s.jr.push.apply(s.jr,l.jr),s.jd=l.jd,s.t=l.t):s=new Kc,i){if(o)if(s.t&&"string"==typeof s.t){const e=Ic(function(e,t){const n={};for(const o in t)t[o].indexOf(e)>=0&&(n[o]=!0);return n}(s.t,o),n);Wc(i,e,o)}else n&&Wc(i,n,o);s.t=i}return r.j[e]=s,s}};const Uc=(e,t,n,o,r)=>e.ta(t,n,o,r),Jc=(e,t,n,o,r)=>e.tr(t,n,o,r),Gc=(e,t,n,o,r)=>e.ts(t,n,o,r),Qc=(e,t,n,o,r)=>e.tt(t,n,o,r),Yc="WORD",Xc="UWORD",Zc="ASCIINUMERICAL",ed="ALPHANUMERICAL",td="LOCALHOST",nd="TLD",od="UTLD",rd="SCHEME",id="SLASH_SCHEME",sd="NUM",ld="WS",ad="NL",cd="OPENBRACE",dd="CLOSEBRACE",hd="OPENBRACKET",pd="CLOSEBRACKET",ud="OPENPAREN",fd="CLOSEPAREN",md="OPENANGLEBRACKET",gd="CLOSEANGLEBRACKET",yd="FULLWIDTHLEFTPAREN",vd="FULLWIDTHRIGHTPAREN",wd="LEFTCORNERBRACKET",bd="RIGHTCORNERBRACKET",kd="LEFTWHITECORNERBRACKET",xd="RIGHTWHITECORNERBRACKET",Sd="FULLWIDTHLESSTHAN",Md="FULLWIDTHGREATERTHAN",Cd="AMPERSAND",Od="APOSTROPHE",Td="ASTERISK",Ad="AT",Ed="BACKSLASH",Nd="BACKTICK",Dd="CARET",Rd="COLON",Id="COMMA",Pd="DOLLAR",Ld="DOT",zd="EQUALS",Bd="EXCLAMATION",Vd="HYPHEN",$d="PERCENT",Hd="PIPE",Fd="PLUS",jd="POUND",_d="QUERY",qd="QUOTE",Wd="FULLWIDTHMIDDLEDOT",Kd="SEMI",Ud="SLASH",Jd="TILDE",Gd="UNDERSCORE",Qd="EMOJI",Yd="SYM";var Xd=Object.freeze({__proto__:null,WORD:Yc,UWORD:Xc,ASCIINUMERICAL:Zc,ALPHANUMERICAL:ed,LOCALHOST:td,TLD:nd,UTLD:od,SCHEME:rd,SLASH_SCHEME:id,NUM:sd,WS:ld,NL:ad,OPENBRACE:cd,CLOSEBRACE:dd,OPENBRACKET:hd,CLOSEBRACKET:pd,OPENPAREN:ud,CLOSEPAREN:fd,OPENANGLEBRACKET:md,CLOSEANGLEBRACKET:gd,FULLWIDTHLEFTPAREN:yd,FULLWIDTHRIGHTPAREN:vd,LEFTCORNERBRACKET:wd,RIGHTCORNERBRACKET:bd,LEFTWHITECORNERBRACKET:kd,RIGHTWHITECORNERBRACKET:xd,FULLWIDTHLESSTHAN:Sd,FULLWIDTHGREATERTHAN:Md,AMPERSAND:Cd,APOSTROPHE:Od,ASTERISK:Td,AT:Ad,BACKSLASH:Ed,BACKTICK:Nd,CARET:Dd,COLON:Rd,COMMA:Id,DOLLAR:Pd,DOT:Ld,EQUALS:zd,EXCLAMATION:Bd,HYPHEN:Vd,PERCENT:$d,PIPE:Hd,PLUS:Fd,POUND:jd,QUERY:_d,QUOTE:qd,FULLWIDTHMIDDLEDOT:Wd,SEMI:Kd,SLASH:Ud,TILDE:Jd,UNDERSCORE:Gd,EMOJI:Qd,SYM:Yd});const Zd=/[a-z]/,eh=new RegExp("\\p{L}","u"),th=new RegExp("\\p{Emoji}","u"),nh=/\d/,oh=/\s/;let rh=null,ih=null;function sh(e,t){const n=function(e){const t=[],n=e.length;let o=0;for(;o<n;){let r,i=e.charCodeAt(o),s=i<55296||i>56319||o+1===n||(r=e.charCodeAt(o+1))<56320||r>57343?e[o]:e.slice(o,o+2);t.push(s),o+=s.length}return t}(t.replace(/[A-Z]/g,(e=>e.toLowerCase()))),o=n.length,r=[];let i=0,s=0;for(;s<o;){let l=e,a=null,c=0,d=null,h=-1,p=-1;for(;s<o&&(a=l.go(n[s]));)l=a,l.accepts()?(h=0,p=0,d=l):h>=0&&(h+=n[s].length,p++),c+=n[s].length,i+=n[s].length,s++;i-=h,s-=p,c-=h,r.push({t:d.t,v:t.slice(i-c,i),s:i-c,e:i})}return r}function lh(e,t,n,o,r){let i;const s=t.length;for(let l=0;l<s-1;l++){const n=t[l];e.j[n]?i=e.j[n]:(i=new Kc(o),i.jr=r.slice(),e.j[n]=i),e=i}return i=new Kc(n),i.jr=r.slice(),e.j[t[s-1]]=i,i}function ah(e){const t=[],n=[];let o=0;for(;o<e.length;){let r=0;for(;"0123456789".indexOf(e[o+r])>=0;)r++;if(r>0){t.push(n.join(""));for(let t=parseInt(e.substring(o,o+r),10);t>0;t--)n.pop();o+=r}else n.push(e[o]),o++}return t}const ch={defaultProtocol:"http",events:null,format:hh,formatHref:hh,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function dh(e,t=null){let n=Ic({},ch);e&&(n=Ic(n,e instanceof dh?e.o:e));const o=n.ignoreTags,r=[];for(let i=0;i<o.length;i++)r.push(o[i].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=r}function hh(e){return e}function ph(e,t){this.t="token",this.v=e,this.tk=t}function uh(e,t){class n extends ph{constructor(t,n){super(t,n),this.t=e}}for(const o in t)n.prototype[o]=t[o];return n.t=e,n}dh.prototype={o:ch,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){const o=null!=t;let r=this.o[e];return r?("object"==typeof r?(r=n.t in r?r[n.t]:ch[e],"function"==typeof r&&o&&(r=r(t,n))):"function"==typeof r&&o&&(r=r(t,n.t,n)),r):r},getObj(e,t,n){let o=this.o[e];return"function"==typeof o&&null!=t&&(o=o(t,n.t,n)),o},render(e){const t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},ph.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const t=this.toString(),n=e.get("truncate",t,this),o=e.get("format",t,this);return n&&o.length>n?o.substring(0,n)+"…":o},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=ch.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const t=this,n=this.toHref(e.get("defaultProtocol")),o=e.get("formatHref",n,this),r=e.get("tagName",n,t),i=this.toFormattedString(e),s={},l=e.get("className",n,t),a=e.get("target",n,t),c=e.get("rel",n,t),d=e.getObj("attributes",n,t),h=e.getObj("events",n,t);return s.href=o,l&&(s.class=l),a&&(s.target=a),c&&(s.rel=c),d&&Ic(s,d),{tagName:r,attributes:s,content:i,eventListeners:h}}};const fh=uh("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),mh=uh("text"),gh=uh("nl"),yh=uh("url",{isLink:!0,toHref(e=ch.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==td&&e[1].t===Rd}}),vh=e=>new Kc(e);function wh(e,t,n){const o=n[0].s,r=n[n.length-1].e;return new e(t.slice(o,r),n)}const bh="undefined"!=typeof console&&console&&console.warn||(()=>{}),kh={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function xh(e,t=!1){if(kh.initialized&&bh(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');kh.customSchemes.push([e,t])}function Sh(){kh.scanner=function(e=[]){const t={};Kc.groups=t;const n=new Kc;null==rh&&(rh=ah("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2ntley5rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6logistics9properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3ncaster6d0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2psy3ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==ih&&(ih=ah("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),Qc(n,"'",Od),Qc(n,"{",cd),Qc(n,"}",dd),Qc(n,"[",hd),Qc(n,"]",pd),Qc(n,"(",ud),Qc(n,")",fd),Qc(n,"<",md),Qc(n,">",gd),Qc(n,"（",yd),Qc(n,"）",vd),Qc(n,"「",wd),Qc(n,"」",bd),Qc(n,"『",kd),Qc(n,"』",xd),Qc(n,"＜",Sd),Qc(n,"＞",Md),Qc(n,"&",Cd),Qc(n,"*",Td),Qc(n,"@",Ad),Qc(n,"`",Nd),Qc(n,"^",Dd),Qc(n,":",Rd),Qc(n,",",Id),Qc(n,"$",Pd),Qc(n,".",Ld),Qc(n,"=",zd),Qc(n,"!",Bd),Qc(n,"-",Vd),Qc(n,"%",$d),Qc(n,"|",Hd),Qc(n,"+",Fd),Qc(n,"#",jd),Qc(n,"?",_d),Qc(n,'"',qd),Qc(n,"/",Ud),Qc(n,";",Kd),Qc(n,"~",Jd),Qc(n,"_",Gd),Qc(n,"\\",Ed),Qc(n,"・",Wd);const o=Jc(n,nh,sd,{[Pc]:!0});Jc(o,nh,o);const r=Jc(o,Zd,Zc,{[Bc]:!0}),i=Jc(o,eh,ed,{[Vc]:!0}),s=Jc(n,Zd,Yc,{[Lc]:!0});Jc(s,nh,r),Jc(s,Zd,s),Jc(r,nh,r),Jc(r,Zd,r);const l=Jc(n,eh,Xc,{[zc]:!0});Jc(l,Zd),Jc(l,nh,i),Jc(l,eh,l),Jc(i,nh,i),Jc(i,Zd),Jc(i,eh,i);const a=Qc(n,"\n",ad,{[_c]:!0}),c=Qc(n,"\r",ld,{[_c]:!0}),d=Jc(n,oh,ld,{[_c]:!0});Qc(n,"￼",d),Qc(c,"\n",a),Qc(c,"￼",d),Jc(c,oh,d),Qc(d,"\r"),Qc(d,"\n"),Jc(d,oh,d),Qc(d,"￼",d);const h=Jc(n,th,Qd,{[Hc]:!0});Qc(h,"#"),Jc(h,th,h),Qc(h,"️",h);const p=Qc(h,"‍");Qc(p,"#"),Jc(p,th,h);const u=[[Zd,s],[nh,r]],f=[[Zd,null],[eh,l],[nh,i]];for(let m=0;m<rh.length;m++)lh(n,rh[m],nd,Yc,u);for(let m=0;m<ih.length;m++)lh(n,ih[m],od,Xc,f);Wc(nd,{tld:!0,ascii:!0},t),Wc(od,{utld:!0,alpha:!0},t),lh(n,"file",rd,Yc,u),lh(n,"mailto",rd,Yc,u),lh(n,"http",id,Yc,u),lh(n,"https",id,Yc,u),lh(n,"ftp",id,Yc,u),lh(n,"ftps",id,Yc,u),Wc(rd,{scheme:!0,ascii:!0},t),Wc(id,{slashscheme:!0,ascii:!0},t),e=e.sort(((e,t)=>e[0]>t[0]?1:-1));for(let m=0;m<e.length;m++){const t=e[m][0],o=e[m][1]?{[Fc]:!0}:{[jc]:!0};t.indexOf("-")>=0?o[$c]=!0:Zd.test(t)?nh.test(t)?o[Bc]=!0:o[Lc]=!0:o[Pc]=!0,Gc(n,t,t,o)}return Gc(n,"localhost",td,{ascii:!0}),n.jd=new Kc(Yd),{start:n,tokens:Ic({groups:t},Xd)}}(kh.customSchemes);for(let e=0;e<kh.tokenQueue.length;e++)kh.tokenQueue[e][1]({scanner:kh.scanner});kh.parser=function({groups:e}){const t=e.domain.concat([Cd,Td,Ad,Ed,Nd,Dd,Pd,zd,Vd,sd,$d,Hd,Fd,jd,Ud,Yd,Jd,Gd]),n=[Rd,Id,Ld,Bd,$d,_d,qd,Kd,md,gd,cd,dd,pd,hd,ud,fd,yd,vd,wd,bd,kd,xd,Sd,Md],o=[Cd,Od,Td,Ed,Nd,Dd,Pd,zd,Vd,cd,dd,$d,Hd,Fd,jd,_d,Ud,Yd,Jd,Gd],r=vh(),i=Qc(r,Jd);Uc(i,o,i),Uc(i,e.domain,i);const s=vh(),l=vh(),a=vh();Uc(r,e.domain,s),Uc(r,e.scheme,l),Uc(r,e.slashscheme,a),Uc(s,o,i),Uc(s,e.domain,s);const c=Qc(s,Ad);Qc(i,Ad,c),Qc(l,Ad,c),Qc(a,Ad,c);const d=Qc(i,Ld);Uc(d,o,i),Uc(d,e.domain,i);const h=vh();Uc(c,e.domain,h),Uc(h,e.domain,h);const p=Qc(h,Ld);Uc(p,e.domain,h);const u=vh(fh);Uc(p,e.tld,u),Uc(p,e.utld,u),Qc(c,td,u);const f=Qc(h,Vd);Qc(f,Vd,f),Uc(f,e.domain,h),Uc(u,e.domain,h),Qc(u,Ld,p),Qc(u,Vd,f);const m=Qc(u,Rd);Uc(m,e.numeric,fh);const g=Qc(s,Vd),y=Qc(s,Ld);Qc(g,Vd,g),Uc(g,e.domain,s),Uc(y,o,i),Uc(y,e.domain,s);const v=vh(yh);Uc(y,e.tld,v),Uc(y,e.utld,v),Uc(v,e.domain,s),Uc(v,o,i),Qc(v,Ld,y),Qc(v,Vd,g),Qc(v,Ad,c);const w=Qc(v,Rd),b=vh(yh);Uc(w,e.numeric,b);const k=vh(yh),x=vh();Uc(k,t,k),Uc(k,n,x),Uc(x,t,k),Uc(x,n,x),Qc(v,Ud,k),Qc(b,Ud,k);const S=Qc(l,Rd),M=Qc(a,Rd),C=Qc(M,Ud),O=Qc(C,Ud);Uc(l,e.domain,s),Qc(l,Ld,y),Qc(l,Vd,g),Uc(a,e.domain,s),Qc(a,Ld,y),Qc(a,Vd,g),Uc(S,e.domain,k),Qc(S,Ud,k),Qc(S,_d,k),Uc(O,e.domain,k),Uc(O,t,k),Qc(O,Ud,k);const T=[[cd,dd],[hd,pd],[ud,fd],[md,gd],[yd,vd],[wd,bd],[kd,xd],[Sd,Md]];for(let A=0;A<T.length;A++){const[e,o]=T[A],r=Qc(k,e);Qc(x,e,r),Qc(r,o,k);const i=vh(yh);Uc(r,t,i);const s=vh();Uc(r,n),Uc(i,t,i),Uc(i,n,s),Uc(s,t,i),Uc(s,n,s),Qc(i,o,k),Qc(s,o,k)}return Qc(r,td,v),Qc(r,ad,gh),{start:r,tokens:Xd}}(kh.scanner.tokens);for(let e=0;e<kh.pluginQueue.length;e++)kh.pluginQueue[e][1]({scanner:kh.scanner,parser:kh.parser});return kh.initialized=!0,kh}function Mh(e){return kh.initialized||Sh(),function(e,t,n){let o=n.length,r=0,i=[],s=[];for(;r<o;){let l=e,a=null,c=null,d=0,h=null,p=-1;for(;r<o&&!(a=l.go(n[r].t));)s.push(n[r++]);for(;r<o&&(c=a||l.go(n[r].t));)a=null,l=c,l.accepts()?(p=0,h=l):p>=0&&p++,r++,d++;if(p<0)r-=d,r<o&&(s.push(n[r]),r++);else{s.length>0&&(i.push(wh(mh,t,s)),s=[]),r-=p,d-=p;const e=h.t,o=n.slice(r-d,r);i.push(wh(e,t,o))}}return s.length>0&&i.push(wh(mh,t,s)),i}(kh.parser.start,e,sh(kh.scanner.start,e))}function Ch(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}const o=new dh(n),r=Mh(e),i=[];for(let s=0;s<r.length;s++){const e=r[s];!e.isLink||t&&e.t!==t||!o.check(e)||i.push(e.toFormattedObject(o))}return i}function Oh(e){return new Kn({key:new Gn("autolink"),appendTransaction:(t,n,o)=>{const r=t.some((e=>e.docChanged))&&!n.doc.eq(o.doc),i=t.some((e=>e.getMeta("preventAutolink")));if(!r||i)return;const{tr:s}=o,l=function(e,t){const n=new Cn(e);return t.forEach((e=>{e.steps.forEach((e=>{n.step(e)}))})),n}(n.doc,[...t]),a=function(e){const{mapping:t,steps:n}=e,o=[];return t.maps.forEach(((e,r)=>{const i=[];if(e.ranges.length)e.forEach(((e,t)=>{i.push({from:e,to:t})}));else{const{from:e,to:t}=n[r];if(void 0===e||void 0===t)return;i.push({from:e,to:t})}i.forEach((({from:e,to:n})=>{const i=t.slice(r).map(e,-1),s=t.slice(r).map(n),l=t.invert().map(i,-1),a=t.invert().map(s);o.push({oldRange:{from:l,to:a},newRange:{from:i,to:s}})}))})),Kl(o)}(l);return a.forEach((({newRange:t})=>{const n=function(e,t,n){const o=[];return e.nodesBetween(t.from,t.to,((e,t)=>{n(e)&&o.push({node:e,pos:t})})),o}(o.doc,t,(e=>e.isTextblock));let r,i;if(n.length>1?(r=n[0],i=o.doc.textBetween(r.pos,r.pos+r.node.nodeSize,void 0," ")):n.length&&o.doc.textBetween(t.from,t.to," "," ").endsWith(" ")&&(r=n[0],i=o.doc.textBetween(r.pos,t.to,void 0," ")),r&&i){const t=i.split(" ").filter((e=>""!==e));if(t.length<=0)return!1;const n=t[t.length-1],a=r.pos+i.lastIndexOf(n);if(!n)return!1;const c=Mh(n).map((t=>t.toObject(e.defaultProtocol)));if(!(1===(l=c).length?l[0].isLink:3===l.length&&l[1].isLink&&["()","[]"].includes(l[0].value+l[2].value)))return!1;c.filter((e=>e.isLink)).map((e=>({...e,from:a+e.start+1,to:a+e.end+1}))).filter((e=>!o.schema.marks.code||!o.doc.rangeHasMark(e.from,e.to,o.schema.marks.code))).filter((t=>e.validate(t.value))).filter((t=>e.shouldAutoLink(t.value))).forEach((t=>{Ul(t.from,t.to,o.doc).some((t=>t.mark.type===e.type))||s.addMark(t.from,t.to,e.type.create({href:t.href}))}))}var l})),s.steps.length?s:void 0}})}Mh.scan=sh;const Th=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function Ah(e,t){const n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach((e=>{const t="string"==typeof e?e:e.scheme;t&&n.push(t)})),!e||e.replace(Th,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const Eh=gl.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach((e=>{"string"!=typeof e?xh(e.scheme,e.optionalSlashes):xh(e)}))},onDestroy(){Kc.groups={},kh.scanner=null,kh.parser=null,kh.tokenQueue=[],kh.pluginQueue=[],kh.customSchemes=[],kh.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!Ah(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{const t=e.getAttribute("href");return!(!t||!this.options.isAllowedUri(t,{defaultValidate:e=>!!Ah(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!Ah(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",tl(this.options.HTMLAttributes,e),0]:["a",tl(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{const{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!Ah(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{const{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!Ah(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ga({find:e=>{const t=[];if(e){const{protocols:n,defaultProtocol:o}=this.options,r=Ch(e).filter((e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!Ah(e,n),protocols:n,defaultProtocol:o})));r.length&&r.forEach((e=>t.push({text:e.value,data:{href:e.href},index:e.start})))}return t},type:this.type,getAttributes:e=>{var t;return{href:null===(t=e.data)||void 0===t?void 0:t.href}}})]},addProseMirrorPlugins(){const e=[],{protocols:t,defaultProtocol:n}=this.options;var o;return this.options.autolink&&e.push(Oh({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!Ah(e,t),protocols:t,defaultProtocol:n}),shouldAutoLink:this.options.shouldAutoLink})),!0===this.options.openOnClick&&e.push((o={type:this.type},new Kn({key:new Gn("handleClickLink"),props:{handleClick:(e,t,n)=>{var r,i;if(0!==n.button)return!1;if(!e.editable)return!1;let s=n.target;const l=[];for(;"DIV"!==s.nodeName;)l.push(s),s=s.parentNode;if(!l.find((e=>"A"===e.nodeName)))return!1;const a=Wl(e.state,o.type.name),c=n.target,d=null!==(r=null==c?void 0:c.href)&&void 0!==r?r:a.href,h=null!==(i=null==c?void 0:c.target)&&void 0!==i?i:a.target;return!(!c||!d||(window.open(d,h),0))}}}))),this.options.linkOnPaste&&e.push(function(e){return new Kn({key:new Gn("handlePasteLink"),props:{handlePaste:(t,n,o)=>{const{state:r}=t,{selection:i}=r,{empty:s}=i;if(s)return!1;let l="";o.content.forEach((e=>{l+=e.textContent}));const a=Ch(l,{defaultProtocol:e.defaultProtocol}).find((e=>e.isLink&&e.value===l));return!(!l||!a)&&e.editor.commands.setMark(e.type,{href:a.href})}}})}({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),e}}),Nh=["title"],Dh={class:"remix"},Rh=["xlink:href"];const Ih=L({name:"TiptapMenuItem",props:{icon:{type:String,required:!0},title:{type:String,required:!0},action:{type:Function,required:!0},isActive:{type:Function,default:null}}},[["render",function(e,t,n,o,r,i){return v(),k("button",{type:"button",class:x(["menu-item",{"is-active":n.isActive?n.isActive():null}]),onClick:t[0]||(t[0]=(...e)=>n.action&&n.action(...e)),title:n.title},[(v(),k("svg",Dh,[z("use",{"xlink:href":`#ri-${n.icon}`},null,8,Rh)]))],10,Nh)}]]),Ph=["title"],Lh={class:"remix"},zh=["xlink:href"],Bh={class:"align-right wbb-mt"};const Vh=L({name:"TiptapMenuItemLink",props:{editor:{type:Object,required:!0},icon:{type:String,required:!0},title:{type:String,required:!0},action:{type:Function,required:!0},isActive:{type:Function,default:null},type:{type:String,default:""}},directives:{clickOutside:ye},setup(e,t){const n=o(!1),r=o(),i=o(),s=o(""),l=()=>{s.value="",n.value=!1},{wbsCnf:a}=B(),{wb_e:c}=V(a);return{linkInput:s,setLinkHandler:()=>{null!==s.value?(s.value?e.editor.chain().focus().extendMarkRange("link").setLink({href:s.value}).run():e.editor.chain().focus().extendMarkRange("link").unsetLink().run(),l()):l()},linkButtonRef:r,linkPopoverRef:i,acivePopover:()=>{const t=e.editor.getAttributes("link").href;s.value=t,n.value=!0},closePopover:l,onClickOutside:()=>{i.value&&i.value.delayHide&&i.value.delayHide(),l()},visiblePopover:n,wb_e:c}}},[["render",function(e,t,n,o,r,i){const s=j,l=_,a=Oe,c=$("click-outside");return v(),k(q,null,[H((v(),k("button",{class:x(["menu-item",{"is-active":n.isActive?n.isActive():null}]),type:"button",onClick:t[0]||(t[0]=(...e)=>o.acivePopover&&o.acivePopover(...e)),title:n.title,ref:"linkButtonRef"},[(v(),k("svg",Lh,[z("use",{"xlink:href":`#ri-${n.icon}`},null,8,zh)]))],10,Ph)),[[c,o.onClickOutside]]),F(a,{ref:"linkPopoverRef","virtual-ref":o.linkButtonRef,visible:o.visiblePopover,trigger:"click","wb-lang":"",title:o.wb_e("设置链接"),"virtual-triggering":"",width:"250"},{default:w((()=>[F(s,{size:"small",modelValue:o.linkInput,"onUpdate:modelValue":t[1]||(t[1]=e=>o.linkInput=e),clearable:""},null,8,["modelValue"]),z("div",Bh,[F(l,{size:"small",text:"",onClick:o.closePopover},{default:w((()=>[M(S(o.wb_e("取消")),1)])),_:1},8,["onClick"]),F(l,{size:"small",type:"primary",onClick:o.setLinkHandler},{default:w((()=>[M(S(o.wb_e("确认")),1)])),_:1},8,["onClick"])])])),_:1},8,["virtual-ref","visible","title"])],64)}]]);const $h=L({name:"TiptapMenuBar",components:{MenuItem:Ih,MenuItemLink:Vh},props:{editor:{type:Object,required:!0}},setup(e,t){const n=o([{icon:"bold",title:"Bold",action:()=>e.editor.chain().focus().toggleBold().run(),isActive:()=>e.editor.isActive("bold")},{icon:"italic",title:"Italic",action:()=>e.editor.chain().focus().toggleItalic().run(),isActive:()=>e.editor.isActive("italic")},{icon:"strikethrough",title:"Strike",action:()=>e.editor.chain().focus().toggleStrike().run(),isActive:()=>e.editor.isActive("strike")},{icon:"underline",title:"Underline",action:()=>e.editor.chain().focus().toggleUnderline().run(),isActive:()=>e.editor.isActive("underline")},{type:"divider"},{icon:"h-2",title:"Heading 2",action:()=>e.editor.chain().focus().toggleHeading({level:2}).run(),isActive:()=>e.editor.isActive("heading",{level:2})},{icon:"h-3",title:"Heading 3",action:()=>e.editor.chain().focus().toggleHeading({level:3}).run(),isActive:()=>e.editor.isActive("heading",{level:3})},{icon:"paragraph",title:"Paragraph",action:()=>e.editor.chain().focus().setParagraph().run(),isActive:()=>e.editor.isActive("paragraph")},{icon:"list-unordered",title:"Bullet List",action:()=>e.editor.chain().focus().toggleBulletList().run(),isActive:()=>e.editor.isActive("bulletList")},{icon:"list-ordered",title:"Ordered List",action:()=>e.editor.chain().focus().toggleOrderedList().run(),isActive:()=>e.editor.isActive("orderedList")},{type:"divider"},{icon:"double-quotes-l",title:"Blockquote",action:()=>e.editor.chain().focus().toggleBlockquote().run(),isActive:()=>e.editor.isActive("blockquote")},{type:"link",icon:"link",title:"Set Link",editor:e.editor,action:()=>{},isActive:()=>e.editor.isActive("link")},{icon:"link-unlink",title:"Unset Link",action:()=>e.editor.chain().focus().unsetLink().run()},{type:"divider"},{icon:"text-wrap",title:"Hard Break",action:()=>e.editor.chain().focus().setHardBreak().run()},{icon:"format-clear",title:"Clear Format",action:()=>e.editor.chain().focus().clearNodes().unsetAllMarks().run()},{type:"divider"},{icon:"arrow-go-back-line",title:"Undo",action:()=>e.editor.chain().focus().undo().run()},{icon:"arrow-go-forward-line",title:"Redo",action:()=>e.editor.chain().focus().redo().run()}]),r=o(),i=o();return{items:n,buttonRef:r,popoverRef:i,onClickOutside:()=>{l(i).popoverRef.delayHide()}}}},[["render",function(e,t,n,o,r,i){const s=Vh,l=Ih;return v(),k("div",null,[(v(!0),k(q,null,W(o.items,((e,t)=>(v(),k(q,null,["divider"===e.type?(v(),k("div",{class:"divider",key:`divider${t}`})):"link"===e.type?(v(),y(s,C({key:"link",ref_for:!0},e),null,16)):(v(),y(l,C({key:t,ref_for:!0},e),null,16))],64)))),256))])}]]),Hh={"aria-hidden":"true",style:{position:"absolute",width:"0",height:"0",overflow:"hidden"},version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"};const Fh=L({name:"MenuItemSvg"},[["render",function(e,t,n,o,r,i){return v(),k("svg",Hh,t[0]||(t[0]=[K('<defs><symbol viewBox="0 0 24 24" id="ri-bold"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M8 11h4.5a2.5 2.5 0 1 0 0-5H8v5zm10 4.5a4.5 4.5 0 0 1-4.5 4.5H6V4h6.5a4.5 4.5 0 0 1 3.256 7.606A4.498 4.498 0 0 1 18 15.5zM8 13v5h5.5a2.5 2.5 0 1 0 0-5H8z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-italic"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M15 20H7v-2h2.927l2.116-12H9V4h8v2h-2.927l-2.116 12H15z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-strikethrough"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M17.154 14c.23.516.346 1.09.346 1.72 0 1.342-.524 2.392-1.571 3.147C14.88 19.622 13.433 20 11.586 20c-1.64 0-3.263-.381-4.87-1.144V16.6c1.52.877 3.075 1.316 4.666 1.316 2.551 0 3.83-.732 3.839-2.197a2.21 2.21 0 0 0-.648-1.603l-.12-.117H3v-2h18v2h-3.846zm-4.078-3H7.629a4.086 4.086 0 0 1-.481-.522C6.716 9.92 6.5 9.246 6.5 8.452c0-1.236.466-2.287 1.397-3.153C8.83 4.433 10.271 4 12.222 4c1.471 0 2.879.328 4.222.984v2.152c-1.2-.687-2.515-1.03-3.946-1.03-2.48 0-3.719.782-3.719 2.346 0 .42.218.786.654 1.099.436.313.974.562 1.613.75.62.18 1.297.414 2.03.699z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-code-view"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M16.95 8.464l1.414-1.414 4.95 4.95-4.95 4.95-1.414-1.414L20.485 12 16.95 8.464zm-9.9 0L3.515 12l3.535 3.536-1.414 1.414L.686 12l4.95-4.95L7.05 8.464z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-h-1"><g><path fill="none" d="M0 0H24V24H0z"></path><path d="M13 20h-2v-7H4v7H2V4h2v7h7V4h2v16zm8-12v12h-2v-9.796l-2 .536V8.67L19.5 8H21z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-h-2"><g><path fill="none" d="M0 0H24V24H0z"></path><path d="M4 4v7h7V4h2v16h-2v-7H4v7H2V4h2zm14.5 4c2.071 0 3.75 1.679 3.75 3.75 0 .857-.288 1.648-.772 2.28l-.148.18L18.034 18H22v2h-7v-1.556l4.82-5.546c.268-.307.43-.709.43-1.148 0-.966-.784-1.75-1.75-1.75-.918 0-1.671.707-1.744 1.606l-.006.144h-2C14.75 9.679 16.429 8 18.5 8z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-h-3"><g><path fill="none" d="M0 0H24V24H0z"></path><path d="M22 8l-.002 2-2.505 2.883c1.59.435 2.757 1.89 2.757 3.617 0 2.071-1.679 3.75-3.75 3.75-1.826 0-3.347-1.305-3.682-3.033l1.964-.382c.156.806.866 1.415 1.718 1.415.966 0 1.75-.784 1.75-1.75s-.784-1.75-1.75-1.75c-.286 0-.556.069-.794.19l-1.307-1.547L19.35 10H15V8h7zM4 4v7h7V4h2v16h-2v-7H4v7H2V4h2z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-h-4"><g><path fill="none" d="M0 0H24V24H0z"></path><path d="M13 20h-2v-7H4v7H2V4h2v7h7V4h2v16zm9-12v8h1.5v2H22v2h-2v-2h-5.5v-1.34l5-8.66H22zm-2 3.133L17.19 16H20v-4.867z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-h-5"><g><path fill="none" d="M0 0H24V24H0z"></path><path d="M22 8v2h-4.323l-.464 2.636c.33-.089.678-.136 1.037-.136 2.21 0 4 1.79 4 4s-1.79 4-4 4c-1.827 0-3.367-1.224-3.846-2.897l1.923-.551c.24.836 1.01 1.448 1.923 1.448 1.105 0 2-.895 2-2s-.895-2-2-2c-.63 0-1.193.292-1.56.748l-1.81-.904L16 8h6zM4 4v7h7V4h2v16h-2v-7H4v7H2V4h2z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-h-6"><g><path fill="none" d="M0 0H24V24H0z"></path><path d="M21.097 8l-2.598 4.5c2.21 0 4.001 1.79 4.001 4s-1.79 4-4 4-4-1.79-4-4c0-.736.199-1.426.546-2.019L18.788 8h2.309zM4 4v7h7V4h2v16h-2v-7H4v7H2V4h2zm14.5 10.5c-1.105 0-2 .895-2 2s.895 2 2 2 2-.895 2-2-.895-2-2-2z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-paragraph"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M12 6v15h-2v-5a6 6 0 1 1 0-12h10v2h-3v15h-2V6h-3zm-2 0a4 4 0 1 0 0 8V6z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-list-unordered"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M8 4h13v2H8V4zM4.5 6.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm0 7a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm0 6.9a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zM8 11h13v2H8v-2zm0 7h13v2H8v-2z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-list-ordered"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M8 4h13v2H8V4zM5 3v3h1v1H3V6h1V4H3V3h2zM3 14v-2.5h2V11H3v-1h3v2.5H4v.5h2v1H3zm2 5.5H3v-1h2V18H3v-1h3v4H3v-1h2v-.5zM8 11h13v2H8v-2zm0 7h13v2H8v-2z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-double-quotes-l"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 0 1-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 0 1-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-text-wrap"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M15 18h1.5a2.5 2.5 0 1 0 0-5H3v-2h13.5a4.5 4.5 0 1 1 0 9H15v2l-4-3 4-3v2zM3 4h18v2H3V4zm6 14v2H3v-2h6z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-format-clear"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M12.651 14.065L11.605 20H9.574l1.35-7.661-7.41-7.41L4.93 3.515 20.485 19.07l-1.414 1.414-6.42-6.42zm-.878-6.535l.27-1.53h-1.8l-2-2H20v2h-5.927L13.5 9.257 11.773 7.53z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-arrow-go-back-line"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M5.828 7l2.536 2.536L6.95 10.95 2 6l4.95-4.95 1.414 1.414L5.828 5H13a8 8 0 1 1 0 16H4v-2h9a6 6 0 1 0 0-12H5.828z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-arrow-go-forward-line"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M18.172 7H11a6 6 0 1 0 0 12h9v2h-9a8 8 0 1 1 0-16h7.172l-2.536-2.536L17.05 1.05 22 6l-4.95 4.95-1.414-1.414L18.172 7z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-link"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M18.4 15.5 17 14.1l1.4-1.4a5 5 0 1 0-7.1-7L9.9 7 8.5 5.7l1.4-1.4a7 7 0 0 1 9.9 10l-1.4 1.3zm-2.9 2.9-1.4 1.4a7 7 0 0 1-9.9-10l1.4-1.3 1.5 1.4-1.5 1.4a5 5 0 1 0 7.1 7l1.4-1.3 1.4 1.4zm-.7-10.6 1.4 1.4-7 7-1.4-1.4 7-7z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-link-unlink"><g><path fill="none" d="M0 0h24v24H0z"></path><path d="M17 17h5v2h-3v3h-2v-5zM7 7H2V5h3V2h2v5zm11.4 8.5L17 14.1l1.4-1.4a5 5 0 1 0-7.1-7L9.9 7 8.5 5.7l1.4-1.4a7 7 0 0 1 9.9 10l-1.4 1.3zm-2.9 2.9-1.4 1.4a7 7 0 0 1-9.9-10l1.4-1.3 1.5 1.4-1.5 1.4a5 5 0 1 0 7.1 7l1.4-1.3 1.4 1.4zm-.7-10.6 1.4 1.4-7 7-1.4-1.4 7-7z"></path></g></symbol><symbol viewBox="0 0 24 24" id="ri-underline"><path fill="currentColor" d="M8 3v9a4 4 0 0 0 8 0V3h2v9a6 6 0 0 1-12 0V3h2ZM4 20h16v2H4v-2Z"></path></symbol></defs>',1)]))}]]),jh={key:0,class:"tiptap-editor"},_h={__name:"Tiptap",props:["modelValue"],emits:["update:modelValue"],setup(e,{emit:t}){const n=e,r=t,i=o(!1),c=f({get:()=>n.modelValue,set:e=>{r("update:modelValue",e)}}),d=((e={})=>{const t=A();return s((()=>{t.value=new va(e)})),a((()=>{var e,n,o;const r=null===(e=t.value)||void 0===e?void 0:e.options.element,i=null==r?void 0:r.cloneNode(!0);null===(n=null==r?void 0:r.parentNode)||void 0===n||n.replaceChild(i,r),null===(o=t.value)||void 0===o||o.destroy()})),t})({extensions:[Rc,ba,Eh.configure({openOnClick:!1})],content:n.modelValue,onUpdate:()=>{const e=d.value.getHTML();r("update:modelValue",e)}});return a((()=>{d.value.destroy()})),(e,t)=>{const n=j;return l(d)?(v(),k("div",jh,[F($h,{class:"editor__header",editor:l(d),mode:i.value,onChangemode:t[0]||(t[0]=e=>i.value=!i.value)},null,8,["editor","mode"]),i.value?(v(),y(n,{key:1,type:"textarea",modelValue:c.value,"onUpdate:modelValue":t[1]||(t[1]=e=>c.value=e)},null,8,["modelValue"])):(v(),y(l(wa),{key:0,editor:l(d)},null,8,["editor"])),F(Fh)])):b("",!0)}}},qh={__name:"HtmlEditor",props:{modelValue:{default:()=>({})},cnf:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,o=t,r=f({get:()=>n.modelValue,set:e=>o("update:modelValue",e)});return(e,t)=>(v(),y(_h,{modelValue:r.value,"onUpdate:modelValue":t[0]||(t[0]=e=>r.value=e)},null,8,["modelValue"]))}},Wh={class:"wbs-content"},Kh={class:"wbs-main"},Uh={class:"align-right"},Jh={class:"ml"},Gh={key:0,class:"wbs-form-table mt"},Qh={class:"row w6em"},Yh={class:"row"},Xh={class:"description ml"},Zh={class:"row"},ep={class:"ml description"},tp={class:"row w8em"},np={class:"show-name"},op={class:"show-img"},rp={class:"show-inner"},ip={class:"show-default"},sp={class:"show-name"},lp={class:"show-name"},ap={class:"with-sub-form-table"},cp={class:"wbs-form-table-sub"},dp={class:"row w8em"},hp={class:"row"},pp={class:"row"},up={class:"ml"},fp={class:"row"},mp={class:"ml"},gp={key:0,class:"mt"},yp={class:"row"},vp={class:"with-sub-form-table"},wp={class:"wbs-form-table-sub"},bp={class:"row w8em"},kp={class:"row"},xp={class:"row"},Sp={class:"row"},Mp={class:"row"},Cp={class:"ib",style:{"line-height":"1.2"}},Op={class:"row"},Tp={class:"section-upload"},Ap={class:"description"},Ep={class:"selector-bar mt"},Np=["href"],Dp={class:"row"},Rp={class:"ib",style:{"line-height":"1.2"}},Ip={class:"description ml"},Pp=["href"],Lp={class:"row"},zp={class:"ib",style:{"line-height":"1.2"}},Bp={class:"description ml"},Vp=["href"],$p={__name:"Base",setup(e){const{wbsCnf:t}=B(),{wb_i18n:n,wb_e:r}=V(t),i=o(0),a=o(0),c=o(t.is_pro),d=U({}),h=U({}),p=o("0"),u=U([{name:J("左中"),value:"lc"},{name:J("左下"),value:"lb"},{name:J("右中"),value:"rc"},{name:J("右下"),value:"rb"}]),f=async e=>{try{await Z.saveData({op:t.action.push,opt:d}),ee.success(r("设置保存成功")),i.value=0,e&&e()}catch(n){ee.error(r("保存失败"))}};return s((()=>{(async()=>{try{const e=await Z.getData({op:t.action.fetch});Object.assign(d,e.data.opt),Object.assign(h,e.data.cnf),a.value=1,D((()=>{i.value=0}))}catch(e){ee.warning(r("数据加载失败"))}})()})),G(d,(()=>i.value++),{deep:!0}),Q(((e,t,n)=>{i.value>0?se({dangerouslyUseHTMLString:!0,confirmButtonText:r("保存并离开"),cancelButtonText:r("放弃修改"),showCancelButton:!0,message:r("您修改的设置尚未保存，确定离开此页面吗？"),beforeClose:async(e,t,o)=>{if("confirm"===e)try{await f(),o(),n()}catch(r){o(),n(!1)}else o(),n()}}):n()})),(e,o)=>{const s=le,m=he,g=de,C=ae,O=ce,T=te,A=j,E=re,N=ie,D=ne("wbs-ctrl-bar"),R=Y;return v(),k("div",Wh,[H((v(),k("div",{class:x(["wbs-content-inner",{"wb-page-loaded":a.value}])},[z("div",Kh,[z("div",Uh,[F(s,{"active-value":"1","inactive-value":"0",modelValue:p.value,"onUpdate:modelValue":o[0]||(o[0]=e=>p.value=e)},null,8,["modelValue"]),z("span",Jh,S(l(r)("预览")),1)]),a.value?(v(),k("table",Gh,[z("tbody",null,[z("tr",null,[z("th",Qh,S(l(r)("展示位置")),1),z("td",null,[F(g,{class:"inline-selector-group",modelValue:d.position,"onUpdate:modelValue":o[1]||(o[1]=e=>d.position=e)},{default:w((()=>[(v(!0),k(q,null,W(u,((e,t)=>(v(),y(m,{class:x(["pst-selector",e.value]),value:e.value,key:"position"+t},{default:w((()=>[z("span",null,S(l(n)[e.name]),1),o[20]||(o[20]=z("div",{class:"position-img"},null,-1))])),_:2},1032,["value","class"])))),128))])),_:1},8,["modelValue"])])]),z("tr",null,[z("th",Yh,S(l(r)("主色调")),1),z("td",null,[F(C,{modelValue:d.wbm.theme_color,"onUpdate:modelValue":o[2]||(o[2]=e=>d.wbm.theme_color=e),"show-alpha":"",predefine:["#0066cc","#a02533","#ca891e","#6bb020","#8b572a"]},null,8,["modelValue"]),z("span",Xh,S(l(r)("* 建议跟网站主色调一致。")),1)])]),z("tr",null,[z("th",Zh,S(l(r)("暗黑模式")),1),z("td",null,[F(s,{"active-value":"1","inactive-value":"0",modelValue:d.dark_switch,"onUpdate:modelValue":o[3]||(o[3]=e=>d.dark_switch=e)},null,8,["modelValue"]),z("span",ep,[z("b",null,S(1==d.dark_switch?l(r)("已设置为暗黑风格，"):l(r)("未启用，")),1),M(S(l(r)("该选项适用于某些固定为暗黑风格的站点。")),1)])])]),z("tr",null,[z("th",tp,S(l(r)("展示模式")),1),z("td",null,[F(g,{class:"inline-selector-group",modelValue:d.is_fold,"onUpdate:modelValue":o[4]||(o[4]=e=>d.is_fold=e)},{default:w((()=>[F(m,{class:"pst-selector",value:"0"},{default:w((()=>[z("span",np,S(l(r)("展开")),1),z("div",op,[z("div",rp,[z("div",ip,[(v(),k(q,null,W(5,((e,t)=>z("div",{class:"inner",key:t}))),64))])])])])),_:1}),F(m,{class:"pst-selector",value:"1"},{default:w((()=>[z("span",sp,S(l(r)("浮标")),1),o[21]||(o[21]=z("div",{class:"show-img"},[z("div",{class:"show-fold"})],-1))])),_:1}),F(m,{class:"pst-selector",value:"2"},{default:w((()=>[z("span",lp,S(l(r)("浮标+窗口")),1),o[22]||(o[22]=z("div",{class:"show-img"},[z("div",{class:"show-fold"}),z("div",{class:"show-fold-more"})],-1))])),_:1})])),_:1},8,["modelValue"])])]),H(z("tr",null,[z("th",null,S(l(r)("展开设置")),1),z("td",null,[z("div",ap,[z("table",cp,[z("tbody",null,[z("tr",null,[z("th",dp,S(l(r)("组件形状")),1),z("td",null,[F(g,{modelValue:d.fillet_select,"onUpdate:modelValue":o[5]||(o[5]=e=>d.fillet_select=e)},{default:w((()=>[F(m,{value:"0"},{default:w((()=>[z("span",null,S(l(r)("默认")),1)])),_:1}),F(m,{value:"1"},{default:w((()=>[z("span",null,S(l(r)("圆角")),1)])),_:1})])),_:1},8,["modelValue"])])]),z("tr",null,[z("th",hp,S(l(r)("组件尺寸")),1),z("td",null,[F(g,{modelValue:d.size_select,"onUpdate:modelValue":o[6]||(o[6]=e=>d.size_select=e)},{default:w((()=>[F(m,{value:"0"},{default:w((()=>[z("span",null,S(l(r)("默认")),1)])),_:1}),F(m,{value:"1"},{default:w((()=>[z("span",null,S(l(r)("大尺寸")),1)])),_:1})])),_:1},8,["modelValue"])])])])])])])],512),[[X,0==d.is_fold]]),H(z("tr",null,[z("th",pp,S(l(r)("浮标名称")),1),z("td",null,[F(s,{"active-value":"1","inactive-value":"0",modelValue:d.name_switch,"onUpdate:modelValue":o[7]||(o[7]=e=>d.name_switch=e)},null,8,["modelValue"]),z("span",up,S(1==d.name_switch?l(r)("显示浮标名称"):l(r)("隐藏浮标名称")),1)])],512),[[X,d.is_fold>0]]),H(z("tr",null,[z("th",fp,S(l(r)("浮标动效")),1),z("td",null,[F(s,{"active-value":"1","inactive-value":"0",modelValue:d.buoy_animation,"onUpdate:modelValue":o[8]||(o[8]=e=>d.buoy_animation=e)},null,8,["modelValue"]),z("span",mp,S(1==d.buoy_animation?l(r)("显示动效"):l(r)("隐藏动效")),1),d.buoy_animation>0?(v(),k("div",gp,[M(S(l(r)("出现时间间隔："))+" ",1),F(O,{modelValue:d.buoy_animation_interval,"onUpdate:modelValue":o[9]||(o[9]=e=>d.buoy_animation_interval=e),min:3,max:20,label:l(r)("秒"),size:"small","model-value":Number(d.buoy_animation_interval)},null,8,["modelValue","label","model-value"]),M(" "+S(l(r)("秒")),1)])):b("",!0)])],512),[[X,1==d.is_fold]]),z("tr",null,[z("th",yp,S(l(r)("界面元素")),1),z("td",null,[z("div",vp,[z("table",wp,[z("tbody",null,[z("tr",null,[z("th",bp,S(l(r)("头像")),1),z("td",null,[F(T,{modelValue:d.avatar_url,"onUpdate:modelValue":o[10]||(o[10]=e=>d.avatar_url=e)},null,8,["modelValue"])])]),z("tr",null,[z("th",kp,S(l(r)("联系人昵称")),1),z("td",null,[F(A,{size:"small",modelValue:d.contact_name,"onUpdate:modelValue":o[11]||(o[11]=e=>d.contact_name=e)},null,8,["modelValue"])])]),z("tr",null,[z("th",xp,S(l(r)("欢迎语")),1),z("td",null,[F(qh,{modelValue:d.contact_msg,"onUpdate:modelValue":o[12]||(o[12]=e=>d.contact_msg=e)},null,8,["modelValue"])])]),z("tr",null,[z("th",Sp,S(l(r)("响应文字")),1),z("td",null,[F(qh,{modelValue:d.open_msg,"onUpdate:modelValue":o[13]||(o[13]=e=>d.open_msg=e)},null,8,["modelValue"])])]),z("tr",null,[z("th",Mp,[z("span",Cp,S(l(r)("选择提示")),1)]),z("td",null,[F(A,{type:"input",modelValue:d.mode_tips,"onUpdate:modelValue":o[14]||(o[14]=e=>d.mode_tips=e),max:"100"},null,8,["modelValue"])])])])])])])])]),z("tbody",null,[z("tr",null,[z("th",Op,S(l(r)("会员中心")),1),z("td",null,[z("div",Tp,[F(T,{placeholder:"Logo图片",modelValue:d.wbm.logo,"onUpdate:modelValue":o[15]||(o[15]=e=>d.wbm.logo=e)},null,8,["modelValue"]),z("p",Ap,S(l(r)("展示时高度为60像素，建议准备高度至少为120像素（包括上下留白）的图片。")),1)]),z("div",Ep,[M(S(l(r)("会员中心路径:"))+" ",1),z("a",{href:l(t).wbm_url,target:"_blank"},S(l(t).wbm_url),9,Np)])])]),z("tr",null,[z("th",Dp,[z("span",Rp,S(l(r)("兼容小部件")),1)]),z("td",null,[F(A,{size:"small",class:"w8em",modelValue:d.other_tool_name,"onUpdate:modelValue":o[16]||(o[16]=e=>d.other_tool_name=e)},null,8,["modelValue"]),z("span",Ip,[M(S(l(r)("* 填写主题小部件模块的css类名，在插件激活的页面隐藏该模块。")),1),z("a",{href:l(oe)("https://www.wbolt.com/faq/41144.html",l(t).locale),target:"faq"},S(l(r)("如何找到小部件css类名?")),9,Pp)])])]),z("tr",null,[z("th",Lp,[z("span",zp,S(l(r)("兼容暗黑模式")),1)]),z("td",null,[F(A,{size:"small",class:"w8em",modelValue:d.dark_mode_class,"onUpdate:modelValue":o[17]||(o[17]=e=>d.dark_mode_class=e)},null,8,["modelValue"]),z("span",Bp,[M(S(l(r)("* 填写主题暗黑模式激活时的css类名，以响应模式间的切换。")),1),z("a",{href:l(oe)("https://www.wbolt.com/faq/41150.html",l(t).locale),target:"faq"},S(l(r)("如何找到暗黑模式css类名?")),9,Vp)])])])])])):b("",!0),H(F(E,{class:"mt"},null,512),[[X,a.value]])]),1==p.value?(v(),y(ue,{key:0,opt:d,cnf:h,onClosePreview:o[18]||(o[18]=e=>p.value="0")},null,8,["opt","cnf"])):b("",!0),c.value?b("",!0):(v(),y(N,{key:1}))],2)),[[R,!a.value]]),F(D,{changed:i.value,onSubmit:o[19]||(o[19]=e=>f())},null,8,["changed"]),F(pe)])}}};export{$p as default};
